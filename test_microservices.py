#!/usr/bin/env python3
"""
Comprehensive API testing script for oneHRMS microservices.

This script tests all the restored microservices APIs to validate functionality
and ensure proper integration between services.
"""

import asyncio
import json
import sys
from datetime import datetime, time
from typing import Dict, Any, Optional
from uuid import uuid4

import httpx
import pytest
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

# Service configurations
SERVICES = {
    "employee": {
        "name": "Employee Service",
        "url": "http://localhost:8001",
        "health_endpoint": "/health",
        "docs_endpoint": "/docs"
    },
    "attendance": {
        "name": "Attendance Service", 
        "url": "http://localhost:8002",
        "health_endpoint": "/health",
        "docs_endpoint": "/docs"
    }
}

# Test data
TEST_DATA = {
    "department": {
        "name": "Engineering",
        "code": "ENG",
        "description": "Engineering Department"
    },
    "position": {
        "title": "Software Engineer",
        "code": "SE",
        "description": "Software Engineer Position",
        "level": 3
    },
    "employee": {
        "employee_id": "EMP001",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "phone": "+**********",
        "status": "active"
    },
    "shift_type": {
        "name": "Standard Shift",
        "code": "STD",
        "description": "Standard 9-5 shift",
        "start_time": "09:00:00",
        "end_time": "17:00:00",
        "working_hours": 8.0,
        "break_duration": 60
    }
}

# Authentication token (mock for development)
AUTH_TOKEN = "mock-jwt-token-for-development"
HEADERS = {
    "Authorization": f"Bearer {AUTH_TOKEN}",
    "Content-Type": "application/json"
}


class APITester:
    """API testing class for microservices."""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
        self.created_resources = {}
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def test_service_health(self, service_name: str, service_config: Dict[str, str]) -> bool:
        """Test service health endpoint."""
        try:
            response = await self.client.get(
                f"{service_config['url']}{service_config['health_endpoint']}"
            )
            
            if response.status_code == 200:
                health_data = response.json()
                console.print(f"✅ {service_config['name']} is healthy", style="green")
                console.print(f"   Status: {health_data.get('status', 'unknown')}")
                console.print(f"   Version: {health_data.get('version', 'unknown')}")
                return True
            else:
                console.print(f"❌ {service_config['name']} health check failed: {response.status_code}", style="red")
                return False
                
        except Exception as e:
            console.print(f"❌ {service_config['name']} is not accessible: {e}", style="red")
            return False
    
    async def test_employee_service(self) -> bool:
        """Test Employee service endpoints."""
        console.print("\n🧪 Testing Employee Service APIs...", style="bold blue")
        
        service_url = SERVICES["employee"]["url"]
        success_count = 0
        total_tests = 0
        
        try:
            # Test 1: Create Department
            total_tests += 1
            console.print("📁 Testing department creation...")
            response = await self.client.post(
                f"{service_url}/api/v1/employees/departments/",
                json=TEST_DATA["department"],
                headers=HEADERS
            )
            
            if response.status_code == 201:
                dept_data = response.json()["data"]
                self.created_resources["department_id"] = dept_data["id"]
                console.print(f"   ✅ Department created: {dept_data['name']} ({dept_data['code']})", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Department creation failed: {response.status_code}", style="red")
                console.print(f"   Response: {response.text}")
            
            # Test 2: Create Position
            total_tests += 1
            console.print("💼 Testing position creation...")
            position_data = TEST_DATA["position"].copy()
            if "department_id" in self.created_resources:
                position_data["department_id"] = self.created_resources["department_id"]
            
            response = await self.client.post(
                f"{service_url}/api/v1/employees/positions/",
                json=position_data,
                headers=HEADERS
            )
            
            if response.status_code == 201:
                pos_data = response.json()["data"]
                self.created_resources["position_id"] = pos_data["id"]
                console.print(f"   ✅ Position created: {pos_data['title']} ({pos_data['code']})", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Position creation failed: {response.status_code}", style="red")
                console.print(f"   Response: {response.text}")
            
            # Test 3: Generate Employee ID
            total_tests += 1
            console.print("🆔 Testing employee ID generation...")
            response = await self.client.post(
                f"{service_url}/api/v1/employees/generate-employee-id?prefix=TEST",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                emp_id_data = response.json()
                generated_id = emp_id_data["employee_id"]
                console.print(f"   ✅ Employee ID generated: {generated_id}", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Employee ID generation failed: {response.status_code}", style="red")
            
            # Test 4: Create Employee
            total_tests += 1
            console.print("👤 Testing employee creation...")
            employee_data = TEST_DATA["employee"].copy()
            if "department_id" in self.created_resources:
                employee_data["department_id"] = self.created_resources["department_id"]
            if "position_id" in self.created_resources:
                employee_data["position_id"] = self.created_resources["position_id"]
            
            response = await self.client.post(
                f"{service_url}/api/v1/employees/",
                json=employee_data,
                headers=HEADERS
            )
            
            if response.status_code == 201:
                emp_data = response.json()["data"]
                self.created_resources["employee_id"] = emp_data["id"]
                console.print(f"   ✅ Employee created: {emp_data['full_name']} ({emp_data['employee_id']})", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Employee creation failed: {response.status_code}", style="red")
                console.print(f"   Response: {response.text}")
            
            # Test 5: Get Employee
            total_tests += 1
            if "employee_id" in self.created_resources:
                console.print("🔍 Testing employee retrieval...")
                response = await self.client.get(
                    f"{service_url}/api/v1/employees/{self.created_resources['employee_id']}",
                    headers=HEADERS
                )
                
                if response.status_code == 200:
                    emp_data = response.json()["data"]
                    console.print(f"   ✅ Employee retrieved: {emp_data['full_name']}", style="green")
                    success_count += 1
                else:
                    console.print(f"   ❌ Employee retrieval failed: {response.status_code}", style="red")
            
            # Test 6: Search Employees
            total_tests += 1
            console.print("🔎 Testing employee search...")
            response = await self.client.get(
                f"{service_url}/api/v1/employees/?page=1&page_size=10",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                search_data = response.json()
                console.print(f"   ✅ Employee search successful: {search_data['total']} employees found", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Employee search failed: {response.status_code}", style="red")
            
        except Exception as e:
            console.print(f"❌ Employee service testing failed: {e}", style="red")
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        console.print(f"\n📊 Employee Service Results: {success_count}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80% success rate threshold
    
    async def test_attendance_service(self) -> bool:
        """Test Attendance service endpoints."""
        console.print("\n🧪 Testing Attendance Service APIs...", style="bold blue")
        
        service_url = SERVICES["attendance"]["url"]
        success_count = 0
        total_tests = 0
        
        try:
            # Test 1: Create Shift Type
            total_tests += 1
            console.print("⏰ Testing shift type creation...")
            response = await self.client.post(
                f"{service_url}/api/v1/attendance/shift-types/",
                json=TEST_DATA["shift_type"],
                headers=HEADERS
            )
            
            if response.status_code == 201:
                shift_data = response.json()["data"]
                self.created_resources["shift_type_id"] = shift_data["id"]
                console.print(f"   ✅ Shift type created: {shift_data['name']} ({shift_data['code']})", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Shift type creation failed: {response.status_code}", style="red")
                console.print(f"   Response: {response.text}")
            
            # Test 2: Employee Check-in
            total_tests += 1
            if "employee_id" in self.created_resources:
                console.print("📥 Testing employee check-in...")
                checkin_data = {
                    "employee_id": self.created_resources["employee_id"],
                    "location": "Office",
                    "device_info": "Test Device",
                    "notes": "API Test Check-in"
                }
                
                response = await self.client.post(
                    f"{service_url}/api/v1/attendance/check-in",
                    json=checkin_data,
                    headers=HEADERS
                )
                
                if response.status_code == 201:
                    attendance_data = response.json()["data"]
                    self.created_resources["attendance_record_id"] = attendance_data["id"]
                    console.print(f"   ✅ Check-in successful at {attendance_data['check_in_time']}", style="green")
                    success_count += 1
                else:
                    console.print(f"   ❌ Check-in failed: {response.status_code}", style="red")
                    console.print(f"   Response: {response.text}")
            
            # Test 3: Get Today's Attendance
            total_tests += 1
            if "employee_id" in self.created_resources:
                console.print("📅 Testing today's attendance retrieval...")
                response = await self.client.get(
                    f"{service_url}/api/v1/attendance/employee/{self.created_resources['employee_id']}/today",
                    headers=HEADERS
                )
                
                if response.status_code == 200:
                    attendance_data = response.json()["data"]
                    if attendance_data:
                        console.print(f"   ✅ Today's attendance found: Status {attendance_data['status']}", style="green")
                        success_count += 1
                    else:
                        console.print("   ⚠️ No attendance record found for today", style="yellow")
                        success_count += 0.5  # Partial success
                else:
                    console.print(f"   ❌ Today's attendance retrieval failed: {response.status_code}", style="red")
            
            # Test 4: Employee Check-out
            total_tests += 1
            if "employee_id" in self.created_resources:
                console.print("📤 Testing employee check-out...")
                checkout_data = {
                    "employee_id": self.created_resources["employee_id"],
                    "location": "Office",
                    "device_info": "Test Device",
                    "notes": "API Test Check-out"
                }
                
                response = await self.client.post(
                    f"{service_url}/api/v1/attendance/check-out",
                    json=checkout_data,
                    headers=HEADERS
                )
                
                if response.status_code == 200:
                    attendance_data = response.json()["data"]
                    console.print(f"   ✅ Check-out successful at {attendance_data['check_out_time']}", style="green")
                    console.print(f"   Working hours: {attendance_data['working_hours']:.2f}", style="cyan")
                    success_count += 1
                else:
                    console.print(f"   ❌ Check-out failed: {response.status_code}", style="red")
                    console.print(f"   Response: {response.text}")
            
            # Test 5: Search Attendance Records
            total_tests += 1
            console.print("🔎 Testing attendance search...")
            response = await self.client.get(
                f"{service_url}/api/v1/attendance/?page=1&page_size=10",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                search_data = response.json()
                console.print(f"   ✅ Attendance search successful: {search_data['total']} records found", style="green")
                success_count += 1
            else:
                console.print(f"   ❌ Attendance search failed: {response.status_code}", style="red")
            
        except Exception as e:
            console.print(f"❌ Attendance service testing failed: {e}", style="red")
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        console.print(f"\n📊 Attendance Service Results: {success_count}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80% success rate threshold
    
    async def run_comprehensive_tests(self) -> Dict[str, bool]:
        """Run comprehensive tests on all services."""
        console.print(Panel.fit("🚀 oneHRMS Microservices API Testing", style="bold magenta"))
        
        results = {}
        
        # Test service health
        console.print("\n🏥 Testing Service Health...", style="bold yellow")
        for service_name, service_config in SERVICES.items():
            results[f"{service_name}_health"] = await self.test_service_health(service_name, service_config)
        
        # Test Employee service
        if results.get("employee_health", False):
            results["employee_apis"] = await self.test_employee_service()
        else:
            console.print("⚠️ Skipping Employee API tests - service not healthy", style="yellow")
            results["employee_apis"] = False
        
        # Test Attendance service
        if results.get("attendance_health", False):
            results["attendance_apis"] = await self.test_attendance_service()
        else:
            console.print("⚠️ Skipping Attendance API tests - service not healthy", style="yellow")
            results["attendance_apis"] = False
        
        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """Print test summary."""
        console.print("\n" + "="*60, style="bold")
        console.print("📋 TEST SUMMARY", style="bold magenta")
        console.print("="*60, style="bold")
        
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("Test Category", style="cyan")
        table.add_column("Status", justify="center")
        table.add_column("Details", style="dim")
        
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            details = "All tests successful" if passed else "Some tests failed"
            table.add_row(test_name.replace("_", " ").title(), status, details)
        
        console.print(table)
        
        # Overall status
        total_tests = len(results)
        passed_tests = sum(results.values())
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        if success_rate >= 80:
            console.print(f"\n🎉 Overall Success Rate: {success_rate:.1f}% - EXCELLENT!", style="bold green")
        elif success_rate >= 60:
            console.print(f"\n⚠️ Overall Success Rate: {success_rate:.1f}% - GOOD", style="bold yellow")
        else:
            console.print(f"\n❌ Overall Success Rate: {success_rate:.1f}% - NEEDS ATTENTION", style="bold red")


async def main():
    """Main testing function."""
    console.print("Starting oneHRMS Microservices API Testing...\n", style="bold")
    
    async with APITester() as tester:
        results = await tester.run_comprehensive_tests()
        tester.print_summary(results)
        
        # Print service documentation links
        console.print("\n📚 Service Documentation:", style="bold blue")
        for service_name, service_config in SERVICES.items():
            docs_url = f"{service_config['url']}{service_config['docs_endpoint']}"
            console.print(f"   {service_config['name']}: {docs_url}")
        
        return results


if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        
        # Exit with appropriate code
        total_tests = len(results)
        passed_tests = sum(results.values())
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        sys.exit(0 if success_rate >= 80 else 1)
        
    except KeyboardInterrupt:
        console.print("\n⚠️ Testing interrupted by user", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n❌ Testing failed with error: {e}", style="red")
        sys.exit(1)
