version: "3.8"

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: onehrms_postgres
    environment:
      POSTGRES_DB: hrms_microservices
      POSTGRES_USER: hrms_user
      POSTGRES_PASSWORD: hrms_password
      POSTGRES_MULTIPLE_DATABASES: employee_db,payroll_db,attendance_db,leave_db,recruitment_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres-init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - onehrms_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hrms_user -d hrms_microservices"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: onehrms_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - onehrms_network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kong API Gateway
  kong:
    image: kong:3.4-alpine
    container_name: onehrms_kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_PROXY_LISTEN: 0.0.0.0:8000
    volumes:
      - ./kong:/kong/declarative
    ports:
      - "8000:8000" # Proxy
      - "8443:8443" # Proxy SSL
      - "8001:8001" # Admin API
      - "8444:8444" # Admin API SSL
    networks:
      - onehrms_network
    depends_on:
      - employee-service
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Employee Management Service
  employee-service:
    build:
      context: .
      dockerfile: hrms/microservices/employee/Dockerfile
      target: development
    container_name: onehrms_employee_service
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=**************************************************/employee_db
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=json
      - TESTING=false
      - KEYCLOAK_URL=http://keycloak:8080
      - KEYCLOAK_REALM=hrms
      - KEYCLOAK_CLIENT_ID=employee-service
    volumes:
      - .:/app
      - employee_uploads:/app/uploads
      - employee_logs:/app/logs
    ports:
      - "8100:8100"
    networks:
      - onehrms_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Payroll Service
  payroll-service:
    build:
      context: .
      dockerfile: hrms/microservices/payroll/Dockerfile
      target: development
    container_name: onehrms_payroll_service
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=**************************************************/payroll_db
      - REDIS_URL=redis://redis:6379/1
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=json
      - TESTING=false
      - KEYCLOAK_URL=http://keycloak:8080
      - KEYCLOAK_REALM=hrms
      - KEYCLOAK_CLIENT_ID=payroll-service
    volumes:
      - .:/app
      - payroll_uploads:/app/uploads
      - payroll_logs:/app/logs
    ports:
      - "8101:8101"
    networks:
      - onehrms_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Attendance Service
  attendance-service:
    build:
      context: .
      dockerfile: hrms/microservices/attendance/Dockerfile
      target: development
    container_name: onehrms_attendance_service
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=**************************************************/attendance_db
      - REDIS_URL=redis://redis:6379/2
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=json
      - TESTING=false
      - KEYCLOAK_URL=http://keycloak:8080
      - KEYCLOAK_REALM=hrms
      - KEYCLOAK_CLIENT_ID=attendance-service
      - EMPLOYEE_SERVICE_URL=http://employee-service:8100
      - PAYROLL_SERVICE_URL=http://payroll-service:8101
    volumes:
      - .:/app
      - attendance_uploads:/app/uploads
      - attendance_logs:/app/logs
    ports:
      - "8102:8102"
    networks:
      - onehrms_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      employee-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8102/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Leave Management Service (placeholder for future implementation)
  leave-service:
    image: nginx:alpine
    container_name: onehrms_leave_service
    volumes:
      - ./docker/nginx-placeholder.conf:/etc/nginx/nginx.conf
    ports:
      - "8103:8103"
    networks:
      - onehrms_network
    profiles:
      - future

  # Recruitment Service (placeholder for future implementation)
  recruitment-service:
    image: nginx:alpine
    container_name: onehrms_recruitment_service
    volumes:
      - ./docker/nginx-placeholder.conf:/etc/nginx/nginx.conf
    ports:
      - "8104:8104"
    networks:
      - onehrms_network
    profiles:
      - future

  # AI Services - Ollama for LLM models
  ollama:
    image: ollama/ollama:latest
    container_name: onehrms_ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - onehrms_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Server for AI agent orchestration
  mcp-server:
    build:
      context: .
      dockerfile: mcp/Dockerfile
    container_name: onehrms_mcp_server
    ports:
      - "8200:8200"
    volumes:
      - ./mcp:/app/mcp
      - ./hrms:/app/hrms
      - mcp_data:/app/data
    environment:
      - MCP_JWT_SECRET=${MCP_JWT_SECRET:-default-secret-change-in-production}
      - MCP_DB_USER=${MCP_DB_USER:-mcp_user}
      - MCP_DB_PASSWORD=${MCP_DB_PASSWORD:-mcp_password}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434
    depends_on:
      - postgres
      - redis
      - ollama
    networks:
      - onehrms_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8200/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Agent Registry for managing AI agents
  agent-registry:
    build:
      context: .
      dockerfile: agents/Dockerfile
    container_name: onehrms_agent_registry
    ports:
      - "8201:8201"
    volumes:
      - ./agents:/app/agents
      - ./hrms:/app/hrms
    environment:
      - REGISTRY_DB_USER=${REGISTRY_DB_USER:-registry_user}
      - REGISTRY_DB_PASSWORD=${REGISTRY_DB_PASSWORD:-registry_password}
    depends_on:
      - postgres
      - mcp-server
    networks:
      - onehrms_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8201/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Observability
  prometheus:
    image: prom/prometheus:latest
    container_name: onehrms_prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - onehrms_network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: onehrms_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    networks:
      - onehrms_network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: onehrms_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - onehrms_network
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: onehrms_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - onehrms_network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: onehrms_logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
      - "9600:9600"
    networks:
      - onehrms_network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  # Development Tools
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: onehrms_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: "False"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - onehrms_network
    depends_on:
      - postgres
    profiles:
      - dev-tools

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: onehrms_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - onehrms_network
    depends_on:
      - redis
    profiles:
      - dev-tools

  # Testing Services
  test-runner:
    build:
      context: .
      dockerfile: hrms/microservices/employee/Dockerfile
      target: testing
    container_name: onehrms_test_runner
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=**************************************************/test_employee_db
      - REDIS_URL=redis://redis:6379/1
      - TESTING=true
    volumes:
      - .:/app
      - test_reports:/app/htmlcov
    networks:
      - onehrms_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - testing

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  employee_uploads:
    driver: local
  employee_logs:
    driver: local
  payroll_uploads:
    driver: local
  payroll_logs:
    driver: local
  attendance_uploads:
    driver: local
  attendance_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  pgadmin_data:
    driver: local
  test_reports:
    driver: local
  ollama_data:
    driver: local
  mcp_data:
    driver: local

networks:
  onehrms_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
