version: "3.8"

services:
  # Database
  mariadb:
    image: mariadb:10.8
    container_name: onehrms_mariadb
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --skip-character-set-client-handshake
      - --skip-innodb-read-only-compressed
    environment:
      MYSQL_ROOT_PASSWORD: hrms_root_password
      MYSQL_DATABASE: hrms_dev
      MYSQL_USER: hrms_user
      MYSQL_PASSWORD: hrms_password
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./docker/mysql-init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - hrms_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: onehrms_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hrms_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # oneHRMS Development Environment
  hrms_dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: onehrms_dev
    environment:
      - PYTHONPATH=/app
      - TESTING=false
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_NAME=hrms_dev
      - DB_USER=hrms_user
      - DB_PASSWORD=hrms_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
      - hrms_logs:/app/logs
      - hrms_uploads:/app/uploads
    ports:
      - "8000:8000" # Main application
      - "8001:8001" # API Gateway (Kong)
      - "8002:8002" # Agent Registry
    networks:
      - hrms_network
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        sleep 10 &&
        echo 'Running tests...' &&
        python -m pytest tests/ -v &&
        echo 'Starting development server...' &&
        tail -f /dev/null
      "

  # Ollama for AI agents (optional)
  ollama:
    image: ollama/ollama:latest
    container_name: onehrms_ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - hrms_network
    environment:
      - OLLAMA_HOST=0.0.0.0
    profiles:
      - ai # Only start with --profile ai

  # Kong API Gateway (for future microservices)
  kong:
    image: kong:3.4-alpine
    container_name: onehrms_kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
    volumes:
      - ./kong:/kong/declarative
    ports:
      - "8000:8000" # Proxy
      - "8443:8443" # Proxy SSL
      - "8001:8001" # Admin API
      - "8444:8444" # Admin API SSL
    networks:
      - hrms_network
    profiles:
      - gateway # Only start with --profile gateway

volumes:
  mariadb_data:
    driver: local
  redis_data:
    driver: local
  hrms_logs:
    driver: local
  hrms_uploads:
    driver: local
  ollama_data:
    driver: local

networks:
  hrms_network:
    driver: bridge
