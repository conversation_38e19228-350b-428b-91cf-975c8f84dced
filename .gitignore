# oneHRMS - Comprehensive .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Database
*.db
*.sqlite
*.sqlite3
*.db-journal

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node_modules
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db

# oneHRMS specific
uploads/
media/
static/
staticfiles/
.env.*
!.env.example
secrets/
*.key
*.pem
*.crt
*.csr

# Backup files
*.bak
*.backup
*.old
*.orig

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Frappe/ERPNext specific (if any legacy files)
sites/
apps/
bench-repo/
config/
logs/

# oneHRMS Development Files (not for production)
# Coverage reports
baseline_test_coverage_*/
htmlcov/

# Development databases
audit.db
onehrms.db
hrms_dev.db
*.db-wal
*.db-shm

# Security reports
bandit-report.json
security-report.json

# Development logs
phase*.log
phase*_execution.log
*.execution.log

# Build artifacts
build/
dist/
.build/

# Development cache
.pytest_cache/
.coverage.*
coverage.xml
.nyc_output/

# Development tools
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml

# Legacy and Deprecated Code (not for production)
Legacy-deprecated/
*-deprecated/
*-legacy/
legacy/
deprecated/

# Legacy Frappe/ERPNext code in hrms
hrms/legacy/
hrms/hrms/
**/legacy/
**/deprecated/

# Development Phase Planning and Reports
docs/PHASE*.md
docs/phase*.md
docs/*PHASE*.md
docs/*phase*.md
docs/AUDIT*.md
docs/audit*.md
docs/*AUDIT*.md
docs/*audit*.md
docs/IMPLEMENTATION*.md
docs/implementation*.md
docs/*IMPLEMENTATION*.md
docs/*implementation*.md
docs/COMPLETION*.md
docs/completion*.md
docs/*COMPLETION*.md
docs/*completion*.md
docs/RECOVERY*.md
docs/recovery*.md
docs/*RECOVERY*.md
docs/*recovery*.md
docs/MIGRATION*.md
docs/migration*.md
docs/*MIGRATION*.md
docs/*migration*.md
docs/BASELINE*.md
docs/baseline*.md
docs/*BASELINE*.md
docs/*baseline*.md
docs/COMPREHENSIVE*.md
docs/comprehensive*.md
docs/*COMPREHENSIVE*.md
docs/*comprehensive*.md
docs/CRITICAL*.md
docs/critical*.md
docs/*CRITICAL*.md
docs/*critical*.md
docs/FRONTEND*.md
docs/frontend*.md
docs/*FRONTEND*.md
docs/*frontend*.md
docs/MISSING*.md
docs/missing*.md
docs/*MISSING*.md
docs/*missing*.md
docs/SECURITY*.md
docs/security*.md
docs/*SECURITY*.md
docs/*security*.md
docs/TESTING*.md
docs/testing*.md
docs/*TESTING*.md
docs/*testing*.md
docs/DASHBOARD*.md
docs/dashboard*.md
docs/*DASHBOARD*.md
docs/*dashboard*.md
docs/ADMIN*.md
docs/admin*.md
docs/*ADMIN*.md
docs/*admin*.md

# Development Planning Documents
docs/Phase-wise*.md
docs/phase-wise*.md
docs/*Phase-wise*.md
docs/*phase-wise*.md
docs/Tasks*.md
docs/tasks*.md
docs/*Tasks*.md
docs/*tasks*.md
docs/Subtasks*.md
docs/subtasks*.md
docs/*Subtasks*.md
docs/*subtasks*.md
docs/Architecture*.md
docs/architecture*.md
docs/*Architecture*.md
docs/*architecture*.md
docs/Restructuring*.md
docs/restructuring*.md
docs/*Restructuring*.md
docs/*restructuring*.md
docs/Implementation_Plan*.md
docs/implementation_plan*.md
docs/*Implementation_Plan*.md
docs/*implementation_plan*.md
docs/UPDATED*.md
docs/updated*.md
docs/*UPDATED*.md
docs/*updated*.md
docs/QUICK_REFERENCE*.md
docs/quick_reference*.md
docs/*QUICK_REFERENCE*.md
docs/*quick_reference*.md

# AI Agents and Development Tools Documentation
docs/AI_AGENTS*.md
docs/ai_agents*.md
docs/*AI_AGENTS*.md
docs/*ai_agents*.md

# Service Implementation Documentation (Development)
docs/*-service-implementation.md
docs/*_service_implementation.md
docs/service-implementation*.md
docs/service_implementation*.md
