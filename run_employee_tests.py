#!/usr/bin/env python3
"""
Test runner script for Employee Management Service tests.

Runs different categories of tests and provides a summary report.
"""

import subprocess
import sys
import time
from pathlib import Path


def run_test_category(category_name, test_path, extra_args=None):
    """Run a category of tests and return results."""
    print(f"\n{'='*60}")
    print(f"Running {category_name} Tests")
    print(f"{'='*60}")

    cmd = [sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"]
    if extra_args:
        cmd.extend(extra_args)

    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120, cwd=Path(__file__).parent)
        end_time = time.time()

        # Parse results
        stdout = result.stdout
        if "failed" in stdout.lower():
            status = "FAILED"
        elif "passed" in stdout.lower():
            status = "PASSED"
        else:
            status = "UNKNOWN"

        # Extract test counts
        lines = stdout.split("\n")
        summary_line = ""
        for line in lines:
            if "passed" in line and ("failed" in line or "error" in line or "warning" in line):
                summary_line = line.strip()
                break
            elif line.strip().endswith("passed"):
                summary_line = line.strip()
                break

        return {
            "category": category_name,
            "status": status,
            "summary": summary_line,
            "duration": f"{end_time - start_time:.2f}s",
            "return_code": result.returncode,
            "stdout": stdout,
            "stderr": result.stderr,
        }

    except subprocess.TimeoutExpired:
        return {
            "category": category_name,
            "status": "TIMEOUT",
            "summary": "Test execution timed out",
            "duration": "120s+",
            "return_code": -1,
            "stdout": "",
            "stderr": "Timeout",
        }
    except Exception as e:
        return {
            "category": category_name,
            "status": "ERROR",
            "summary": f"Error running tests: {e}",
            "duration": "N/A",
            "return_code": -1,
            "stdout": "",
            "stderr": str(e),
        }


def main():
    """Run all test categories and provide summary."""
    print("Employee Management Service - Test Suite Runner")
    print("=" * 60)

    # Define test categories
    test_categories = [
        {
            "name": "Unit Tests (Service Layer)",
            "path": "tests/microservices/employee/test_employee_service.py",
            "args": None,
        },
        {
            "name": "Unit Tests (Models)",
            "path": "tests/microservices/employee/test_employee_models.py",
            "args": None,
        },
        {"name": "API Tests", "path": "tests/microservices/employee/test_employee_api.py", "args": None},
        {
            "name": "Integration Tests",
            "path": "tests/microservices/employee/test_integration.py",
            "args": None,
        },
        {
            "name": "Performance Tests",
            "path": "tests/microservices/employee/test_performance.py",
            "args": ["-m", "performance"],
        },
        {"name": "Security Tests", "path": "tests/microservices/employee/test_security.py", "args": None},
    ]

    # Run tests
    results = []
    for category in test_categories:
        result = run_test_category(category["name"], category["path"], category["args"])
        results.append(result)

        # Print immediate result
        print(f"Status: {result['status']}")
        print(f"Duration: {result['duration']}")
        if result["summary"]:
            print(f"Summary: {result['summary']}")

        # Show errors if any
        if result["status"] in ["FAILED", "ERROR", "TIMEOUT"]:
            print(f"Error details:")
            if result["stderr"]:
                print(result["stderr"][:500])  # First 500 chars of stderr
            if result["stdout"]:
                # Show last part of stdout for error context
                stdout_lines = result["stdout"].split("\n")
                error_lines = [line for line in stdout_lines if "FAILED" in line or "ERROR" in line]
                if error_lines:
                    print("Failed tests:")
                    for line in error_lines[:5]:  # Show first 5 failed tests
                        print(f"  {line}")

    # Summary report
    print(f"\n{'='*60}")
    print("TEST SUMMARY REPORT")
    print(f"{'='*60}")

    total_categories = len(results)
    passed_categories = sum(1 for r in results if r["status"] == "PASSED")
    failed_categories = sum(1 for r in results if r["status"] == "FAILED")
    error_categories = sum(1 for r in results if r["status"] in ["ERROR", "TIMEOUT"])

    print(f"Total Test Categories: {total_categories}")
    print(f"Passed: {passed_categories}")
    print(f"Failed: {failed_categories}")
    print(f"Errors/Timeouts: {error_categories}")
    print(f"Success Rate: {(passed_categories/total_categories)*100:.1f}%")

    print(f"\nDetailed Results:")
    print(f"{'Category':<30} {'Status':<10} {'Duration':<10} {'Summary'}")
    print("-" * 80)

    for result in results:
        category = result["category"][:29]
        status = result["status"]
        duration = result["duration"]
        summary = result["summary"][:30] if result["summary"] else ""

        print(f"{category:<30} {status:<10} {duration:<10} {summary}")

    # Coverage report for working tests
    print(f"\n{'='*60}")
    print("RUNNING COVERAGE REPORT")
    print(f"{'='*60}")

    working_tests = [
        "tests/microservices/employee/test_employee_service.py",
        "tests/microservices/employee/test_employee_models.py",
    ]

    coverage_cmd = (
        [sys.executable, "-m", "pytest"]
        + working_tests
        + ["--cov=hrms/microservices/employee", "--cov-report=term-missing", "--cov-report=html", "-q"]
    )

    try:
        coverage_result = subprocess.run(
            coverage_cmd, capture_output=True, text=True, timeout=60, cwd=Path(__file__).parent
        )

        if coverage_result.stdout:
            # Extract coverage percentage
            lines = coverage_result.stdout.split("\n")
            for line in lines:
                if "TOTAL" in line and "%" in line:
                    print(f"Coverage: {line}")
                    break

        print("HTML coverage report generated in htmlcov/")

    except Exception as e:
        print(f"Error generating coverage report: {e}")

    # Final recommendations
    print(f"\n{'='*60}")
    print("RECOMMENDATIONS")
    print(f"{'='*60}")

    if passed_categories == total_categories:
        print("🎉 All test categories are passing! Great work!")
        print("✅ Consider adding more edge cases and integration scenarios")
        print("✅ Monitor performance benchmarks in CI/CD pipeline")
    else:
        print("🔧 Areas for improvement:")
        for result in results:
            if result["status"] != "PASSED":
                print(f"❌ Fix {result['category']}: {result['summary']}")

        print("\n📋 Next steps:")
        print("1. Fix failing unit tests first (highest priority)")
        print("2. Address integration test issues")
        print("3. Ensure security tests pass")
        print("4. Optimize performance test scenarios")

    return passed_categories == total_categories


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
