-- oneHRMS Database Initialization Script
-- This script creates the necessary databases, users, and permissions for the oneHRMS microservices

-- Create the main database user (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'hrms_user') THEN
        CREATE USER hrms_user WITH PASSWORD 'hrms_password';
    END IF;
END
$$;

-- Create the main microservices database
CREATE DATABASE hrms_microservices OWNER hrms_user;

-- Grant all privileges on the database to the user
GRANT ALL PRIVILEGES ON DATABASE hrms_microservices TO hrms_user;

-- Connect to the hrms_microservices database to set up schemas
\c hrms_microservices;

-- Create schemas for each microservice
CREATE SCHEMA IF NOT EXISTS employee AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS payroll AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS attendance AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS leave_management AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS recruitment AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS shared AUTHORIZATION hrms_user;

-- Grant usage and create privileges on schemas
GRANT USAGE, CREATE ON SCHEMA employee TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA payroll TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA attendance TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA leave_management TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA recruitment TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA shared TO hrms_user;

-- Grant all privileges on all tables in schemas (for future tables)
ALTER DEFAULT PRIVILEGES IN SCHEMA employee GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA payroll GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA attendance GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA leave_management GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA recruitment GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA shared GRANT ALL ON TABLES TO hrms_user;

-- Grant all privileges on all sequences in schemas (for future sequences)
ALTER DEFAULT PRIVILEGES IN SCHEMA employee GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA payroll GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA attendance GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA leave_management GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA recruitment GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA shared GRANT ALL ON SEQUENCES TO hrms_user;

-- Create test database for running tests
CREATE DATABASE hrms_test OWNER hrms_user;
GRANT ALL PRIVILEGES ON DATABASE hrms_test TO hrms_user;

-- Connect to test database and create schemas
\c hrms_test;

CREATE SCHEMA IF NOT EXISTS employee AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS payroll AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS attendance AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS leave_management AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS recruitment AUTHORIZATION hrms_user;
CREATE SCHEMA IF NOT EXISTS shared AUTHORIZATION hrms_user;

GRANT USAGE, CREATE ON SCHEMA employee TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA payroll TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA attendance TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA leave_management TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA recruitment TO hrms_user;
GRANT USAGE, CREATE ON SCHEMA shared TO hrms_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA employee GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA payroll GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA attendance GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA leave_management GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA recruitment GRANT ALL ON TABLES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA shared GRANT ALL ON TABLES TO hrms_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA employee GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA payroll GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA attendance GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA leave_management GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA recruitment GRANT ALL ON SEQUENCES TO hrms_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA shared GRANT ALL ON SEQUENCES TO hrms_user;

-- Switch back to main database
\c hrms_microservices;

-- Create some basic shared tables that all microservices might need
CREATE TABLE IF NOT EXISTS shared.tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    domain VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert a default tenant for development
INSERT INTO shared.tenants (name, slug, domain) 
VALUES ('Default Organization', 'default', 'localhost') 
ON CONFLICT (slug) DO NOTHING;

-- Create audit log table for tracking changes
CREATE TABLE IF NOT EXISTS shared.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES shared.tenants(id),
    table_name VARCHAR(255) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by UUID,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON shared.audit_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON shared.audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_record_id ON shared.audit_logs(record_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_changed_at ON shared.audit_logs(changed_at);

-- Grant permissions on the shared tables
GRANT ALL ON shared.tenants TO hrms_user;
GRANT ALL ON shared.audit_logs TO hrms_user;

-- Print success message
\echo 'oneHRMS database initialization completed successfully!'
\echo 'Created databases: hrms_microservices, hrms_test'
\echo 'Created user: hrms_user'
\echo 'Created schemas: employee, payroll, attendance, leave_management, recruitment, shared'
