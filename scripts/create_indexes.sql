-- Critical Database Indexes for oneHRMS Performance Optimization
-- 
-- This script creates essential database indexes to improve query performance
-- across all microservices. These indexes address the most common query patterns
-- identified in the performance audit.
--
-- IMPORTANT: Run this script during maintenance window as index creation
-- can be resource intensive on large datasets.

-- =============================================================================
-- EMPLOYEE SERVICE INDEXES
-- =============================================================================

-- Employee table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_department_id 
ON employees(department_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_position_id 
ON employees(position_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_manager_id 
ON employees(manager_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_status 
ON employees(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_email 
ON employees(email);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_employee_id 
ON employees(employee_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_created_at 
ON employees(created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_updated_at 
ON employees(updated_at);

-- Composite indexes for common employee queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_dept_status 
ON employees(department_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_status_created 
ON employees(status, created_at);

-- Full-text search index for employee names
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_name_search 
ON employees USING gin(to_tsvector('english', first_name || ' ' || last_name));

-- Department table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_departments_parent_id 
ON departments(parent_department_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_departments_is_active 
ON departments(is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_departments_name 
ON departments(name);

-- Position table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_department_id 
ON positions(department_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_is_active 
ON positions(is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_level 
ON positions(level);

-- =============================================================================
-- ATTENDANCE SERVICE INDEXES
-- =============================================================================

-- Attendance records indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_employee_id 
ON attendance_records(employee_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date 
ON attendance_records(attendance_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_status 
ON attendance_records(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_shift_id 
ON attendance_records(shift_id);

-- Composite indexes for common attendance queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_employee_date 
ON attendance_records(employee_id, attendance_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date_status 
ON attendance_records(attendance_date, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_employee_status_date 
ON attendance_records(employee_id, status, attendance_date);

-- Date range queries optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date_range 
ON attendance_records(attendance_date) 
WHERE attendance_date >= CURRENT_DATE - INTERVAL '1 year';

-- Shift types indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shift_types_is_active 
ON shift_types(is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shift_types_name 
ON shift_types(name);

-- Timesheet entries indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_timesheet_employee_id 
ON timesheet_entries(employee_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_timesheet_date 
ON timesheet_entries(date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_timesheet_employee_date 
ON timesheet_entries(employee_id, date);

-- =============================================================================
-- LEAVE SERVICE INDEXES
-- =============================================================================

-- Leave applications indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_employee_id 
ON leave_applications(employee_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_status 
ON leave_applications(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_start_date 
ON leave_applications(start_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_end_date 
ON leave_applications(end_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_leave_type_id 
ON leave_applications(leave_type_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_approver_id 
ON leave_applications(approver_id);

-- Composite indexes for common leave queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_employee_status 
ON leave_applications(employee_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_dates 
ON leave_applications(start_date, end_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_employee_dates 
ON leave_applications(employee_id, start_date, end_date);

-- Date range queries for leave overlap checking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_applications_date_overlap 
ON leave_applications(employee_id, start_date, end_date) 
WHERE status IN ('approved', 'pending');

-- Leave balances indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_balances_employee_id 
ON leave_balances(employee_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_balances_leave_type_id 
ON leave_balances(leave_type_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_balances_year 
ON leave_balances(year);

-- Composite index for leave balance queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_balances_employee_type_year 
ON leave_balances(employee_id, leave_type_id, year);

-- Leave types indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_types_is_active 
ON leave_types(is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_types_name 
ON leave_types(name);

-- =============================================================================
-- AUDIT AND SECURITY INDEXES
-- =============================================================================

-- Audit logs indexes (for security monitoring and compliance)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_id 
ON audit_logs(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_timestamp 
ON audit_logs(timestamp);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_action 
ON audit_logs(action);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_resource_type 
ON audit_logs(resource_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_severity 
ON audit_logs(severity);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_success 
ON audit_logs(success);

-- Composite indexes for audit queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_timestamp 
ON audit_logs(user_id, timestamp);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_resource_action 
ON audit_logs(resource_type, action);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_timestamp_severity 
ON audit_logs(timestamp, severity);

-- Time-based partitioning support for audit logs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_timestamp_month 
ON audit_logs(date_trunc('month', timestamp));

-- =============================================================================
-- PERFORMANCE MONITORING INDEXES
-- =============================================================================

-- Indexes for common reporting queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_hire_date 
ON employees(hire_date) 
WHERE hire_date IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_termination_date 
ON employees(termination_date) 
WHERE termination_date IS NOT NULL;

-- Monthly attendance summary index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_monthly_summary 
ON attendance_records(employee_id, date_trunc('month', attendance_date), status);

-- Leave usage summary index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_usage_summary 
ON leave_applications(employee_id, leave_type_id, date_trunc('year', start_date)) 
WHERE status = 'approved';

-- =============================================================================
-- CLEANUP AND MAINTENANCE
-- =============================================================================

-- Update table statistics after index creation
ANALYZE employees;
ANALYZE departments;
ANALYZE positions;
ANALYZE attendance_records;
ANALYZE shift_types;
ANALYZE timesheet_entries;
ANALYZE leave_applications;
ANALYZE leave_balances;
ANALYZE leave_types;
ANALYZE audit_logs;

-- Display index creation summary
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
