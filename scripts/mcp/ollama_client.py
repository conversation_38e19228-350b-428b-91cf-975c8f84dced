#!/usr/bin/env python3
"""
Ollama Client for MCP Server
Handles communication with Ollama models for AI agent execution
"""

import asyncio
import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

import aiohttp
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class OllamaModel(BaseModel):
    """Ollama model configuration"""

    name: str
    purpose: str
    max_tokens: int = 2048
    temperature: float = 0.7
    top_p: float = 0.9


class OllamaRequest(BaseModel):
    """Ollama request model"""

    model: str
    prompt: str
    system: Optional[str] = None
    context: Optional[List[int]] = None
    options: Dict[str, Any] = {}


class OllamaResponse(BaseModel):
    """Ollama response model"""

    model: str
    response: str
    done: bool
    context: Optional[List[int]] = None
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    eval_count: Optional[int] = None


class OllamaClient:
    """Client for interacting with Ollama API"""

    def __init__(self, host: str = "localhost", port: int = 11434):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.models: Dict[str, OllamaModel] = {}

    async def initialize(self, models_config: List[Dict[str, Any]]):
        """Initialize Ollama client with model configurations"""
        for model_config in models_config:
            model = OllamaModel(**model_config)
            self.models[model.name] = model

        # Check if Ollama is available
        await self.health_check()

        # Pull required models if not available
        await self.ensure_models_available()

    async def health_check(self) -> bool:
        """Check if Ollama server is healthy"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/tags", timeout=10) as response:
                    if response.status == 200:
                        logger.info("Ollama server is healthy")
                        return True
                    else:
                        logger.error(f"Ollama server health check failed: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Ollama server health check failed: {e}")
            return False

    async def list_models(self) -> List[str]:
        """List available models on Ollama server"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        return [model["name"] for model in data.get("models", [])]
                    else:
                        logger.error(f"Failed to list models: {response.status}")
                        return []
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []

    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            logger.info(f"Pulling model: {model_name}")
            async with aiohttp.ClientSession() as session:
                payload = {"name": model_name}
                async with session.post(
                    f"{self.base_url}/api/pull",
                    json=payload,
                    timeout=300,  # 5 minutes timeout for model pulling
                ) as response:
                    if response.status == 200:
                        # Stream the response to track progress
                        async for line in response.content:
                            if line:
                                try:
                                    data = json.loads(line.decode())
                                    if data.get("status"):
                                        logger.info(f"Pull progress: {data['status']}")
                                except json.JSONDecodeError:
                                    continue
                        logger.info(f"Successfully pulled model: {model_name}")
                        return True
                    else:
                        logger.error(f"Failed to pull model {model_name}: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
            return False

    async def ensure_models_available(self):
        """Ensure all configured models are available"""
        available_models = await self.list_models()

        for model_name in self.models:
            if model_name not in available_models:
                logger.info(f"Model {model_name} not found, attempting to pull...")
                await self.pull_model(model_name)

    async def generate(self, request: OllamaRequest) -> OllamaResponse:
        """Generate response using Ollama model"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": request.model,
                    "prompt": request.prompt,
                    "stream": False,
                    "options": request.options,
                }

                if request.system:
                    payload["system"] = request.system

                if request.context:
                    payload["context"] = request.context

                async with session.post(
                    f"{self.base_url}/api/generate", json=payload, timeout=120  # 2 minutes timeout
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return OllamaResponse(**data)
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama generation failed: {response.status} - {error_text}")
                        raise Exception(f"Ollama generation failed: {response.status}")

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            raise

    async def generate_stream(self, request: OllamaRequest) -> AsyncGenerator[str, None]:
        """Generate streaming response using Ollama model"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": request.model,
                    "prompt": request.prompt,
                    "stream": True,
                    "options": request.options,
                }

                if request.system:
                    payload["system"] = request.system

                if request.context:
                    payload["context"] = request.context

                async with session.post(
                    f"{self.base_url}/api/generate", json=payload, timeout=120
                ) as response:
                    if response.status == 200:
                        async for line in response.content:
                            if line:
                                try:
                                    data = json.loads(line.decode())
                                    if "response" in data:
                                        yield data["response"]
                                    if data.get("done", False):
                                        break
                                except json.JSONDecodeError:
                                    continue
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama streaming failed: {response.status} - {error_text}")
                        raise Exception(f"Ollama streaming failed: {response.status}")

        except Exception as e:
            logger.error(f"Error in streaming generation: {e}")
            raise

    async def chat(self, model: str, messages: List[Dict[str, str]]) -> str:
        """Chat with Ollama model using conversation format"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {"model": model, "messages": messages, "stream": False}

                async with session.post(f"{self.base_url}/api/chat", json=payload, timeout=120) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("message", {}).get("content", "")
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama chat failed: {response.status} - {error_text}")
                        raise Exception(f"Ollama chat failed: {response.status}")

        except Exception as e:
            logger.error(f"Error in chat: {e}")
            raise

    def get_model_for_purpose(self, purpose: str) -> Optional[OllamaModel]:
        """Get the best model for a specific purpose"""
        for model in self.models.values():
            if model.purpose == purpose:
                return model
        return None

    async def test_model(self, model_name: str) -> bool:
        """Test if a model is working correctly"""
        try:
            request = OllamaRequest(
                model=model_name, prompt="Hello, this is a test. Please respond with 'Test successful'."
            )
            response = await self.generate(request)
            return "test successful" in response.response.lower()
        except Exception as e:
            logger.error(f"Model test failed for {model_name}: {e}")
            return False


# Global Ollama client instance
ollama_client = OllamaClient()


async def initialize_ollama(config: Dict[str, Any]):
    """Initialize the global Ollama client"""
    ollama_config = config.get("ollama", {})
    host = ollama_config.get("host", "localhost")
    port = ollama_config.get("port", 11434)
    models = ollama_config.get("models", [])

    global ollama_client
    ollama_client = OllamaClient(host, port)
    await ollama_client.initialize(models)

    logger.info(f"Ollama client initialized with {len(models)} models")


def get_ollama_client() -> OllamaClient:
    """Get the global Ollama client instance"""
    return ollama_client
