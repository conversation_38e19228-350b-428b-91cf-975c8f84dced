# MCP Server Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for MCP and AI agents
RUN pip install --no-cache-dir \
    aiohttp \
    pyyaml \
    uvicorn[standard] \
    fastapi \
    pydantic \
    asyncio \
    python-multipart

# Copy application code
COPY mcp/ ./mcp/
COPY hrms/microservices/shared/ ./hrms/microservices/shared/

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV MCP_CONFIG_PATH=/app/mcp/mcp-config.yaml

# Expose port
EXPOSE 8200

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8200/health || exit 1

# Run the MCP server
CMD ["python", "-m", "mcp.server"]
