# MCP (Model Context Protocol) Configuration for oneHRMS
# This configuration defines the MCP server setup for AI agent orchestration

apiVersion: v1
kind: ConfigMap
metadata:
  name: mcp-config
  namespace: onehrms
data:
  # MCP Server Configuration
  server:
    host: "0.0.0.0"
    port: 8200
    protocol: "http"
    version: "1.0"

  # Service Discovery Configuration
  discovery:
    enabled: true
    registry_type: "consul"
    consul:
      host: "consul"
      port: 8500
      datacenter: "dc1"

  # Agent Management Configuration
  agents:
    registry_url: "http://agent-registry:8201"
    health_check_interval: 30
    max_retries: 3
    timeout: 60

  # Ollama Integration Configuration
  ollama:
    host: "ollama"
    port: 11434
    models:
      - name: "codellama:7b"
        purpose: "code_generation"
        max_tokens: 4096
      - name: "llama2:13b"
        purpose: "documentation"
        max_tokens: 2048
      - name: "mistral:7b"
        purpose: "refactoring"
        max_tokens: 2048

  # Microservices Configuration
  services:
    employee-service:
      url: "http://employee-service:8100"
      health_endpoint: "/health"
      agents: ["test-updater", "doc-generator"]

    payroll-service:
      url: "http://payroll-service:8101"
      health_endpoint: "/health"
      agents: ["test-updater", "doc-generator"]

    attendance-service:
      url: "http://attendance-service:8102"
      health_endpoint: "/health"
      agents: ["test-updater", "doc-generator"]

    ess-service:
      url: "http://ess-service:8103"
      health_endpoint: "/health"
      agents: ["test-updater", "doc-generator"]

    recruitment-service:
      url: "http://recruitment-service:8104"
      health_endpoint: "/health"
      agents: ["test-updater", "doc-generator"]

  # Agent Definitions
  agent_definitions:
    test-updater:
      name: "Test Updater Agent"
      description: "Automated test generation and maintenance"
      model: "codellama:7b"
      triggers:
        - "code_change"
        - "api_change"
        - "model_change"
      capabilities:
        - "generate_unit_tests"
        - "generate_integration_tests"
        - "update_existing_tests"
        - "validate_test_coverage"

    doc-generator:
      name: "Documentation Generator Agent"
      description: "Automated documentation generation and updates"
      model: "llama2:13b"
      triggers:
        - "api_change"
        - "milestone_reached"
        - "manual_request"
      capabilities:
        - "generate_openapi_specs"
        - "generate_markdown_docs"
        - "update_readme_files"
        - "create_api_documentation"

    refactor-assistant:
      name: "Refactor Assistant Agent"
      description: "Code quality improvement and refactoring suggestions"
      model: "mistral:7b"
      triggers:
        - "code_smell_detected"
        - "complexity_threshold"
        - "manual_request"
      capabilities:
        - "detect_code_smells"
        - "suggest_refactoring"
        - "improve_code_quality"
        - "enforce_patterns"

  # Monitoring and Logging
  monitoring:
    enabled: true
    metrics_endpoint: "/metrics"
    log_level: "INFO"
    log_format: "json"

  # Security Configuration
  security:
    authentication:
      enabled: true
      type: "jwt"
      secret_key: "${MCP_JWT_SECRET}"
    authorization:
      enabled: true
      rbac:
        enabled: true

  # Database Configuration
  database:
    type: "postgresql"
    host: "postgres"
    port: 5432
    database: "mcp_db"
    username: "${MCP_DB_USER}"
    password: "${MCP_DB_PASSWORD}"

  # Redis Configuration for Caching
  redis:
    host: "redis"
    port: 6379
    database: 5
    password: "${REDIS_PASSWORD}"

  # Workflow Configuration
  workflows:
    test_generation:
      enabled: true
      auto_trigger: true
      approval_required: false

    documentation_update:
      enabled: true
      auto_trigger: true
      approval_required: false

    refactoring_suggestions:
      enabled: true
      auto_trigger: false
      approval_required: true

  # Integration Configuration
  integrations:
    git:
      enabled: true
      webhook_url: "/webhooks/git"

    ci_cd:
      enabled: true
      webhook_url: "/webhooks/cicd"

    monitoring:
      prometheus:
        enabled: true
        endpoint: "http://prometheus:9090"

    notifications:
      slack:
        enabled: false
        webhook_url: "${SLACK_WEBHOOK_URL}"
      email:
        enabled: false
        smtp_host: "${SMTP_HOST}"
        smtp_port: 587
