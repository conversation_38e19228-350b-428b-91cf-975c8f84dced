#!/usr/bin/env python3
"""
MCP (Model Context Protocol) Server for oneHRMS
Orchestrates AI agents and manages service communication
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

import aiohttp
import uvicorn
import yaml
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class MCPConfig:
    """MCP Configuration Manager"""

    def __init__(self, config_path: str = "mcp-config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """Load MCP configuration from YAML file"""
        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)
                return config.get("data", {})
        except FileNotFoundError:
            logger.error(f"Configuration file {self.config_path} not found")
            return self.get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self.get_default_config()

    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "server": {"host": "0.0.0.0", "port": 8200},
            "ollama": {"host": "localhost", "port": 11434},
            "services": {},
            "agents": {},
            "monitoring": {"enabled": True},
        }


class ServiceInfo(BaseModel):
    """Service information model"""

    name: str
    url: str
    health_endpoint: str = "/health"
    status: str = "unknown"
    last_check: Optional[datetime] = None
    agents: List[str] = []


class AgentInfo(BaseModel):
    """Agent information model"""

    name: str
    description: str
    model: str
    status: str = "inactive"
    capabilities: List[str] = []
    triggers: List[str] = []
    last_execution: Optional[datetime] = None


class AgentRequest(BaseModel):
    """Agent execution request model"""

    agent_name: str
    service_name: str
    task_type: str
    parameters: Dict[str, Any] = {}
    priority: int = Field(default=5, ge=1, le=10)


class MCPServer:
    """MCP Server implementation"""

    def __init__(self, config: MCPConfig):
        self.config = config
        self.app = FastAPI(
            title="MCP Server", description="Model Context Protocol Server for oneHRMS", version="1.0.0"
        )
        self.services: Dict[str, ServiceInfo] = {}
        self.agents: Dict[str, AgentInfo] = {}
        self.setup_middleware()
        self.setup_routes()
        self.load_services()
        self.load_agents()

    def setup_middleware(self):
        """Setup FastAPI middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routes(self):
        """Setup API routes"""

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "services": len(self.services),
                "agents": len(self.agents),
            }

        @self.app.get("/services")
        async def list_services():
            """List all registered services"""
            return {"services": list(self.services.values())}

        @self.app.get("/services/{service_name}")
        async def get_service(service_name: str):
            """Get specific service information"""
            if service_name not in self.services:
                raise HTTPException(status_code=404, detail="Service not found")
            return self.services[service_name]

        @self.app.post("/services/{service_name}/health-check")
        async def check_service_health(service_name: str, background_tasks: BackgroundTasks):
            """Trigger health check for a service"""
            if service_name not in self.services:
                raise HTTPException(status_code=404, detail="Service not found")

            background_tasks.add_task(self.check_service_health_async, service_name)
            return {"message": "Health check initiated"}

        @self.app.get("/agents")
        async def list_agents():
            """List all available agents"""
            return {"agents": list(self.agents.values())}

        @self.app.get("/agents/{agent_name}")
        async def get_agent(agent_name: str):
            """Get specific agent information"""
            if agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")
            return self.agents[agent_name]

        @self.app.post("/agents/execute")
        async def execute_agent(request: AgentRequest, background_tasks: BackgroundTasks):
            """Execute an agent task"""
            if request.agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")
            if request.service_name not in self.services:
                raise HTTPException(status_code=404, detail="Service not found")

            background_tasks.add_task(
                self.execute_agent_async,
                request.agent_name,
                request.service_name,
                request.task_type,
                request.parameters,
            )

            return {
                "message": "Agent execution initiated",
                "agent": request.agent_name,
                "service": request.service_name,
                "task": request.task_type,
            }

        @self.app.get("/metrics")
        async def get_metrics():
            """Get MCP server metrics"""
            active_agents = sum(1 for agent in self.agents.values() if agent.status == "active")
            healthy_services = sum(1 for service in self.services.values() if service.status == "healthy")

            return {
                "timestamp": datetime.utcnow().isoformat(),
                "services": {
                    "total": len(self.services),
                    "healthy": healthy_services,
                    "unhealthy": len(self.services) - healthy_services,
                },
                "agents": {
                    "total": len(self.agents),
                    "active": active_agents,
                    "inactive": len(self.agents) - active_agents,
                },
            }

    def load_services(self):
        """Load services from configuration"""
        services_config = self.config.config.get("services", {})
        for name, config in services_config.items():
            self.services[name] = ServiceInfo(
                name=name,
                url=config.get("url", ""),
                health_endpoint=config.get("health_endpoint", "/health"),
                agents=config.get("agents", []),
            )
            logger.info(f"Loaded service: {name}")

    def load_agents(self):
        """Load agents from configuration"""
        agents_config = self.config.config.get("agent_definitions", {})
        for name, config in agents_config.items():
            self.agents[name] = AgentInfo(
                name=config.get("name", name),
                description=config.get("description", ""),
                model=config.get("model", ""),
                capabilities=config.get("capabilities", []),
                triggers=config.get("triggers", []),
            )
            logger.info(f"Loaded agent: {name}")

    async def check_service_health_async(self, service_name: str):
        """Asynchronously check service health"""
        service = self.services[service_name]
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{service.url}{service.health_endpoint}"
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        service.status = "healthy"
                    else:
                        service.status = "unhealthy"
        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {e}")
            service.status = "unhealthy"

        service.last_check = datetime.utcnow()
        logger.info(f"Health check for {service_name}: {service.status}")

    async def execute_agent_async(
        self, agent_name: str, service_name: str, task_type: str, parameters: Dict[str, Any]
    ):
        """Asynchronously execute agent task"""
        agent = self.agents[agent_name]
        service = self.services[service_name]

        try:
            agent.status = "active"
            logger.info(f"Executing agent {agent_name} for service {service_name} with task {task_type}")

            # Here we would integrate with Ollama to execute the actual agent task
            # For now, we'll simulate the execution
            await asyncio.sleep(2)  # Simulate processing time

            agent.last_execution = datetime.utcnow()
            agent.status = "inactive"

            logger.info(f"Agent {agent_name} execution completed")

        except Exception as e:
            logger.error(f"Agent execution failed: {e}")
            agent.status = "error"

    async def start_background_tasks(self):
        """Start background monitoring tasks"""
        asyncio.create_task(self.periodic_health_checks())

    async def periodic_health_checks(self):
        """Periodically check service health"""
        while True:
            for service_name in self.services:
                await self.check_service_health_async(service_name)
            await asyncio.sleep(30)  # Check every 30 seconds


def create_app() -> FastAPI:
    """Create and configure the MCP server application"""
    config = MCPConfig()
    server = MCPServer(config)
    return server.app


if __name__ == "__main__":
    config = MCPConfig()
    server_config = config.config.get("server", {})

    uvicorn.run(
        "server:create_app",
        host=server_config.get("host", "0.0.0.0"),
        port=server_config.get("port", 8200),
        reload=True,
        factory=True,
    )
