#!/usr/bin/env python3
"""
Production Database Setup Script for oneHRMS

This script sets up PostgreSQL for production use with:
- Optimized configuration
- Connection pooling
- Performance monitoring
- Security hardening
- Database migrations
"""

import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'admin_user': os.getenv('DB_ADMIN_USER', 'postgres'),
    'admin_password': os.getenv('DB_ADMIN_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'onehrms'),
    'user': os.getenv('DB_USER', 'hrms_user'),
    'password': os.getenv('DB_PASSWORD', ''),
}

# Production PostgreSQL configuration
POSTGRESQL_CONFIG = {
    # Connection settings
    'max_connections': 200,
    'shared_buffers': '256MB',
    'effective_cache_size': '1GB',
    'maintenance_work_mem': '64MB',
    'checkpoint_completion_target': 0.9,
    'wal_buffers': '16MB',
    'default_statistics_target': 100,
    'random_page_cost': 1.1,
    'effective_io_concurrency': 200,
    'work_mem': '4MB',
    'min_wal_size': '1GB',
    'max_wal_size': '4GB',
    
    # Logging settings
    'log_destination': 'stderr',
    'logging_collector': 'on',
    'log_directory': 'log',
    'log_filename': 'postgresql-%Y-%m-%d_%H%M%S.log',
    'log_rotation_age': '1d',
    'log_rotation_size': '10MB',
    'log_min_duration_statement': '1000ms',
    'log_line_prefix': '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ',
    'log_checkpoints': 'on',
    'log_connections': 'on',
    'log_disconnections': 'on',
    'log_lock_waits': 'on',
    'log_temp_files': '0',
    
    # Security settings
    'ssl': 'on',
    'password_encryption': 'scram-sha-256',
    'row_security': 'on',
}


def check_postgresql_installed():
    """Check if PostgreSQL is installed and accessible."""
    try:
        result = subprocess.run(['psql', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"PostgreSQL found: {result.stdout.strip()}")
            return True
        else:
            logger.error("PostgreSQL not found in PATH")
            return False
    except FileNotFoundError:
        logger.error("PostgreSQL not installed or not in PATH")
        return False


def create_database_and_user():
    """Create database and user for oneHRMS."""
    try:
        # Connect as admin user
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['admin_user'],
            password=DB_CONFIG['admin_password'],
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create user if not exists
        cursor.execute(f"""
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{DB_CONFIG['user']}') THEN
                    CREATE USER {DB_CONFIG['user']} WITH PASSWORD '{DB_CONFIG['password']}';
                END IF;
            END
            $$;
        """)
        logger.info(f"User {DB_CONFIG['user']} created/verified")
        
        # Create database if not exists
        cursor.execute(f"""
            SELECT 1 FROM pg_database WHERE datname = '{DB_CONFIG['database']}'
        """)
        if not cursor.fetchone():
            cursor.execute(f"CREATE DATABASE {DB_CONFIG['database']} OWNER {DB_CONFIG['user']}")
            logger.info(f"Database {DB_CONFIG['database']} created")
        else:
            logger.info(f"Database {DB_CONFIG['database']} already exists")
        
        # Grant privileges
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {DB_CONFIG['database']} TO {DB_CONFIG['user']}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to create database and user: {e}")
        return False


def configure_postgresql():
    """Apply production PostgreSQL configuration."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['admin_user'],
            password=DB_CONFIG['admin_password'],
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        logger.info("Applying production PostgreSQL configuration...")
        
        # Apply configuration settings
        for setting, value in POSTGRESQL_CONFIG.items():
            try:
                if isinstance(value, str) and value not in ['on', 'off']:
                    cursor.execute(f"ALTER SYSTEM SET {setting} = '{value}'")
                else:
                    cursor.execute(f"ALTER SYSTEM SET {setting} = {value}")
                logger.info(f"Set {setting} = {value}")
            except Exception as e:
                logger.warning(f"Failed to set {setting}: {e}")
        
        # Reload configuration
        cursor.execute("SELECT pg_reload_conf()")
        logger.info("PostgreSQL configuration reloaded")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to configure PostgreSQL: {e}")
        return False


def create_extensions():
    """Create necessary PostgreSQL extensions."""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        extensions = [
            'uuid-ossp',  # UUID generation
            'pg_stat_statements',  # Query statistics
            'pg_trgm',  # Trigram matching for search
        ]
        
        for ext in extensions:
            try:
                cursor.execute(f"CREATE EXTENSION IF NOT EXISTS \"{ext}\"")
                logger.info(f"Extension {ext} created/verified")
            except Exception as e:
                logger.warning(f"Failed to create extension {ext}: {e}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to create extensions: {e}")
        return False


def run_migrations():
    """Run database migrations using Alembic."""
    try:
        logger.info("Running database migrations...")
        
        # Set environment variables for Alembic
        env = os.environ.copy()
        env['DATABASE_URL'] = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
        
        # Initialize Alembic if not already done
        alembic_dir = Path('alembic')
        if not alembic_dir.exists():
            subprocess.run(['alembic', 'init', 'alembic'], env=env, check=True)
            logger.info("Alembic initialized")
        
        # Run migrations
        result = subprocess.run(['alembic', 'upgrade', 'head'], env=env, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("Database migrations completed successfully")
            return True
        else:
            logger.error(f"Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run migrations: {e}")
        return False


def create_indexes():
    """Create performance indexes."""
    try:
        logger.info("Creating performance indexes...")
        
        # Read and execute index creation script
        script_path = Path(__file__).parent / 'create_indexes.sql'
        if not script_path.exists():
            logger.warning("Index creation script not found, skipping...")
            return True
        
        with open(script_path, 'r') as f:
            sql_script = f.read()
        
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Execute the script
        cursor.execute(sql_script)
        logger.info("Performance indexes created successfully")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")
        return False


def verify_setup():
    """Verify the database setup."""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database']
        )
        cursor = conn.cursor()
        
        # Check connection
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        logger.info(f"Connected to: {version}")
        
        # Check extensions
        cursor.execute("SELECT extname FROM pg_extension")
        extensions = [row[0] for row in cursor.fetchall()]
        logger.info(f"Available extensions: {', '.join(extensions)}")
        
        # Check indexes
        cursor.execute("""
            SELECT count(*) FROM pg_indexes 
            WHERE indexname LIKE 'idx_%'
        """)
        index_count = cursor.fetchone()[0]
        logger.info(f"Performance indexes created: {index_count}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Setup verification failed: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("Starting oneHRMS production database setup...")
    
    # Check prerequisites
    if not check_postgresql_installed():
        logger.error("PostgreSQL is required but not found")
        sys.exit(1)
    
    # Validate configuration
    required_vars = ['DB_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        sys.exit(1)
    
    # Setup steps
    steps = [
        ("Creating database and user", create_database_and_user),
        ("Configuring PostgreSQL", configure_postgresql),
        ("Creating extensions", create_extensions),
        ("Running migrations", run_migrations),
        ("Creating indexes", create_indexes),
        ("Verifying setup", verify_setup),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"Step: {step_name}")
        if not step_func():
            logger.error(f"Failed: {step_name}")
            sys.exit(1)
        logger.info(f"Completed: {step_name}")
    
    logger.info("oneHRMS production database setup completed successfully!")
    logger.info(f"Database URL: postgresql://{DB_CONFIG['user']}:***@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")


if __name__ == "__main__":
    main()
