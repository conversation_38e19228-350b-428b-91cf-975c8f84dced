#!/usr/bin/env python3
"""
Documentation Generator Agent for oneHRMS
Automatically generates and maintains documentation using AI
"""

import asyncio
import json
import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import yaml

from mcp.ollama_client import OllamaRequest, get_ollama_client

logger = logging.getLogger(__name__)


class APIAnalyzer:
    """Analyzes FastAPI applications to extract API information"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)

    def analyze_fastapi_service(self, service_path: Path) -> Dict[str, Any]:
        """Analyze a FastAPI service to extract API information"""
        api_file = service_path / "api.py"
        models_file = service_path / "models.py"

        if not api_file.exists():
            return {"error": f"API file not found: {api_file}"}

        analysis = {
            "service_name": service_path.name,
            "endpoints": [],
            "models": [],
            "dependencies": [],
            "middleware": [],
        }

        # Analyze API endpoints
        try:
            with open(api_file, "r", encoding="utf-8") as f:
                api_content = f.read()

            # Extract FastAPI route decorators and functions
            route_pattern = r'@app\.(get|post|put|delete|patch)\(["\']([^"\']+)["\'].*?\)\s*(?:async\s+)?def\s+(\w+)\s*\([^)]*\):'
            routes = re.findall(route_pattern, api_content, re.MULTILINE | re.DOTALL)

            for method, path, function_name in routes:
                # Extract function details
                func_pattern = rf"def\s+{function_name}\s*\([^)]*\):(.*?)(?=\n@|\ndef\s|\nclass\s|\Z)"
                func_match = re.search(func_pattern, api_content, re.DOTALL)

                docstring = ""
                if func_match:
                    func_body = func_match.group(1)
                    docstring_match = re.search(r'"""(.*?)"""', func_body, re.DOTALL)
                    if docstring_match:
                        docstring = docstring_match.group(1).strip()

                analysis["endpoints"].append(
                    {
                        "method": method.upper(),
                        "path": path,
                        "function": function_name,
                        "docstring": docstring,
                    }
                )

        except Exception as e:
            logger.error(f"Error analyzing API file {api_file}: {e}")
            analysis["api_error"] = str(e)

        # Analyze Pydantic models
        if models_file.exists():
            try:
                with open(models_file, "r", encoding="utf-8") as f:
                    models_content = f.read()

                # Extract Pydantic model classes
                model_pattern = r"class\s+(\w+)\s*\([^)]*BaseModel[^)]*\):(.*?)(?=\nclass\s|\Z)"
                models = re.findall(model_pattern, models_content, re.DOTALL)

                for model_name, model_body in models:
                    # Extract fields
                    field_pattern = r"(\w+):\s*([^=\n]+)(?:\s*=\s*[^#\n]*)?(?:\s*#\s*(.*))?"
                    fields = re.findall(field_pattern, model_body)

                    # Extract docstring
                    docstring_match = re.search(r'"""(.*?)"""', model_body, re.DOTALL)
                    docstring = docstring_match.group(1).strip() if docstring_match else ""

                    analysis["models"].append(
                        {
                            "name": model_name,
                            "docstring": docstring,
                            "fields": [
                                {"name": name, "type": type_hint, "description": desc or ""}
                                for name, type_hint, desc in fields
                            ],
                        }
                    )

            except Exception as e:
                logger.error(f"Error analyzing models file {models_file}: {e}")
                analysis["models_error"] = str(e)

        return analysis

    def extract_service_dependencies(self, service_path: Path) -> List[str]:
        """Extract dependencies from service files"""
        dependencies = set()

        for py_file in service_path.glob("*.py"):
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # Extract imports
                import_pattern = r"^(?:from\s+(\S+)\s+import|import\s+(\S+))"
                imports = re.findall(import_pattern, content, re.MULTILINE)

                for from_import, direct_import in imports:
                    if from_import:
                        dependencies.add(from_import.split(".")[0])
                    if direct_import:
                        dependencies.add(direct_import.split(".")[0])

            except Exception as e:
                logger.warning(f"Error reading {py_file}: {e}")

        # Filter out standard library and local imports
        external_deps = []
        for dep in dependencies:
            if not dep.startswith(".") and dep not in ["os", "sys", "json", "datetime", "typing"]:
                external_deps.append(dep)

        return sorted(external_deps)


class OpenAPIGenerator:
    """Generates OpenAPI specifications"""

    def __init__(self):
        self.ollama_client = get_ollama_client()

    async def generate_openapi_spec(self, service_analysis: Dict[str, Any]) -> str:
        """Generate OpenAPI specification from service analysis"""

        system_prompt = """You are an expert in OpenAPI 3.0 specification generation.
Generate a complete, valid OpenAPI 3.0 YAML specification based on the provided FastAPI service analysis.

Requirements:
1. Follow OpenAPI 3.0 specification exactly
2. Include all endpoints with proper HTTP methods
3. Define request/response schemas for all models
4. Add comprehensive descriptions and examples
5. Include proper error responses (400, 401, 404, 422, 500)
6. Add security schemes for JWT authentication
7. Include server information and contact details
8. Use proper YAML formatting

The specification should be production-ready and comprehensive."""

        user_prompt = f"""Generate a complete OpenAPI 3.0 specification for this FastAPI service:

Service Analysis:
{json.dumps(service_analysis, indent=2)}

Requirements:
- Service name: {service_analysis.get('service_name', 'Unknown Service')}
- Include all {len(service_analysis.get('endpoints', []))} endpoints
- Define schemas for all {len(service_analysis.get('models', []))} Pydantic models
- Add proper authentication security schemes
- Include comprehensive descriptions and examples
- Add standard error responses

Return only the YAML OpenAPI specification, no explanations."""

        try:
            request = OllamaRequest(
                model="llama2:13b",
                prompt=user_prompt,
                system=system_prompt,
                options={"temperature": 0.2, "top_p": 0.9, "num_predict": 4096},
            )

            response = await self.ollama_client.generate(request)
            return self._clean_yaml_output(response.response)

        except Exception as e:
            logger.error(f"Error generating OpenAPI spec: {e}")
            raise

    def _clean_yaml_output(self, yaml_content: str) -> str:
        """Clean and validate YAML output"""
        # Remove markdown code blocks if present
        yaml_content = re.sub(r"```yaml\n?", "", yaml_content)
        yaml_content = re.sub(r"```\n?", "", yaml_content)

        # Clean up the content
        yaml_content = yaml_content.strip()

        # Validate YAML syntax
        try:
            yaml.safe_load(yaml_content)
        except yaml.YAMLError as e:
            logger.warning(f"Generated YAML has syntax errors: {e}")
            # Try to fix common issues
            yaml_content = self._fix_common_yaml_issues(yaml_content)

        return yaml_content

    def _fix_common_yaml_issues(self, yaml_content: str) -> str:
        """Fix common YAML formatting issues"""
        # Fix indentation issues
        lines = yaml_content.split("\n")
        fixed_lines = []

        for line in lines:
            # Ensure proper indentation (2 spaces)
            if line.strip():
                indent_level = len(line) - len(line.lstrip())
                if indent_level % 2 != 0:
                    line = " " + line  # Add one space to make it even
            fixed_lines.append(line)

        return "\n".join(fixed_lines)


class MarkdownGenerator:
    """Generates markdown documentation"""

    def __init__(self):
        self.ollama_client = get_ollama_client()

    async def generate_service_readme(self, service_analysis: Dict[str, Any], openapi_spec: str) -> str:
        """Generate README.md for a service"""

        system_prompt = """You are a technical documentation expert specializing in microservices documentation.
Generate comprehensive, well-structured README.md files for microservices.

Requirements:
1. Use proper Markdown formatting
2. Include clear sections with appropriate headers
3. Add code examples and usage instructions
4. Include API endpoint documentation
5. Add setup and deployment instructions
6. Include testing information
7. Use badges and visual elements where appropriate
8. Make it developer-friendly and comprehensive"""

        user_prompt = f"""Generate a comprehensive README.md file for this microservice:

Service Analysis:
{json.dumps(service_analysis, indent=2)}

OpenAPI Specification Available: {'Yes' if openapi_spec else 'No'}

Requirements:
- Service name: {service_analysis.get('service_name', 'Unknown Service')}
- Include overview and features
- Document all API endpoints with examples
- Add setup and installation instructions
- Include testing and development information
- Add deployment and configuration details
- Include troubleshooting section
- Use proper Markdown formatting with headers, code blocks, and lists

Return only the Markdown content, no explanations."""

        try:
            request = OllamaRequest(
                model="llama2:13b",
                prompt=user_prompt,
                system=system_prompt,
                options={"temperature": 0.3, "top_p": 0.9, "num_predict": 3072},
            )

            response = await self.ollama_client.generate(request)
            return self._clean_markdown_output(response.response)

        except Exception as e:
            logger.error(f"Error generating README: {e}")
            raise

    async def generate_api_documentation(self, service_analysis: Dict[str, Any]) -> str:
        """Generate detailed API documentation"""

        system_prompt = """You are an API documentation specialist.
Generate comprehensive API documentation in Markdown format.

Requirements:
1. Document all endpoints with detailed descriptions
2. Include request/response examples
3. Document all parameters and their types
4. Add error response documentation
5. Include authentication information
6. Use proper Markdown formatting
7. Add usage examples and best practices"""

        endpoints = service_analysis.get("endpoints", [])
        models = service_analysis.get("models", [])

        user_prompt = f"""Generate comprehensive API documentation for this service:

Service: {service_analysis.get('service_name', 'Unknown Service')}

Endpoints ({len(endpoints)}):
{json.dumps(endpoints, indent=2)}

Models ({len(models)}):
{json.dumps(models, indent=2)}

Requirements:
- Document each endpoint with method, path, description
- Include request/response schemas
- Add example requests and responses
- Document authentication requirements
- Include error handling information
- Use proper Markdown formatting

Return only the Markdown documentation, no explanations."""

        try:
            request = OllamaRequest(
                model="llama2:13b",
                prompt=user_prompt,
                system=system_prompt,
                options={"temperature": 0.3, "top_p": 0.9, "num_predict": 3072},
            )

            response = await self.ollama_client.generate(request)
            return self._clean_markdown_output(response.response)

        except Exception as e:
            logger.error(f"Error generating API documentation: {e}")
            raise

    def _clean_markdown_output(self, markdown_content: str) -> str:
        """Clean and format markdown output"""
        # Remove markdown code blocks if present
        markdown_content = re.sub(r"```markdown\n?", "", markdown_content)
        markdown_content = re.sub(r"```\n?", "", markdown_content)

        # Clean up the content
        markdown_content = markdown_content.strip()

        # Ensure proper line endings
        markdown_content = re.sub(r"\n{3,}", "\n\n", markdown_content)

        return markdown_content


class DocumentationGeneratorAgent:
    """Main Documentation Generator Agent class"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.analyzer = APIAnalyzer(project_root)
        self.openapi_generator = OpenAPIGenerator()
        self.markdown_generator = MarkdownGenerator()

    async def generate_service_documentation(self, service_name: str) -> Dict[str, Any]:
        """Generate complete documentation for a service"""
        service_path = self.project_root / "hrms" / "microservices" / service_name

        if not service_path.exists():
            return {"error": f"Service not found: {service_name}"}

        logger.info(f"Generating documentation for {service_name}")

        # Analyze the service
        service_analysis = self.analyzer.analyze_fastapi_service(service_path)

        if "error" in service_analysis:
            return service_analysis

        # Add dependencies
        service_analysis["dependencies"] = self.analyzer.extract_service_dependencies(service_path)

        results = {}

        try:
            # Generate OpenAPI specification
            openapi_spec = await self.openapi_generator.generate_openapi_spec(service_analysis)
            openapi_file = service_path / "openapi.yaml"

            with open(openapi_file, "w", encoding="utf-8") as f:
                f.write(openapi_spec)

            results["openapi"] = {"file": str(openapi_file), "lines": len(openapi_spec.split("\n"))}

            # Generate README
            readme_content = await self.markdown_generator.generate_service_readme(
                service_analysis, openapi_spec
            )
            readme_file = service_path / "README.md"

            with open(readme_file, "w", encoding="utf-8") as f:
                f.write(readme_content)

            results["readme"] = {"file": str(readme_file), "lines": len(readme_content.split("\n"))}

            # Generate API documentation
            api_docs = await self.markdown_generator.generate_api_documentation(service_analysis)
            api_docs_file = self.project_root / "docs" / f"{service_name}-api.md"
            api_docs_file.parent.mkdir(parents=True, exist_ok=True)

            with open(api_docs_file, "w", encoding="utf-8") as f:
                f.write(api_docs)

            results["api_docs"] = {"file": str(api_docs_file), "lines": len(api_docs.split("\n"))}

            logger.info(f"Documentation generated for {service_name}")

            return {
                "success": True,
                "service": service_name,
                "endpoints_documented": len(service_analysis.get("endpoints", [])),
                "models_documented": len(service_analysis.get("models", [])),
                "files_generated": results,
            }

        except Exception as e:
            logger.error(f"Error generating documentation for {service_name}: {e}")
            return {"error": str(e)}

    async def generate_all_service_documentation(self) -> Dict[str, Any]:
        """Generate documentation for all microservices"""
        microservices_dir = self.project_root / "hrms" / "microservices"

        if not microservices_dir.exists():
            return {"error": "Microservices directory not found"}

        results = {}

        # Find all service directories
        for service_dir in microservices_dir.iterdir():
            if service_dir.is_dir() and service_dir.name != "shared":
                try:
                    result = await self.generate_service_documentation(service_dir.name)
                    results[service_dir.name] = result
                except Exception as e:
                    logger.error(f"Error generating docs for {service_dir.name}: {e}")
                    results[service_dir.name] = {"error": str(e)}

        return {
            "total_services": len(results),
            "successful": len([r for r in results.values() if r.get("success")]),
            "failed": len([r for r in results.values() if "error" in r]),
            "results": results,
        }
