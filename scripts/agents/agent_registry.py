#!/usr/bin/env python3
"""
Agent Registry and Management System for oneHRMS
Manages AI agent lifecycle, discovery, and execution
"""

import asyncio
import json
import logging
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import uvicorn
from fastapi import BackgroundTasks, FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class AgentStatus(str, Enum):
    """Agent status enumeration"""

    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class TaskStatus(str, Enum):
    """Task status enumeration"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AgentCapability:
    """Agent capability definition"""

    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]


@dataclass
class AgentMetrics:
    """Agent performance metrics"""

    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_execution_time: float = 0.0
    last_execution: Optional[datetime] = None
    uptime_percentage: float = 100.0


class AgentRegistration(BaseModel):
    """Agent registration model"""

    name: str
    description: str
    version: str
    capabilities: List[str]
    triggers: List[str]
    model: str
    endpoint: Optional[str] = None
    health_check_interval: int = 60
    max_concurrent_tasks: int = 5


class TaskRequest(BaseModel):
    """Task execution request"""

    agent_name: str
    capability: str
    parameters: Dict[str, Any] = {}
    priority: int = Field(default=5, ge=1, le=10)
    timeout: int = Field(default=300, ge=30, le=3600)
    callback_url: Optional[str] = None


class TaskResponse(BaseModel):
    """Task execution response"""

    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None


@dataclass
class RegisteredAgent:
    """Registered agent information"""

    name: str
    description: str
    version: str
    capabilities: List[AgentCapability]
    triggers: List[str]
    model: str
    status: AgentStatus = AgentStatus.INACTIVE
    endpoint: Optional[str] = None
    health_check_interval: int = 60
    max_concurrent_tasks: int = 5
    current_tasks: int = 0
    registered_at: datetime = None
    last_heartbeat: Optional[datetime] = None
    metrics: AgentMetrics = None

    def __post_init__(self):
        if self.registered_at is None:
            self.registered_at = datetime.utcnow()
        if self.metrics is None:
            self.metrics = AgentMetrics()


@dataclass
class Task:
    """Task execution information"""

    task_id: str
    agent_name: str
    capability: str
    parameters: Dict[str, Any]
    priority: int
    timeout: int
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    callback_url: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()

    @property
    def execution_time(self) -> Optional[float]:
        """Calculate execution time in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


class AgentRegistry:
    """Agent registry and management system"""

    def __init__(self):
        self.agents: Dict[str, RegisteredAgent] = {}
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []  # Task IDs in priority order
        self.app = FastAPI(
            title="Agent Registry", description="AI Agent Registry and Management System", version="1.0.0"
        )
        self.setup_middleware()
        self.setup_routes()
        self._running = False

    def setup_middleware(self):
        """Setup FastAPI middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routes(self):
        """Setup API routes"""

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "agents": len(self.agents),
                "active_agents": len([a for a in self.agents.values() if a.status == AgentStatus.ACTIVE]),
                "pending_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
                "running_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
            }

        @self.app.post("/agents/register")
        async def register_agent(registration: AgentRegistration):
            """Register a new agent"""
            if registration.name in self.agents:
                raise HTTPException(status_code=409, detail="Agent already registered")

            # Convert capabilities to AgentCapability objects
            capabilities = []
            for cap_name in registration.capabilities:
                capabilities.append(
                    AgentCapability(
                        name=cap_name, description=f"{cap_name} capability", input_schema={}, output_schema={}
                    )
                )

            agent = RegisteredAgent(
                name=registration.name,
                description=registration.description,
                version=registration.version,
                capabilities=capabilities,
                triggers=registration.triggers,
                model=registration.model,
                endpoint=registration.endpoint,
                health_check_interval=registration.health_check_interval,
                max_concurrent_tasks=registration.max_concurrent_tasks,
            )

            self.agents[registration.name] = agent
            logger.info(f"Agent registered: {registration.name}")

            return {"message": "Agent registered successfully", "agent_name": registration.name}

        @self.app.delete("/agents/{agent_name}")
        async def unregister_agent(agent_name: str):
            """Unregister an agent"""
            if agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")

            # Cancel any running tasks for this agent
            for task in self.tasks.values():
                if task.agent_name == agent_name and task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = datetime.utcnow()

            del self.agents[agent_name]
            logger.info(f"Agent unregistered: {agent_name}")

            return {"message": "Agent unregistered successfully"}

        @self.app.get("/agents")
        async def list_agents():
            """List all registered agents"""
            agents_data = []
            for agent in self.agents.values():
                agent_dict = asdict(agent)
                agent_dict["capabilities"] = [cap.name for cap in agent.capabilities]
                agents_data.append(agent_dict)
            return {"agents": agents_data}

        @self.app.get("/agents/{agent_name}")
        async def get_agent(agent_name: str):
            """Get specific agent information"""
            if agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")

            agent = self.agents[agent_name]
            agent_dict = asdict(agent)
            agent_dict["capabilities"] = [asdict(cap) for cap in agent.capabilities]
            return agent_dict

        @self.app.post("/agents/{agent_name}/heartbeat")
        async def agent_heartbeat(agent_name: str):
            """Receive heartbeat from agent"""
            if agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")

            agent = self.agents[agent_name]
            agent.last_heartbeat = datetime.utcnow()
            if agent.status == AgentStatus.INACTIVE:
                agent.status = AgentStatus.ACTIVE

            return {"message": "Heartbeat received"}

        @self.app.post("/tasks/submit")
        async def submit_task(task_request: TaskRequest, background_tasks: BackgroundTasks):
            """Submit a task for execution"""
            if task_request.agent_name not in self.agents:
                raise HTTPException(status_code=404, detail="Agent not found")

            agent = self.agents[task_request.agent_name]

            # Check if agent has the required capability
            agent_capabilities = [cap.name for cap in agent.capabilities]
            if task_request.capability not in agent_capabilities:
                raise HTTPException(status_code=400, detail="Agent does not have required capability")

            # Check if agent can handle more tasks
            if agent.current_tasks >= agent.max_concurrent_tasks:
                raise HTTPException(status_code=429, detail="Agent at maximum capacity")

            # Create task
            task_id = str(uuid.uuid4())
            task = Task(
                task_id=task_id,
                agent_name=task_request.agent_name,
                capability=task_request.capability,
                parameters=task_request.parameters,
                priority=task_request.priority,
                timeout=task_request.timeout,
                callback_url=task_request.callback_url,
            )

            self.tasks[task_id] = task
            self._add_task_to_queue(task_id)

            # Start task execution in background
            background_tasks.add_task(self._execute_task, task_id)

            logger.info(f"Task submitted: {task_id} for agent {task_request.agent_name}")

            return {
                "task_id": task_id,
                "status": "submitted",
                "estimated_start_time": datetime.utcnow().isoformat(),
            }

        @self.app.get("/tasks/{task_id}")
        async def get_task(task_id: str):
            """Get task status and result"""
            if task_id not in self.tasks:
                raise HTTPException(status_code=404, detail="Task not found")

            task = self.tasks[task_id]
            return TaskResponse(
                task_id=task.task_id,
                status=task.status,
                result=task.result,
                error=task.error,
                started_at=task.started_at,
                completed_at=task.completed_at,
                execution_time=task.execution_time,
            )

        @self.app.delete("/tasks/{task_id}")
        async def cancel_task(task_id: str):
            """Cancel a pending or running task"""
            if task_id not in self.tasks:
                raise HTTPException(status_code=404, detail="Task not found")

            task = self.tasks[task_id]

            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                raise HTTPException(status_code=400, detail="Task cannot be cancelled")

            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()

            # Remove from queue if pending
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)

            logger.info(f"Task cancelled: {task_id}")

            return {"message": "Task cancelled successfully"}

        @self.app.get("/tasks")
        async def list_tasks(status: Optional[TaskStatus] = None, agent_name: Optional[str] = None):
            """List tasks with optional filtering"""
            tasks = list(self.tasks.values())

            if status:
                tasks = [t for t in tasks if t.status == status]

            if agent_name:
                tasks = [t for t in tasks if t.agent_name == agent_name]

            # Convert to response format
            task_responses = []
            for task in tasks:
                task_responses.append(
                    TaskResponse(
                        task_id=task.task_id,
                        status=task.status,
                        result=task.result,
                        error=task.error,
                        started_at=task.started_at,
                        completed_at=task.completed_at,
                        execution_time=task.execution_time,
                    )
                )

            return {"tasks": task_responses}

        @self.app.get("/metrics")
        async def get_metrics():
            """Get system metrics"""
            total_agents = len(self.agents)
            active_agents = len([a for a in self.agents.values() if a.status == AgentStatus.ACTIVE])
            total_tasks = len(self.tasks)
            pending_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING])
            running_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING])
            completed_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED])
            failed_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED])

            return {
                "timestamp": datetime.utcnow().isoformat(),
                "agents": {
                    "total": total_agents,
                    "active": active_agents,
                    "inactive": total_agents - active_agents,
                },
                "tasks": {
                    "total": total_tasks,
                    "pending": pending_tasks,
                    "running": running_tasks,
                    "completed": completed_tasks,
                    "failed": failed_tasks,
                },
                "queue_length": len(self.task_queue),
            }

    def _add_task_to_queue(self, task_id: str):
        """Add task to priority queue"""
        task = self.tasks[task_id]

        # Insert task in priority order (higher priority first)
        inserted = False
        for i, existing_task_id in enumerate(self.task_queue):
            existing_task = self.tasks[existing_task_id]
            if task.priority > existing_task.priority:
                self.task_queue.insert(i, task_id)
                inserted = True
                break

        if not inserted:
            self.task_queue.append(task_id)

    async def _execute_task(self, task_id: str):
        """Execute a task"""
        task = self.tasks[task_id]
        agent = self.agents[task.agent_name]

        try:
            # Wait for task to be at front of queue
            while self.task_queue and self.task_queue[0] != task_id:
                await asyncio.sleep(1)

            if task.status == TaskStatus.CANCELLED:
                return

            # Remove from queue and start execution
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)

            task.status = TaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            agent.current_tasks += 1

            logger.info(f"Starting task execution: {task_id}")

            # Here we would integrate with the actual agent execution
            # For now, simulate task execution
            await asyncio.sleep(2)  # Simulate processing time

            # Simulate successful completion
            task.result = {
                "success": True,
                "message": f"Task {task.capability} completed successfully",
                "output": "Simulated task output",
            }
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()

            # Update agent metrics
            agent.metrics.total_executions += 1
            agent.metrics.successful_executions += 1
            agent.metrics.last_execution = datetime.utcnow()

            if task.execution_time:
                # Update average execution time
                total_time = agent.metrics.average_execution_time * (agent.metrics.total_executions - 1)
                agent.metrics.average_execution_time = (
                    total_time + task.execution_time
                ) / agent.metrics.total_executions

            logger.info(f"Task completed successfully: {task_id}")

        except Exception as e:
            logger.error(f"Task execution failed: {task_id} - {e}")
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = datetime.utcnow()

            # Update agent metrics
            agent.metrics.total_executions += 1
            agent.metrics.failed_executions += 1

        finally:
            agent.current_tasks -= 1

    async def start_background_tasks(self):
        """Start background monitoring tasks"""
        self._running = True
        asyncio.create_task(self._monitor_agent_health())
        asyncio.create_task(self._cleanup_old_tasks())

    async def _monitor_agent_health(self):
        """Monitor agent health and update status"""
        while self._running:
            current_time = datetime.utcnow()

            for agent in self.agents.values():
                if agent.last_heartbeat:
                    time_since_heartbeat = (current_time - agent.last_heartbeat).total_seconds()
                    if time_since_heartbeat > agent.health_check_interval * 2:
                        if agent.status == AgentStatus.ACTIVE:
                            agent.status = AgentStatus.ERROR
                            logger.warning(f"Agent {agent.name} marked as ERROR - no heartbeat")

            await asyncio.sleep(30)  # Check every 30 seconds

    async def _cleanup_old_tasks(self):
        """Clean up old completed tasks"""
        while self._running:
            current_time = datetime.utcnow()
            cutoff_time = current_time - timedelta(hours=24)  # Keep tasks for 24 hours

            tasks_to_remove = []
            for task_id, task in self.tasks.items():
                if (
                    task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
                    and task.completed_at
                    and task.completed_at < cutoff_time
                ):
                    tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                logger.info(f"Cleaned up old task: {task_id}")

            await asyncio.sleep(3600)  # Clean up every hour


def create_app() -> FastAPI:
    """Create and configure the agent registry application"""
    registry = AgentRegistry()
    return registry.app


if __name__ == "__main__":
    registry = AgentRegistry()

    uvicorn.run("agent_registry:create_app", host="0.0.0.0", port=8201, reload=True, factory=True)
