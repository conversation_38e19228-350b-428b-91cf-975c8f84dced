#!/usr/bin/env python3
"""
Test Updater Agent for oneHRMS
Automatically generates and maintains test suites using AI
"""

import ast
import asyncio
import json
import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from mcp.ollama_client import OllamaRequest, get_ollama_client

logger = logging.getLogger(__name__)


class TestAnalyzer:
    """Analyzes existing code and tests to understand patterns"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)

    def analyze_code_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a Python code file to extract testable components"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)

            analysis = {
                "file_path": str(file_path),
                "classes": [],
                "functions": [],
                "imports": [],
                "complexity_score": 0,
            }

            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "methods": [],
                        "line_number": node.lineno,
                        "docstring": ast.get_docstring(node),
                    }

                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                "name": item.name,
                                "args": [arg.arg for arg in item.args.args],
                                "line_number": item.lineno,
                                "docstring": ast.get_docstring(item),
                                "is_async": isinstance(item, ast.AsyncFunctionDef),
                            }
                            class_info["methods"].append(method_info)

                    analysis["classes"].append(class_info)

                elif isinstance(node, ast.FunctionDef) and not any(
                    isinstance(parent, ast.ClassDef)
                    for parent in ast.walk(tree)
                    if hasattr(parent, "body") and node in parent.body
                ):
                    function_info = {
                        "name": node.name,
                        "args": [arg.arg for arg in node.args.args],
                        "line_number": node.lineno,
                        "docstring": ast.get_docstring(node),
                        "is_async": isinstance(node, ast.AsyncFunctionDef),
                    }
                    analysis["functions"].append(function_info)

                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        analysis["imports"].append(alias.name)

                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        analysis["imports"].append(f"{module}.{alias.name}")

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {"error": str(e)}

    def find_existing_tests(self, code_file: Path) -> Optional[Path]:
        """Find existing test file for a given code file"""
        # Common test file patterns
        patterns = [f"test_{code_file.stem}.py", f"{code_file.stem}_test.py", f"test_{code_file.stem}_*.py"]

        # Look in tests directory
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            for pattern in patterns:
                test_files = list(tests_dir.rglob(pattern))
                if test_files:
                    return test_files[0]

        # Look in same directory
        for pattern in patterns:
            test_file = code_file.parent / pattern
            if test_file.exists():
                return test_file

        return None

    def calculate_test_coverage_gaps(
        self, code_analysis: Dict[str, Any], test_file: Optional[Path]
    ) -> List[str]:
        """Calculate what components are missing tests"""
        gaps = []

        if not test_file or not test_file.exists():
            # No test file exists, everything needs testing
            for cls in code_analysis.get("classes", []):
                gaps.append(f"class:{cls['name']}")
                for method in cls.get("methods", []):
                    if not method["name"].startswith("_"):  # Skip private methods
                        gaps.append(f"method:{cls['name']}.{method['name']}")

            for func in code_analysis.get("functions", []):
                if not func["name"].startswith("_"):  # Skip private functions
                    gaps.append(f"function:{func['name']}")

            return gaps

        # Analyze existing test file
        try:
            test_analysis = self.analyze_code_file(test_file)
            tested_components = set()

            # Extract what's being tested from test method names
            for cls in test_analysis.get("classes", []):
                for method in cls.get("methods", []):
                    if method["name"].startswith("test_"):
                        # Extract component being tested from test name
                        component = method["name"].replace("test_", "").replace("_", "")
                        tested_components.add(component.lower())

            # Check for gaps
            for cls in code_analysis.get("classes", []):
                if cls["name"].lower() not in tested_components:
                    gaps.append(f"class:{cls['name']}")

                for method in cls.get("methods", []):
                    if not method["name"].startswith("_"):
                        method_key = f"{cls['name'].lower()}{method['name'].lower()}"
                        if method_key not in tested_components:
                            gaps.append(f"method:{cls['name']}.{method['name']}")

            for func in code_analysis.get("functions", []):
                if not func["name"].startswith("_"):
                    if func["name"].lower() not in tested_components:
                        gaps.append(f"function:{func['name']}")

        except Exception as e:
            logger.error(f"Error analyzing test file {test_file}: {e}")

        return gaps


class TestGenerator:
    """Generates test code using AI"""

    def __init__(self):
        self.ollama_client = get_ollama_client()

    async def generate_test_class(
        self, code_analysis: Dict[str, Any], existing_tests: Optional[str] = None
    ) -> str:
        """Generate a complete test class for the analyzed code"""

        # Prepare context for the AI model
        context = self._prepare_test_context(code_analysis, existing_tests)

        system_prompt = """You are an expert Python test developer specializing in pytest and FastAPI testing.
Your task is to generate comprehensive, high-quality test code that follows these principles:

1. Use pytest framework with async support
2. Follow TDD best practices
3. Include unit tests and integration tests
4. Mock external dependencies appropriately
5. Test both success and failure scenarios
6. Include edge cases and boundary conditions
7. Use descriptive test method names
8. Add proper docstrings
9. Follow the existing code style and patterns
10. Ensure 90%+ test coverage

Generate complete, runnable test code that can be directly saved to a file."""

        user_prompt = f"""Generate comprehensive pytest test code for the following Python code analysis:

{context}

Requirements:
- Generate a complete test file with all necessary imports
- Include test fixtures for setup and teardown
- Test all public methods and functions
- Include both positive and negative test cases
- Use appropriate mocking for external dependencies
- Follow async/await patterns where needed
- Include integration tests for API endpoints if applicable
- Add comprehensive docstrings for all test methods

Return only the Python test code, no explanations."""

        try:
            request = OllamaRequest(
                model="codellama:7b",
                prompt=user_prompt,
                system=system_prompt,
                options={
                    "temperature": 0.3,  # Lower temperature for more consistent code
                    "top_p": 0.9,
                    "num_predict": 4096,
                },
            )

            response = await self.ollama_client.generate(request)
            return self._clean_generated_code(response.response)

        except Exception as e:
            logger.error(f"Error generating test class: {e}")
            raise

    async def generate_specific_test(
        self, component_type: str, component_name: str, code_context: str
    ) -> str:
        """Generate a specific test for a component"""

        system_prompt = """You are an expert Python test developer. Generate a specific test method for the given component.
The test should be comprehensive, follow pytest conventions, and include proper mocking."""

        user_prompt = f"""Generate a pytest test method for this {component_type}:

Component: {component_name}
Context: {code_context}

Requirements:
- Generate only the test method (not a full class)
- Include proper docstring
- Test both success and failure scenarios
- Use appropriate mocking
- Follow async/await if needed
- Use descriptive assertions

Return only the test method code."""

        try:
            request = OllamaRequest(
                model="codellama:7b",
                prompt=user_prompt,
                system=system_prompt,
                options={"temperature": 0.3, "top_p": 0.9, "num_predict": 1024},
            )

            response = await self.ollama_client.generate(request)
            return self._clean_generated_code(response.response)

        except Exception as e:
            logger.error(f"Error generating specific test: {e}")
            raise

    def _prepare_test_context(
        self, code_analysis: Dict[str, Any], existing_tests: Optional[str] = None
    ) -> str:
        """Prepare context information for test generation"""
        context_parts = []

        # Add file information
        context_parts.append(f"File: {code_analysis.get('file_path', 'unknown')}")

        # Add imports
        if code_analysis.get("imports"):
            context_parts.append("Imports:")
            for imp in code_analysis["imports"][:10]:  # Limit to first 10
                context_parts.append(f"  - {imp}")

        # Add classes
        if code_analysis.get("classes"):
            context_parts.append("\nClasses:")
            for cls in code_analysis["classes"]:
                context_parts.append(f"  - {cls['name']} (line {cls['line_number']})")
                if cls.get("docstring"):
                    context_parts.append(f"    Docstring: {cls['docstring'][:100]}...")

                for method in cls.get("methods", []):
                    args_str = ", ".join(method["args"])
                    async_prefix = "async " if method["is_async"] else ""
                    context_parts.append(f"    - {async_prefix}{method['name']}({args_str})")

        # Add functions
        if code_analysis.get("functions"):
            context_parts.append("\nFunctions:")
            for func in code_analysis["functions"]:
                args_str = ", ".join(func["args"])
                async_prefix = "async " if func["is_async"] else ""
                context_parts.append(
                    f"  - {async_prefix}{func['name']}({args_str}) (line {func['line_number']})"
                )

        # Add existing tests context
        if existing_tests:
            context_parts.append(f"\nExisting tests (for reference):\n{existing_tests[:500]}...")

        return "\n".join(context_parts)

    def _clean_generated_code(self, code: str) -> str:
        """Clean and format generated code"""
        # Remove markdown code blocks if present
        code = re.sub(r"```python\n?", "", code)
        code = re.sub(r"```\n?", "", code)

        # Remove any leading/trailing whitespace
        code = code.strip()

        # Ensure proper imports are at the top
        if not code.startswith("import") and not code.startswith("from"):
            # Add basic imports if missing
            basic_imports = """import pytest
from unittest.mock import MagicMock, patch
import asyncio

"""
            code = basic_imports + code

        return code


class TestUpdaterAgent:
    """Main Test Updater Agent class"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.analyzer = TestAnalyzer(project_root)
        self.generator = TestGenerator()

    async def update_tests_for_file(self, file_path: str) -> Dict[str, Any]:
        """Update tests for a specific file"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        logger.info(f"Updating tests for {file_path}")

        # Analyze the code file
        code_analysis = self.analyzer.analyze_code_file(file_path)

        if "error" in code_analysis:
            return {"error": code_analysis["error"]}

        # Find existing test file
        existing_test_file = self.analyzer.find_existing_tests(file_path)
        existing_tests = None

        if existing_test_file:
            try:
                with open(existing_test_file, "r", encoding="utf-8") as f:
                    existing_tests = f.read()
            except Exception as e:
                logger.warning(f"Could not read existing test file: {e}")

        # Calculate coverage gaps
        gaps = self.analyzer.calculate_test_coverage_gaps(code_analysis, existing_test_file)

        # Generate new tests
        generated_tests = await self.generator.generate_test_class(code_analysis, existing_tests)

        # Determine output file path
        if existing_test_file:
            output_file = existing_test_file
        else:
            # Create new test file
            test_filename = f"test_{file_path.stem}.py"
            tests_dir = self.project_root / "tests" / "microservices" / file_path.parent.name
            tests_dir.mkdir(parents=True, exist_ok=True)
            output_file = tests_dir / test_filename

        # Save generated tests
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(generated_tests)

            logger.info(f"Tests saved to {output_file}")

            return {
                "success": True,
                "test_file": str(output_file),
                "coverage_gaps_found": len(gaps),
                "gaps": gaps,
                "generated_lines": len(generated_tests.split("\n")),
            }

        except Exception as e:
            logger.error(f"Error saving test file: {e}")
            return {"error": f"Failed to save test file: {e}"}

    async def update_all_microservice_tests(self) -> Dict[str, Any]:
        """Update tests for all microservices"""
        microservices_dir = self.project_root / "hrms" / "microservices"

        if not microservices_dir.exists():
            return {"error": "Microservices directory not found"}

        results = {}

        # Find all Python files in microservices
        for service_dir in microservices_dir.iterdir():
            if service_dir.is_dir() and service_dir.name != "shared":
                for py_file in service_dir.glob("*.py"):
                    if py_file.name != "__init__.py":
                        try:
                            result = await self.update_tests_for_file(str(py_file))
                            results[str(py_file)] = result
                        except Exception as e:
                            logger.error(f"Error updating tests for {py_file}: {e}")
                            results[str(py_file)] = {"error": str(e)}

        return {
            "total_files": len(results),
            "successful": len([r for r in results.values() if r.get("success")]),
            "failed": len([r for r in results.values() if "error" in r]),
            "results": results,
        }

    async def run_test_validation(self, test_file: str) -> Dict[str, Any]:
        """Run validation on generated tests"""
        try:
            # Run pytest on the generated test file
            import subprocess

            result = subprocess.run(
                ["python", "-m", "pytest", test_file, "-v", "--tb=short"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode,
            }

        except Exception as e:
            logger.error(f"Error running test validation: {e}")
            return {"error": str(e)}
