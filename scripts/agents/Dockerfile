# Agent Registry Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for agents
RUN pip install --no-cache-dir \
    aiohttp \
    uvicorn[standard] \
    fastapi \
    pydantic \
    asyncio \
    python-multipart \
    ast \
    pathlib

# Copy application code
COPY agents/ ./agents/
COPY mcp/ ./mcp/
COPY hrms/microservices/shared/ ./hrms/microservices/shared/

# Set environment variables
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8201

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8201/health || exit 1

# Run the agent registry
CMD ["python", "-m", "agents.agent_registry"]
