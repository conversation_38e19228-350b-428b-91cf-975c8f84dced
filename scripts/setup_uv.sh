#!/bin/bash
# Setup script for uv (Astral package manager) in oneHRMS project

set -e

echo "🚀 oneHRMS - uv Setup Script"
echo "=============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if uv is already installed
if command -v uv &> /dev/null; then
    UV_VERSION=$(uv --version)
    print_status "uv is already installed: $UV_VERSION"
else
    print_info "Installing uv (Astral package manager)..."
    
    # Install uv using the official installer
    if curl -LsSf https://astral.sh/uv/install.sh | sh; then
        print_status "uv installed successfully!"
        
        # Add to PATH for current session
        export PATH="$HOME/.local/bin:$PATH"
        
        print_warning "Please restart your terminal or run: source ~/.bashrc"
        print_info "Or add this to your shell profile: export PATH=\"\$HOME/.local/bin:\$PATH\""
    else
        print_error "Failed to install uv"
        exit 1
    fi
fi

# Check if we're in the oneHRMS project directory
if [ ! -f "pyproject.toml" ]; then
    print_error "pyproject.toml not found. Please run this script from the oneHRMS project root."
    exit 1
fi

print_info "Setting up Python environment with uv..."

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    print_info "Creating virtual environment..."
    uv venv
    print_status "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Install dependencies
print_info "Installing project dependencies..."
if uv pip install -e ".[dev]"; then
    print_status "Dependencies installed successfully!"
else
    print_warning "Failed to install from pyproject.toml, trying requirements files..."
    
    if [ -f "requirements-dev.txt" ]; then
        if uv pip install -r requirements-dev.txt; then
            print_status "Dependencies installed from requirements-dev.txt"
        else
            print_error "Failed to install dependencies"
            exit 1
        fi
    else
        print_error "No requirements-dev.txt found"
        exit 1
    fi
fi

echo ""
print_status "Setup complete! 🎉"
echo ""
print_info "Next steps:"
echo "  1. Activate the virtual environment: source .venv/bin/activate"
echo "  2. Start the attendance service: python scripts/start_attendance_service.py --dev"
echo "  3. Or use Docker: docker-compose -f docker-compose.microservices.yml up attendance-service"
echo ""
print_info "uv commands you can use:"
echo "  • uv pip install <package>     # Install a package"
echo "  • uv pip list                  # List installed packages"
echo "  • uv pip freeze               # Show installed packages with versions"
echo "  • uv pip install -e \".[dev]\"   # Install project in development mode"
echo "  • uv venv                      # Create virtual environment"
echo ""
print_info "Learn more about uv: https://docs.astral.sh/uv/"
