#!/usr/bin/env python3
"""
Microservices Management Script for oneHRMS

This script provides automation for managing microservices:
- Service deployment and scaling
- Health monitoring
- Log aggregation
- Testing automation
- Database migrations
- Configuration management
"""

import argparse
import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import requests
import yaml

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class MicroservicesManager:
    """Manager for oneHRMS microservices operations."""

    def __init__(self, config_file: str = "microservices.config.yaml"):
        self.config_file = config_file
        self.config = self.load_config()
        self.services = self.config.get("services", {})
        self.docker_compose_file = "docker-compose.microservices.yml"

    def load_config(self) -> Dict:
        """Load microservices configuration."""
        config_path = project_root / self.config_file

        if config_path.exists():
            with open(config_path, "r") as f:
                return yaml.safe_load(f)

        # Default configuration
        return {
            "services": {
                "employee-service": {
                    "port": 8100,
                    "health_endpoint": "/health",
                    "dependencies": ["postgres", "redis"],
                    "status": "active",
                },
                "payroll-service": {
                    "port": 8101,
                    "health_endpoint": "/health",
                    "dependencies": ["postgres", "redis"],
                    "status": "active",
                },
                "attendance-service": {
                    "port": 8102,
                    "health_endpoint": "/health",
                    "dependencies": ["postgres", "redis"],
                    "status": "placeholder",
                },
                "leave-service": {
                    "port": 8103,
                    "health_endpoint": "/health",
                    "dependencies": ["postgres", "redis"],
                    "status": "placeholder",
                },
                "recruitment-service": {
                    "port": 8104,
                    "health_endpoint": "/health",
                    "dependencies": ["postgres", "redis"],
                    "status": "placeholder",
                },
            },
            "infrastructure": {
                "kong": {"port": 8000, "admin_port": 8001},
                "postgres": {"port": 5432},
                "redis": {"port": 6379},
                "prometheus": {"port": 9090},
                "grafana": {"port": 3000},
            },
        }

    def run_command(self, command: List[str], cwd: Optional[str] = None) -> subprocess.CompletedProcess:
        """Run shell command and return result."""
        try:
            result = subprocess.run(
                command, cwd=cwd or project_root, capture_output=True, text=True, check=True
            )
            return result
        except subprocess.CalledProcessError as e:
            print(f"Command failed: {' '.join(command)}")
            print(f"Error: {e.stderr}")
            raise

    def start_services(self, services: Optional[List[str]] = None, profiles: Optional[List[str]] = None):
        """Start microservices."""
        print("🚀 Starting oneHRMS microservices...")

        command = ["docker-compose", "-f", self.docker_compose_file, "up", "-d"]

        if profiles:
            for profile in profiles:
                command.extend(["--profile", profile])

        if services:
            command.extend(services)

        self.run_command(command)

        print("✅ Services started successfully!")

        # Wait for services to be healthy
        if not services or "employee-service" in services:
            self.wait_for_service_health("employee-service")

    def stop_services(self, services: Optional[List[str]] = None):
        """Stop microservices."""
        print("🛑 Stopping oneHRMS microservices...")

        command = ["docker-compose", "-f", self.docker_compose_file, "down"]

        if services:
            command.extend(services)

        self.run_command(command)
        print("✅ Services stopped successfully!")

    def restart_services(self, services: Optional[List[str]] = None):
        """Restart microservices."""
        print("🔄 Restarting oneHRMS microservices...")

        command = ["docker-compose", "-f", self.docker_compose_file, "restart"]

        if services:
            command.extend(services)

        self.run_command(command)
        print("✅ Services restarted successfully!")

    def check_health(self, service: Optional[str] = None) -> Dict[str, bool]:
        """Check health of services."""
        print("🏥 Checking service health...")

        health_status = {}
        services_to_check = [service] if service else list(self.services.keys())

        for svc in services_to_check:
            if svc not in self.services:
                continue

            service_config = self.services[svc]
            port = service_config["port"]
            health_endpoint = service_config["health_endpoint"]

            try:
                response = requests.get(f"http://localhost:{port}{health_endpoint}", timeout=5)
                health_status[svc] = response.status_code == 200

                if response.status_code == 200:
                    print(f"✅ {svc}: Healthy")
                else:
                    print(f"❌ {svc}: Unhealthy (HTTP {response.status_code})")

            except requests.RequestException as e:
                health_status[svc] = False
                print(f"❌ {svc}: Unreachable ({str(e)})")

        return health_status

    def wait_for_service_health(self, service: str, timeout: int = 120):
        """Wait for service to become healthy."""
        print(f"⏳ Waiting for {service} to become healthy...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            health = self.check_health(service)
            if health.get(service, False):
                print(f"✅ {service} is healthy!")
                return True

            time.sleep(5)

        print(f"❌ {service} failed to become healthy within {timeout} seconds")
        return False

    def run_tests(self, service: Optional[str] = None, test_type: str = "unit"):
        """Run tests for microservices."""
        print(f"🧪 Running {test_type} tests...")

        if service:
            # Run tests for specific service
            test_command = [
                "docker-compose",
                "-f",
                self.docker_compose_file,
                "run",
                "--rm",
                "test-runner",
                "python",
                "-m",
                "pytest",
                f"tests/microservices/{service}/",
                "-v",
                "--tb=short",
            ]
        else:
            # Run all tests
            test_command = [
                "docker-compose",
                "-f",
                self.docker_compose_file,
                "run",
                "--rm",
                "test-runner",
                "python",
                "-m",
                "pytest",
                "tests/microservices/",
                "-v",
                "--tb=short",
            ]

        if test_type == "integration":
            test_command.extend(["-m", "integration"])
        elif test_type == "unit":
            test_command.extend(["-m", "unit"])

        self.run_command(test_command)
        print("✅ Tests completed!")

    def view_logs(self, service: Optional[str] = None, follow: bool = False):
        """View service logs."""
        print(f"📋 Viewing logs for {service or 'all services'}...")

        command = ["docker-compose", "-f", self.docker_compose_file, "logs"]

        if follow:
            command.append("-f")

        if service:
            command.append(service)

        # Run without capturing output to show logs in real-time
        subprocess.run(command, cwd=project_root)

    def scale_service(self, service: str, replicas: int):
        """Scale a service to specified number of replicas."""
        print(f"📈 Scaling {service} to {replicas} replicas...")

        command = [
            "docker-compose",
            "-f",
            self.docker_compose_file,
            "up",
            "-d",
            "--scale",
            f"{service}={replicas}",
            service,
        ]

        self.run_command(command)
        print(f"✅ {service} scaled to {replicas} replicas!")

    def run_migrations(self, service: str):
        """Run database migrations for a service."""
        print(f"🗃️ Running migrations for {service}...")

        command = [
            "docker-compose",
            "-f",
            self.docker_compose_file,
            "exec",
            service,
            "python",
            "-m",
            "alembic",
            "upgrade",
            "head",
        ]

        self.run_command(command)
        print(f"✅ Migrations completed for {service}!")

    def backup_database(self, output_file: Optional[str] = None):
        """Backup all databases."""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"backup_hrms_{timestamp}.sql"

        print(f"💾 Creating database backup: {output_file}")

        command = [
            "docker-compose",
            "-f",
            self.docker_compose_file,
            "exec",
            "-T",
            "postgres",
            "pg_dumpall",
            "-U",
            "hrms_user",
        ]

        with open(output_file, "w") as f:
            subprocess.run(command, cwd=project_root, stdout=f, check=True)

        print(f"✅ Database backup created: {output_file}")

    def restore_database(self, backup_file: str):
        """Restore database from backup."""
        print(f"📥 Restoring database from: {backup_file}")

        if not os.path.exists(backup_file):
            print(f"❌ Backup file not found: {backup_file}")
            return

        command = [
            "docker-compose",
            "-f",
            self.docker_compose_file,
            "exec",
            "-T",
            "postgres",
            "psql",
            "-U",
            "hrms_user",
            "-d",
            "postgres",
        ]

        with open(backup_file, "r") as f:
            subprocess.run(command, cwd=project_root, stdin=f, check=True)

        print("✅ Database restored successfully!")

    def generate_openapi_docs(self, service: str):
        """Generate OpenAPI documentation for a service."""
        print(f"📚 Generating OpenAPI docs for {service}...")

        service_config = self.services.get(service)
        if not service_config:
            print(f"❌ Unknown service: {service}")
            return

        port = service_config["port"]

        try:
            response = requests.get(f"http://localhost:{port}/openapi.json")
            response.raise_for_status()

            docs_dir = project_root / "docs" / "api"
            docs_dir.mkdir(parents=True, exist_ok=True)

            with open(docs_dir / f"{service}-openapi.json", "w") as f:
                json.dump(response.json(), f, indent=2)

            print(f"✅ OpenAPI docs generated: docs/api/{service}-openapi.json")

        except requests.RequestException as e:
            print(f"❌ Failed to generate docs for {service}: {str(e)}")

    def status(self):
        """Show overall system status."""
        print("📊 oneHRMS Microservices Status")
        print("=" * 50)

        # Check Docker Compose services
        try:
            result = self.run_command(
                ["docker-compose", "-f", self.docker_compose_file, "ps", "--format", "json"]
            )

            services_status = json.loads(result.stdout) if result.stdout.strip() else []

            print("\n🐳 Docker Services:")
            for service in services_status:
                name = service.get("Name", "Unknown")
                state = service.get("State", "Unknown")
                status = service.get("Status", "Unknown")

                status_icon = "✅" if state == "running" else "❌"
                print(f"  {status_icon} {name}: {state} ({status})")

        except Exception as e:
            print(f"❌ Failed to get Docker status: {str(e)}")

        # Check service health
        print("\n🏥 Service Health:")
        health_status = self.check_health()

        # Check Kong API Gateway
        print("\n🌉 API Gateway (Kong):")
        try:
            response = requests.get("http://localhost:8001/status", timeout=5)
            if response.status_code == 200:
                print("  ✅ Kong Admin API: Healthy")
            else:
                print(f"  ❌ Kong Admin API: Unhealthy (HTTP {response.status_code})")
        except requests.RequestException:
            print("  ❌ Kong Admin API: Unreachable")


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="oneHRMS Microservices Manager")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Start command
    start_parser = subparsers.add_parser("start", help="Start services")
    start_parser.add_argument("--services", nargs="+", help="Specific services to start")
    start_parser.add_argument("--profiles", nargs="+", help="Docker Compose profiles to include")

    # Stop command
    stop_parser = subparsers.add_parser("stop", help="Stop services")
    stop_parser.add_argument("--services", nargs="+", help="Specific services to stop")

    # Restart command
    restart_parser = subparsers.add_parser("restart", help="Restart services")
    restart_parser.add_argument("--services", nargs="+", help="Specific services to restart")

    # Health command
    health_parser = subparsers.add_parser("health", help="Check service health")
    health_parser.add_argument("--service", help="Specific service to check")

    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--service", help="Specific service to test")
    test_parser.add_argument(
        "--type", choices=["unit", "integration", "all"], default="unit", help="Test type"
    )

    # Logs command
    logs_parser = subparsers.add_parser("logs", help="View logs")
    logs_parser.add_argument("--service", help="Specific service logs")
    logs_parser.add_argument("--follow", "-f", action="store_true", help="Follow logs")

    # Scale command
    scale_parser = subparsers.add_parser("scale", help="Scale service")
    scale_parser.add_argument("service", help="Service to scale")
    scale_parser.add_argument("replicas", type=int, help="Number of replicas")

    # Migrate command
    migrate_parser = subparsers.add_parser("migrate", help="Run migrations")
    migrate_parser.add_argument("service", help="Service to migrate")

    # Backup command
    backup_parser = subparsers.add_parser("backup", help="Backup database")
    backup_parser.add_argument("--output", help="Output file path")

    # Restore command
    restore_parser = subparsers.add_parser("restore", help="Restore database")
    restore_parser.add_argument("backup_file", help="Backup file to restore")

    # Docs command
    docs_parser = subparsers.add_parser("docs", help="Generate API documentation")
    docs_parser.add_argument("service", help="Service to generate docs for")

    # Status command
    subparsers.add_parser("status", help="Show system status")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    manager = MicroservicesManager()

    try:
        if args.command == "start":
            manager.start_services(args.services, args.profiles)
        elif args.command == "stop":
            manager.stop_services(args.services)
        elif args.command == "restart":
            manager.restart_services(args.services)
        elif args.command == "health":
            manager.check_health(args.service)
        elif args.command == "test":
            manager.run_tests(args.service, args.type)
        elif args.command == "logs":
            manager.view_logs(args.service, args.follow)
        elif args.command == "scale":
            manager.scale_service(args.service, args.replicas)
        elif args.command == "migrate":
            manager.run_migrations(args.service)
        elif args.command == "backup":
            manager.backup_database(args.output)
        elif args.command == "restore":
            manager.restore_database(args.backup_file)
        elif args.command == "docs":
            manager.generate_openapi_docs(args.service)
        elif args.command == "status":
            manager.status()

    except KeyboardInterrupt:
        print("\n⏹️ Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
