#!/usr/bin/env python3
"""
Python refactoring tools for oneHRMS project.

This script provides automated refactoring capabilities using:
- rope: Advanced Python refactoring library
- pyupgrade: Upgrade Python syntax to newer versions
- autoflake: Remove unused imports and variables
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional


class RefactorTools:
    """Python refactoring tools wrapper."""

    def __init__(self, project_root: Optional[str] = None):
        """Initialize refactoring tools."""
        self.project_root = Path(project_root or os.getcwd())
        self.python_files = []
        self._discover_python_files()

    def _discover_python_files(self) -> None:
        """Discover Python files in the project."""
        exclude_dirs = {
            ".git",
            ".venv",
            "venv",
            "__pycache__",
            ".pytest_cache",
            "node_modules",
            "build",
            "dist",
            ".mypy_cache",
        }

        for py_file in self.project_root.rglob("*.py"):
            # Skip files in excluded directories
            if any(excluded in py_file.parts for excluded in exclude_dirs):
                continue
            self.python_files.append(py_file)

        print(f"Found {len(self.python_files)} Python files to process")

    def run_pyupgrade(self, target_version: str = "py310") -> bool:
        """Run pyupgrade to modernize Python syntax."""
        print(f"🔄 Running pyupgrade (target: {target_version})...")

        cmd = ["pyupgrade", f"--{target_version}-plus"] + [str(f) for f in self.python_files]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ pyupgrade completed successfully")
                return True
            else:
                print(f"❌ pyupgrade failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error running pyupgrade: {e}")
            return False

    def run_autoflake(self) -> bool:
        """Run autoflake to remove unused imports and variables."""
        print("🔄 Running autoflake...")

        cmd = [
            "autoflake",
            "--in-place",
            "--remove-all-unused-imports",
            "--remove-unused-variables",
            "--remove-duplicate-keys",
            "--expand-star-imports",
        ] + [str(f) for f in self.python_files]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ autoflake completed successfully")
                return True
            else:
                print(f"❌ autoflake failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error running autoflake: {e}")
            return False

    def run_isort(self) -> bool:
        """Run isort to organize imports."""
        print("🔄 Running isort...")

        cmd = ["isort", "--profile=black", "--line-length=110"] + [str(f) for f in self.python_files]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ isort completed successfully")
                return True
            else:
                print(f"❌ isort failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error running isort: {e}")
            return False

    def run_black(self) -> bool:
        """Run black to format code."""
        print("🔄 Running black...")

        cmd = ["black", "--line-length=110"] + [str(f) for f in self.python_files]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ black completed successfully")
                return True
            else:
                print(f"❌ black failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error running black: {e}")
            return False

    def run_all_refactoring(self) -> bool:
        """Run all refactoring tools in sequence."""
        print("🚀 Starting comprehensive refactoring...")

        tools = [
            ("pyupgrade", self.run_pyupgrade),
            ("autoflake", self.run_autoflake),
            ("isort", self.run_isort),
            ("black", self.run_black),
        ]

        success = True
        for tool_name, tool_func in tools:
            print(f"\n--- Running {tool_name} ---")
            if not tool_func():
                success = False
                print(f"❌ {tool_name} failed")
            else:
                print(f"✅ {tool_name} succeeded")

        if success:
            print("\n🎉 All refactoring tools completed successfully!")
        else:
            print("\n⚠️  Some refactoring tools failed. Check output above.")

        return success

    def analyze_code_quality(self) -> None:
        """Analyze code quality with various tools."""
        print("📊 Analyzing code quality...")

        # Run flake8
        print("\n--- Running flake8 ---")
        subprocess.run(["flake8"] + [str(f) for f in self.python_files])

        # Run mypy
        print("\n--- Running mypy ---")
        subprocess.run(["mypy"] + [str(f) for f in self.python_files])


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="Python refactoring tools for oneHRMS")
    parser.add_argument("--project-root", help="Project root directory")
    parser.add_argument("--pyupgrade", action="store_true", help="Run pyupgrade only")
    parser.add_argument("--autoflake", action="store_true", help="Run autoflake only")
    parser.add_argument("--isort", action="store_true", help="Run isort only")
    parser.add_argument("--black", action="store_true", help="Run black only")
    parser.add_argument("--analyze", action="store_true", help="Analyze code quality")
    parser.add_argument("--all", action="store_true", help="Run all refactoring tools")

    args = parser.parse_args()

    refactor = RefactorTools(args.project_root)

    if args.pyupgrade:
        refactor.run_pyupgrade()
    elif args.autoflake:
        refactor.run_autoflake()
    elif args.isort:
        refactor.run_isort()
    elif args.black:
        refactor.run_black()
    elif args.analyze:
        refactor.analyze_code_quality()
    elif args.all:
        refactor.run_all_refactoring()
    else:
        print("No action specified. Use --help for options.")
        print("Tip: Use --all to run all refactoring tools")


if __name__ == "__main__":
    main()
