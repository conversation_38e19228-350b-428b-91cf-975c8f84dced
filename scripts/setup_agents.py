#!/usr/bin/env python3
"""
Complete setup script for oneHRMS AI agents and MCP integration
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

import aiohttp
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm

console = Console()
logger = logging.getLogger(__name__)


class AgentSetup:
    """Complete setup for AI agents and MCP integration"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.services_to_check = {
            "Ollama": "http://localhost:11434/api/tags",
            "MCP Server": "http://localhost:8200/health",
            "Agent Registry": "http://localhost:8201/health",
        }

    def print_banner(self):
        """Print setup banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    oneHRMS AI Agent Setup                   ║
║              Shared Microservices Architecture              ║
╚══════════════════════════════════════════════════════════════╝
        """
        console.print(Panel(banner, border_style="blue"))

    def check_prerequisites(self) -> bool:
        """Check if prerequisites are installed"""
        console.print("\n[bold]Checking Prerequisites...[/bold]")

        prerequisites = {
            "Docker": ["docker", "--version"],
            "Docker Compose": ["docker-compose", "--version"],
            "Python": ["python", "--version"],
            "uv": ["uv", "--version"],
        }

        all_good = True

        for name, command in prerequisites.items():
            try:
                result = subprocess.run(command, capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip().split("\n")[0]
                    console.print(f"[green]✓ {name}: {version}[/green]")
                else:
                    console.print(f"[red]✗ {name}: Not found[/red]")
                    all_good = False
            except FileNotFoundError:
                console.print(f"[red]✗ {name}: Not installed[/red]")
                all_good = False

        return all_good

    def install_dependencies(self) -> bool:
        """Install Python dependencies using uv"""
        console.print("\n[bold]Installing Dependencies...[/bold]")

        try:
            with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
            ) as progress:
                task = progress.add_task("Installing Python packages...", total=None)

                result = subprocess.run(
                    ["uv", "pip", "install", "-r", "requirements.txt"],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                )

                if result.returncode == 0:
                    console.print("[green]✓ Dependencies installed successfully[/green]")
                    return True
                else:
                    console.print(f"[red]✗ Failed to install dependencies: {result.stderr}[/red]")
                    return False

        except Exception as e:
            console.print(f"[red]✗ Error installing dependencies: {e}[/red]")
            return False

    def start_docker_services(self) -> bool:
        """Start required Docker services"""
        console.print("\n[bold]Starting Docker Services...[/bold]")

        services = ["postgres", "redis", "ollama"]

        try:
            with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
            ) as progress:
                for service in services:
                    task = progress.add_task(f"Starting {service}...", total=None)

                    result = subprocess.run(
                        ["docker-compose", "-f", "docker-compose.microservices.yml", "up", "-d", service],
                        cwd=self.project_root,
                        capture_output=True,
                        text=True,
                    )

                    if result.returncode == 0:
                        console.print(f"[green]✓ {service} started[/green]")
                    else:
                        console.print(f"[red]✗ Failed to start {service}: {result.stderr}[/red]")
                        return False

            # Wait for services to be ready
            console.print("[yellow]Waiting for services to be ready...[/yellow]")
            time.sleep(10)

            return True

        except Exception as e:
            console.print(f"[red]✗ Error starting Docker services: {e}[/red]")
            return False

    async def setup_ollama_models(self) -> bool:
        """Setup Ollama models"""
        console.print("\n[bold]Setting up Ollama Models...[/bold]")

        try:
            # Import and run the Ollama setup
            from setup_ollama import OllamaSetup

            setup = OllamaSetup()
            results = await setup.setup_all_models()
            await setup.create_model_config(results)

            successful = sum(1 for success in results.values() if success)
            total = len(results)

            if successful == total:
                console.print(f"[green]✓ All {total} models setup successfully[/green]")
                return True
            else:
                console.print(f"[yellow]⚠ {successful}/{total} models setup successfully[/yellow]")
                return successful > 0

        except Exception as e:
            console.print(f"[red]✗ Error setting up Ollama models: {e}[/red]")
            return False

    def start_mcp_services(self) -> bool:
        """Start MCP and Agent Registry services"""
        console.print("\n[bold]Starting MCP Services...[/bold]")

        services = ["mcp-server", "agent-registry"]

        try:
            with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
            ) as progress:
                for service in services:
                    task = progress.add_task(f"Starting {service}...", total=None)

                    result = subprocess.run(
                        ["docker-compose", "-f", "docker-compose.microservices.yml", "up", "-d", service],
                        cwd=self.project_root,
                        capture_output=True,
                        text=True,
                    )

                    if result.returncode == 0:
                        console.print(f"[green]✓ {service} started[/green]")
                    else:
                        console.print(f"[red]✗ Failed to start {service}: {result.stderr}[/red]")
                        return False

            # Wait for services to be ready
            console.print("[yellow]Waiting for MCP services to be ready...[/yellow]")
            time.sleep(15)

            return True

        except Exception as e:
            console.print(f"[red]✗ Error starting MCP services: {e}[/red]")
            return False

    async def verify_services(self) -> Dict[str, bool]:
        """Verify all services are running"""
        console.print("\n[bold]Verifying Services...[/bold]")

        results = {}

        async with aiohttp.ClientSession() as session:
            for service_name, url in self.services_to_check.items():
                try:
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            console.print(f"[green]✓ {service_name}: Running[/green]")
                            results[service_name] = True
                        else:
                            console.print(f"[red]✗ {service_name}: HTTP {response.status}[/red]")
                            results[service_name] = False
                except Exception as e:
                    console.print(f"[red]✗ {service_name}: {e}[/red]")
                    results[service_name] = False

        return results

    async def register_default_agents(self) -> bool:
        """Register default agents with the registry"""
        console.print("\n[bold]Registering Default Agents...[/bold]")

        agents = [
            {
                "name": "test-updater",
                "description": "Automated test generation and maintenance",
                "version": "1.0.0",
                "capabilities": [
                    "generate_unit_tests",
                    "generate_integration_tests",
                    "update_existing_tests",
                ],
                "triggers": ["code_change", "api_change", "manual_request"],
                "model": "codellama:7b",
            },
            {
                "name": "doc-generator",
                "description": "Automated documentation generation and updates",
                "version": "1.0.0",
                "capabilities": ["generate_openapi_specs", "generate_markdown_docs", "update_readme_files"],
                "triggers": ["api_change", "milestone_reached", "manual_request"],
                "model": "llama2:13b",
            },
            {
                "name": "refactor-assistant",
                "description": "Code quality improvement and refactoring suggestions",
                "version": "1.0.0",
                "capabilities": ["detect_code_smells", "suggest_refactoring", "improve_code_quality"],
                "triggers": ["code_smell_detected", "complexity_threshold", "manual_request"],
                "model": "mistral:7b",
            },
        ]

        registry_url = "http://localhost:8201"
        successful = 0

        async with aiohttp.ClientSession() as session:
            for agent in agents:
                try:
                    async with session.post(f"{registry_url}/agents/register", json=agent) as response:
                        if response.status == 200:
                            console.print(f"[green]✓ Registered agent: {agent['name']}[/green]")
                            successful += 1
                        else:
                            error_text = await response.text()
                            console.print(f"[red]✗ Failed to register {agent['name']}: {error_text}[/red]")
                except Exception as e:
                    console.print(f"[red]✗ Error registering {agent['name']}: {e}[/red]")

        return successful == len(agents)

    def create_env_file(self):
        """Create environment file with default values"""
        env_file = self.project_root / ".env"

        if env_file.exists():
            console.print("[yellow]⚠ .env file already exists, skipping creation[/yellow]")
            return

        env_content = """# oneHRMS AI Agent Configuration
MCP_JWT_SECRET=your-secret-key-change-in-production
MCP_DB_USER=mcp_user
MCP_DB_PASSWORD=mcp_password
REGISTRY_DB_USER=registry_user
REGISTRY_DB_PASSWORD=registry_password
REDIS_PASSWORD=redis_password

# Ollama Configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434

# Development Settings
ENVIRONMENT=development
DEBUG=true
"""

        with open(env_file, "w") as f:
            f.write(env_content)

        console.print(f"[green]✓ Created .env file at {env_file}[/green]")

    def print_completion_summary(self, service_status: Dict[str, bool], agents_registered: bool):
        """Print setup completion summary"""
        console.print("\n" + "=" * 60)
        console.print(Panel("Setup Complete!", title="oneHRMS AI Agent Setup", border_style="green"))

        # Service status
        console.print("[bold]Service Status:[/bold]")
        for service, status in service_status.items():
            status_text = "[green]✓ Running[/green]" if status else "[red]✗ Failed[/red]"
            console.print(f"  {service}: {status_text}")

        # Agent registration status
        agent_status = "[green]✓ Registered[/green]" if agents_registered else "[red]✗ Failed[/red]"
        console.print(f"  Default Agents: {agent_status}")

        # Next steps
        console.print("\n[bold]Next Steps:[/bold]")
        console.print("1. Check agent status: [cyan]python scripts/agent_cli.py status[/cyan]")
        console.print("2. List agents: [cyan]python scripts/agent_cli.py list-agents[/cyan]")
        console.print("3. Update tests: [cyan]python scripts/agent_cli.py update-tests[/cyan]")
        console.print("4. Generate docs: [cyan]python scripts/agent_cli.py generate-docs[/cyan]")

        console.print("\n[bold]Web Interfaces:[/bold]")
        console.print("• MCP Server: [link]http://localhost:8200[/link]")
        console.print("• Agent Registry: [link]http://localhost:8201[/link]")
        console.print("• Ollama API: [link]http://localhost:11434[/link]")

        all_good = all(service_status.values()) and agents_registered
        if all_good:
            console.print("\n[green]🎉 All systems are ready! Your AI agents are now operational.[/green]")
        else:
            console.print(
                "\n[yellow]⚠ Some components failed to setup. Check the logs above for details.[/yellow]"
            )


async def main():
    """Main setup function"""
    setup = AgentSetup()

    try:
        setup.print_banner()

        # Check prerequisites
        if not setup.check_prerequisites():
            console.print("\n[red]Prerequisites not met. Please install missing components.[/red]")
            return False

        # Confirm setup
        if not Confirm.ask("\nProceed with AI agent setup?"):
            console.print("[yellow]Setup cancelled by user[/yellow]")
            return False

        # Create environment file
        setup.create_env_file()

        # Install dependencies
        if not setup.install_dependencies():
            return False

        # Start Docker services
        if not setup.start_docker_services():
            return False

        # Setup Ollama models
        if not await setup.setup_ollama_models():
            console.print("[yellow]⚠ Ollama setup had issues, but continuing...[/yellow]")

        # Start MCP services
        if not setup.start_mcp_services():
            return False

        # Verify services
        service_status = await setup.verify_services()

        # Register default agents
        agents_registered = await setup.register_default_agents()

        # Print summary
        setup.print_completion_summary(service_status, agents_registered)

        return all(service_status.values()) and agents_registered

    except KeyboardInterrupt:
        console.print("\n[yellow]Setup interrupted by user[/yellow]")
        return False
    except Exception as e:
        console.print(f"\n[red]Setup failed with error: {e}[/red]")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
