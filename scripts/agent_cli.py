#!/usr/bin/env python3
"""
Agent CLI - Command Line Interface for managing oneHRMS AI agents
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import aiohttp
import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm, Prompt
from rich.table import Table

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.documentation_generator_agent import DocumentationGeneratorAgent
from agents.test_updater_agent import TestUpdaterAgent

console = Console()


class AgentCLI:
    """CLI for managing AI agents"""

    def __init__(self, registry_url: str = "http://localhost:8201", mcp_url: str = "http://localhost:8200"):
        self.registry_url = registry_url
        self.mcp_url = mcp_url
        self.project_root = Path(__file__).parent.parent

    async def check_services(self) -> Dict[str, bool]:
        """Check if required services are running"""
        services = {"Agent Registry": False, "MCP Server": False}

        try:
            async with aiohttp.ClientSession() as session:
                # Check Agent Registry
                try:
                    async with session.get(f"{self.registry_url}/health", timeout=5) as response:
                        services["Agent Registry"] = response.status == 200
                except:
                    pass

                # Check MCP Server
                try:
                    async with session.get(f"{self.mcp_url}/health", timeout=5) as response:
                        services["MCP Server"] = response.status == 200
                except:
                    pass

        except Exception as e:
            console.print(f"[red]Error checking services: {e}[/red]")

        return services

    async def list_agents(self):
        """List all registered agents"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.registry_url}/agents") as response:
                    if response.status == 200:
                        data = await response.json()
                        agents = data.get("agents", [])

                        if not agents:
                            console.print("[yellow]No agents registered[/yellow]")
                            return

                        table = Table(title="Registered Agents")
                        table.add_column("Name", style="cyan")
                        table.add_column("Status", style="green")
                        table.add_column("Version", style="blue")
                        table.add_column("Model", style="magenta")
                        table.add_column("Capabilities", style="yellow")
                        table.add_column("Current Tasks", style="red")

                        for agent in agents:
                            status_color = "green" if agent["status"] == "active" else "red"
                            capabilities = ", ".join(agent.get("capabilities", []))

                            table.add_row(
                                agent["name"],
                                f"[{status_color}]{agent['status']}[/{status_color}]",
                                agent["version"],
                                agent["model"],
                                capabilities,
                                str(agent.get("current_tasks", 0)),
                            )

                        console.print(table)
                    else:
                        console.print(f"[red]Failed to list agents: {response.status}[/red]")

        except Exception as e:
            console.print(f"[red]Error listing agents: {e}[/red]")

    async def register_agent(self, name: str, description: str, version: str, capabilities: list, model: str):
        """Register a new agent"""
        registration_data = {
            "name": name,
            "description": description,
            "version": version,
            "capabilities": capabilities,
            "triggers": ["manual_request"],
            "model": model,
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.registry_url}/agents/register", json=registration_data
                ) as response:
                    if response.status == 200:
                        console.print(f"[green]Agent '{name}' registered successfully[/green]")
                    else:
                        error_text = await response.text()
                        console.print(f"[red]Failed to register agent: {error_text}[/red]")

        except Exception as e:
            console.print(f"[red]Error registering agent: {e}[/red]")

    async def submit_task(self, agent_name: str, capability: str, parameters: Dict[str, Any]):
        """Submit a task to an agent"""
        task_data = {
            "agent_name": agent_name,
            "capability": capability,
            "parameters": parameters,
            "priority": 5,
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.registry_url}/tasks/submit", json=task_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        task_id = data["task_id"]
                        console.print(f"[green]Task submitted successfully. Task ID: {task_id}[/green]")

                        # Monitor task progress
                        await self.monitor_task(task_id)
                    else:
                        error_text = await response.text()
                        console.print(f"[red]Failed to submit task: {error_text}[/red]")

        except Exception as e:
            console.print(f"[red]Error submitting task: {e}[/red]")

    async def monitor_task(self, task_id: str):
        """Monitor task execution"""
        with Progress(
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
        ) as progress:
            task = progress.add_task(f"Monitoring task {task_id}...", total=None)

            while True:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(f"{self.registry_url}/tasks/{task_id}") as response:
                            if response.status == 200:
                                data = await response.json()
                                status = data["status"]

                                if status in ["completed", "failed", "cancelled"]:
                                    progress.stop()

                                    if status == "completed":
                                        console.print(f"[green]Task {task_id} completed successfully[/green]")
                                        if data.get("result"):
                                            console.print(
                                                Panel(
                                                    json.dumps(data["result"], indent=2),
                                                    title="Task Result",
                                                    border_style="green",
                                                )
                                            )
                                    else:
                                        console.print(f"[red]Task {task_id} {status}[/red]")
                                        if data.get("error"):
                                            console.print(f"[red]Error: {data['error']}[/red]")
                                    break

                                progress.update(task, description=f"Task {task_id} - {status}")

                            await asyncio.sleep(2)

                except Exception as e:
                    console.print(f"[red]Error monitoring task: {e}[/red]")
                    break

    async def run_test_updater(self, file_path: Optional[str] = None):
        """Run the test updater agent"""
        agent = TestUpdaterAgent(str(self.project_root))

        with Progress(
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
        ) as progress:
            if file_path:
                task = progress.add_task(f"Updating tests for {file_path}...", total=None)
                result = await agent.update_tests_for_file(file_path)
            else:
                task = progress.add_task("Updating tests for all microservices...", total=None)
                result = await agent.update_all_microservice_tests()

        if result.get("success") or result.get("total_files"):
            console.print("[green]Test update completed successfully[/green]")
            console.print(
                Panel(
                    json.dumps(result, indent=2, default=str),
                    title="Test Update Result",
                    border_style="green",
                )
            )
        else:
            console.print("[red]Test update failed[/red]")
            console.print(
                Panel(
                    json.dumps(result, indent=2, default=str), title="Test Update Error", border_style="red"
                )
            )

    async def run_doc_generator(self, service_name: Optional[str] = None):
        """Run the documentation generator agent"""
        agent = DocumentationGeneratorAgent(str(self.project_root))

        with Progress(
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console
        ) as progress:
            if service_name:
                task = progress.add_task(f"Generating docs for {service_name}...", total=None)
                result = await agent.generate_service_documentation(service_name)
            else:
                task = progress.add_task("Generating docs for all services...", total=None)
                result = await agent.generate_all_service_documentation()

        if result.get("success") or result.get("total_services"):
            console.print("[green]Documentation generation completed successfully[/green]")
            console.print(
                Panel(
                    json.dumps(result, indent=2, default=str),
                    title="Documentation Generation Result",
                    border_style="green",
                )
            )
        else:
            console.print("[red]Documentation generation failed[/red]")
            console.print(
                Panel(
                    json.dumps(result, indent=2, default=str),
                    title="Documentation Generation Error",
                    border_style="red",
                )
            )


@click.group()
@click.option("--registry-url", default="http://localhost:8201", help="Agent Registry URL")
@click.option("--mcp-url", default="http://localhost:8200", help="MCP Server URL")
@click.pass_context
def cli(ctx, registry_url, mcp_url):
    """oneHRMS AI Agent CLI"""
    ctx.ensure_object(dict)
    ctx.obj["cli"] = AgentCLI(registry_url, mcp_url)


@cli.command()
@click.pass_context
def status(ctx):
    """Check status of AI services"""
    cli_obj = ctx.obj["cli"]

    async def check_status():
        services = await cli_obj.check_services()

        table = Table(title="Service Status")
        table.add_column("Service", style="cyan")
        table.add_column("Status", style="green")

        for service, is_running in services.items():
            status_text = "[green]Running[/green]" if is_running else "[red]Not Running[/red]"
            table.add_row(service, status_text)

        console.print(table)

    asyncio.run(check_status())


@cli.command()
@click.pass_context
def list_agents(ctx):
    """List all registered agents"""
    cli_obj = ctx.obj["cli"]
    asyncio.run(cli_obj.list_agents())


@cli.command()
@click.option("--file", help="Specific file to update tests for")
@click.pass_context
def update_tests(ctx, file):
    """Update tests using Test Updater Agent"""
    cli_obj = ctx.obj["cli"]
    asyncio.run(cli_obj.run_test_updater(file))


@cli.command()
@click.option("--service", help="Specific service to generate docs for")
@click.pass_context
def generate_docs(ctx, service):
    """Generate documentation using Documentation Generator Agent"""
    cli_obj = ctx.obj["cli"]
    asyncio.run(cli_obj.run_doc_generator(service))


@cli.command()
@click.option("--name", prompt=True, help="Agent name")
@click.option("--description", prompt=True, help="Agent description")
@click.option("--version", default="1.0.0", help="Agent version")
@click.option("--model", default="codellama:7b", help="AI model to use")
@click.pass_context
def register(ctx, name, description, version, model):
    """Register a new agent"""
    cli_obj = ctx.obj["cli"]

    # Get capabilities
    capabilities = []
    while True:
        capability = Prompt.ask("Enter capability (or press Enter to finish)")
        if not capability:
            break
        capabilities.append(capability)

    if not capabilities:
        console.print("[red]At least one capability is required[/red]")
        return

    asyncio.run(cli_obj.register_agent(name, description, version, capabilities, model))


if __name__ == "__main__":
    cli()
