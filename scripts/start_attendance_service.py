#!/usr/bin/env python3
"""
Startup script for Attendance Management Service.

This script helps with development setup and service startup.
"""

import os
import subprocess
import sys
import time
from pathlib import Path

import requests

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_uv_installation():
    """Check if uv is installed and install if needed."""
    try:
        result = subprocess.run(["uv", "--version"], check=True, capture_output=True, text=True)
        print(f"✅ uv is installed: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("📦 Installing uv (Astral package manager)...")
        try:
            # Install uv using the official installer
            install_cmd = "curl -LsSf https://astral.sh/uv/install.sh | sh"
            subprocess.run(install_cmd, shell=True, check=True)
            print("✅ uv installed successfully")
            print("💡 You may need to restart your terminal or run: source ~/.bashrc")
            return True
        except Exception as e:
            print(f"❌ Failed to install uv: {str(e)}")
            print("💡 Please install uv manually: https://docs.astral.sh/uv/#installation")
            return False


def install_dependencies():
    """Install project dependencies using uv."""
    print("📦 Installing dependencies with uv...")

    try:
        # Create virtual environment if it doesn't exist
        if not (project_root / ".venv").exists():
            print("🔧 Creating virtual environment...")
            subprocess.run(["uv", "venv"], cwd=project_root, check=True)

        # Install dependencies
        print("📥 Installing project dependencies...")
        subprocess.run(["uv", "pip", "install", "-e", ".[dev]"], cwd=project_root, check=True)

        print("✅ Dependencies installed successfully")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {str(e)}")
        print("💡 Falling back to requirements files...")

        try:
            # Fallback to requirements files
            if (project_root / "requirements-dev.txt").exists():
                subprocess.run(["uv", "pip", "install", "-r", "requirements-dev.txt"],
                             cwd=project_root, check=True)
                print("✅ Dependencies installed from requirements-dev.txt")
                return True
        except Exception as fallback_e:
            print(f"❌ Fallback also failed: {str(fallback_e)}")
            return False


def check_dependencies():
    """Check if required services are running."""
    print("🔍 Checking dependencies...")

    # First check if uv is available
    if not check_uv_installation():
        return False

    dependencies = {
        "PostgreSQL": "postgresql://localhost:5432",
        "Redis": "redis://localhost:6379",
        "Employee Service": "http://localhost:8100/health",
    }

    for name, url in dependencies.items():
        try:
            if "postgresql" in url:
                # Check PostgreSQL
                import psycopg2

                conn = psycopg2.connect(
                    host="localhost",
                    port=5432,
                    database="postgres",
                    user="hrms_user",
                    password="hrms_password",
                )
                conn.close()
                print(f"✅ {name} is running")
            elif "redis" in url:
                # Check Redis
                import redis

                r = redis.Redis(host="localhost", port=6379, db=0)
                r.ping()
                print(f"✅ {name} is running")
            else:
                # Check HTTP service
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name} is running")
                else:
                    print(f"⚠️  {name} returned status {response.status_code}")
        except Exception as e:
            print(f"❌ {name} is not available: {str(e)}")
            return False

    return True


def setup_database():
    """Set up the attendance database."""
    print("🗄️  Setting up attendance database...")

    try:
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host="localhost", port=5432, database="postgres", user="hrms_user", password="hrms_password"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Create attendance database if it doesn't exist
        cursor.execute("SELECT 1 FROM pg_database WHERE datname='attendance_db'")
        if not cursor.fetchone():
            cursor.execute("CREATE DATABASE attendance_db")
            print("✅ Created attendance_db database")
        else:
            print("✅ attendance_db database already exists")

        cursor.close()
        conn.close()

        # Run migrations
        migration_file = (
            project_root / "hrms" / "microservices" / "attendance" / "migrations" / "001_initial_schema.sql"
        )
        if migration_file.exists():
            print("🔄 Running database migrations...")
            subprocess.run(
                [
                    "psql",
                    "-h",
                    "localhost",
                    "-p",
                    "5432",
                    "-U",
                    "hrms_user",
                    "-d",
                    "attendance_db",
                    "-f",
                    str(migration_file),
                ],
                env={**os.environ, "PGPASSWORD": "hrms_password"},
                check=True,
            )
            print("✅ Database migrations completed")

        return True

    except Exception as e:
        print(f"❌ Database setup failed: {str(e)}")
        return False


def start_service():
    """Start the attendance service."""
    print("🚀 Starting Attendance Management Service...")

    # Set environment variables
    env = os.environ.copy()
    env.update(
        {
            "PYTHONPATH": str(project_root),
            "DATABASE_URL": "postgresql://hrms_user:hrms_password@localhost:5432/attendance_db",
            "REDIS_URL": "redis://localhost:6379/2",
            "LOG_LEVEL": "DEBUG",
            "TESTING": "false",
            "EMPLOYEE_SERVICE_URL": "http://localhost:8100",
        }
    )

    # Start the service
    try:
        subprocess.run(
            [
                sys.executable,
                "-m",
                "uvicorn",
                "hrms.microservices.attendance.api:app",
                "--host",
                "0.0.0.0",
                "--port",
                "8102",
                "--reload",
                "--log-level",
                "debug",
            ],
            env=env,
            cwd=project_root,
        )
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start service: {str(e)}")


def wait_for_service():
    """Wait for the service to be ready."""
    print("⏳ Waiting for service to be ready...")

    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:8102/health", timeout=2)
            if response.status_code == 200:
                print("✅ Service is ready!")
                return True
        except:
            pass

        time.sleep(1)
        print(f"   Attempt {attempt + 1}/{max_attempts}...")

    print("❌ Service failed to start within timeout")
    return False


def show_service_info():
    """Show service information and useful URLs."""
    print("\n" + "=" * 60)
    print("🎉 Attendance Management Service Started Successfully!")
    print("=" * 60)
    print()
    print("📋 Service Information:")
    print("   • Service: Attendance Management Service")
    print("   • Version: 1.0.0")
    print("   • Port: 8102")
    print()
    print("🔗 Useful URLs:")
    print("   • Health Check: http://localhost:8102/health")
    print("   • API Documentation: http://localhost:8102/docs")
    print("   • ReDoc Documentation: http://localhost:8102/redoc")
    print("   • OpenAPI Spec: http://localhost:8102/openapi.json")
    print()
    print("📊 Sample API Calls:")
    print("   • List Shifts: GET http://localhost:8102/api/v1/attendance/shifts")
    print("   • Create Attendance: POST http://localhost:8102/api/v1/attendance/records")
    print("   • Health Check: GET http://localhost:8102/health")
    print()
    print("🛠️  Development:")
    print("   • Hot reload is enabled - changes will auto-restart the service")
    print("   • Logs are displayed in the console")
    print("   • Press Ctrl+C to stop the service")
    print()
    print("=" * 60)


def main():
    """Main startup function."""
    print("🏢 oneHRMS - Attendance Management Service Startup")
    print("=" * 50)

    # Check if we're in development mode
    if "--dev" in sys.argv:
        print("🔧 Development mode enabled")

        # Install dependencies
        if not install_dependencies():
            print("\n❌ Failed to install dependencies.")
            return 1

        # Check dependencies
        if not check_dependencies():
            print("\n❌ Some dependencies are not available.")
            print(
                "💡 Try running: docker-compose -f docker-compose.microservices.yml up postgres redis employee-service -d"
            )
            return 1

        # Setup database
        if not setup_database():
            print("\n❌ Database setup failed.")
            return 1

        # Show info and start service
        show_service_info()
        start_service()

    elif "--check" in sys.argv:
        print("🔍 Running dependency check only...")
        if check_dependencies():
            print("\n✅ All dependencies are available!")
            return 0
        else:
            print("\n❌ Some dependencies are missing.")
            return 1

    elif "--setup-db" in sys.argv:
        print("🗄️  Setting up database only...")
        if setup_database():
            print("\n✅ Database setup completed!")
            return 0
        else:
            print("\n❌ Database setup failed.")
            return 1

    elif "--install" in sys.argv:
        print("📦 Installing dependencies only...")
        if install_dependencies():
            print("\n✅ Dependencies installed successfully!")
            return 0
        else:
            print("\n❌ Dependency installation failed.")
            return 1

    else:
        print("Usage:")
        print("  python scripts/start_attendance_service.py --dev      # Start in development mode")
        print("  python scripts/start_attendance_service.py --check    # Check dependencies only")
        print("  python scripts/start_attendance_service.py --setup-db # Setup database only")
        print("  python scripts/start_attendance_service.py --install  # Install dependencies only")
        print("")
        print("💡 Tip: Use 'uv' for faster package management!")
        print("   Install uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
