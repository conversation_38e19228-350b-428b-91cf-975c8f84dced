#!/usr/bin/env python3
"""
Ollama Setup Script for oneHRMS
Downloads and configures required AI models
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List

import aiohttp
from rich.console import Console
from rich.panel import Panel
from rich.progress import BarColumn, Progress, SpinnerColumn, TaskProgressColumn, TextColumn

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mcp.ollama_client import OllamaClient

console = Console()
logger = logging.getLogger(__name__)


class OllamaSetup:
    """Setup and configure Ollama for oneHRMS"""

    def __init__(self, host: str = "localhost", port: int = 11434):
        self.client = OllamaClient(host, port)
        self.required_models = [
            {
                "name": "codellama:7b",
                "purpose": "code_generation",
                "description": "Code generation and analysis for Test Updater Agent",
            },
            {
                "name": "llama2:13b",
                "purpose": "documentation",
                "description": "Documentation generation and technical writing",
            },
            {
                "name": "mistral:7b",
                "purpose": "refactoring",
                "description": "Code refactoring and quality improvement",
            },
        ]

    async def check_ollama_status(self) -> bool:
        """Check if Ollama server is running"""
        try:
            return await self.client.health_check()
        except Exception as e:
            console.print(f"[red]Ollama server not accessible: {e}[/red]")
            return False

    async def list_available_models(self) -> List[str]:
        """List models available on Ollama server"""
        try:
            return await self.client.list_models()
        except Exception as e:
            console.print(f"[red]Error listing models: {e}[/red]")
            return []

    async def pull_model_with_progress(self, model_name: str, description: str) -> bool:
        """Pull a model with progress indication"""
        console.print(f"\n[cyan]Pulling model: {model_name}[/cyan]")
        console.print(f"[dim]{description}[/dim]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console,
        ) as progress:
            task = progress.add_task(f"Downloading {model_name}...", total=100)

            try:
                # Start the pull operation
                async with aiohttp.ClientSession() as session:
                    payload = {"name": model_name}
                    async with session.post(
                        f"{self.client.base_url}/api/pull", json=payload, timeout=1800  # 30 minutes timeout
                    ) as response:
                        if response.status == 200:
                            async for line in response.content:
                                if line:
                                    try:
                                        data = json.loads(line.decode())
                                        status = data.get("status", "")

                                        if "pulling" in status.lower():
                                            # Try to extract progress percentage
                                            if "%" in status:
                                                try:
                                                    percent_str = status.split("%")[0].split()[-1]
                                                    percent = float(percent_str)
                                                    progress.update(task, completed=percent)
                                                except:
                                                    pass

                                        progress.update(task, description=f"{model_name}: {status}")

                                        if data.get("status") == "success":
                                            progress.update(task, completed=100)
                                            console.print(
                                                f"[green]✓ {model_name} downloaded successfully[/green]"
                                            )
                                            return True

                                    except json.JSONDecodeError:
                                        continue

                        else:
                            console.print(
                                f"[red]✗ Failed to download {model_name}: HTTP {response.status}[/red]"
                            )
                            return False

            except Exception as e:
                console.print(f"[red]✗ Error downloading {model_name}: {e}[/red]")
                return False

        return False

    async def test_model(self, model_name: str) -> bool:
        """Test if a model is working correctly"""
        console.print(f"[yellow]Testing model: {model_name}[/yellow]")

        try:
            success = await self.client.test_model(model_name)
            if success:
                console.print(f"[green]✓ {model_name} is working correctly[/green]")
            else:
                console.print(f"[red]✗ {model_name} test failed[/red]")
            return success
        except Exception as e:
            console.print(f"[red]✗ Error testing {model_name}: {e}[/red]")
            return False

    async def setup_all_models(self) -> Dict[str, bool]:
        """Setup all required models"""
        console.print(
            Panel("Setting up Ollama models for oneHRMS AI agents", title="Ollama Setup", border_style="blue")
        )

        # Check Ollama status
        if not await self.check_ollama_status():
            console.print("[red]Ollama server is not running. Please start Ollama first.[/red]")
            return {}

        console.print("[green]✓ Ollama server is running[/green]")

        # Get currently available models
        available_models = await self.list_available_models()
        console.print(f"[dim]Currently available models: {len(available_models)}[/dim]")

        results = {}

        # Process each required model
        for model_info in self.required_models:
            model_name = model_info["name"]
            description = model_info["description"]

            if model_name in available_models:
                console.print(f"[green]✓ {model_name} already available[/green]")
                # Still test the model to ensure it works
                results[model_name] = await self.test_model(model_name)
            else:
                console.print(f"[yellow]⚠ {model_name} not found, downloading...[/yellow]")
                success = await self.pull_model_with_progress(model_name, description)
                if success:
                    results[model_name] = await self.test_model(model_name)
                else:
                    results[model_name] = False

        return results

    async def create_model_config(self, results: Dict[str, bool]):
        """Create model configuration file"""
        config = {"ollama": {"host": self.client.host, "port": self.client.port, "models": []}}

        for model_info in self.required_models:
            model_name = model_info["name"]
            if results.get(model_name, False):
                config["ollama"]["models"].append(
                    {
                        "name": model_name,
                        "purpose": model_info["purpose"],
                        "max_tokens": 4096 if "13b" in model_name else 2048,
                        "description": model_info["description"],
                    }
                )

        # Save configuration
        config_path = Path(__file__).parent.parent / "mcp" / "ollama-models.json"
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)

        console.print(f"[green]Model configuration saved to {config_path}[/green]")

    def print_setup_summary(self, results: Dict[str, bool]):
        """Print setup summary"""
        console.print("\n" + "=" * 60)
        console.print(Panel("Ollama Setup Summary", title="Setup Complete", border_style="green"))

        successful = sum(1 for success in results.values() if success)
        total = len(results)

        console.print(f"[bold]Models processed: {total}[/bold]")
        console.print(f"[green]Successful: {successful}[/green]")
        console.print(f"[red]Failed: {total - successful}[/red]")

        console.print("\n[bold]Model Status:[/bold]")
        for model_name, success in results.items():
            status = "[green]✓ Ready[/green]" if success else "[red]✗ Failed[/red]"
            purpose = next(m["purpose"] for m in self.required_models if m["name"] == model_name)
            console.print(f"  {model_name} ({purpose}): {status}")

        if successful == total:
            console.print("\n[green]🎉 All models are ready! You can now start the AI agents.[/green]")
        else:
            console.print("\n[yellow]⚠ Some models failed to setup. Check the errors above.[/yellow]")

        console.print("\n[dim]Next steps:[/dim]")
        console.print("[dim]1. Start the MCP server: docker-compose up mcp-server[/dim]")
        console.print("[dim]2. Start the agent registry: docker-compose up agent-registry[/dim]")
        console.print("[dim]3. Use the agent CLI: python scripts/agent_cli.py status[/dim]")


async def main():
    """Main setup function"""
    setup = OllamaSetup()

    try:
        results = await setup.setup_all_models()
        await setup.create_model_config(results)
        setup.print_setup_summary(results)

        return len([r for r in results.values() if r]) == len(results)

    except KeyboardInterrupt:
        console.print("\n[yellow]Setup interrupted by user[/yellow]")
        return False
    except Exception as e:
        console.print(f"\n[red]Setup failed with error: {e}[/red]")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
