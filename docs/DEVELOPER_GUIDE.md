# Developer Guide

## Prerequisites

- Node.js 18+ and npm
- Python 3.8+ and pip
- PostgreSQL 12+
- Java Runtime Environment (JRE) 17+ for API client generation
- `uv` (Rust-based Python package manager)

## Development Environment Setup

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd oneHRMS
```

### 2. Backend Setup

```bash
# Install Python dependencies
cd hrms
pip install -r requirements.txt

# Setup database
# (Add your database setup instructions here)

# Run migrations
# (Add migration commands here)
```

### 3. Frontend Setup

```bash
cd frontend/dashboard-ui
npm install
```

### 4. API Client Generation

```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install openapi-generator-cli
uvx openapi-generator-cli

# Generate API clients
npm run generate:api
```

## Running the Application

### Backend Services

```bash
# Start recruitment microservice
cd hrms/microservices/recruitment
uvicorn api:app --reload --port 8104
```

### Frontend Development Server

```bash
cd frontend/dashboard-ui
npm run dev
```

## Project Structure

```
oneHRMS/
├── hrms/
│   └── microservices/
│       └── recruitment/
│           ├── api.py              # FastAPI endpoints
│           ├── models.py           # SQLAlchemy models & Pydantic schemas
│           ├── repository.py       # Data access layer
│           ├── service.py          # Business logic
│           ├── migrations/         # Database migrations
│           └── shared/
│               ├── auth.py         # RBAC middleware
│               └── notifications.py # Notification service
├── frontend/
│   └── dashboard-ui/               # Single React application
│       ├── src/
│       │   ├── components/         # Shared React components
│       │   ├── features/           # Feature-based modules
│       │   │   └── recruitment/
│       │   │       ├── components/ # Feature-specific components
│       │   │       ├── hooks/      # Custom React hooks
│       │   │       └── services/   # API services
│       │   ├── contexts/           # React contexts
│       │   ├── utils/              # Utility functions
│       │   └── generated/          # Auto-generated API clients
│       │   │       ├── types/       # TypeScript types
│       │   │       └── router/      # Routing
│       │   ├── generated/          # Generated API clients
│       │   └── contexts/           # React contexts
│       └── scripts/
│           └── generate-api-clients.cjs
└── docs/
    ├── DEVELOPER_GUIDE.md
    └── CHANGELOG.md
```

## API Client Generation

The project uses OpenAPI specifications to generate TypeScript API clients:

1. **Setup**: Install `uv` and `openapi-generator-cli`
2. **Configuration**: Update `scripts/generate-api-clients.cjs` for your API specs
3. **Generation**: Run `npm run generate:api`

## Troubleshooting

### Common Issues

1. **Java Runtime Error**: Install OpenJDK 17
   ```bash
   brew install openjdk@17
   export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
   ```

2. **Material-UI Grid v2 Migration**: Replace Grid components with Box + CSS Grid
   ```tsx
   // Old
   <Grid item xs={12} md={6}>

   // New
   <Box sx={{ gridColumn: 'span 12', '@media (min-width: 900px)': { gridColumn: 'span 6' } }}>
   ```

3. **TypeScript Errors**: Use type assertions for complex form data
   ```tsx
   const formData = getValues() as JobRequisitionCreate;
   ```

4. **Environment Variables**: Use `import.meta.env` in Vite
   ```tsx
   const apiUrl = import.meta.env.VITE_API_BASE_URL;
   ```

## Recent Feature Implementations

### Job Requisition System (Completed ✅)

- **Backend**: Complete CRUD operations with RBAC
- **Frontend**: Form, list, and workflow management
- **Features**: Approval workflow, status tracking, notifications

### Job Listings System (Completed ✅)

- **Components**: JobListingsList, JobListingsForm, JobApplicationForm
- **Features**: Rich job descriptions, salary transparency, application tracking
- **Integration**: Seamlessly integrated into Recruitment tab

### Next Phase: Advanced Recruitment Features

#### Phase 1: Candidate Management & Application Processing
- **Candidate Profiles**: Comprehensive candidate database
- **Application Tracking**: Status management and progress tracking
- **Resume Parsing**: Automated data extraction from resumes
- **Skill Matching**: AI-powered candidate-job matching

#### Phase 2: Interview Management
- **Interview Scheduling**: Calendar integration and automated scheduling
- **Interview Feedback**: Structured feedback forms and scoring
- **Video Interviews**: Integration with video conferencing platforms
- **Interview Templates**: Standardized interview processes

#### Phase 3: Advanced Analytics & Reporting
- **Hiring Metrics**: Time-to-hire, cost-per-hire, source effectiveness
- **Pipeline Analytics**: Conversion rates at each stage
- **Diversity Reporting**: EEO compliance and diversity metrics
- **Predictive Analytics**: Hiring success predictions

#### Phase 4: Integration & Automation
- **ATS Integration**: Connect with external applicant tracking systems
- **Background Checks**: Automated background verification
- **Onboarding Integration**: Seamless transition to onboarding
- **Email Automation**: Automated communication workflows

## Development Best Practices

1. **TDD Approach**: Write tests before implementing features
2. **Type Safety**: Use TypeScript for all new code
3. **Component Architecture**: Follow React best practices
4. **API Design**: Use OpenAPI specifications
5. **Database Migrations**: Always use migrations for schema changes
6. **Error Handling**: Implement comprehensive error handling
7. **Performance**: Monitor and optimize performance
8. **Security**: Follow security best practices

## Testing Strategy

### Unit Testing
- Component testing with React Testing Library
- Hook testing with custom test utilities
- API testing with pytest

### Integration Testing
- End-to-end workflows
- API integration tests
- Database integration tests

### Performance Testing
- Load testing for API endpoints
- Frontend performance monitoring
- Database query optimization

## Deployment

### Backend Deployment
- Docker containerization
- Environment-specific configurations
- Database migration strategies

### Frontend Deployment
- Build optimization
- CDN integration
- Environment variable management

## Monitoring & Logging

- Application performance monitoring
- Error tracking and alerting
- User analytics and behavior tracking
- Database performance monitoring

## Security Considerations

- Role-based access control (RBAC)
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Secure authentication and authorization
