# oneHRMS Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying the oneHRMS system in various environments, from development to production.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+), macOS, or Windows
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: Version 2.30+
- **Node.js**: Version 18+
- **Python**: Version 3.11+
- **PostgreSQL**: Version 14+

### Hardware Requirements
- **Development**: 4GB RAM, 2 CPU cores, 20GB storage
- **Production**: 8GB RAM, 4 CPU cores, 100GB storage
- **High Availability**: 16GB RAM, 8 CPU cores, 500GB storage

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
│   Port: 3002    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   Keycloak     │◄─────────────┘
                        │   (Auth)       │
                        │   Port: 8080   │
                        └─────────────────┘
```

## 🔧 Development Deployment

### Quick Start with Docker

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-org/onehrms.git
   cd onehrms
   ```

2. **Start All Services**
   ```bash
   docker-compose up -d
   ```

3. **Initialize Database**
   ```bash
   docker-compose exec backend alembic upgrade head
   ```

4. **Access the Application**
   - Frontend: http://localhost:3002
   - Backend API: http://localhost:8000
   - Keycloak: http://localhost:8080
   - Database: localhost:5432

### Manual Development Setup

#### Frontend Setup
```bash
# Navigate to frontend directory
cd frontend/dashboard-ui

# Install dependencies
npm install

# Create environment file
cp .env.example .env.local

# Start development server
npm run dev
```

#### Backend Setup
```bash
# Navigate to backend directory
cd hrms

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="*************************************"
export SECRET_KEY="your-secret-key"
export KEYCLOAK_URL="http://localhost:8080"

# Run migrations
alembic upgrade head

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Database Setup
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb hrms

# Create user
sudo -u postgres psql
CREATE USER hrms_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE hrms TO hrms_user;
\q
```

## 🚀 Production Deployment

### Docker Production Deployment

1. **Create Production Environment File**
   ```bash
   cp docker-compose.yml docker-compose.prod.yml
   ```

2. **Configure Environment Variables**
   ```bash
   # Create .env.production file
   cat > .env.production << EOF
   NODE_ENV=production
   DATABASE_URL=******************************
   SECRET_KEY=your-production-secret-key
   KEYCLOAK_URL=https://auth.yourdomain.com
   VITE_API_BASE_URL=https://api.yourdomain.com
   VITE_AUTH_URL=https://auth.yourdomain.com
   EOF
   ```

3. **Build and Deploy**
   ```bash
   # Build production images
   docker-compose -f docker-compose.prod.yml build

   # Deploy services
   docker-compose -f docker-compose.prod.yml up -d

   # Run migrations
   docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
   ```

### Kubernetes Deployment

1. **Create Namespace**
   ```yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: onehrms
   ```

2. **Deploy PostgreSQL**
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: postgres
     namespace: onehrms
   spec:
     replicas: 1
     selector:
       matchLabels:
         app: postgres
     template:
       metadata:
         labels:
           app: postgres
       spec:
         containers:
         - name: postgres
           image: postgres:14
           env:
           - name: POSTGRES_DB
             value: hrms
           - name: POSTGRES_USER
             value: hrms_user
           - name: POSTGRES_PASSWORD
             valueFrom:
               secretKeyRef:
                 name: postgres-secret
                 key: password
           ports:
           - containerPort: 5432
   ```

3. **Deploy Backend**
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: backend
     namespace: onehrms
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: backend
     template:
       metadata:
         labels:
           app: backend
       spec:
         containers:
         - name: backend
           image: onehrms/backend:latest
           env:
           - name: DATABASE_URL
             valueFrom:
               secretKeyRef:
                 name: db-secret
                 key: url
           ports:
           - containerPort: 8000
   ```

4. **Deploy Frontend**
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: frontend
     namespace: onehrms
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: frontend
     template:
       metadata:
         labels:
           app: frontend
       spec:
         containers:
         - name: frontend
           image: onehrms/frontend:latest
           ports:
           - containerPort: 3002
   ```

### Cloud Platform Deployment

#### AWS Deployment

1. **Create ECS Cluster**
   ```bash
   aws ecs create-cluster --cluster-name onehrms
   ```

2. **Create ECR Repositories**
   ```bash
   aws ecr create-repository --repository-name onehrms-frontend
   aws ecr create-repository --repository-name onehrms-backend
   ```

3. **Deploy with CloudFormation**
   ```bash
   aws cloudformation create-stack \
     --stack-name onehrms \
     --template-body file://aws/onehrms.yml \
     --parameters ParameterKey=Environment,ParameterValue=production
   ```

#### Google Cloud Deployment

1. **Create GKE Cluster**
   ```bash
   gcloud container clusters create onehrms \
     --zone us-central1-a \
     --num-nodes 3
   ```

2. **Deploy Application**
   ```bash
   kubectl apply -f k8s/
   ```

#### Azure Deployment

1. **Create AKS Cluster**
   ```bash
   az aks create \
     --resource-group onehrms-rg \
     --name onehrms-cluster \
     --node-count 3
   ```

2. **Deploy Application**
   ```bash
   kubectl apply -f k8s/
   ```

## 🔐 Security Configuration

### SSL/TLS Configuration

1. **Generate SSL Certificate**
   ```bash
   # Using Let's Encrypt
   certbot certonly --standalone -d yourdomain.com

   # Or using self-signed certificate
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout private.key -out certificate.crt
   ```

2. **Configure Nginx**
   ```nginx
   server {
       listen 443 ssl;
       server_name yourdomain.com;

       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;

       location / {
           proxy_pass http://localhost:3002;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }

       location /api {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Keycloak Configuration

1. **Create Realm**
   ```bash
   # Access Keycloak admin console
   # Create new realm: onehrms
   ```

2. **Configure Client**
   ```json
   {
     "clientId": "onehrms-frontend",
     "publicClient": true,
     "redirectUris": ["https://yourdomain.com/*"],
     "webOrigins": ["https://yourdomain.com"]
   }
   ```

3. **Create Users and Roles**
   ```bash
   # Create roles: employee, manager, admin
   # Create users and assign roles
   ```

## 📊 Monitoring and Logging

### Application Monitoring

1. **Prometheus Configuration**
   ```yaml
   global:
     scrape_interval: 15s

   scrape_configs:
     - job_name: 'onehrms-backend'
       static_configs:
         - targets: ['localhost:8000']

     - job_name: 'onehrms-frontend'
       static_configs:
         - targets: ['localhost:3002']
   ```

2. **Grafana Dashboard**
   ```json
   {
     "dashboard": {
       "title": "oneHRMS Metrics",
       "panels": [
         {
           "title": "Request Rate",
           "type": "graph",
           "targets": [
             {
               "expr": "rate(http_requests_total[5m])"
             }
           ]
         }
       ]
     }
   }
   ```

### Logging Configuration

1. **ELK Stack Setup**
   ```yaml
   # docker-compose.logging.yml
   version: '3.8'
   services:
     elasticsearch:
       image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
       environment:
         - discovery.type=single-node
       ports:
         - "9200:9200"

     logstash:
       image: docker.elastic.co/logstash/logstash:7.17.0
       volumes:
         - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
       ports:
         - "5044:5044"

     kibana:
       image: docker.elastic.co/kibana/kibana:7.17.0
       ports:
         - "5601:5601"
   ```

2. **Application Logging**
   ```python
   import logging
   from logging.handlers import RotatingFileHandler

   # Configure logging
   logging.basicConfig(
       level=logging.INFO,
       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
       handlers=[
           RotatingFileHandler('app.log', maxBytes=10000000, backupCount=5),
           logging.StreamHandler()
       ]
   )
   ```

## 🔄 Backup and Recovery

### Database Backup

1. **Automated Backup Script**
   ```bash
   #!/bin/bash
   BACKUP_DIR="/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   DB_NAME="hrms"

   # Create backup
   pg_dump $DB_NAME > $BACKUP_DIR/hrms_$DATE.sql

   # Compress backup
   gzip $BACKUP_DIR/hrms_$DATE.sql

   # Clean old backups (keep last 30 days)
   find $BACKUP_DIR -name "hrms_*.sql.gz" -mtime +30 -delete
   ```

2. **Scheduled Backup**
   ```bash
   # Add to crontab
   0 2 * * * /path/to/backup_script.sh
   ```

### File Storage Backup

1. **S3 Backup Script**
   ```python
   import boto3
   import os
   from datetime import datetime

   s3 = boto3.client('s3')
   bucket_name = 'onehrms-backups'

   def backup_files():
       backup_date = datetime.now().strftime('%Y%m%d_%H%M%S')

       for root, dirs, files in os.walk('/uploads'):
           for file in files:
               file_path = os.path.join(root, file)
               s3_key = f'files/{backup_date}/{file}'

               s3.upload_file(file_path, bucket_name, s3_key)
   ```

## 🚨 Disaster Recovery

### Recovery Procedures

1. **Database Recovery**
   ```bash
   # Stop application
   docker-compose down

   # Restore database
   psql hrms < backup/hrms_20240115_120000.sql

   # Start application
   docker-compose up -d
   ```

2. **Application Recovery**
   ```bash
   # Restore from backup
   tar -xzf backup/app_20240115_120000.tar.gz -C /

   # Update configuration
   cp backup/config.yml /app/config/

   # Restart services
   systemctl restart onehrms
   ```

### High Availability Setup

1. **Load Balancer Configuration**
   ```nginx
   upstream backend {
       server backend1:8000;
       server backend2:8000;
       server backend3:8000;
   }

   upstream frontend {
       server frontend1:3002;
       server frontend2:3002;
       server frontend3:3002;
   }
   ```

2. **Database Replication**
   ```sql
   -- Primary database
   ALTER SYSTEM SET wal_level = replica;
   ALTER SYSTEM SET max_wal_senders = 3;
   ALTER SYSTEM SET max_replication_slots = 3;

   -- Replica database
   pg_basebackup -h primary_host -D /var/lib/postgresql/data -U replicator
   ```

## 📈 Performance Optimization

### Frontend Optimization

1. **Build Optimization**
   ```bash
   # Production build
   npm run build

   # Analyze bundle
   npm run analyze
   ```

2. **CDN Configuration**
   ```html
   <!-- Use CDN for static assets -->
   <script src="https://cdn.jsdelivr.net/npm/react@18/umd/react.production.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.production.min.js"></script>
   ```

### Backend Optimization

1. **Database Optimization**
   ```sql
   -- Create indexes
   CREATE INDEX idx_employees_department ON employees(department_id);
   CREATE INDEX idx_attendance_date ON attendance(date);
   CREATE INDEX idx_leaves_employee ON leaves(employee_id);

   -- Optimize queries
   ANALYZE employees;
   ANALYZE attendance;
   ANALYZE leaves;
   ```

2. **Caching Configuration**
   ```python
   from fastapi_cache import FastAPICache
   from fastapi_cache.backends.redis import RedisBackend

   # Configure Redis cache
   FastAPICache.init(
       RedisBackend(redis),
       prefix="onehrms-cache"
   )
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose ps postgres

   # Check logs
   docker-compose logs postgres

   # Test connection
   psql -h localhost -U hrms_user -d hrms
   ```

2. **Application Startup Issues**
   ```bash
   # Check application logs
   docker-compose logs backend
   docker-compose logs frontend

   # Check environment variables
   docker-compose exec backend env

   # Test API endpoints
   curl http://localhost:8000/health
   ```

3. **Authentication Issues**
   ```bash
   # Check Keycloak status
   docker-compose logs keycloak

   # Verify client configuration
   curl http://localhost:8080/auth/realms/onehrms/.well-known/openid_configuration
   ```

### Performance Issues

1. **Memory Usage**
   ```bash
   # Check memory usage
   docker stats

   # Monitor application memory
   top -p $(pgrep -f "uvicorn\|node")
   ```

2. **Database Performance**
   ```sql
   -- Check slow queries
   SELECT query, mean_time, calls
   FROM pg_stat_statements
   ORDER BY mean_time DESC
   LIMIT 10;
   ```

## 📚 Additional Resources

### Documentation
- [API Documentation](./API_REFERENCE.md)
- [Developer Guide](./DEVELOPER_GUIDE.md)
- [Features Overview](./FEATURES_OVERVIEW.md)

### External Resources
- [Docker Documentation](https://docs.docker.com/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### Support
- [GitHub Issues](https://github.com/your-org/onehrms/issues)
- [Documentation](https://docs.onehrms.com)
- [Community Forum](https://community.onehrms.com)

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Deployment Team**: oneHRMS Development Team
