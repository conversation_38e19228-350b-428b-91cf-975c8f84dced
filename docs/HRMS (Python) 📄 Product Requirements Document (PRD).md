# **📄 Product Requirements Document (PRD)**

## **Project Title:**

Frappe HRMS Modular Refactor – Multi‑Tenant SaaS (Phased, TDD, Agent‑Assisted)

## **1\. Project Overview**

Fork Frappe HRMS and refactor it to fit your multi‑tenant SaaS architecture. Leverage existing Keycloak and Kong instances by generating their integration files (OIDC clients, Kong routes). Develop modules as microservices with TDD, continuously verifying behavior preservation. Use Ollama‑driven local agents and MCPs for automated code, test, and documentation generation.

## **2\. Objectives & Success Metrics**

* ✅ Provide configuration directly consumable by your Keycloak and Kong deployments (not building them).

* ✅ Decouple modules into independently deployable microservices.

* ✅ Enforce shared‑schema multi-tenancy with `tenant_id`.

* ✅ Maintain full business logic through TDD with continuous integration testing.

* ✅ Integrate Python automatic refactoring tools analogous to PHP Rector.

## **3\. Phased Roadmap**

### **Phase 0 – Initialization**

* Fork Frappe HRMS & run baseline tests.

* Bootstrap test harness (pytest \+ CI pipeline).

### **Phase 1 – TDD & Refactor Setup**

* Define test specs for payroll/recruitment/attendance.

* Enforce TDD methodology: write tests first, ensure failure, refactor minimally to pass.

* Integrate Python refactoring tools:

  * **Teddy** for idiomatic Python conversion [YouTube+8Josef Jebavý's blog+8Level Up Coding+8](https://blog.josefjebavy.cz/en/programming/php-upgrade-rector?utm_source=chatgpt.com)[arXiv+1Medium+1](https://arxiv.org/abs/2207.05613?utm_source=chatgpt.com)[Stack Overflow](https://stackoverflow.com/questions/4057791/php-refactoring-tool?utm_source=chatgpt.com)[arXiv](https://arxiv.org/abs/2009.03302?utm_source=chatgpt.com).

  * **Revizor** for automated frequent code pattern transformations [arXiv](https://arxiv.org/abs/2108.11199?utm_source=chatgpt.com).

  * **PyExamine** for code-smell detection [arXiv](https://arxiv.org/abs/2501.18327?utm_source=chatgpt.com).

* Configure linting (`flake8`, `pylint`), formatting (`black`), static analysis.

### **Phase 2 – Module Extraction**

For each module (Payroll, Recruitment, Attendance, ESS):

1. Write a test suite covering existing behavior.

2. Extract into a standalone service (FastAPI or Flask).

3. Implement OIDC-JWT security (placeholders for Keycloak).

4. Persist/refactor DB models to use `tenant_id`.

5. Auto-generate Kong route definitions (YAML/JSON).

6. Run TDD cycle ensuring no functionality regression.

### **Phase 3 – Shared Microservices Architecture**

* Containerize modules.

* Generate MCP manifest for deployment orchestration.

* Build local Ollama agents:

  * **“Test Updater”**: re-generates missing tests upon refactoring.

  * **“Docgen”**: generates API docs from code.

  * **“Refactor Assistant”**: applies repetitive Teddy/Revizor/PyExamine fixes.

### **Phase 4 – UI Decoupling**

* Write TDD-driven UI spec for selected components.

* Scaffold new UI modules in Next.js/Flutter that consume microservices.

* Generate Kong route definitions for UI endpoints.

### **Phase 5 – Multi‑Tenancy Enforcement**

* Add `tenant_id` injection in APIs and DB.

* Tests for tenant isolation (cross-tenant access blocked).

* Automate tenant provisioning scripts.

## **4\. Developer Tasks**

### **All Phases**

* Adopt strict **TDD cycle**:
   `write failing test → implement minimal code → refactor using tool → commit & verify tests pass`.

* Add static type hints (`mypy`) and maintain 100% test coverage in refactored code.

### **Phase 1**

* Configure `pytest`, `flake8`, `black`, `mypy`.

* Integrate Teddy, Revizor, PyExamine in pre-commit and CI.

* Write baseline functional tests for each existing Frappe module.

### **Phase 2**

* For each module:

  * Implement tests for critical behavior.

  * Extract module into REST service with defined OpenAPI spec.

  * Add test fixtures for multi-tenant use.

  * Write Kong route file (e.g., `kong/routes/payroll.yml`).

### **Phase 3**

* Containerize services

* Create MCP manifests

* Build and integrate Ollama agents scripts.

### **Phase 5**

* Enforce `tenant_id` in DB models

* Write tests to ensure no cross-tenant data access

* Provide tenant onboarding CLI/script

## **5\. Integration Files & CI Workflows**

* **Keycloak configs**: JSON files defining OIDC clients per service.

* **Kong configs**: YAML route definitions placed under `kong/routes/`.

* **TDD & CI**:

  * Run test suites per microservice

  * Static analysis, type checking, formatting, test coverage check

  * Teddy/Revizor/PyExamine automated refactor step before merge

  * MCP orchestrates build, test, and agent workflows

## **6\. Technology Stack**

* **Refactor Tools**: Teddy, Revizor, PyExamine [Stack Overflow](https://stackoverflow.com/questions/50569943/use-laravel-to-refactor-old-large-php-application-partially-over-time?utm_source=chatgpt.com)[Stack Overflow+3Medium+3overcast blog+3](https://medium.com/%40skowron.dev/elevate-your-code-mastering-refactoring-in-symfony-with-rectorphp-a5baa7f0fbc?utm_source=chatgpt.com)[LogRocket Blog+2arXiv+2arXiv+2](https://arxiv.org/abs/2501.18327?utm_source=chatgpt.com)[arXiv+1arXiv+1](https://arxiv.org/abs/2108.11199?utm_source=chatgpt.com)[overcast blog+1Workik+1](https://overcast.blog/15-ai-code-refactoring-tools-you-should-know-50cf38d26877?utm_source=chatgpt.com)

* **Backend**: Python (FastAPI/Flask), PostgreSQL/MariaDB

* **Testing**: pytest, pytest-cov

* **Lint/Format/Typing**: flake8, black, mypy

* **Agentic Dev**: MCP Server/Client, Ollama

* **CI/CD**: GitHub Actions / GitLab CI

## **7\. Success Metrics**

* Each refactored service passes 100% of its own test suite with TDD coverage.

* No regression test failures on comprehensive integration tests.

* Configuration files generated for Keycloak and Kong for all services.

* Agent tools detect and auto-apply ≥80% of code-smell corrections.

* Confirmed tenant isolation via tests and audits.

## **8\. Risk & Mitigation**

* **Legacy behavior drift**: aggressive TDD ensures functional consistency.

* **Refactor tool mismatch**: manual validation on autogenerated changes.

* **Agent noise**: human approval gate on all agent-driven PRs.

**Conclusion:** This PRD encodes a strategic, TDD-driven, phased refactor of Frappe HR into a modular, multi‑tenant SaaS platform. It integrates Python code-refactor tools to maintain code quality, automates auth/gateway configs for existing infrastructure, and continues functional testing to avoid regression. Let me know if you’d like full agent script samples, MCP manifests, or a Gantt timeline\!

## **Developer Tasks & Subtasks for Adapting Frappe HRMS**

### **1\. Initial Fork & Baseline**

* **1.1** Fork and clone the latest Frappe HRMS repository.

* **1.2** Dockerize the baseline stack for reproducibility.

* **1.3** Run the default test suite; document baseline functional coverage and any failing tests.

* **1.4** Set up GitHub/GitLab repo with branch protection, pre-commit hooks, and CI integration (linting, formatting).

### **2\. Test-Driven Development (TDD) & Refactor Toolchain Setup**

* **2.1** Configure Python test harness (pytest \+ pytest-cov).

* **2.2** Integrate code linting (`flake8`), formatting (`black`), and type checking (`mypy`).

* **2.3** Integrate Python refactoring tools (Teddy, Revizor, PyExamine) into CI (pre-commit and pre-merge).

* **2.4** Write or extend baseline functional tests for each critical HRMS module (payroll, recruitment, attendance, ESS, employee management).

* **2.5** Establish TDD workflow:

  * Always write/extend failing test before refactoring any code.

  * Document rationale for each test or refactor.

### **3\. Keycloak & Kong Integration Config Preparation**

* **3.1** Analyze Frappe’s current authentication flows and decouple them from internal user/password logic.

* **3.2** Add OIDC/JWT support to services, with configuration stubs for Keycloak integration.

* **3.3** For each microservice/module:

  * **3.3.1** Generate and maintain Keycloak client configuration file (JSON/YAML).

  * **3.3.2** Generate Kong route/service definition files (YAML/JSON).

* **3.4** Add test coverage for OIDC token validation (using mock tokens in development).

### **4\. Microservices Decomposition (per module)**

**For each major module (repeat for Payroll, Attendance, Recruitment, ESS, Employee Mgmt, etc):**

#### **4.1 Pre-Extraction**

* **4.1.1** Write exhaustive test suite for existing module features (cover all business rules, edge cases, and expected failures).

* **4.1.2** Analyze inter-module dependencies and shared models.

#### **4.2 Extraction/Isolation**

* **4.2.1** Create new service project (FastAPI/Flask recommended for Python microservices).

* **4.2.2** Move module’s business logic into service.

* **4.2.3** Refactor data models to include `tenant_id`.

* **4.2.4** Ensure all endpoints are protected with OIDC token validation middleware (use stubs for dev/test).

* **4.2.5** Define and maintain an OpenAPI/Swagger spec for each service.

#### **4.3 Integration/Testing**

* **4.3.1** Containerize the service (Dockerfile, docker-compose).

* **4.3.2** Register API endpoints in Kong route definition file.

* **4.3.3** Register service with MCP manifest for orchestration (if using).

* **4.3.4** Validate all tests pass (unit, integration, end-to-end).

* **4.3.5** Confirm no functional business logic regression via CI.

### **5\. Multi-Tenancy Enforcement**

* **5.1** Refactor all relevant database models to include a `tenant_id` field (with appropriate indices).

* **5.2** Implement tenant-aware query layer (ORM middleware or decorators to scope by tenant\_id).

* **5.3** Write and run tests to ensure:

  * **5.3.1** No cross-tenant data leakage.

  * **5.3.2** Tenant isolation in API requests.

* **5.4** Build automated scripts for tenant provisioning, onboarding, and offboarding.

### **6\. Frontend/UI Decoupling (as needed)**

* **6.1** Audit current Frappe HRMS UI and identify dependencies on backend logic.

* **6.2** Build UI modules in Next.js/Flutter for each refactored microservice:

  * **6.2.1** Implement login/logout using OIDC (Keycloak).

  * **6.2.2** Call APIs exclusively through Kong.

  * **6.2.3** Enforce RBAC and tenant-scoped data in frontend.

* **6.3** Write frontend tests (Jest, Cypress, or Flutter’s test framework).

* **6.4** Generate Kong route definitions for all frontend endpoints.

### **7\. Agentic/MCP Automation**

* **7.1** Deploy MCP Server and MCP Client(s).

* **7.2** Build/Configure Ollama-based local agents:

  * **7.2.1** Test Writer Agent – auto-generates/updates tests during refactor.

  * **7.2.2** Refactor Assistant Agent – suggests and auto-applies code improvements.

  * **7.2.3** Docgen Agent – auto-generates and updates OpenAPI and markdown documentation.

* **7.3** Integrate agent activity into CI:

  * **7.3.1** Agent-generated diffs require human approval before merge.

  * **7.3.2** Ensure agent logs are retained for audit.

### **8\. Continuous Integration & TDD**

* **8.1** Ensure every microservice and UI module is covered by automated CI pipelines:

  * **8.1.1** Linting, formatting, and type checking.

  * **8.1.2** 100% test pass requirement before merge.

  * **8.1.3** Agentic checks and suggested improvements as status checks.

* **8.2** Run periodic integration and regression tests across all services to detect accidental breakages.

### **9\. Documentation & Handover**

* **9.1** Maintain living documentation:

  * **9.1.1** Developer guides for each service.

  * **9.1.2** API reference (OpenAPI docs).

  * **9.1.3** Deployment/runbook for MCP and agent orchestration.

* **9.2** Prepare and deliver comprehensive release notes at each milestone.

### **10\. Final Validation & Success Criteria**

* **10.1** Run full regression suite; no test failures allowed.

* **10.2** Validate business process flows (manual UAT).

* **10.3** Confirm that tenant isolation, auth, and API routing work end-to-end using generated config files.

## **Summary Table: Major Tasks & Key Subtasks**

| Major Task | Key Subtasks |
| ----- | ----- |
| 1\. Fork & Baseline | Clone, Dockerize, Baseline Test, Setup CI |
| 2\. TDD & Toolchain | Pytest, Flake8, Black, Mypy, Teddy, Revizor, PyExamine, Write Baseline Tests |
| 3\. Keycloak/Kong Configs | Remove legacy auth, OIDC support, Config file gen, Mock test coverage |
| 4\. Module Extraction | Write exhaustive tests, Isolate logic, Add tenant\_id, Dockerize, OpenAPI Spec |
| 5\. Multi-Tenancy | DB model refactor, Tenant middleware, Isolation tests, Provisioning scripts |
| 6\. UI Decoupling | Audit, Build UI modules, Integrate OIDC, Route via Kong, Test UI |
| 7\. Agentic Automation | Deploy MCP, Integrate agents, Auto-generate tests/docs, Audit logs |
| 8\. CI & Continuous Test | Enforce test/lint/agent checks, 100% coverage, Regression/integration tests |
| 9\. Documentation | Developer/API docs, Runbooks, Release notes |
| 10\. Final Validation | Regression, UAT, End-to-end validation, Config confirmation |

**Direct Advice:**
 Do not cut corners. Enforce TDD and automation at every step. Document all config changes for Keycloak/Kong so prod integration is seamless. Use Python refactor tools (Teddy, Revizor, PyExamine) aggressively—but always verify their output with tests. Require MCP/Agent activity logs for auditability.

# **Role-Based Agent Scripts**

## **1\. Test Writer Agent**

**Role:** Ensures test-first workflow, generates or updates tests when business logic or API endpoints change, flags code without test coverage.

python
CopyEdit
`# agent_test_writer.py`
`import os`
`from pathlib import Path`
`from ollama import Ollama`

`def get_untested_files():`
    `# Find all Python files lacking corresponding test files`
    `src_dir = Path('services/')`
    `untested = []`
    `for pyfile in src_dir.rglob('*.py'):`
        `testfile = pyfile.parent / f'test_{pyfile.name}'`
        `if not testfile.exists():`
            `untested.append(pyfile)`
    `return untested`

`def generate_test_code(pyfile):`
    `with open(pyfile) as f:`
        `code = f.read()`
    `prompt = f"Generate Pytest unit tests for the following code, preserving business logic:\n{code}\n"`
    `return Ollama.generate(model='phi3', prompt=prompt)`

`def main():`
    `files = get_untested_files()`
    `for file in files:`
        `test_code = generate_test_code(file)`
        `testfile = file.parent / f'test_{file.name}'`
        `with open(testfile, 'w') as f:`
            `f.write(test_code)`
        `print(f"Test written for {file}")`

`if __name__ == "__main__":`
    `main()`

## **2\. Refactor Assistant Agent**

**Role:** Uses AI and Python tools (Teddy/Revizor) to automate safe refactoring, code cleanups, and modernizations.

python
CopyEdit
`# agent_refactor_assistant.py`
`import subprocess`
`from ollama import Ollama`

`def run_teddy(file):`
    `subprocess.run(["teddy", "--fix", str(file)])`

`def ollama_refactor(file):`
    `with open(file) as f:`
        `code = f.read()`
    `prompt = f"Refactor and modernize this Python code (PEP8, idiomatic, clean):\n{code}\n"`
    `refactored = Ollama.generate(model='phi3', prompt=prompt)`
    `with open(file, 'w') as f:`
        `f.write(refactored)`

`def main():`
    `for pyfile in Path('services/').rglob('*.py'):`
        `run_teddy(pyfile)     # Run static tool`
        `ollama_refactor(pyfile) # AI review & suggestion`
        `print(f"Refactored {pyfile}")`

`if __name__ == "__main__":`
    `main()`

## **3\. DocGen Agent**

**Role:** Keeps documentation up-to-date, generates OpenAPI specs and Markdown docs from code and test changes.

python
CopyEdit
`# agent_docgen.py`
`from ollama import Ollama`

`def doc_from_code(pyfile):`
    `with open(pyfile) as f:`
        `code = f.read()`
    `prompt = f"Generate Markdown API documentation and OpenAPI YAML for the following FastAPI service:\n{code}\n"`
    `return Ollama.generate(model='phi3', prompt=prompt)`

`def main():`
    `for pyfile in Path('services/').rglob('*.py'):`
        `docs = doc_from_code(pyfile)`
        `docfile = pyfile.parent / f"{pyfile.stem}_API_DOC.md"`
        `with open(docfile, 'w') as f:`
            `f.write(docs)`
        `print(f"Docs generated for {pyfile}")`

`if __name__ == "__main__":`
    `main()`

## **4\. Kong/Keycloak Config Agent**

**Role:** Generates Kong route and Keycloak client configuration files for each microservice, based on service code and OpenAPI spec.

python
CopyEdit
`# agent_config_generator.py`
`from ollama import Ollama`

`def kong_route_from_openapi(openapi_yaml):`
    `prompt = f"From this OpenAPI spec, generate Kong declarative config for routing and securing the service with JWT auth (OIDC):\n{openapi_yaml}\n"`
    `return Ollama.generate(model='phi3', prompt=prompt)`

`def keycloak_client_from_openapi(openapi_yaml):`
    `prompt = f"From this OpenAPI spec, generate a Keycloak client JSON config for OIDC integration:\n{openapi_yaml}\n"`
    `return Ollama.generate(model='phi3', prompt=prompt)`

`def main():`
    `for openapifile in Path('services/').rglob('openapi.yaml'):`
        `with open(openapifile) as f:`
            `yaml = f.read()`
        `kong_conf = kong_route_from_openapi(yaml)`
        `keycloak_conf = keycloak_client_from_openapi(yaml)`
        `with open(openapifile.parent / 'kong_route.yml', 'w') as f:`
            `f.write(kong_conf)`
        `with open(openapifile.parent / 'keycloak_client.json', 'w') as f:`
            `f.write(keycloak_conf)`
        `print(f"Generated Kong/Keycloak config for {openapifile}")`

`if __name__ == "__main__":`
    `main()`

## **5\. Tenant Isolation Checker Agent**

**Role:** Ensures no cross-tenant data access. Runs queries and API calls, validates only correct tenant data is returned.

python
CopyEdit
`# agent_tenant_checker.py`
`import requests`

`def check_tenant_isolation(api_url, tenant1_token, tenant2_token):`
    `resp1 = requests.get(api_url, headers={"Authorization": f"Bearer {tenant1_token}"})`
    `resp2 = requests.get(api_url, headers={"Authorization": f"Bearer {tenant2_token}"})`
    `assert not any(item in resp1.json() for item in resp2.json()), "Tenant data leak detected!"`

`def main():`
    `# Example: test payroll endpoint isolation`
    `check_tenant_isolation("http://localhost:8000/api/payroll", "tenant1-jwt", "tenant2-jwt")`
    `print("Tenant isolation checks passed.")`

`if __name__ == "__main__":`
    `main()`

## **Agent Role Table**

| Agent | Role | Trigger |
| ----- | ----- | ----- |
| Test Writer Agent | Generate/update tests (Pytest) | On code or API change |
| Refactor Assistant Agent | Apply AI and static tool refactoring | Pre-commit/merge |
| DocGen Agent | Maintain API docs and OpenAPI specs | On code or test change |
| Config Generator Agent | Write Kong/Keycloak configs from OpenAPI | On service/API deployment |
| Tenant Isolation Agent | Test for cross-tenant data leaks | Post-deploy, scheduled |

### **How to Orchestrate**

* **MCP server** triggers agents based on repo/webhook/events.

* **Local dev**: Agents can be run as pre-commit hooks, CLI tools, or IDE extensions.

* **All agent logs and diffs** should require human approval before mainline merges.

**Direct Advice:**
 Set up these agents as your “CI teammates.”
 **Do not merge code unless all agents pass and logs show no data leak or business logic regression.**
 Regularly audit agent outputs; treat agents as code reviewers, not as infallible—always have human sign-off.

## **YAML agent manifest** that declares all your role-based agents, including metadata, triggers, and explicit support for **local Ollama** (model selection, resource limits, etc.). This manifest is designed for integration with an **MCP Server/Orchestrator** and assumes all agents run locally, using the Ollama API for LLM-powered operations.

yaml
CopyEdit
`# agents-manifest.yaml`

`version: 1.0`

`agents:`
  `- name: test-writer-agent`
    `description: >`
      `Generates and updates Pytest test files for all business logic and API endpoints. Ensures test-first discipline and detects code without sufficient coverage.`
    `language: python`
    `entrypoint: agent_test_writer.py`
    `triggers:`
      `- code-change`
      `- api-change`
      `- pre-commit`
    `ollama:`
      `enabled: true`
      `model: phi3`
      `endpoint: http://localhost:11434/api/generate`
      `max_context: 32000`
    `env:`
      `TEST_DIR: tests/`
    `resources:`
      `cpu: 1`
      `memory: 2Gi`
    `approvals_required: 1`

  `- name: refactor-assistant-agent`
    `description: >`
      `Automates safe code refactoring using Python static tools and Ollama LLM review. Modernizes and cleans code, flagging potential anti-patterns.`
    `language: python`
    `entrypoint: agent_refactor_assistant.py`
    `triggers:`
      `- pre-commit`
      `- code-push`
      `- scheduled`
    `ollama:`
      `enabled: true`
      `model: phi3`
      `endpoint: http://localhost:11434/api/generate`
      `max_context: 32000`
    `env:`
      `REFACTOR_TOOL: teddy`
    `resources:`
      `cpu: 1`
      `memory: 2Gi`
    `approvals_required: 1`

  `- name: docgen-agent`
    `description: >`
      `Generates and updates Markdown API documentation and OpenAPI YAML definitions from source code and test files, using Ollama for accuracy.`
    `language: python`
    `entrypoint: agent_docgen.py`
    `triggers:`
      `- code-change`
      `- api-change`
      `- test-change`
      `- pre-release`
    `ollama:`
      `enabled: true`
      `model: phi3`
      `endpoint: http://localhost:11434/api/generate`
      `max_context: 32000`
    `env:`
      `DOCS_DIR: docs/`
    `resources:`
      `cpu: 1`
      `memory: 2Gi`
    `approvals_required: 1`

  `- name: config-generator-agent`
    `description: >`
      `Generates Kong route configuration YAML and Keycloak client JSON files for each microservice, based on OpenAPI specs and Ollama-powered code parsing.`
    `language: python`
    `entrypoint: agent_config_generator.py`
    `triggers:`
      `- api-change`
      `- new-service`
      `- pre-release`
    `ollama:`
      `enabled: true`
      `model: phi3`
      `endpoint: http://localhost:11434/api/generate`
      `max_context: 32000`
    `env:`
      `CONFIG_DIR: configs/`
    `resources:`
      `cpu: 1`
      `memory: 2Gi`
    `approvals_required: 1`

  `- name: tenant-isolation-checker-agent`
    `description: >`
      `Continuously tests for cross-tenant data leaks by performing authenticated API requests with different tenant credentials and validating strict data boundaries.`
    `language: python`
    `entrypoint: agent_tenant_checker.py`
    `triggers:`
      `- post-deploy`
      `- scheduled`
      `- pre-release`
    `ollama:`
      `enabled: false   # LLM not needed for simple API test/checks`
    `env:`
      `TENANT_TEST_CASES: tenant_cases.yaml`
    `resources:`
      `cpu: 1`
      `memory: 1Gi`
    `approvals_required: 0`

`ollama:`
  `enabled: true`
  `default_endpoint: http://localhost:11434/api/generate`
  `default_model: phi3`
  `models_allowed:`
    `- phi3`
    `- codellama`
    `- mistral`
  `local_runtime: true`
  `cpu_limit: 4`
  `memory_limit: 8Gi`

`logging:`
  `level: INFO`
  `file: logs/agentic_dev.log`

`audit:`
  `require_human_approval: true`
  `notify_on_failures: <EMAIL>`

### **Key Points & Usage**

* **Agents** are defined by name, description, triggers, resource requirements, and environment variables.

* **Ollama Integration:**

  * `enabled: true` under each agent means LLM-powered.

  * Each agent specifies the **Ollama model** (e.g., `phi3`, `codellama`).

  * Local Ollama endpoint is set as `http://localhost:11434/api/generate`.

  * Agent resource allocation is clear for future scaling or scheduling.

* **Tenant Isolation Checker** does not require LLM, so Ollama is disabled for it.

* **Global Ollama settings** define which models can be used, local runtime, and resource limits for the LLM container/service.

* **Logging and audit** settings ensure traceability and notifications.

**Direct Recommendations:**

* Mount this YAML as the manifest for your MCP server (or your in-house orchestrator).

* Each agent script will read its section (via env or MCP context), so ensure local Ollama is running and accessible at the endpoint before running LLM agents.

* You may add more agents (code quality, dependency review, etc.) by extending this YAML.

* Use **`approvals_required`** to enforce human review for safety on sensitive operations.

## **REST API: Agent Registration Example (FastAPI)**

**Features:**

* Accepts a YAML or JSON agent manifest (see previous response).

* Stores agent definitions in a registry (could be a DB or in-memory dict).

* Provides endpoints to register, update, list, and query agents.

* Checks for Ollama endpoint availability if the agent requires LLM.

python
CopyEdit
`# agent_registry_api.py`
`from fastapi import FastAPI, HTTPException, Body`
`from pydantic import BaseModel`
`from typing import List, Optional, Dict, Any`
`import yaml`
`import requests`

`app = FastAPI(title="MCP Agent Registry API")`

`AGENT_REGISTRY: Dict[str, Any] = {}  # In-memory; replace with DB in prod`

`class AgentDef(BaseModel):`
    `name: str`
    `description: str`
    `language: str`
    `entrypoint: str`
    `triggers: List[str]`
    `ollama: Optional[Dict[str, Any]] = None`
    `env: Optional[Dict[str, str]] = None`
    `resources: Optional[Dict[str, Any]] = None`
    `approvals_required: int = 0`

`@app.post("/register-agent", summary="Register a new agent")`
`def register_agent(agent: AgentDef):`
    `# Ollama endpoint check if enabled`
    `if agent.ollama and agent.ollama.get("enabled", False):`
        `endpoint = agent.ollama.get("endpoint", "http://localhost:11434/api/generate")`
        `try:`
            `r = requests.get(endpoint.replace("/generate","/tags"), timeout=2)`
            `if not r.ok:`
                `raise Exception()`
        `except Exception:`
            `raise HTTPException(status_code=400, detail="Ollama endpoint not available")`
    `if agent.name in AGENT_REGISTRY:`
        `raise HTTPException(status_code=409, detail="Agent already registered")`
    `AGENT_REGISTRY[agent.name] = agent.dict()`
    `return {"message": f"Agent '{agent.name}' registered successfully."}`

`@app.post("/register-agents-manifest", summary="Bulk register from YAML manifest")`
`def register_agents_manifest(manifest_yaml: str = Body(...)):`
    `manifest = yaml.safe_load(manifest_yaml)`
    `for agent in manifest.get("agents", []):`
        `agent_def = AgentDef(**agent)`
        `if agent_def.name in AGENT_REGISTRY:`
            `continue  # Or update/raise error as needed`
        `AGENT_REGISTRY[agent_def.name] = agent_def.dict()`
    `return {"message": f"{len(manifest.get('agents', []))} agents registered."}`

`@app.get("/agents", response_model=List[AgentDef], summary="List all agents")`
`def list_agents():`
    `return [AgentDef(**a) for a in AGENT_REGISTRY.values()]`

`@app.get("/agents/{name}", response_model=AgentDef, summary="Get agent by name")`
`def get_agent(name: str):`
    `if name not in AGENT_REGISTRY:`
        `raise HTTPException(status_code=404, detail="Agent not found")`
    `return AgentDef(**AGENT_REGISTRY[name])`

`@app.put("/agents/{name}", summary="Update agent config")`
`def update_agent(name: str, agent: AgentDef):`
    `if name not in AGENT_REGISTRY:`
        `raise HTTPException(status_code=404, detail="Agent not found")`
    `AGENT_REGISTRY[name] = agent.dict()`
    `return {"message": f"Agent '{name}' updated."}`

`@app.delete("/agents/{name}", summary="Delete agent")`
`def delete_agent(name: str):`
    `if name in AGENT_REGISTRY:`
        `AGENT_REGISTRY.pop(name)`
        `return {"message": f"Agent '{name}' deleted."}`
    `raise HTTPException(status_code=404, detail="Agent not found")`

## **How to Use**

**Register Single Agent:**

 bash
CopyEdit
`curl -X POST http://localhost:8000/register-agent \`
`-H "Content-Type: application/json" \`
`-d '{"name":"test-writer-agent", "description":"Generates tests...", ...}'`

1.

**Register Multiple Agents via Manifest:**

 bash
CopyEdit
`curl -X POST http://localhost:8000/register-agents-manifest \`
`-H "Content-Type: text/plain" \`
`--data-binary @agents-manifest.yaml`

2.
3. **List All Agents:**
    `GET /agents`

4. **Get/Update/Delete Agent by Name:**
    `GET /agents/{name}`, `PUT /agents/{name}`, `DELETE /agents/{name}`

## **Direct Advice**

* Swap in a database (MongoDB, PostgreSQL) for AGENT\_REGISTRY for persistence.

* Add authentication and audit logging in production.

* Adjust Ollama endpoint check as needed for your network.

* You can auto-trigger registration on repo/manifest change (CI/CD or webhook).

## **Production-ready CLI tool** for registering agents with your MCP Agent Registry API.  This CLI supports both single-agent JSON files and bulk YAML manifest uploads, mirroring the REST API previously shared.

**Requirements:**

* Python 3.8+

* `requests` and `pyyaml` libraries (`pip install requests pyyaml`)

## **`agentcli.py` — Agent Registration CLI**

python
CopyEdit
`#!/usr/bin/env python3`

`import argparse`
`import requests`
`import json`
`import yaml`
`import sys`
`from pathlib import Path`

`API_BASE = "http://localhost:8000"  # Change if running elsewhere`

`def register_agent(agent_json_path):`
    `with open(agent_json_path, "r") as f:`
        `agent = json.load(f)`
    `resp = requests.post(f"{API_BASE}/register-agent", json=agent)`
    `print(f"Status: {resp.status_code}")`
    `print(resp.json())`

`def register_agents_manifest(yaml_path):`
    `with open(yaml_path, "r") as f:`
        `manifest = f.read()`
    `headers = {"Content-Type": "text/plain"}`
    `resp = requests.post(f"{API_BASE}/register-agents-manifest", data=manifest, headers=headers)`
    `print(f"Status: {resp.status_code}")`
    `print(resp.json())`

`def list_agents():`
    `resp = requests.get(f"{API_BASE}/agents")`
    `agents = resp.json()`
    `print(json.dumps(agents, indent=2))`

`def get_agent(name):`
    `resp = requests.get(f"{API_BASE}/agents/{name}")`
    `if resp.status_code == 404:`
        `print("Agent not found")`
    `else:`
        `print(json.dumps(resp.json(), indent=2))`

`def update_agent(agent_json_path):`
    `with open(agent_json_path, "r") as f:`
        `agent = json.load(f)`
    `name = agent.get("name")`
    `if not name:`
        `print("Agent JSON must include a 'name' field.")`
        `return`
    `resp = requests.put(f"{API_BASE}/agents/{name}", json=agent)`
    `print(f"Status: {resp.status_code}")`
    `print(resp.json())`

`def delete_agent(name):`
    `resp = requests.delete(f"{API_BASE}/agents/{name}")`
    `print(f"Status: {resp.status_code}")`
    `print(resp.json())`

`def main():`
    `parser = argparse.ArgumentParser(description="MCP Agent Registry CLI")`
    `subparsers = parser.add_subparsers(dest="command")`

    `reg = subparsers.add_parser("register", help="Register a single agent from JSON file")`
    `reg.add_argument("json_path", type=Path)`

    `regm = subparsers.add_parser("register-manifest", help="Bulk register agents from YAML manifest")`
    `regm.add_argument("yaml_path", type=Path)`

    `listp = subparsers.add_parser("list", help="List all registered agents")`

    `getp = subparsers.add_parser("get", help="Get agent by name")`
    `getp.add_argument("name", type=str)`

    `upd = subparsers.add_parser("update", help="Update agent from JSON file")`
    `upd.add_argument("json_path", type=Path)`

    `delp = subparsers.add_parser("delete", help="Delete agent by name")`
    `delp.add_argument("name", type=str)`

    `args = parser.parse_args()`

    `if args.command == "register":`
        `register_agent(args.json_path)`
    `elif args.command == "register-manifest":`
        `register_agents_manifest(args.yaml_path)`
    `elif args.command == "list":`
        `list_agents()`
    `elif args.command == "get":`
        `get_agent(args.name)`
    `elif args.command == "update":`
        `update_agent(args.json_path)`
    `elif args.command == "delete":`
        `delete_agent(args.name)`
    `else:`
        `parser.print_help()`

`if __name__ == "__main__":`
    `main()`

## **Usage Examples**

**Register a single agent JSON:**

 bash
CopyEdit
`python agentcli.py register agent_def.json`

1. **Bulk register from YAML manifest:**

    bash
   CopyEdit
   `python agentcli.py register-manifest agents-manifest.yaml`
2. **List all agents:**

    bash
   CopyEdit
   `python agentcli.py list`
3. **Get details of one agent:**

    bash
   CopyEdit
   `python agentcli.py get test-writer-agent`
4. **Update an agent:**

    bash
   CopyEdit
   `python agentcli.py update agent_def.json`
5. **Delete an agent:**

    bash
   CopyEdit
   `python agentcli.py delete test-writer-agent`

## **Best Practices**

* Use `register-manifest` for first-time or bulk agent registration.

* Store all agent JSON/YAML under version control for audit.

* For updates, always include the full (updated) agent definition.

* Extend CLI for advanced features (status, logs) as needed.

## **A sample pre-commit hook** to automate **agent registration and validation via your CLI** (`agentcli.py`) every time you commit. This ensures agents and manifests are always in sync with your registry **before code lands in your repo**.

## **1\. Make Your CLI Accessible**

* Place `agentcli.py` at your project root or in a common tools directory.

Make it executable:

 bash
CopyEdit
`chmod +x agentcli.py`

* (Optional) Add a shebang line if not present:

   python
  CopyEdit
  `#!/usr/bin/env python3`

## **2\. Prepare the Pre-commit Hook Script**

**Location:**
 `.git/hooks/pre-commit`
 (Make sure it is executable: `chmod +x .git/hooks/pre-commit`)

### **Sample Pre-commit Hook (`.git/hooks/pre-commit`):**

bash
CopyEdit
`#!/bin/bash`
`# Pre-commit hook: Validate and auto-register all agent manifests before commit`

`set -e`

`AGENTCLI="./agentcli.py"  # Adjust path if needed`
`MANIFEST="agents-manifest.yaml"`

`if [ ! -f "$AGENTCLI" ]; then`
    `echo "ERROR: agentcli.py not found! Commit aborted."`
    `exit 1`
`fi`

`if [ ! -f "$MANIFEST" ]; then`
    `echo "WARNING: No agents-manifest.yaml found. Skipping agent registration."`
`else`
    `echo "Registering agents from $MANIFEST with MCP server..."`
    `python3 "$AGENTCLI" register-manifest "$MANIFEST"`
    `if [ $? -ne 0 ]; then`
        `echo "Agent registration failed! Commit aborted."`
        `exit 1`
    `fi`
    `echo "Agent registration successful."`
`fi`

`# Optionally run lint or tests for agent scripts here`
`# Example:`
`# flake8 agents/`

`# Continue with the commit`
`exit 0`

## **3\. What This Pre-commit Does:**

* Runs before every commit.

* Checks for the presence of `agentcli.py` and `agents-manifest.yaml`.

* Runs `python3 agentcli.py register-manifest agents-manifest.yaml`.

* Aborts the commit **if agent registration fails** (e.g., due to errors in the manifest or connectivity issues).

* You can easily extend this script to validate single-agent JSONs, run code quality checks, or even ping your MCP server’s health endpoint before allowing the commit.

## **4\. Activate the Hook**

Ensure the file is named `.git/hooks/pre-commit` and is executable:

 bash
CopyEdit
`chmod +x .git/hooks/pre-commit`

## **5\. (Optional) Use pre-commit Framework**

If you want to manage hooks with pre-commit:

**.pre-commit-config.yaml**

yaml
CopyEdit
`repos:`
  `- repo: local`
    `hooks:`
      `- id: agent-manifest-register`
        `name: Agent Manifest Register`
        `entry: python3 agentcli.py register-manifest agents-manifest.yaml`
        `language: system`
        `types: [yaml]`
        `files: ^agents-manifest\.yaml$`

Then install:

bash
CopyEdit
`pip install pre-commit`
`pre-commit install`

## **6\. Direct Advice**

* Use the pre-commit hook for continuous compliance and early error catching—**never allow agent config drift**.

* Always run a dry run (`git commit --dry-run`) after setting up to confirm smooth integration.

* Adjust the manifest/CLI/script paths to your repo structure.

## A **ready-to-use advanced TUI (Text User Interface)** for managing your MCP Agent Registry—**view, register, update, and delete agents** interactively from the terminal. This TUI uses the popular [`Textual`](https://github.com/Textualize/textual) library, which is modern, Python-based, and supports real-time interactivity, mouse, and keyboard navigation.

## **1\. Install Requirements**

bash

CopyEdit

`pip install textual requests pyyaml rich`

## **2\. TUI Source Code: `agent_tui.py`**

python

CopyEdit

`import asyncio`

`from textual.app import App, ComposeResult`

`from textual.widgets import Header, Footer, Button, Static, Input, Label, ListView, ListItem, DataTable`

`from textual.containers import Container, Horizontal, Vertical`

`from textual.message import Message`

`from textual.reactive import reactive`

`from textual import events`

`import requests`

`import json`

`import yaml`

`from pathlib import Path`

`API_BASE = "http://localhost:8000"  # MCP API URL`

`class AgentListView(ListView):`

    `def __init__(self, *args, **kwargs):`

        `super().__init__(*args, **kwargs)`



    `def set_agents(self, agents):`

        `self.clear()`

        `for agent in agents:`

            `display = f"{agent['name']} ({agent.get('language', '-')})"`

            `self.append(ListItem(Label(display), id=agent['name']))`

`class AgentTUI(App):`

    `CSS_PATH = None`

    `agents = reactive([])`

    `def compose(self) -> ComposeResult:`

        `yield Header()`

        `with Horizontal():`

            `with Vertical():`

                `yield Label("Registered Agents", id="lbl_agents")`

                `self.agent_list = AgentListView()`

                `yield self.agent_list`

                `yield Button("Refresh", id="refresh")`

                `yield Button("Register New Agent", id="register")`

                `yield Button("Bulk Register (YAML)", id="bulk_register")`

                `yield Button("Delete Agent", id="delete")`

            `with Vertical():`

                `self.agent_detail = Static("Select an agent to view details.", id="details")`

                `yield self.agent_detail`

                `self.input_json = Input(placeholder="Paste agent JSON here", id="input_json")`

                `yield self.input_json`

                `yield Button("Update Agent", id="update")`

        `yield Footer()`

    `def on_mount(self):`

        `self.load_agents()`

    `def load_agents(self):`

        `try:`

            `resp = requests.get(f"{API_BASE}/agents")`

            `resp.raise_for_status()`

            `self.agents = resp.json()`

            `self.agent_list.set_agents(self.agents)`

        `except Exception as e:`

            `self.agent_detail.update(f"[red]Error loading agents: {e}[/red]")`

    `def get_agent(self, name):`

        `try:`

            `resp = requests.get(f"{API_BASE}/agents/{name}")`

            `resp.raise_for_status()`

            `return resp.json()`

        `except Exception as e:`

            `self.agent_detail.update(f"[red]Error: {e}[/red]")`

            `return None`

    `async def handle_refresh(self):`

        `self.load_agents()`

    `async def handle_agent_select(self, name):`

        `agent = self.get_agent(name)`

        `if agent:`

            `self.agent_detail.update(f"[bold]{agent['name']}[/bold]\n" + json.dumps(agent, indent=2))`

            `self.input_json.value = json.dumps(agent, indent=2)`

    `async def handle_delete(self):`

        `selected = self.agent_list.highlighted_child`

        `if not selected:`

            `self.agent_detail.update("[yellow]No agent selected.[/yellow]")`

            `return`

        `name = selected.id`

        `resp = requests.delete(f"{API_BASE}/agents/{name}")`

        `if resp.status_code == 200:`

            `self.agent_detail.update(f"[green]Deleted agent {name}[/green]")`

            `self.load_agents()`

        `else:`

            `self.agent_detail.update(f"[red]Delete failed: {resp.text}[/red]")`

    `async def handle_update(self):`

        `try:`

            `agent_json = json.loads(self.input_json.value)`

            `name = agent_json.get("name")`

            `if not name:`

                `self.agent_detail.update("[yellow]Agent JSON must have a name.[/yellow]")`

                `return`

            `resp = requests.put(f"{API_BASE}/agents/{name}", json=agent_json)`

            `if resp.status_code == 200:`

                `self.agent_detail.update(f"[green]Updated agent {name}.[/green]")`

                `self.load_agents()`

            `else:`

                `self.agent_detail.update(f"[red]Update failed: {resp.text}[/red]")`

        `except Exception as e:`

            `self.agent_detail.update(f"[red]JSON Error: {e}[/red]")`

    `async def handle_register(self):`

        `try:`

            `agent_json = json.loads(self.input_json.value)`

            `resp = requests.post(f"{API_BASE}/register-agent", json=agent_json)`

            `if resp.status_code == 200:`

                `self.agent_detail.update(f"[green]Agent registered.[/green]")`

                `self.load_agents()`

            `else:`

                `self.agent_detail.update(f"[red]Register failed: {resp.text}[/red]")`

        `except Exception as e:`

            `self.agent_detail.update(f"[red]JSON Error: {e}[/red]")`

    `async def handle_bulk_register(self):`

        `# Open file dialog for YAML manifest`

        `import tkinter as tk`

        `from tkinter import filedialog`

        `root = tk.Tk()`

        `root.withdraw()`

        `yaml_file = filedialog.askopenfilename(filetypes=[("YAML Files", "*.yaml *.yml")])`

        `if not yaml_file:`

            `self.agent_detail.update("[yellow]Bulk register cancelled.[/yellow]")`

            `return`

        `with open(yaml_file, "r") as f:`

            `manifest = f.read()`

        `headers = {"Content-Type": "text/plain"}`

        `resp = requests.post(f"{API_BASE}/register-agents-manifest", data=manifest, headers=headers)`

        `if resp.status_code == 200:`

            `self.agent_detail.update("[green]Bulk agent registration successful.[/green]")`

            `self.load_agents()`

        `else:`

            `self.agent_detail.update(f"[red]Bulk registration failed: {resp.text}[/red]")`

    `async def on_list_view_highlighted(self, event: ListView.Highlighted):`

        `await self.handle_agent_select(event.item.id)`

    `async def on_button_pressed(self, event: Button.Pressed):`

        `if event.button.id == "refresh":`

            `await self.handle_refresh()`

        `elif event.button.id == "delete":`

            `await self.handle_delete()`

        `elif event.button.id == "update":`

            `await self.handle_update()`

        `elif event.button.id == "register":`

            `await self.handle_register()`

        `elif event.button.id == "bulk_register":`

            `await self.handle_bulk_register()`

`if __name__ == "__main__":`

    `AgentTUI().run()`

## **3\. Usage**

1. **Start your Agent Registry API** (see earlier FastAPI sample).

**Launch the TUI:**

 bash
CopyEdit
`python agent_tui.py`

2. **Features you get:**

   * **List agents** (auto-refresh).

   * **View full details** of any agent (highlight to show in right pane).

   * **Register a new agent:** Paste JSON in right pane, click "Register New Agent".

   * **Bulk register:** Click "Bulk Register", select a YAML manifest.

   * **Update agent:** Edit JSON and click "Update Agent".

   * **Delete agent:** Select and click "Delete Agent".

   * **Refresh**: Click "Refresh" for current state.

**Tips:**

* You can select/copy/paste from the TUI (supports mouse and keyboard).

* Easily extend to add filtering, export, agent run/monitor, or logs.

## **Direct Advice**

* TUI is ideal for DevOps and admins—no web server needed.

* Use the TUI for live production registry management or as a local dev productivity tool.

* This solution is forward-compatible with your full MCP registry and will scale with agent count.
