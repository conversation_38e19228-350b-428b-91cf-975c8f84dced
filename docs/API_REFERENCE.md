# oneHRMS API Reference

## 🔗 API Overview

The oneHRMS API provides a comprehensive set of endpoints for managing all aspects of the Human Resource Management System. The API follows RESTful principles and uses JSON for data exchange.

## 📋 Base Information

- **Base URL**: `https://api.onehrms.com/v1`
- **Authentication**: Bearer <PERSON> (JWT)
- **Content-Type**: `application/json`
- **API Version**: v1.0.0

## 🔐 Authentication

### Authentication Flow
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

### Response
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

### Using the Token
```http
GET /employees
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📊 Employee Management API

### Get All Employees
```http
GET /employees
```

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `department` (optional): Filter by department
- `status` (optional): Filter by status (active, inactive, terminated)

**Response**:
```json
{
  "data": [
    {
      "id": "EMP001",
      "employee_id": "EMP001",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "department": {
        "id": "DEPT001",
        "name": "Engineering"
      },
      "designation": "Software Engineer",
      "hire_date": "2023-01-15",
      "status": "active",
      "manager": {
        "id": "EMP002",
        "name": "Jane Smith"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### Get Employee by ID
```http
GET /employees/{employee_id}
```

**Response**:
```json
{
  "id": "EMP001",
  "employee_id": "EMP001",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "address": {
    "street": "123 Main Street",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  },
  "department": {
    "id": "DEPT001",
    "name": "Engineering"
  },
  "designation": "Software Engineer",
  "hire_date": "2023-01-15",
  "status": "active",
  "salary": {
    "amount": 75000,
    "currency": "USD",
    "effective_date": "2024-01-01"
  },
  "benefits": [
    {
      "type": "health_insurance",
      "provider": "Blue Cross",
      "amount": 500
    }
  ]
}
```

### Create Employee
```http
POST /employees
Content-Type: application/json

{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "******-0124",
  "department_id": "DEPT001",
  "designation": "Senior Engineer",
  "hire_date": "2024-01-01",
  "salary": {
    "amount": 85000,
    "currency": "USD"
  }
}
```

### Update Employee
```http
PUT /employees/{employee_id}
Content-Type: application/json

{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "******-0124"
}
```

### Delete Employee
```http
DELETE /employees/{employee_id}
```

## ⏰ Attendance API

### Get Attendance Records
```http
GET /attendance
```

**Query Parameters**:
- `employee_id` (optional): Filter by employee
- `date_from` (optional): Start date (YYYY-MM-DD)
- `date_to` (optional): End date (YYYY-MM-DD)
- `status` (optional): Filter by status (present, absent, late)

**Response**:
```json
{
  "data": [
    {
      "id": "ATT001",
      "employee_id": "EMP001",
      "date": "2024-01-15",
      "check_in": "2024-01-15T09:00:00Z",
      "check_out": "2024-01-15T17:00:00Z",
      "total_hours": 8.0,
      "status": "present",
      "location": {
        "latitude": 40.7128,
        "longitude": -74.0060
      }
    }
  ]
}
```

### Check In
```http
POST /attendance/check-in
Content-Type: application/json

{
  "employee_id": "EMP001",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "timestamp": "2024-01-15T09:00:00Z"
}
```

### Check Out
```http
POST /attendance/check-out
Content-Type: application/json

{
  "employee_id": "EMP001",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "timestamp": "2024-01-15T17:00:00Z"
}
```

## 🏖️ Leave Management API

### Get Leave Requests
```http
GET /leaves
```

**Query Parameters**:
- `employee_id` (optional): Filter by employee
- `status` (optional): Filter by status (pending, approved, rejected)
- `leave_type` (optional): Filter by leave type
- `date_from` (optional): Start date
- `date_to` (optional): End date

**Response**:
```json
{
  "data": [
    {
      "id": "LEAVE001",
      "employee_id": "EMP001",
      "leave_type": "annual",
      "start_date": "2024-02-01",
      "end_date": "2024-02-05",
      "days": 5,
      "reason": "Vacation",
      "status": "approved",
      "approved_by": "EMP002",
      "approved_date": "2024-01-20T10:00:00Z"
    }
  ]
}
```

### Create Leave Request
```http
POST /leaves
Content-Type: application/json

{
  "employee_id": "EMP001",
  "leave_type": "annual",
  "start_date": "2024-02-01",
  "end_date": "2024-02-05",
  "reason": "Vacation"
}
```

### Update Leave Request
```http
PUT /leaves/{leave_id}
Content-Type: application/json

{
  "status": "approved",
  "comments": "Approved for vacation"
}
```

### Get Leave Balance
```http
GET /leaves/balance/{employee_id}
```

**Response**:
```json
{
  "employee_id": "EMP001",
  "balances": [
    {
      "leave_type": "annual",
      "total": 25,
      "used": 8,
      "remaining": 17,
      "pending": 0
    },
    {
      "leave_type": "sick",
      "total": 12,
      "used": 2,
      "remaining": 10,
      "pending": 0
    }
  ]
}
```

## 💰 Payroll API

### Get Salary Information
```http
GET /payroll/salary/{employee_id}
```

**Response**:
```json
{
  "employee_id": "EMP001",
  "current_salary": {
    "amount": 75000,
    "currency": "USD",
    "effective_date": "2024-01-01"
  },
  "benefits": [
    {
      "type": "health_insurance",
      "provider": "Blue Cross",
      "amount": 500,
      "frequency": "monthly"
    },
    {
      "type": "retirement",
      "provider": "401k",
      "amount": 3000,
      "frequency": "annual"
    }
  ],
  "tax_info": {
    "tax_code": "W-2",
    "exemptions": 2,
    "withholding": "single"
  }
}
```

### Get Payslips
```http
GET /payroll/payslips
```

**Query Parameters**:
- `employee_id` (optional): Filter by employee
- `month` (optional): Filter by month (YYYY-MM)
- `year` (optional): Filter by year

**Response**:
```json
{
  "data": [
    {
      "id": "PAY001",
      "employee_id": "EMP001",
      "month": "2024-01",
      "gross_salary": 75000,
      "net_salary": 55000,
      "deductions": [
        {
          "type": "tax",
          "amount": 15000,
          "description": "Federal Tax"
        },
        {
          "type": "insurance",
          "amount": 500,
          "description": "Health Insurance"
        }
      ],
      "status": "paid",
      "payment_date": "2024-01-31"
    }
  ]
}
```

### Process Payroll
```http
POST /payroll/process
Content-Type: application/json

{
  "month": "2024-01",
  "year": 2024,
  "include_benefits": true,
  "include_tax": true
}
```

## 📋 Recruitment API

### Get Job Postings
```http
GET /recruitment/jobs
```

**Query Parameters**:
- `status` (optional): Filter by status (open, closed, draft)
- `department` (optional): Filter by department
- `location` (optional): Filter by location

**Response**:
```json
{
  "data": [
    {
      "id": "JOB001",
      "title": "Senior Software Engineer",
      "department": "Engineering",
      "location": "New York",
      "type": "full-time",
      "status": "open",
      "description": "We are looking for a senior software engineer...",
      "requirements": [
        "5+ years of experience",
        "React/TypeScript knowledge",
        "Team leadership skills"
      ],
      "posted_date": "2024-01-15",
      "deadline": "2024-02-15"
    }
  ]
}
```

### Create Job Posting
```http
POST /recruitment/jobs
Content-Type: application/json

{
  "title": "Senior Software Engineer",
  "department_id": "DEPT001",
  "location": "New York",
  "type": "full-time",
  "description": "We are looking for a senior software engineer...",
  "requirements": [
    "5+ years of experience",
    "React/TypeScript knowledge"
  ],
  "salary_range": {
    "min": 80000,
    "max": 120000,
    "currency": "USD"
  }
}
```

### Get Candidates
```http
GET /recruitment/candidates
```

**Query Parameters**:
- `job_id` (optional): Filter by job posting
- `status` (optional): Filter by status (applied, shortlisted, interviewed, hired, rejected)

**Response**:
```json
{
  "data": [
    {
      "id": "CAND001",
      "first_name": "Alice",
      "last_name": "Johnson",
      "email": "<EMAIL>",
      "phone": "******-0125",
      "job_id": "JOB001",
      "status": "shortlisted",
      "applied_date": "2024-01-20",
      "resume_url": "https://storage.example.com/resumes/cand001.pdf"
    }
  ]
}
```

## 📊 Reports API

### Get Employee Report
```http
GET /reports/employees
```

**Query Parameters**:
- `department` (optional): Filter by department
- `status` (optional): Filter by status
- `date_from` (optional): Start date
- `date_to` (optional): End date

**Response**:
```json
{
  "summary": {
    "total_employees": 150,
    "active_employees": 145,
    "new_hires": 12,
    "terminations": 3
  },
  "by_department": [
    {
      "department": "Engineering",
      "count": 45,
      "percentage": 30
    }
  ],
  "by_status": [
    {
      "status": "active",
      "count": 145,
      "percentage": 96.7
    }
  ]
}
```

### Get Attendance Report
```http
GET /reports/attendance
```

**Query Parameters**:
- `employee_id` (optional): Filter by employee
- `department` (optional): Filter by department
- `date_from` (required): Start date
- `date_to` (required): End date

**Response**:
```json
{
  "summary": {
    "total_days": 22,
    "present_days": 20,
    "absent_days": 2,
    "attendance_rate": 90.9
  },
  "daily_data": [
    {
      "date": "2024-01-15",
      "present": 145,
      "absent": 5,
      "late": 3
    }
  ]
}
```

## 🔧 System API

### Get System Health
```http
GET /system/health
```

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:00:00Z",
  "services": {
    "database": "healthy",
    "authentication": "healthy",
    "file_storage": "healthy"
  },
  "version": "1.0.0"
}
```

### Get System Configuration
```http
GET /system/config
```

**Response**:
```json
{
  "company": {
    "name": "Example Corp",
    "timezone": "America/New_York",
    "currency": "USD"
  },
  "features": {
    "attendance_tracking": true,
    "leave_management": true,
    "payroll": true,
    "recruitment": true
  },
  "settings": {
    "max_file_size": 10485760,
    "allowed_file_types": ["pdf", "doc", "docx", "jpg", "png"]
  }
}
```

## 📝 Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ],
    "timestamp": "2024-01-15T10:00:00Z"
  }
}
```

### Common Error Codes
- `AUTHENTICATION_ERROR`: Invalid or expired token
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource conflict
- `INTERNAL_ERROR`: Server error

### Rate Limiting
```http
HTTP/1.1 429 Too Many Requests
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1642233600
```

## 📚 SDKs and Libraries

### JavaScript/TypeScript SDK
```bash
npm install @onehrms/api-client
```

```javascript
import { OneHRMSClient } from '@onehrms/api-client';

const client = new OneHRMSClient({
  baseURL: 'https://api.onehrms.com/v1',
  token: 'your-access-token'
});

// Get employees
const employees = await client.employees.getAll();

// Create employee
const newEmployee = await client.employees.create({
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>'
});
```

### Python SDK
```bash
pip install onehrms-api-client
```

```python
from onehrms import OneHRMSClient

client = OneHRMSClient(
    base_url='https://api.onehrms.com/v1',
    token='your-access-token'
)

# Get employees
employees = client.employees.get_all()

# Create employee
new_employee = client.employees.create({
    'first_name': 'John',
    'last_name': 'Doe',
    'email': '<EMAIL>'
})
```

## 🔄 Webhooks

### Webhook Configuration
```http
POST /webhooks
Content-Type: application/json

{
  "url": "https://your-app.com/webhooks/onehrms",
  "events": ["employee.created", "attendance.checked_in"],
  "secret": "your-webhook-secret"
}
```

### Webhook Events
- `employee.created`: New employee created
- `employee.updated`: Employee information updated
- `attendance.checked_in`: Employee checked in
- `attendance.checked_out`: Employee checked out
- `leave.requested`: Leave request submitted
- `leave.approved`: Leave request approved
- `leave.rejected`: Leave request rejected
- `payroll.processed`: Payroll processed

### Webhook Payload
```json
{
  "event": "employee.created",
  "timestamp": "2024-01-15T10:00:00Z",
  "data": {
    "employee_id": "EMP001",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>"
  }
}
```

## 📖 API Documentation

### Interactive Documentation
- **Swagger UI**: `https://api.onehrms.com/docs`
- **ReDoc**: `https://api.onehrms.com/redoc`
- **OpenAPI Spec**: `https://api.onehrms.com/openapi.json`

### SDK Documentation
- **JavaScript SDK**: [GitHub Repository](https://github.com/onehrms/js-sdk)
- **Python SDK**: [GitHub Repository](https://github.com/onehrms/python-sdk)

---

**Last Updated**: December 2024
**API Version**: v1.0.0
**Documentation Team**: oneHRMS Development Team
