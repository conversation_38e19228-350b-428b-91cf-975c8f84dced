# oneHRMS Development Environment Variables

# Python Environment
PYTHONPATH=.
PYTHON_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hrms_dev
DB_USER=hrms_user
DB_PASSWORD=hrms_password

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Authentication & Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=hrms
KEYCLOAK_CLIENT_ID=hrms-client

# API Gateway
KONG_ADMIN_URL=http://localhost:8001
KONG_PROXY_URL=http://localhost:8000

# Development Settings
DEBUG=true
LOG_LEVEL=DEBUG
TESTING=false

# Multi-tenancy
DEFAULT_TENANT_ID=default
TENANT_ISOLATION_ENABLED=true

# External Services
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=

# File Storage
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10MB

# Monitoring & Logging
SENTRY_DSN=
LOG_FILE=logs/hrms.log

# Agent Configuration
OLLAMA_URL=http://localhost:11434
AGENT_REGISTRY_URL=http://localhost:8002
MCP_SERVER_URL=http://localhost:8003

# Testing
TEST_DB_NAME=hrms_test
COVERAGE_THRESHOLD=80
PYTEST_WORKERS=auto
