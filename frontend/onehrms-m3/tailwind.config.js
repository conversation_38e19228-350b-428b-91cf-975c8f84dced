/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Material Design 3 Color System
      colors: {
        // Primary colors
        primary: {
          DEFAULT: '#6750A4',
          50: '#F7F2FF',
          100: '#EADDFF',
          200: '#D0BCFF',
          300: '#B69DF8',
          400: '#9A82DB',
          500: '#7F67BE',
          600: '#6750A4',
          700: '#4F378B',
          800: '#381E72',
          900: '#21005D',
        },
        // Secondary colors
        secondary: {
          DEFAULT: '#625B71',
          50: '#F9F8FC',
          100: '#E7E0EC',
          200: '#CCC2DC',
          300: '#B0A7C0',
          400: '#958DA5',
          500: '#7A7289',
          600: '#625B71',
          700: '#4A4458',
          800: '#332D41',
          900: '#1D192B',
        },
        // Tertiary colors
        tertiary: {
          DEFAULT: '#7D5260',
          50: '#FFF8F9',
          100: '#FFD8E4',
          200: '#EFB8C8',
          300: '#D69AAC',
          400: '#BC7F91',
          500: '#A26577',
          600: '#8B4C5E',
          700: '#7D5260',
          800: '#633B48',
          900: '#492532',
        },
        // Error colors
        error: {
          DEFAULT: '#BA1A1A',
          50: '#FFFBF9',
          100: '#FFDAD6',
          200: '#FFB4AB',
          300: '#FF897D',
          400: '#FF5449',
          500: '#DE3730',
          600: '#BA1A1A',
          700: '#93000A',
          800: '#690005',
          900: '#410002',
        },
        // Neutral colors
        neutral: {
          DEFAULT: '#1D1B20',
          0: '#FFFFFF',
          10: '#1D1B20',
          20: '#313033',
          25: '#3C383E',
          30: '#484649',
          35: '#545054',
          40: '#605D62',
          50: '#787579',
          60: '#939094',
          70: '#AEAAAE',
          80: '#CAC4D0',
          90: '#E6E0E9',
          95: '#F4EFF4',
          99: '#FFFBFE',
        },
        // Surface colors
        surface: {
          DEFAULT: '#FEF7FF',
          dim: '#DED8E1',
          bright: '#FEF7FF',
          'container-lowest': '#FFFFFF',
          'container-low': '#F7F2FA',
          container: '#F3EDF7',
          'container-high': '#ECE6F0',
          'container-highest': '#E6E0E9',
        },
      },
      // Material Design 3 Typography
      fontFamily: {
        sans: ['Roboto', 'system-ui', 'sans-serif'],
        display: ['Roboto', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        // Display styles
        'display-large': ['57px', { lineHeight: '64px', fontWeight: '400' }],
        'display-medium': ['45px', { lineHeight: '52px', fontWeight: '400' }],
        'display-small': ['36px', { lineHeight: '44px', fontWeight: '400' }],
        // Headline styles
        'headline-large': ['32px', { lineHeight: '40px', fontWeight: '400' }],
        'headline-medium': ['28px', { lineHeight: '36px', fontWeight: '400' }],
        'headline-small': ['24px', { lineHeight: '32px', fontWeight: '400' }],
        // Title styles
        'title-large': ['22px', { lineHeight: '28px', fontWeight: '400' }],
        'title-medium': ['16px', { lineHeight: '24px', fontWeight: '500' }],
        'title-small': ['14px', { lineHeight: '20px', fontWeight: '500' }],
        // Label styles
        'label-large': ['14px', { lineHeight: '20px', fontWeight: '500' }],
        'label-medium': ['12px', { lineHeight: '16px', fontWeight: '500' }],
        'label-small': ['11px', { lineHeight: '16px', fontWeight: '500' }],
        // Body styles
        'body-large': ['16px', { lineHeight: '24px', fontWeight: '400' }],
        'body-medium': ['14px', { lineHeight: '20px', fontWeight: '400' }],
        'body-small': ['12px', { lineHeight: '16px', fontWeight: '400' }],
      },
      // Material Design 3 Spacing
      spacing: {
        '4': '4px',
        '8': '8px',
        '12': '12px',
        '16': '16px',
        '20': '20px',
        '24': '24px',
        '32': '32px',
        '40': '40px',
        '48': '48px',
        '56': '56px',
        '64': '64px',
        '72': '72px',
        '80': '80px',
        '88': '88px',
        '96': '96px',
      },
      // Material Design 3 Border Radius
      borderRadius: {
        'none': '0px',
        'xs': '4px',
        'sm': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '20px',
        '2xl': '24px',
        '3xl': '28px',
        'full': '9999px',
      },
      // Material Design 3 Shadows
      boxShadow: {
        'elevation-1': '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
        'elevation-2': '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)',
        'elevation-3': '0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)',
        'elevation-4': '0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)',
        'elevation-5': '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
      },
    },
  },
  plugins: [],
  // Ensure compatibility with MUI
  corePlugins: {
    preflight: false,
  },
}
