/**
 * Root Page - Redirects to appropriate dashboard based on role
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useNavigation } from '@/components/providers/NavigationProvider';
import { Box, CircularProgress, Typography } from '@mui/material';

export default function RootPage() {
  const router = useRouter();
  const { currentRole } = useNavigation();

  useEffect(() => {
    // Redirect to appropriate dashboard based on role
    const redirectPath = currentRole === 'admin' ? '/admin/dashboard' : '/dashboard';
    router.replace(redirectPath);
  }, [currentRole, router]);

  return (
    <Box
      className="flex flex-col items-center justify-center min-h-screen gap-24"
    >
      <CircularProgress size={48} />
      <Typography variant="body1" className="text-on-surface-variant">
        Redirecting to dashboard...
      </Typography>
    </Box>
  );
}
