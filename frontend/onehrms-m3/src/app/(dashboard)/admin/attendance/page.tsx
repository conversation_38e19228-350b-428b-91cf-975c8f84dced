/**
 * Admin Attendance Management Page
 * Attendance tracking and management dashboard
 */

'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Schedule,
  People,
  TrendingUp,
  Warning,
  CheckCircle,
  AccessTime,
  CalendarToday,
  Assessment,
} from '@mui/icons-material';
import ViewToggle, { ViewType } from '@/components/ui/ViewToggle';

// Mock attendance data
const attendanceMetrics = [
  {
    title: 'Present Today',
    value: '1,156',
    total: '1,247',
    percentage: 92.7,
    change: '****%',
    changeType: 'increase' as const,
    icon: <CheckCircle className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'Late Arrivals',
    value: '23',
    change: '-5',
    changeType: 'decrease' as const,
    icon: <AccessTime className="w-6 h-6" />,
    color: 'warning',
  },
  {
    title: 'Absent Today',
    value: '91',
    change: '+8',
    changeType: 'increase' as const,
    icon: <Warning className="w-6 h-6" />,
    color: 'error',
  },
  {
    title: 'Avg. Hours/Day',
    value: '8.2',
    change: '+0.3',
    changeType: 'increase' as const,
    icon: <TrendingUp className="w-6 h-6" />,
    color: 'info',
  },
];

const pendingRequests = [
  { id: 1, employee: 'Sarah Johnson', type: 'Leave Request', date: '2024-01-15', status: 'pending' },
  { id: 2, employee: 'Michael Chen', type: 'Overtime', date: '2024-01-14', status: 'pending' },
  { id: 3, employee: 'Emily Rodriguez', type: 'Time Correction', date: '2024-01-13', status: 'pending' },
];

const quickActions = [
  { title: 'Generate Reports', description: 'Create attendance reports', icon: <Assessment /> },
  { title: 'Manage Shifts', description: 'Configure work schedules', icon: <Schedule /> },
  { title: 'Review Requests', description: 'Process pending requests', icon: <People /> },
];

export default function AdminAttendancePage() {
  const [view, setView] = React.useState<ViewType>('grid');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderRequestsListView = () => (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: 'var(--md-sys-color-surface)',
        border: '1px solid var(--md-sys-color-outline-variant)',
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Employee
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Type
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Date
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Status
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {pendingRequests.map((request) => (
            <TableRow
              key={request.id}
              sx={{
                '&:hover': {
                  backgroundColor: 'var(--md-sys-color-surface-container-high)',
                },
              }}
            >
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface font-medium"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {request.employee}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {request.type}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {request.date}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={request.status}
                  color={getStatusColor(request.status) as any}
                  size="small"
                  variant="filled"
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Attendance Management
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Monitor employee attendance, manage schedules, and track time records.
        </Typography>
      </Box>

      {/* Attendance Metrics */}
      <Grid container spacing={3} className="mb-32">
        {attendanceMetrics.map((metric, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${metric.color}-container)`,
                      color: `var(--md-sys-color-on-${metric.color}-container)`,
                    }}
                  >
                    {metric.icon}
                  </Box>
                </Box>

                <Typography
                  variant="h4"
                  className="text-on-surface font-normal mb-4"
                  sx={{ fontSize: '1.75rem', fontWeight: 500 }}
                >
                  {metric.value}
                  {metric.total && (
                    <Typography component="span" variant="body2" className="text-on-surface-variant ml-4">
                      / {metric.total}
                    </Typography>
                  )}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-8"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {metric.title}
                </Typography>

                {metric.percentage && (
                  <Box className="mb-8">
                    <LinearProgress
                      variant="determinate"
                      value={metric.percentage}
                      className="h-4 rounded-full"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface-variant)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: `var(--md-sys-color-${metric.color})`,
                        },
                      }}
                    />
                  </Box>
                )}

                <Box className="flex items-center gap-4">
                  <Chip
                    label={metric.change}
                    size="small"
                    color={metric.changeType === 'increase' ? 'success' : 'error'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        border: '1px solid var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <CardContent className="p-16 text-center">
                        <Box
                          className="mb-12 mx-auto w-12 h-12 flex items-center justify-center rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          className="text-on-surface font-medium mb-4"
                          sx={{ fontSize: '1rem' }}
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Pending Requests */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-normal"
                  sx={{ fontSize: '1.5rem' }}
                >
                  Pending Requests
                </Typography>
                <Box className="flex items-center gap-8">
                  <ViewToggle view={view} onViewChange={setView} />
                  <Chip
                    label={pendingRequests.length}
                    color="warning"
                    size="small"
                  />
                </Box>
              </Box>

              {view === 'list' ? (
                renderRequestsListView()
              ) : (
                <Box className="space-y-12">
                  {pendingRequests.map((request) => (
                    <Box
                      key={request.id}
                      className="p-12 rounded-lg border"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        borderColor: 'var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <Typography
                        variant="body2"
                        className="text-on-surface font-medium mb-4"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {request.employee}
                      </Typography>
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant mb-8 block"
                      >
                        {request.type} • {request.date}
                      </Typography>
                      <Chip
                        label={request.status}
                        color={getStatusColor(request.status) as any}
                        size="small"
                        variant="filled"
                      />
                    </Box>
                  ))}
                </Box>
              )}

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View All Requests
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
