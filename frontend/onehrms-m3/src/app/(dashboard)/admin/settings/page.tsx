/**
 * Admin Settings & Configuration Page
 * System administration and configuration dashboard
 */

'use client';

import { Box, Typography, Grid, Card, CardContent, Button, Chip, Switch } from '@mui/material';
import {
  Settings,
  Business,
  Security,
  Link as Integration,
  Notifications,
  Palette,
  Language,
  Storage,
} from '@mui/icons-material';

// Mock settings data
const settingsCategories = [
  {
    title: 'Company Settings',
    description: 'Organization profile and business configuration',
    icon: <Business className="w-6 h-6" />,
    color: 'primary',
    settings: ['Company Profile', 'Locations', 'Departments', 'Holidays']
  },
  {
    title: 'System Settings',
    description: 'Application behavior and preferences',
    icon: <Settings className="w-6 h-6" />,
    color: 'success',
    settings: ['General', 'Email Templates', 'Workflows', 'Notifications']
  },
  {
    title: 'Security',
    description: 'Access control and security policies',
    icon: <Security className="w-6 h-6" />,
    color: 'error',
    settings: ['User Roles', 'Permissions', 'Password Policy', 'Audit Logs']
  },
  {
    title: 'Integrations',
    description: 'Third-party services and API connections',
    icon: <Integration className="w-6 h-6" />,
    color: 'info',
    settings: ['Payroll Systems', 'Time Tracking', 'Email Services', 'SSO']
  },
];

const systemStatus = [
  { name: 'Email Notifications', status: true, description: 'Send system notifications via email' },
  { name: 'Auto Backup', status: true, description: 'Automatic daily data backups' },
  { name: 'Maintenance Mode', status: false, description: 'Enable system maintenance mode' },
  { name: 'Debug Mode', status: false, description: 'Enable detailed error logging' },
];

const recentChanges = [
  {
    id: 1,
    setting: 'Password Policy Updated',
    changedBy: 'Admin User',
    date: '2024-01-15',
    type: 'security'
  },
  {
    id: 2,
    setting: 'Email Template Modified',
    changedBy: 'Sarah Johnson',
    date: '2024-01-14',
    type: 'system'
  },
  {
    id: 3,
    setting: 'New Department Added',
    changedBy: 'Michael Chen',
    date: '2024-01-13',
    type: 'company'
  },
];

const quickActions = [
  { title: 'Backup Data', description: 'Create system backup', icon: <Storage /> },
  { title: 'User Management', description: 'Manage user accounts', icon: <Security /> },
  { title: 'System Health', description: 'Check system status', icon: <Settings /> },
];

export default function AdminSettingsPage() {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'security':
        return 'error';
      case 'system':
        return 'success';
      case 'company':
        return 'primary';
      case 'integration':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Settings & Configuration
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Configure system settings, manage security, and customize your HRMS experience.
        </Typography>
      </Box>

      {/* Settings Categories */}
      <Grid container spacing={3} className="mb-32">
        {settingsCategories.map((category, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${category.color}-container)`,
                      color: `var(--md-sys-color-on-${category.color}-container)`,
                    }}
                  >
                    {category.icon}
                  </Box>
                </Box>

                <Typography
                  variant="h6"
                  className="text-on-surface font-medium mb-8"
                  sx={{ fontSize: '1.125rem' }}
                >
                  {category.title}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-12"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {category.description}
                </Typography>

                <Box className="space-y-4">
                  {category.settings.map((setting, idx) => (
                    <Typography
                      key={idx}
                      variant="caption"
                      className="text-on-surface-variant block"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      • {setting}
                    </Typography>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* System Status */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                System Status
              </Typography>

              <Box className="space-y-16">
                {systemStatus.map((item, index) => (
                  <Box
                    key={index}
                    className="flex items-center justify-between p-12 rounded-lg"
                    sx={{
                      backgroundColor: 'var(--md-sys-color-surface)',
                      border: '1px solid var(--md-sys-color-outline-variant)',
                    }}
                  >
                    <Box className="flex-1">
                      <Typography
                        variant="body2"
                        className="text-on-surface font-medium mb-4"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {item.name}
                      </Typography>
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant"
                      >
                        {item.description}
                      </Typography>
                    </Box>
                    <Switch
                      checked={item.status}
                      color="primary"
                      size="small"
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Box className="space-y-12">
                {quickActions.map((action, index) => (
                  <Card
                    key={index}
                    className="cursor-pointer transition-all duration-200 hover:shadow-elevation-1"
                    sx={{
                      backgroundColor: 'var(--md-sys-color-surface)',
                      border: '1px solid var(--md-sys-color-outline-variant)',
                    }}
                  >
                    <CardContent className="p-16">
                      <Box className="flex items-center gap-12">
                        <Box
                          className="p-8 rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Box>
                          <Typography
                            variant="body2"
                            className="text-on-surface font-medium mb-2"
                            sx={{ fontSize: '0.875rem' }}
                          >
                            {action.title}
                          </Typography>
                          <Typography
                            variant="caption"
                            className="text-on-surface-variant"
                          >
                            {action.description}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Changes */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Recent Changes
              </Typography>

              <Box className="space-y-12">
                {recentChanges.map((change) => (
                  <Box
                    key={change.id}
                    className="p-12 rounded-lg border"
                    sx={{
                      backgroundColor: 'var(--md-sys-color-surface)',
                      borderColor: 'var(--md-sys-color-outline-variant)',
                    }}
                  >
                    <Typography
                      variant="body2"
                      className="text-on-surface font-medium mb-4"
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {change.setting}
                    </Typography>
                    <Typography
                      variant="caption"
                      className="text-on-surface-variant mb-8 block"
                    >
                      By {change.changedBy} • {change.date}
                    </Typography>
                    <Chip
                      label={change.type}
                      color={getTypeColor(change.type) as any}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                ))}
              </Box>

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View Audit Log
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
