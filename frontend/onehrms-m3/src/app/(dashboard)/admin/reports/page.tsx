/**
 * Admin Reports & Analytics Page
 * Comprehensive reporting and analytics dashboard
 */

'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Assessment,
  TrendingUp,
  <PERSON><PERSON>hart,
  Bar<PERSON>hart,
  Download,
  Schedule,
  People,
  AttachMoney,
} from '@mui/icons-material';
import ViewToggle, { ViewType } from '@/components/ui/ViewToggle';

// Mock reports data
const reportCategories = [
  {
    title: 'HR Reports',
    description: 'Employee data and workforce analytics',
    icon: <People className="w-6 h-6" />,
    color: 'primary',
    count: 12,
    reports: ['Employee Directory', 'Attendance Summary', 'Leave Analysis', 'Performance Review']
  },
  {
    title: 'Financial Reports',
    description: 'Payroll and compensation analytics',
    icon: <AttachMoney className="w-6 h-6" />,
    color: 'success',
    count: 8,
    reports: ['Payroll Summary', 'Tax Reports', 'Benefits Cost', 'Salary Analysis']
  },
  {
    title: 'Compliance Reports',
    description: 'Regulatory and audit reports',
    icon: <Assessment className="w-6 h-6" />,
    color: 'warning',
    count: 6,
    reports: ['Labor Law Compliance', 'Safety Reports', 'Audit Trail', 'Policy Adherence']
  },
  {
    title: 'Custom Reports',
    description: 'User-defined analytics and insights',
    icon: <BarChart className="w-6 h-6" />,
    color: 'info',
    count: 15,
    reports: ['Department Metrics', 'Turnover Analysis', 'Recruitment Funnel', 'Training ROI']
  },
];

const recentReports = [
  {
    id: 1,
    name: 'Monthly Attendance Report',
    type: 'HR',
    generatedBy: 'Sarah Johnson',
    date: '2024-01-15',
    status: 'completed',
    downloads: 23
  },
  {
    id: 2,
    name: 'Payroll Summary Q4 2023',
    type: 'Financial',
    generatedBy: 'Michael Chen',
    date: '2024-01-14',
    status: 'completed',
    downloads: 45
  },
  {
    id: 3,
    name: 'Employee Turnover Analysis',
    type: 'Custom',
    generatedBy: 'Emily Rodriguez',
    date: '2024-01-13',
    status: 'processing',
    downloads: 0
  },
];

const quickActions = [
  { title: 'Generate Report', description: 'Create new analytics report', icon: <Assessment /> },
  { title: 'Schedule Reports', description: 'Automate report generation', icon: <Schedule /> },
  { title: 'Export Data', description: 'Download raw data exports', icon: <Download /> },
];

export default function AdminReportsPage() {
  const [view, setView] = React.useState<ViewType>('grid');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'HR':
        return 'primary';
      case 'Financial':
        return 'success';
      case 'Compliance':
        return 'warning';
      case 'Custom':
        return 'info';
      default:
        return 'default';
    }
  };

  const renderReportsListView = () => (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: 'var(--md-sys-color-surface)',
        border: '1px solid var(--md-sys-color-outline-variant)',
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Report Name
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Type
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Generated By
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Date
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Status
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Downloads
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {recentReports.map((report) => (
            <TableRow
              key={report.id}
              sx={{
                '&:hover': {
                  backgroundColor: 'var(--md-sys-color-surface-container-high)',
                },
              }}
            >
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface font-medium"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {report.name}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={report.type}
                  color={getTypeColor(report.type) as any}
                  size="small"
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {report.generatedBy}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {report.date}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={report.status}
                  color={getStatusColor(report.status) as any}
                  size="small"
                  variant="filled"
                />
              </TableCell>
              <TableCell>
                <Box className="flex items-center gap-4">
                  <Download className="w-4 h-4 text-on-surface-variant" />
                  <Typography
                    variant="body2"
                    className="text-on-surface"
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {report.downloads}
                  </Typography>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Reports & Analytics
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Generate insights, track metrics, and create comprehensive reports for data-driven decisions.
        </Typography>
      </Box>

      {/* Report Categories */}
      <Grid container spacing={3} className="mb-32">
        {reportCategories.map((category, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${category.color}-container)`,
                      color: `var(--md-sys-color-on-${category.color}-container)`,
                    }}
                  >
                    {category.icon}
                  </Box>
                  <Chip
                    label={category.count}
                    size="small"
                    color={category.color as any}
                    variant="filled"
                  />
                </Box>

                <Typography
                  variant="h6"
                  className="text-on-surface font-medium mb-8"
                  sx={{ fontSize: '1.125rem' }}
                >
                  {category.title}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-12"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {category.description}
                </Typography>

                <Box className="space-y-4">
                  {category.reports.slice(0, 3).map((report, idx) => (
                    <Typography
                      key={idx}
                      variant="caption"
                      className="text-on-surface-variant block"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      • {report}
                    </Typography>
                  ))}
                  {category.reports.length > 3 && (
                    <Typography
                      variant="caption"
                      className="text-primary"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      +{category.reports.length - 3} more
                    </Typography>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        border: '1px solid var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <CardContent className="p-16 text-center">
                        <Box
                          className="mb-12 mx-auto w-12 h-12 flex items-center justify-center rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          className="text-on-surface font-medium mb-4"
                          sx={{ fontSize: '1rem' }}
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Reports */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-normal"
                  sx={{ fontSize: '1.5rem' }}
                >
                  Recent Reports
                </Typography>
                <ViewToggle view={view} onViewChange={setView} />
              </Box>

              {view === 'list' ? (
                renderReportsListView()
              ) : (
                <Box className="space-y-12">
                  {recentReports.map((report) => (
                    <Box
                      key={report.id}
                      className="p-12 rounded-lg border"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        borderColor: 'var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <Typography
                        variant="body2"
                        className="text-on-surface font-medium mb-4"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {report.name}
                      </Typography>
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant mb-8 block"
                      >
                        By {report.generatedBy} • {report.date}
                      </Typography>
                      <Box className="flex items-center justify-between">
                        <Box className="flex items-center gap-8">
                          <Chip
                            label={report.type}
                            color={getTypeColor(report.type) as any}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            label={report.status}
                            color={getStatusColor(report.status) as any}
                            size="small"
                            variant="filled"
                          />
                        </Box>
                        {report.downloads > 0 && (
                          <Box className="flex items-center gap-4">
                            <Download className="w-4 h-4 text-on-surface-variant" />
                            <Typography variant="caption" className="text-on-surface-variant">
                              {report.downloads}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  ))}
                </Box>
              )}

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View All Reports
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
