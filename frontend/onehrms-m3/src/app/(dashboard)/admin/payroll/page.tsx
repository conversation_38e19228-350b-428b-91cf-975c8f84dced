/**
 * Admin Payroll Management Page
 * Payroll processing and salary management dashboard
 */

'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Receipt,
  AttachMoney,
  TrendingUp,
  Schedule,
  CheckCircle,
  Warning,
  Assessment,
  AccountBalance,
} from '@mui/icons-material';
import ViewToggle, { ViewType } from '@/components/ui/ViewToggle';

// Mock payroll data
const payrollMetrics = [
  {
    title: 'Monthly Payroll',
    value: '$2.4M',
    change: '****%',
    changeType: 'increase' as const,
    icon: <AttachMoney className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'Processed This Month',
    value: '1,247',
    total: '1,247',
    percentage: 100,
    change: 'Complete',
    changeType: 'increase' as const,
    icon: <CheckCircle className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'Pending Approvals',
    value: '12',
    change: '-3',
    changeType: 'decrease' as const,
    icon: <Schedule className="w-6 h-6" />,
    color: 'warning',
  },
  {
    title: 'Tax Deductions',
    value: '$485K',
    change: '+2.1%',
    changeType: 'increase' as const,
    icon: <AccountBalance className="w-6 h-6" />,
    color: 'info',
  },
];

const recentPayrolls = [
  { id: 1, period: 'January 2024', employees: 1247, amount: '$2.4M', status: 'completed', date: '2024-01-31' },
  { id: 2, period: 'December 2023', employees: 1235, amount: '$2.3M', status: 'completed', date: '2023-12-31' },
  { id: 3, period: 'November 2023', employees: 1228, amount: '$2.2M', status: 'completed', date: '2023-11-30' },
];

const quickActions = [
  { title: 'Process Payroll', description: 'Run monthly payroll processing', icon: <Receipt /> },
  { title: 'Generate Reports', description: 'Create payroll reports', icon: <Assessment /> },
  { title: 'Salary Structures', description: 'Manage compensation plans', icon: <TrendingUp /> },
];

export default function AdminPayrollPage() {
  const [view, setView] = React.useState<ViewType>('grid');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'pending':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderPayrollsListView = () => (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: 'var(--md-sys-color-surface)',
        border: '1px solid var(--md-sys-color-outline-variant)',
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Period
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Employees
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Amount
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Status
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Date
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {recentPayrolls.map((payroll) => (
            <TableRow
              key={payroll.id}
              sx={{
                '&:hover': {
                  backgroundColor: 'var(--md-sys-color-surface-container-high)',
                },
              }}
            >
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface font-medium"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {payroll.period}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {payroll.employees}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {payroll.amount}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={payroll.status}
                  color={getStatusColor(payroll.status) as any}
                  size="small"
                  variant="filled"
                />
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {payroll.date}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Payroll Management
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Manage employee compensation, process payroll, and track financial metrics.
        </Typography>
      </Box>

      {/* Payroll Metrics */}
      <Grid container spacing={3} className="mb-32">
        {payrollMetrics.map((metric, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${metric.color}-container)`,
                      color: `var(--md-sys-color-on-${metric.color}-container)`,
                    }}
                  >
                    {metric.icon}
                  </Box>
                </Box>

                <Typography
                  variant="h4"
                  className="text-on-surface font-normal mb-4"
                  sx={{ fontSize: '1.75rem', fontWeight: 500 }}
                >
                  {metric.value}
                  {metric.total && (
                    <Typography component="span" variant="body2" className="text-on-surface-variant ml-4">
                      / {metric.total}
                    </Typography>
                  )}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-8"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {metric.title}
                </Typography>

                {metric.percentage && (
                  <Box className="mb-8">
                    <LinearProgress
                      variant="determinate"
                      value={metric.percentage}
                      className="h-4 rounded-full"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface-variant)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: `var(--md-sys-color-${metric.color})`,
                        },
                      }}
                    />
                  </Box>
                )}

                <Box className="flex items-center gap-4">
                  <Chip
                    label={metric.change}
                    size="small"
                    color={metric.changeType === 'increase' ? 'success' : 'warning'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        border: '1px solid var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <CardContent className="p-16 text-center">
                        <Box
                          className="mb-12 mx-auto w-12 h-12 flex items-center justify-center rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          className="text-on-surface font-medium mb-4"
                          sx={{ fontSize: '1rem' }}
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Payrolls */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-normal"
                  sx={{ fontSize: '1.5rem' }}
                >
                  Recent Payrolls
                </Typography>
                <ViewToggle view={view} onViewChange={setView} />
              </Box>

              {view === 'list' ? (
                renderPayrollsListView()
              ) : (
                <Box className="space-y-12">
                  {recentPayrolls.map((payroll) => (
                    <Box
                      key={payroll.id}
                      className="p-12 rounded-lg border"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        borderColor: 'var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <Typography
                        variant="body2"
                        className="text-on-surface font-medium mb-4"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {payroll.period}
                      </Typography>
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant mb-8 block"
                      >
                        {payroll.employees} employees • {payroll.amount}
                      </Typography>
                      <Box className="flex items-center justify-between">
                        <Chip
                          label={payroll.status}
                          color={getStatusColor(payroll.status) as any}
                          size="small"
                          variant="filled"
                        />
                        <Typography variant="caption" className="text-on-surface-variant">
                          {payroll.date}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              )}

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View All Payrolls
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
