/**
 * Admin Recruitment Management Page
 * Job posting and candidate management dashboard
 */

'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  Rating,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Work,
  People,
  TrendingUp,
  Schedule,
  Star,
  Business,
  Assessment,
  PersonAdd,
} from '@mui/icons-material';
import ViewToggle, { ViewType } from '@/components/ui/ViewToggle';

// Mock recruitment data
const recruitmentMetrics = [
  {
    title: 'Open Positions',
    value: '23',
    change: '+5',
    changeType: 'increase' as const,
    icon: <Work className="w-6 h-6" />,
    color: 'primary',
  },
  {
    title: 'Active Applications',
    value: '156',
    change: '+12',
    changeType: 'increase' as const,
    icon: <People className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'Interviews Scheduled',
    value: '34',
    change: '+8',
    changeType: 'increase' as const,
    icon: <Schedule className="w-6 h-6" />,
    color: 'warning',
  },
  {
    title: 'Hires This Month',
    value: '12',
    change: '+3',
    changeType: 'increase' as const,
    icon: <PersonAdd className="w-6 h-6" />,
    color: 'info',
  },
];

const recentApplications = [
  {
    id: 1,
    name: 'Alex Thompson',
    position: 'Senior Developer',
    department: 'Engineering',
    status: 'interview',
    rating: 4.5,
    appliedDate: '2024-01-15'
  },
  {
    id: 2,
    name: 'Maria Garcia',
    position: 'Marketing Manager',
    department: 'Marketing',
    status: 'review',
    rating: 4.2,
    appliedDate: '2024-01-14'
  },
  {
    id: 3,
    name: 'David Kim',
    position: 'UX Designer',
    department: 'Design',
    status: 'shortlisted',
    rating: 4.8,
    appliedDate: '2024-01-13'
  },
];

const quickActions = [
  { title: 'Post New Job', description: 'Create job requisitions', icon: <Work /> },
  { title: 'Review Applications', description: 'Screen candidates', icon: <Assessment /> },
  { title: 'Schedule Interviews', description: 'Manage interview calendar', icon: <Schedule /> },
];

export default function AdminRecruitmentPage() {
  const [view, setView] = React.useState<ViewType>('grid');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'interview':
        return 'warning';
      case 'review':
        return 'info';
      case 'shortlisted':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'interview':
        return 'Interview';
      case 'review':
        return 'Under Review';
      case 'shortlisted':
        return 'Shortlisted';
      case 'rejected':
        return 'Rejected';
      default:
        return status;
    }
  };

  const renderApplicationsListView = () => (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: 'var(--md-sys-color-surface)',
        border: '1px solid var(--md-sys-color-outline-variant)',
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Candidate
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Position
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Department
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Rating
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Status
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Applied
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {recentApplications.map((application) => (
            <TableRow
              key={application.id}
              sx={{
                '&:hover': {
                  backgroundColor: 'var(--md-sys-color-surface-container-high)',
                },
              }}
            >
              <TableCell>
                <Box className="flex items-center gap-8">
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: 'var(--md-sys-color-primary)',
                      color: 'var(--md-sys-color-on-primary)',
                      fontSize: '0.75rem',
                    }}
                  >
                    {application.name.split(' ').map(n => n[0]).join('')}
                  </Avatar>
                  <Typography
                    variant="body2"
                    className="text-on-surface font-medium"
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {application.name}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {application.position}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {application.department}
                </Typography>
              </TableCell>
              <TableCell>
                <Box className="flex items-center gap-4">
                  <Rating value={application.rating} readOnly size="small" />
                  <Typography variant="caption" className="text-on-surface-variant">
                    {application.rating}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Chip
                  label={getStatusLabel(application.status)}
                  color={getStatusColor(application.status) as any}
                  size="small"
                  variant="filled"
                />
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {application.appliedDate}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Recruitment Management
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Manage job postings, track applications, and streamline hiring processes.
        </Typography>
      </Box>

      {/* Recruitment Metrics */}
      <Grid container spacing={3} className="mb-32">
        {recruitmentMetrics.map((metric, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${metric.color}-container)`,
                      color: `var(--md-sys-color-on-${metric.color}-container)`,
                    }}
                  >
                    {metric.icon}
                  </Box>
                </Box>

                <Typography
                  variant="h4"
                  className="text-on-surface font-normal mb-4"
                  sx={{ fontSize: '1.75rem', fontWeight: 500 }}
                >
                  {metric.value}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-8"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {metric.title}
                </Typography>

                <Box className="flex items-center gap-4">
                  <Chip
                    label={metric.change}
                    size="small"
                    color={metric.changeType === 'increase' ? 'success' : 'error'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        border: '1px solid var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <CardContent className="p-16 text-center">
                        <Box
                          className="mb-12 mx-auto w-12 h-12 flex items-center justify-center rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          className="text-on-surface font-medium mb-4"
                          sx={{ fontSize: '1rem' }}
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Applications */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-normal"
                  sx={{ fontSize: '1.5rem' }}
                >
                  Recent Applications
                </Typography>
                <Box className="flex items-center gap-8">
                  <ViewToggle view={view} onViewChange={setView} />
                  <Chip
                    label={recentApplications.length}
                    color="primary"
                    size="small"
                  />
                </Box>
              </Box>

              {view === 'list' ? (
                renderApplicationsListView()
              ) : (
                <Box className="space-y-12">
                  {recentApplications.map((application) => (
                    <Box
                      key={application.id}
                      className="p-12 rounded-lg border"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        borderColor: 'var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <Box className="flex items-start gap-12 mb-8">
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            backgroundColor: 'var(--md-sys-color-primary)',
                            color: 'var(--md-sys-color-on-primary)',
                            fontSize: '0.875rem',
                          }}
                        >
                          {application.name.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <Box className="flex-1">
                          <Typography
                            variant="body2"
                            className="text-on-surface font-medium mb-2"
                            sx={{ fontSize: '0.875rem' }}
                          >
                            {application.name}
                          </Typography>
                          <Typography
                            variant="caption"
                            className="text-on-surface-variant mb-4 block"
                          >
                            {application.position} • {application.department}
                          </Typography>
                          <Box className="flex items-center justify-between">
                            <Box className="flex items-center gap-4">
                              <Star className="w-4 h-4 text-warning" />
                              <Typography variant="caption" className="text-on-surface-variant">
                                {application.rating}
                              </Typography>
                            </Box>
                            <Chip
                              label={getStatusLabel(application.status)}
                              color={getStatusColor(application.status) as any}
                              size="small"
                              variant="filled"
                            />
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  ))}
                </Box>
              )}

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View All Applications
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
