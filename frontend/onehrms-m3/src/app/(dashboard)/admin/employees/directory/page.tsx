/**
 * Admin Employee Directory Page
 * Complete employee directory with search and management
 */

'use client';

import React from 'react';

import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  IconButton,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  People,
  Add,
  Search,
  FilterList,
  MoreVert,
  Email,
  Phone,
  LocationOn,
  Work,
  CalendarToday,
} from '@mui/icons-material';
import { cn } from '@/lib/utils';
import ViewToggle, { ViewType } from '@/components/ui/ViewToggle';

// Mock employee data
const employees = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Marketing',
    position: 'Marketing Manager',
    location: 'New York',
    status: 'Active',
    joinDate: '2023-01-15',
    avatar: null,
  },
  {
    id: 2,
    name: '<PERSON>',
    email: 'micha<PERSON>.<EMAIL>',
    phone: '+****************',
    department: 'Engineering',
    position: 'Senior Developer',
    location: 'San Francisco',
    status: 'Active',
    joinDate: '2022-08-20',
    avatar: null,
  },
  {
    id: 3,
    name: 'Emily <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Human Resources',
    position: 'HR Specialist',
    location: 'Chicago',
    status: 'Active',
    joinDate: '2023-03-10',
    avatar: null,
  },
  {
    id: 4,
    name: 'David Kim',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Finance',
    position: 'Financial Analyst',
    location: 'Boston',
    status: 'On Leave',
    joinDate: '2022-11-05',
    avatar: null,
  },
  {
    id: 5,
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Sales',
    position: 'Sales Director',
    location: 'Los Angeles',
    status: 'Active',
    joinDate: '2021-06-12',
    avatar: null,
  },
  {
    id: 6,
    name: 'James Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Operations',
    position: 'Operations Manager',
    location: 'Seattle',
    status: 'Active',
    joinDate: '2022-02-28',
    avatar: null,
  },
];

const departmentStats = [
  { name: 'Engineering', count: 45, color: 'primary' },
  { name: 'Marketing', count: 23, color: 'success' },
  { name: 'Sales', count: 31, color: 'warning' },
  { name: 'HR', count: 12, color: 'info' },
  { name: 'Finance', count: 18, color: 'error' },
];

export default function AdminEmployeeDirectoryPage() {
  const [view, setView] = React.useState<ViewType>('grid');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'success';
      case 'On Leave':
        return 'warning';
      case 'Inactive':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderListView = () => (
    <TableContainer
      component={Paper}
      sx={{
        backgroundColor: 'var(--md-sys-color-surface-container)',
        border: '1px solid var(--md-sys-color-outline-variant)',
      }}
    >
      <Table>
        <TableHead>
          <TableRow>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Employee
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Position
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Department
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Location
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Status
            </TableCell>
            <TableCell sx={{ color: 'var(--md-sys-color-on-surface)', fontWeight: 500 }}>
              Actions
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {employees.map((employee) => (
            <TableRow
              key={employee.id}
              sx={{
                '&:hover': {
                  backgroundColor: 'var(--md-sys-color-surface-container-high)',
                },
              }}
            >
              <TableCell>
                <Box className="flex items-center gap-12">
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      backgroundColor: 'var(--md-sys-color-primary)',
                      color: 'var(--md-sys-color-on-primary)',
                    }}
                  >
                    {employee.name.split(' ').map(n => n[0]).join('')}
                  </Avatar>
                  <Box>
                    <Typography
                      variant="body1"
                      className="text-on-surface font-medium"
                      sx={{ fontSize: '0.875rem', fontWeight: 500 }}
                    >
                      {employee.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      className="text-on-surface-variant"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      {employee.email}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {employee.position}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {employee.department}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  className="text-on-surface"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {employee.location}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={employee.status}
                  color={getStatusColor(employee.status) as any}
                  size="small"
                  variant="filled"
                />
              </TableCell>
              <TableCell>
                <Box className="flex gap-4">
                  <IconButton size="small" className="text-on-surface-variant">
                    <Email className="w-4 h-4" />
                  </IconButton>
                  <IconButton size="small" className="text-on-surface-variant">
                    <Phone className="w-4 h-4" />
                  </IconButton>
                  <IconButton size="small" className="text-on-surface-variant">
                    <MoreVert className="w-4 h-4" />
                  </IconButton>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Box className="flex items-center justify-between mb-12">
          <Typography
            variant="h4"
            className="text-on-surface font-normal"
            sx={{
              color: 'var(--md-sys-color-on-surface)',
              fontWeight: 400,
              fontSize: '2rem',
              lineHeight: 1.2,
            }}
          >
            Employee Directory
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            className="bg-primary text-on-primary"
            sx={{
              backgroundColor: 'var(--md-sys-color-primary)',
              color: 'var(--md-sys-color-on-primary)',
              '&:hover': {
                backgroundColor: 'var(--md-sys-color-primary-container)',
              },
            }}
          >
            Add Employee
          </Button>
        </Box>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Search, filter, and manage all employees in your organization.
        </Typography>
      </Box>

      {/* Search and Filters */}
      <Box className="mb-24">
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search employees..."
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search className="text-on-surface-variant" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'var(--md-sys-color-surface-container)',
                  '& fieldset': {
                    borderColor: 'var(--md-sys-color-outline-variant)',
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box className="flex gap-12 justify-end items-center">
              <ViewToggle view={view} onViewChange={setView} />
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                className="border-outline text-on-surface"
              >
                Filter
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Department Stats */}
      <Grid container spacing={3} className="mb-32">
        {departmentStats.map((dept, index) => (
          <Grid item xs={12} sm={6} lg={2.4} key={index}>
            <Card
              className="content-card h-full"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20 text-center">
                <Typography
                  variant="h6"
                  className="text-on-surface font-medium mb-8"
                  sx={{ fontSize: '1.25rem', fontWeight: 500 }}
                >
                  {dept.count}
                </Typography>
                <Typography
                  variant="body2"
                  className="text-on-surface-variant"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {dept.name}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Employee Display */}
      {view === 'list' ? (
        renderListView()
      ) : (
        <Grid container spacing={3}>
          {employees.map((employee) => (
          <Grid item xs={12} sm={6} lg={4} key={employee.id}>
            <Card
              className="content-card h-full transition-all duration-200 ease-in-out hover:shadow-elevation-2"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-24">
                {/* Employee Header */}
                <Box className="flex items-start justify-between mb-16">
                  <Box className="flex items-center gap-12">
                    <Avatar
                      sx={{
                        width: 48,
                        height: 48,
                        backgroundColor: 'var(--md-sys-color-primary)',
                        color: 'var(--md-sys-color-on-primary)',
                      }}
                    >
                      {employee.name.split(' ').map(n => n[0]).join('')}
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h6"
                        className="text-on-surface font-medium"
                        sx={{ fontSize: '1.125rem', fontWeight: 500 }}
                      >
                        {employee.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        className="text-on-surface-variant"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {employee.position}
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton size="small" className="text-on-surface-variant">
                    <MoreVert className="w-4 h-4" />
                  </IconButton>
                </Box>

                {/* Employee Details */}
                <Box className="space-y-12 mb-16">
                  <Box className="flex items-center gap-8">
                    <Work className="w-4 h-4 text-on-surface-variant" />
                    <Typography
                      variant="body2"
                      className="text-on-surface-variant"
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {employee.department}
                    </Typography>
                  </Box>
                  <Box className="flex items-center gap-8">
                    <LocationOn className="w-4 h-4 text-on-surface-variant" />
                    <Typography
                      variant="body2"
                      className="text-on-surface-variant"
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {employee.location}
                    </Typography>
                  </Box>
                  <Box className="flex items-center gap-8">
                    <CalendarToday className="w-4 h-4 text-on-surface-variant" />
                    <Typography
                      variant="body2"
                      className="text-on-surface-variant"
                      sx={{ fontSize: '0.875rem' }}
                    >
                      Joined {new Date(employee.joinDate).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Box>

                {/* Status and Actions */}
                <Box className="flex items-center justify-between">
                  <Chip
                    label={employee.status}
                    color={getStatusColor(employee.status) as any}
                    size="small"
                    variant="filled"
                  />
                  <Box className="flex gap-8">
                    <IconButton size="small" className="text-on-surface-variant">
                      <Email className="w-4 h-4" />
                    </IconButton>
                    <IconButton size="small" className="text-on-surface-variant">
                      <Phone className="w-4 h-4" />
                    </IconButton>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
}
