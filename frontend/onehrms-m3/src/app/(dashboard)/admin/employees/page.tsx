/**
 * Admin Employees Overview Dashboard
 * Employee management overview and key metrics
 */

'use client';

import { Box, Typography, Grid, Card, CardContent, Button, Chip, LinearProgress } from '@mui/material';
import {
  People,
  PersonAdd,
  TrendingUp,
  Work,
  School,
  ExitToApp,
  Assessment,
  Group,
} from '@mui/icons-material';
import { cn } from '@/lib/utils';

// Mock employee metrics data
const employeeMetrics = [
  {
    title: 'Total Employees',
    value: '1,247',
    change: '+23',
    changeType: 'increase' as const,
    icon: <People className="w-6 h-6" />,
    color: 'primary',
  },
  {
    title: 'New Hires This Month',
    value: '12',
    change: '+3',
    changeType: 'increase' as const,
    icon: <PersonAdd className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'Pending Onboarding',
    value: '8',
    change: '-2',
    changeType: 'decrease' as const,
    icon: <School className="w-6 h-6" />,
    color: 'warning',
  },
  {
    title: 'Resignations This Month',
    value: '5',
    change: '+1',
    changeType: 'increase' as const,
    icon: <ExitToApp className="w-6 h-6" />,
    color: 'error',
  },
];

const departmentStats = [
  { name: 'Engineering', count: 45, percentage: 36.1, color: 'primary' },
  { name: 'Marketing', count: 23, percentage: 18.5, color: 'success' },
  { name: 'Sales', count: 31, percentage: 24.9, color: 'warning' },
  { name: 'HR', count: 12, percentage: 9.6, color: 'info' },
  { name: 'Finance', count: 18, percentage: 14.5, color: 'error' },
];

const recentActivities = [
  { id: 1, activity: 'Sarah Johnson joined Marketing team', type: 'hire', date: '2024-01-15' },
  { id: 2, activity: 'Michael Chen completed onboarding', type: 'onboarding', date: '2024-01-14' },
  { id: 3, activity: 'Emily Rodriguez submitted resignation', type: 'resignation', date: '2024-01-13' },
  { id: 4, activity: 'David Kim started leave of absence', type: 'leave', date: '2024-01-12' },
];

const quickActions = [
  { title: 'Employee Directory', description: 'View and manage all employees', icon: <People /> },
  { title: 'Add New Employee', description: 'Onboard new team members', icon: <PersonAdd /> },
  { title: 'Generate Reports', description: 'Create employee reports', icon: <Assessment /> },
];

export default function AdminEmployeesPage() {
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'hire':
        return 'success';
      case 'onboarding':
        return 'info';
      case 'resignation':
        return 'error';
      case 'leave':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Employee Management
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Monitor workforce metrics, track employee lifecycle, and manage organizational structure.
        </Typography>
      </Box>

      {/* Employee Metrics */}
      <Grid container spacing={3} className="mb-32">
        {employeeMetrics.map((metric, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className="content-card h-full"
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-20">
                <Box className="flex items-center justify-between mb-12">
                  <Box
                    className="p-12 rounded-full"
                    sx={{
                      backgroundColor: `var(--md-sys-color-${metric.color}-container)`,
                      color: `var(--md-sys-color-on-${metric.color}-container)`,
                    }}
                  >
                    {metric.icon}
                  </Box>
                </Box>

                <Typography
                  variant="h4"
                  className="text-on-surface font-normal mb-4"
                  sx={{ fontSize: '1.75rem', fontWeight: 500 }}
                >
                  {metric.value}
                </Typography>

                <Typography
                  variant="body2"
                  className="text-on-surface-variant mb-8"
                  sx={{ fontSize: '0.875rem' }}
                >
                  {metric.title}
                </Typography>

                <Box className="flex items-center gap-4">
                  <Chip
                    label={metric.change}
                    size="small"
                    color={metric.changeType === 'increase' ? 'success' : 'error'}
                    variant="filled"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Department Distribution */}
      <Grid container spacing={3} className="mb-32">
        <Grid item xs={12}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Department Distribution
              </Typography>

              <Grid container spacing={3}>
                {departmentStats.map((dept, index) => (
                  <Grid item xs={12} sm={6} md={2.4} key={index}>
                    <Box className="text-center">
                      <Typography
                        variant="h6"
                        className="text-on-surface font-medium mb-8"
                        sx={{ fontSize: '1.25rem', fontWeight: 500 }}
                      >
                        {dept.count}
                      </Typography>
                      <Typography
                        variant="body2"
                        className="text-on-surface-variant mb-8"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {dept.name}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={dept.percentage}
                        className="h-4 rounded-full"
                        sx={{
                          backgroundColor: 'var(--md-sys-color-surface-variant)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: `var(--md-sys-color-${dept.color})`,
                          },
                        }}
                      />
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant mt-4 block"
                      >
                        {dept.percentage}%
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:shadow-elevation-2"
                      sx={{
                        backgroundColor: 'var(--md-sys-color-surface)',
                        border: '1px solid var(--md-sys-color-outline-variant)',
                      }}
                    >
                      <CardContent className="p-16 text-center">
                        <Box
                          className="mb-12 mx-auto w-12 h-12 flex items-center justify-center rounded-full"
                          sx={{
                            backgroundColor: 'var(--md-sys-color-primary-container)',
                            color: 'var(--md-sys-color-on-primary-container)',
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          className="text-on-surface font-medium mb-4"
                          sx={{ fontSize: '1rem' }}
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={4}>
          <Card
            className="content-card"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Typography
                variant="h5"
                className="text-on-surface font-normal mb-20"
                sx={{ fontSize: '1.5rem' }}
              >
                Recent Activities
              </Typography>

              <Box className="space-y-12">
                {recentActivities.map((activity) => (
                  <Box
                    key={activity.id}
                    className="p-12 rounded-lg border"
                    sx={{
                      backgroundColor: 'var(--md-sys-color-surface)',
                      borderColor: 'var(--md-sys-color-outline-variant)',
                    }}
                  >
                    <Typography
                      variant="body2"
                      className="text-on-surface font-medium mb-4"
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {activity.activity}
                    </Typography>
                    <Box className="flex items-center justify-between">
                      <Chip
                        label={activity.type}
                        color={getActivityColor(activity.type) as any}
                        size="small"
                        variant="outlined"
                      />
                      <Typography variant="caption" className="text-on-surface-variant">
                        {activity.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>

              <Button
                variant="outlined"
                fullWidth
                className="mt-16"
                sx={{
                  borderColor: 'var(--md-sys-color-outline)',
                  color: 'var(--md-sys-color-primary)',
                }}
              >
                View All Activities
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
