/**
 * Admin Dashboard Page
 * Organization management dashboard
 */

'use client';

import { Box, Typography, Grid, Card, CardContent, IconButton } from '@mui/material';
import {
  People,
  Schedule,
  TrendingUp,
  Assignment,
  MoreVert,
  ArrowUpward,
  ArrowDownward,
  Business,
  AttachMoney,
  Work,
  Assessment,
} from '@mui/icons-material';
import { cn } from '@/lib/utils';

// Mock data for admin dashboard
const organizationMetrics = [
  {
    title: 'Total Employees',
    value: '1,247',
    change: '+12',
    changeType: 'increase' as const,
    icon: <People className="w-6 h-6" />,
    color: 'primary',
  },
  {
    title: 'Active Today',
    value: '1,156',
    change: '+8',
    changeType: 'increase' as const,
    icon: <Schedule className="w-6 h-6" />,
    color: 'success',
  },
  {
    title: 'This Month Revenue',
    value: '$2.4M',
    change: '+15.3%',
    changeType: 'increase' as const,
    icon: <AttachMoney className="w-6 h-6" />,
    color: 'warning',
  },
  {
    title: 'Open Positions',
    value: '23',
    change: '-3',
    changeType: 'decrease' as const,
    icon: <Work className="w-6 h-6" />,
    color: 'info',
  },
];

const quickActions = [
  {
    title: 'Add New Employee',
    description: 'Onboard a new team member',
    icon: <People className="w-6 h-6" />,
    action: () => console.log('Add employee'),
  },
  {
    title: 'Generate Reports',
    description: 'Create HR and financial reports',
    icon: <Assessment className="w-6 h-6" />,
    action: () => console.log('Generate reports'),
  },
  {
    title: 'Review Applications',
    description: 'Process pending job applications',
    icon: <Assignment className="w-6 h-6" />,
    action: () => console.log('Review applications'),
  },
];

const recentActivities = [
  {
    title: 'New employee onboarded',
    description: 'Sarah Johnson joined Marketing team',
    time: '2 hours ago',
    type: 'success',
  },
  {
    title: 'Payroll processed',
    description: 'Monthly payroll for 1,247 employees',
    time: '4 hours ago',
    type: 'info',
  },
  {
    title: 'Leave request approved',
    description: '15 leave requests processed',
    time: '6 hours ago',
    type: 'warning',
  },
  {
    title: 'Performance review completed',
    description: 'Q4 reviews for Engineering team',
    time: '1 day ago',
    type: 'success',
  },
];

export default function AdminDashboardPage() {
  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          Organization Dashboard
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Monitor your organization's performance and manage HR operations.
        </Typography>
      </Box>

      {/* Organization Metrics */}
      <Grid container spacing={3} className="mb-32">
        {organizationMetrics.map((metric, index) => (
          <Grid item xs={12} sm={6} lg={3} key={index}>
            <Card
              className={cn(
                'kpi-card h-full transition-all duration-200 ease-in-out',
                'hover:shadow-elevation-2 hover:-translate-y-1'
              )}
              sx={{
                backgroundColor: 'var(--md-sys-color-surface-container)',
                border: '1px solid var(--md-sys-color-outline-variant)',
              }}
            >
              <CardContent className="p-24">
                <Box className="flex items-start justify-between mb-16">
                  <Box
                    className={cn(
                      'p-12 rounded-lg',
                      metric.color === 'primary' && 'bg-primary/10 text-primary',
                      metric.color === 'success' && 'bg-green-100 text-green-600',
                      metric.color === 'warning' && 'bg-orange-100 text-orange-600',
                      metric.color === 'info' && 'bg-blue-100 text-blue-600'
                    )}
                  >
                    {metric.icon}
                  </Box>
                  <IconButton size="small" className="text-on-surface-variant">
                    <MoreVert className="w-4 h-4" />
                  </IconButton>
                </Box>

                <Typography
                  variant="h6"
                  className="text-on-surface font-medium mb-4"
                  sx={{ fontSize: '1.5rem', fontWeight: 500 }}
                >
                  {metric.value}
                </Typography>

                <Box className="flex items-center justify-between">
                  <Typography
                    variant="body2"
                    className="text-on-surface-variant"
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {metric.title}
                  </Typography>

                  <Box
                    className={cn(
                      'flex items-center gap-4 px-8 py-4 rounded-full text-xs font-medium',
                      metric.changeType === 'increase'
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    )}
                  >
                    {metric.changeType === 'increase' ? (
                      <ArrowUpward className="w-3 h-3" />
                    ) : (
                      <ArrowDownward className="w-3 h-3" />
                    )}
                    {metric.change}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} lg={4}>
          <Card
            className="content-card h-full"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-medium"
                  sx={{ fontSize: '1.25rem', fontWeight: 500 }}
                >
                  Quick Actions
                </Typography>
                <IconButton size="small" className="text-on-surface-variant">
                  <MoreVert className="w-4 h-4" />
                </IconButton>
              </Box>

              <Box className="space-y-16">
                {quickActions.map((action, index) => (
                  <Box
                    key={index}
                    className={cn(
                      'p-16 rounded-lg border border-outline-variant',
                      'hover:bg-surface-variant cursor-pointer transition-colors'
                    )}
                    onClick={action.action}
                  >
                    <Box className="flex items-start gap-12">
                      <Box className="p-8 rounded-lg bg-primary/10 text-primary">
                        {action.icon}
                      </Box>
                      <Box className="flex-1">
                        <Typography
                          variant="subtitle2"
                          className="text-on-surface font-medium mb-4"
                        >
                          {action.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="text-on-surface-variant"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {action.description}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} lg={8}>
          <Card
            className="content-card h-full"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography
                  variant="h5"
                  className="text-on-surface font-medium"
                  sx={{ fontSize: '1.25rem', fontWeight: 500 }}
                >
                  Recent Activities
                </Typography>
                <IconButton size="small" className="text-on-surface-variant">
                  <MoreVert className="w-4 h-4" />
                </IconButton>
              </Box>

              <Box className="space-y-16">
                {recentActivities.map((activity, index) => (
                  <Box key={index} className="flex items-start gap-12">
                    <Box
                      className={cn(
                        'w-8 h-8 rounded-full flex items-center justify-center',
                        activity.type === 'success' && 'bg-green-100',
                        activity.type === 'info' && 'bg-blue-100',
                        activity.type === 'warning' && 'bg-orange-100'
                      )}
                    >
                      <Box
                        className={cn(
                          'w-3 h-3 rounded-full',
                          activity.type === 'success' && 'bg-green-500',
                          activity.type === 'info' && 'bg-blue-500',
                          activity.type === 'warning' && 'bg-orange-500'
                        )}
                      />
                    </Box>
                    <Box className="flex-1">
                      <Typography
                        variant="subtitle2"
                        className="text-on-surface font-medium mb-4"
                      >
                        {activity.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        className="text-on-surface-variant mb-4"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        {activity.description}
                      </Typography>
                      <Typography
                        variant="caption"
                        className="text-on-surface-variant"
                        sx={{ fontSize: '0.75rem' }}
                      >
                        {activity.time}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
