/**
 * User Dashboard Page
 * Material Design 3 dashboard with KPI cards and modern layout
 */

'use client';

import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  IconButton,
  Fab,
} from '@mui/material';
import {
  TrendingUp,
  Schedule,
  BeachAccess,
  AttachMoney,
  CheckCircle,
  Warning,
  Add,
  MoreVert,
  PlayArrow,
  Stop,
} from '@mui/icons-material';
import { cn } from '@/lib/utils';

export default function DashboardPage() {
  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-12"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
            lineHeight: 1.2,
          }}
        >
          My Dashboard
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
            lineHeight: 1.5,
          }}
        >
          Welcome back! Here's your daily summary and quick actions.
        </Typography>
      </Box>

      {/* KPI Cards Grid */}
      <Grid container spacing={3} className="mb-32">
        {/* Attendance Status */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-12">
                <Typography
                  variant="body2"
                  className="text-on-surface-variant"
                  sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
                >
                  Today's Status
                </Typography>
                <Schedule
                  className="text-primary"
                  sx={{ color: 'var(--md-sys-color-primary)' }}
                />
              </Box>
              <Typography
                variant="h6"
                className="text-on-surface font-normal mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 400,
                  fontSize: '1.25rem',
                }}
              >
                Checked In
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                9:15 AM • On time
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Leave Balance */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-12">
                <Typography
                  variant="body2"
                  className="text-on-surface-variant"
                  sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
                >
                  Leave Balance
                </Typography>
                <BeachAccess
                  className="text-secondary"
                  sx={{ color: 'var(--md-sys-color-secondary)' }}
                />
              </Box>
              <Typography
                variant="h6"
                className="text-on-surface font-normal mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 400,
                  fontSize: '1.25rem',
                }}
              >
                12 Days
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                Annual leave remaining
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* This Month Salary */}
        <Grid item xs={12} sm={6} md={3}>
          <Card className="kpi-card h-full">
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-12">
                <Typography variant="body2" className="text-on-surface-variant">
                  This Month
                </Typography>
                <AttachMoney className="text-tertiary" />
              </Box>
              <Typography variant="h6" className="text-on-surface font-normal mb-8" sx={{ fontSize: '1.25rem' }}>
                $5,200
              </Typography>
              <Typography variant="caption" className="text-on-surface-variant">
                Gross salary
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Score */}
        <Grid item xs={12} sm={6} md={3}>
          <Card className="kpi-card h-full">
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-12">
                <Typography variant="body2" className="text-on-surface-variant">
                  Performance
                </Typography>
                <TrendingUp className="text-tertiary" />
              </Box>
              <Typography variant="h6" className="text-on-surface font-normal mb-8" sx={{ fontSize: '1.25rem' }}>
                94%
              </Typography>
              <Typography variant="caption" className="text-on-surface-variant">
                This quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Content Cards Grid */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card className="content-card h-full">
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography variant="h5" className="text-on-surface font-normal" sx={{ fontSize: '1.5rem' }}>
                  Quick Actions
                </Typography>
                <IconButton size="small" className="text-on-surface-variant">
                  <MoreVert />
                </IconButton>
              </Box>

              <Box className="flex flex-col gap-12">
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  fullWidth
                  className="justify-start m3-shape-lg py-12"
                  sx={{
                    backgroundColor: 'var(--md-sys-color-primary)',
                    color: 'var(--md-sys-color-on-primary)',
                    '&:hover': {
                      backgroundColor: 'var(--md-sys-color-primary)',
                      opacity: 0.9,
                      transform: 'translateY(-1px)',
                      boxShadow: 'var(--md-sys-elevation-level2)',
                    },
                  }}
                  onClick={() => console.log('Navigate to Apply Leave')}
                >
                  Apply for Leave
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Schedule />}
                  fullWidth
                  className="justify-start m3-shape-lg py-12"
                  sx={{
                    borderColor: 'var(--md-sys-color-outline)',
                    color: 'var(--md-sys-color-primary)',
                    '&:hover': {
                      backgroundColor: 'var(--md-sys-color-primary-container)',
                      borderColor: 'var(--md-sys-color-primary)',
                      transform: 'translateY(-1px)',
                    },
                  }}
                  onClick={() => console.log('Navigate to Timesheet')}
                >
                  View Timesheet
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AttachMoney />}
                  fullWidth
                  className="justify-start m3-shape-lg py-12"
                  sx={{
                    borderColor: 'var(--md-sys-color-outline)',
                    color: 'var(--md-sys-color-primary)',
                    '&:hover': {
                      backgroundColor: 'var(--md-sys-color-primary-container)',
                      borderColor: 'var(--md-sys-color-primary)',
                      transform: 'translateY(-1px)',
                    },
                  }}
                  onClick={() => console.log('Navigate to Payslip')}
                >
                  Download Payslip
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={6}>
          <Card className="content-card h-full">
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-20">
                <Typography variant="h5" className="text-on-surface font-normal" sx={{ fontSize: '1.5rem' }}>
                  Recent Activities
                </Typography>
                <IconButton size="small" className="text-on-surface-variant">
                  <MoreVert />
                </IconButton>
              </Box>

              <Box className="flex flex-col gap-16">
                <Box className="flex items-center gap-16">
                  <Avatar className="w-32 h-32 bg-tertiary-container text-on-tertiary-container">
                    <CheckCircle fontSize="small" />
                  </Avatar>
                  <Box className="flex-1">
                    <Typography variant="body2" className="text-on-surface font-medium">
                      Leave request approved
                    </Typography>
                    <Typography variant="caption" className="text-on-surface-variant">
                      2 hours ago
                    </Typography>
                  </Box>
                  <Chip label="Approved" color="success" size="small" className="m3-shape-sm" />
                </Box>

                <Box className="flex items-center gap-16">
                  <Avatar className="w-32 h-32 bg-error-container text-on-error-container">
                    <Warning fontSize="small" />
                  </Avatar>
                  <Box className="flex-1">
                    <Typography variant="body2" className="text-on-surface font-medium">
                      Timesheet reminder
                    </Typography>
                    <Typography variant="caption" className="text-on-surface-variant">
                      1 day ago
                    </Typography>
                  </Box>
                  <Chip label="Pending" color="warning" size="small" className="m3-shape-sm" />
                </Box>

                <Box className="flex items-center gap-16">
                  <Avatar className="w-32 h-32 bg-primary-container text-on-primary-container">
                    <AttachMoney fontSize="small" />
                  </Avatar>
                  <Box className="flex-1">
                    <Typography variant="body2" className="text-on-surface font-medium">
                      Salary credited
                    </Typography>
                    <Typography variant="caption" className="text-on-surface-variant">
                      3 days ago
                    </Typography>
                  </Box>
                  <Chip label="Completed" color="primary" size="small" className="m3-shape-sm" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* This Week's Attendance */}
        <Grid item xs={12}>
          <Card className="content-card">
            <CardContent className="p-24">
              <Box className="flex items-center justify-between mb-24">
                <Typography variant="h5" className="text-on-surface font-normal" sx={{ fontSize: '1.5rem' }}>
                  This Week's Attendance
                </Typography>
                <Button variant="outlined" size="small" className="m3-shape-lg">
                  View Details
                </Button>
              </Box>

              <Box className="flex flex-col gap-20">
                <Box>
                  <Box className="flex justify-between mb-8">
                    <Typography variant="body2" className="text-on-surface">
                      Hours Worked
                    </Typography>
                    <Typography variant="body2" className="text-on-surface font-medium">
                      32/40 hours
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={80}
                    className="h-8 m3-shape-sm"
                  />
                </Box>

                <Grid container spacing={2}>
                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri'].map((day, index) => (
                    <Grid item xs key={day}>
                      <Box className="text-center">
                        <Typography variant="caption" className="text-on-surface-variant mb-8 block">
                          {day}
                        </Typography>
                        <Box
                          className={cn(
                            'h-40 m3-shape-sm flex flex-col items-center justify-center text-white',
                            index < 3 ? 'bg-tertiary' : index === 3 ? 'bg-error' : 'bg-outline'
                          )}
                        >
                          <Typography variant="caption" className="font-medium">
                            {index < 3 ? '8h' : index === 3 ? '6h' : '-'}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="quick action"
        className="fixed bottom-24 right-24 m3-elevation-3"
      >
        <Add />
      </Fab>
    </Box>
  );
}
