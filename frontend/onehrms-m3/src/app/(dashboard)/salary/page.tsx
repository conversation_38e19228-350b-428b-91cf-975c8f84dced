/**
 * Salary Page
 * Test page to verify navigation functionality
 */

'use client';

import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
} from '@mui/material';
import {
  AttachMoney,
  Receipt,
  Description,
  Assessment,
} from '@mui/icons-material';

export default function SalaryPage() {
  return (
    <Box
      className="content-padding"
      sx={{
        opacity: 1,
        pointerEvents: 'auto',
        minHeight: '100vh',
        backgroundColor: 'var(--md-sys-color-surface)',
      }}
    >
      {/* Page Header */}
      <Box className="mb-24">
        <Typography
          variant="h4"
          className="text-on-surface font-normal mb-8"
          sx={{
            color: 'var(--md-sys-color-on-surface)',
            fontWeight: 400,
            fontSize: '2rem',
          }}
        >
          My Salary
        </Typography>
        <Typography
          variant="body1"
          className="text-on-surface-variant"
          sx={{
            color: 'var(--md-sys-color-on-surface-variant)',
            fontSize: '1.125rem',
          }}
        >
          Manage your salary information, payslips, and tax documents.
        </Typography>
      </Box>

      {/* Quick Actions Grid */}
      <Grid container spacing={3} className="mb-32">
        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full cursor-pointer"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24 text-center">
              <Receipt
                className="text-primary mb-12"
                sx={{
                  color: 'var(--md-sys-color-primary)',
                  fontSize: '2rem',
                }}
              />
              <Typography
                variant="h6"
                className="text-on-surface font-medium mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 500,
                  fontSize: '1.125rem',
                }}
              >
                Payslips
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                View and download payslips
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full cursor-pointer"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24 text-center">
              <AttachMoney
                className="text-secondary mb-12"
                sx={{
                  color: 'var(--md-sys-color-secondary)',
                  fontSize: '2rem',
                }}
              />
              <Typography
                variant="h6"
                className="text-on-surface font-medium mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 500,
                  fontSize: '1.125rem',
                }}
              >
                Salary Breakdown
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                Detailed salary components
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full cursor-pointer"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24 text-center">
              <Description
                className="text-tertiary mb-12"
                sx={{
                  color: 'var(--md-sys-color-tertiary)',
                  fontSize: '2rem',
                }}
              />
              <Typography
                variant="h6"
                className="text-on-surface font-medium mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 500,
                  fontSize: '1.125rem',
                }}
              >
                Tax Documents
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                Form 16, TDS certificates
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            className="kpi-card h-full cursor-pointer"
            sx={{
              backgroundColor: 'var(--md-sys-color-surface-container)',
              border: '1px solid var(--md-sys-color-outline-variant)',
              boxShadow: 'var(--md-sys-elevation-level1)',
              '&:hover': {
                boxShadow: 'var(--md-sys-elevation-level2)',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <CardContent className="p-24 text-center">
              <Assessment
                className="text-primary mb-12"
                sx={{
                  color: 'var(--md-sys-color-primary)',
                  fontSize: '2rem',
                }}
              />
              <Typography
                variant="h6"
                className="text-on-surface font-medium mb-8"
                sx={{
                  color: 'var(--md-sys-color-on-surface)',
                  fontWeight: 500,
                  fontSize: '1.125rem',
                }}
              >
                Salary History
              </Typography>
              <Typography
                variant="caption"
                className="text-on-surface-variant"
                sx={{ color: 'var(--md-sys-color-on-surface-variant)' }}
              >
                Historical salary data
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Test Navigation Message */}
      <Card
        className="content-card"
        sx={{
          backgroundColor: 'var(--md-sys-color-primary-container)',
          border: '1px solid var(--md-sys-color-primary)',
        }}
      >
        <CardContent className="p-24">
          <Typography
            variant="h5"
            className="text-on-primary-container font-normal mb-16"
            sx={{
              color: 'var(--md-sys-color-on-primary-container)',
              fontWeight: 400,
              fontSize: '1.5rem',
            }}
          >
            🎉 Navigation Test Successful!
          </Typography>
          <Typography
            variant="body1"
            className="text-on-primary-container"
            sx={{
              color: 'var(--md-sys-color-on-primary-container)',
              fontSize: '1.125rem',
            }}
          >
            If you can see this page, it means the "My Salary" navigation is working correctly.
            The expandable navigation functionality has been successfully implemented.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}
