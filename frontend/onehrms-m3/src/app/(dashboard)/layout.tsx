/**
 * Dashboard Layout
 * Provides the main application shell for authenticated users
 */

'use client';

import { AppShell } from '@/components/navigation/AppShell';
import { useAuth } from '@/components/providers/AuthProvider';
import { Box, CircularProgress, Typography } from '@mui/material';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <Box className="flex flex-col items-center justify-center min-h-screen gap-24">
        <CircularProgress size={48} />
        <Typography variant="body1" className="text-on-surface-variant">
          Loading application...
        </Typography>
      </Box>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <Box className="flex flex-col items-center justify-center min-h-screen gap-24 p-24">
        <Typography variant="h4" className="text-on-surface text-center" sx={{ fontSize: '2rem' }}>
          Welcome to oneHRMS
        </Typography>
        <Typography variant="body1" className="text-on-surface-variant text-center max-w-md" sx={{ fontSize: '1.125rem' }}>
          Please log in to access your dashboard and manage your HR activities.
        </Typography>
        {/* TODO: Add login form or redirect to login page */}
      </Box>
    );
  }

  // Render authenticated dashboard
  return (
    <AppShell>
      {children}
    </AppShell>
  );
}
