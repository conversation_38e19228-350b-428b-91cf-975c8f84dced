import type { <PERSON>ada<PERSON> } from 'next';
import { Inter, Roboto } from 'next/font/google';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v14-appRouter';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { QueryProvider } from '@/components/providers/QueryProvider';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { NavigationProvider } from '@/components/providers/NavigationProvider';
import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  variable: '--font-roboto',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'oneHRMS - Material Design 3 Dashboard',
  description: 'Modern HR management system with Material Design 3, Tailwind CSS, and React Hook Form',
  keywords: 'HR, HRMS, Employee Management, Payroll, Attendance, Leave Management, Material Design 3',
  authors: [{ name: 'oneHRMS Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#6750A4',
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${roboto.variable}`}>
      <body className="font-sans antialiased">
        <AppRouterCacheProvider>
          <QueryProvider>
            <AuthProvider>
              <NavigationProvider>
                <ThemeProvider>
                  {children}
                </ThemeProvider>
              </NavigationProvider>
            </AuthProvider>
          </QueryProvider>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
