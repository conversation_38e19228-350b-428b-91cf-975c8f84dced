@tailwind base;
@tailwind components;
@tailwind utilities;

/* Material Design 3 Global Styles */

:root {
  /* M3 Color Tokens */
  --md-sys-color-primary: #6750A4;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #EADDFF;
  --md-sys-color-on-primary-container: #21005D;

  --md-sys-color-secondary: #625B71;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #E8DEF8;
  --md-sys-color-on-secondary-container: #1D192B;

  --md-sys-color-tertiary: #7D5260;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #FFD8E4;
  --md-sys-color-on-tertiary-container: #31111D;

  --md-sys-color-surface: #FEF7FF;
  --md-sys-color-on-surface: #1D1B20;
  --md-sys-color-surface-variant: #E7E0EC;
  --md-sys-color-on-surface-variant: #49454F;

  --md-sys-color-outline: #79747E;
  --md-sys-color-outline-variant: #CAC4D0;

  --md-sys-color-error: #BA1A1A;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFDAD6;
  --md-sys-color-on-error-container: #410002;

  /* M3 Elevation */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* M3 Shape */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 9999px;
}

/* Dark theme */
[data-theme="dark"] {
  --md-sys-color-primary: #D0BCFF;
  --md-sys-color-on-primary: #381E72;
  --md-sys-color-primary-container: #4F378B;
  --md-sys-color-on-primary-container: #EADDFF;

  --md-sys-color-secondary: #CCC2DC;
  --md-sys-color-on-secondary: #332D41;
  --md-sys-color-secondary-container: #4A4458;
  --md-sys-color-on-secondary-container: #E8DEF8;

  --md-sys-color-tertiary: #EFB8C8;
  --md-sys-color-on-tertiary: #492532;
  --md-sys-color-tertiary-container: #633B48;
  --md-sys-color-on-tertiary-container: #FFD8E4;

  --md-sys-color-surface: #1D1B20;
  --md-sys-color-on-surface: #E6E0E9;
  --md-sys-color-surface-variant: #49454F;
  --md-sys-color-on-surface-variant: #CAC4D0;

  --md-sys-color-outline: #938F99;
  --md-sys-color-outline-variant: #49454F;

  --md-sys-color-error: #FFB4AB;
  --md-sys-color-on-error: #690005;
  --md-sys-color-error-container: #93000A;
  --md-sys-color-on-error-container: #FFDAD6;
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-roboto), system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

/* Material Design 3 Component Classes */
.m3-surface {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

.m3-surface-variant {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
}

.m3-primary-container {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.m3-secondary-container {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.m3-tertiary-container {
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

.m3-error-container {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

/* M3 Elevation Classes */
.m3-elevation-0 {
  box-shadow: var(--md-sys-elevation-level0);
}

.m3-elevation-1 {
  box-shadow: var(--md-sys-elevation-level1);
}

.m3-elevation-2 {
  box-shadow: var(--md-sys-elevation-level2);
}

.m3-elevation-3 {
  box-shadow: var(--md-sys-elevation-level3);
}

.m3-elevation-4 {
  box-shadow: var(--md-sys-elevation-level4);
}

.m3-elevation-5 {
  box-shadow: var(--md-sys-elevation-level5);
}

/* M3 Shape Classes */
.m3-shape-none {
  border-radius: var(--md-sys-shape-corner-none);
}

.m3-shape-xs {
  border-radius: var(--md-sys-shape-corner-extra-small);
}

.m3-shape-sm {
  border-radius: var(--md-sys-shape-corner-small);
}

.m3-shape-md {
  border-radius: var(--md-sys-shape-corner-medium);
}

.m3-shape-lg {
  border-radius: var(--md-sys-shape-corner-large);
}

.m3-shape-xl {
  border-radius: var(--md-sys-shape-corner-extra-large);
}

.m3-shape-full {
  border-radius: var(--md-sys-shape-corner-full);
}

/* Custom utility classes */
.sidebar-width {
  width: 280px;
}

.content-padding {
  padding: 24px;
}

@media (max-width: 768px) {
  .content-padding {
    padding: 16px;
  }
}

/* KPI Card styles */
.kpi-card {
  @apply m3-elevation-1 m3-shape-lg min-h-[120px];
  background-color: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all 0.2s ease-in-out;
}

.kpi-card:hover {
  box-shadow: var(--md-sys-elevation-level2);
  transform: translateY(-2px);
}

.content-card {
  @apply m3-elevation-1 m3-shape-lg;
  background-color: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all 0.2s ease-in-out;
}

.content-card:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

/* Interactive elements */
.cursor-pointer {
  cursor: pointer;
}

.transition-m3 {
  transition: all 0.2s ease-in-out;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* Button enhancements */
.MuiButton-root {
  transition: all 0.2s ease-in-out !important;
}

.MuiButton-root:hover {
  transform: translateY(-1px);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--md-sys-color-surface-variant);
}

::-webkit-scrollbar-thumb {
  background: var(--md-sys-color-outline);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--md-sys-color-on-surface-variant);
}

/* Animation utilities */
.transition-m3 {
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
}

.transition-m3-emphasized {
  transition: all 0.5s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .kpi-card,
  .content-card {
    border: 1px solid var(--md-sys-color-outline);
  }
}
