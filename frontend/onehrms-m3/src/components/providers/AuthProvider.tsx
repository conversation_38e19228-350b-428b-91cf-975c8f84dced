/**
 * Authentication Provider
 * Handles authentication state and user management
 */

'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Role } from '@/types/navigation';

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  roles: Role[];
  department?: string;
  position?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize auth state
    // In development, use mock user
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        setUser({
          id: '1',
          email: '<EMAIL>',
          name: 'System Administrator',
          avatar: undefined,
          roles: ['admin', 'user'],
          department: 'Information Technology',
          position: 'System Administrator',
        });
        setIsLoading(false);
      }, 1000);
    } else {
      // Production: Initialize Keycloak or other auth provider
      // TODO: Implement production authentication
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement actual login logic
      // For now, simulate login
      await new Promise(resolve => setTimeout(resolve, 1000));

      setUser({
        id: '1',
        email,
        name: 'System Administrator',
        roles: ['admin', 'user'],
        department: 'Information Technology',
        position: 'System Administrator',
      });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    // TODO: Implement logout logic (clear tokens, redirect, etc.)
    console.log('User logged out');
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
