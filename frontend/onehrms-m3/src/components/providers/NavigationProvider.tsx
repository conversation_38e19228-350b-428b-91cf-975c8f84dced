/**
 * Navigation Provider
 * Manages navigation state and routing
 */

'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useMediaQuery, useTheme } from '@mui/material';
import { Role, NavigationState, NavigationActions } from '@/types/navigation';
import { getDefaultRoute, getNavigationItem, NAVIGATION_CONFIG } from '@/config/navigation';

interface NavigationContextType extends NavigationState, NavigationActions {}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

interface NavigationProviderProps {
  children: ReactNode;
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Initialize navigation state with responsive drawer state
  const [state, setState] = useState<NavigationState>({
    currentRole: 'user', // Default to 'user' instead of 'manager'
    activeItem: '',
    activeSubItem: '',
    isDrawerOpen: !isMobile, // Open on desktop, closed on mobile by default
    isMiniMode: false, // Start in full mode
  });

  // Handle responsive drawer state - only auto-close on mobile, allow manual control on desktop
  useEffect(() => {
    if (isMobile) {
      setState(prev => ({
        ...prev,
        isDrawerOpen: false // Always closed on mobile
      }));
    }
    // On desktop, preserve the current state (allow manual control)
  }, [isMobile]);

  // Derive role from pathname
  useEffect(() => {
    const urlRole: Role = pathname.startsWith('/admin')
      ? 'admin'
      : 'user';

    if (urlRole !== state.currentRole) {
      setState(prev => ({ ...prev, currentRole: urlRole }));
    }
  }, [pathname, state.currentRole]);

  // Derive active navigation items from pathname
  useEffect(() => {
    // Find matching navigation item based on path
    const matchingItem = NAVIGATION_CONFIG.items.find(item => {
      if (item.path === pathname) return true;
      return pathname.startsWith(item.path + '/');
    });

    if (matchingItem) {
      setState(prev => ({ ...prev, activeItem: matchingItem.key }));

      // Find matching sub item
      if (matchingItem.children) {
        const matchingSubItem = matchingItem.children.find(subItem => {
          if (subItem.path === pathname) return true;
          return pathname.startsWith(subItem.path + '/');
        });

        if (matchingSubItem) {
          setState(prev => ({ ...prev, activeSubItem: matchingSubItem.key }));
        } else if (matchingItem.children.length > 0) {
          // Default to first sub item if no exact match
          setState(prev => ({ ...prev, activeSubItem: matchingItem.children![0].key }));
        }
      }
    }
  }, [pathname]);

  // Navigation actions
  const setRole = (role: Role) => {
    console.log('🔄 Role change requested from', state.currentRole, 'to', role);
    setState(prev => ({ ...prev, currentRole: role }));

    // Navigate to default route for the new role
    const defaultRoute = getDefaultRoute(role);
    router.push(defaultRoute);

    console.log('✅ Navigated to', role, 'default route:', defaultRoute);
  };

  const setActiveItem = (key: string) => {
    setState(prev => ({ ...prev, activeItem: key }));

    // Clear sub navigation when primary changes
    const item = getNavigationItem(key);
    if (item?.children && item.children.length > 0) {
      setState(prev => ({ ...prev, activeSubItem: item.children![0].key }));
    } else {
      setState(prev => ({ ...prev, activeSubItem: '' }));
    }
  };

  const setActiveSubItem = (key: string) => {
    setState(prev => ({ ...prev, activeSubItem: key }));
  };

  const toggleDrawer = () => {
    if (isMobile) {
      // On mobile, toggle between open/closed
      setState(prev => ({ ...prev, isDrawerOpen: !prev.isDrawerOpen }));
    } else {
      // On desktop, toggle between full/mini mode
      setState(prev => ({
        ...prev,
        isMiniMode: !prev.isMiniMode,
        isDrawerOpen: true // Always keep drawer open on desktop
      }));
    }
  };

  const closeDrawer = () => {
    if (isMobile) {
      setState(prev => ({ ...prev, isDrawerOpen: false }));
    } else {
      // On desktop, switch to mini mode instead of closing
      setState(prev => ({ ...prev, isMiniMode: true }));
    }
  };

  const value: NavigationContextType = {
    ...state,
    setRole,
    setActiveItem,
    setActiveSubItem,
    toggleDrawer,
    closeDrawer,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
}
