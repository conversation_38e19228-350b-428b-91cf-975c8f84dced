/**
 * Role Switcher Component
 * Material Design 3 compliant role switching interface
 */

'use client';

import {
  Box,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  useTheme,
} from '@mui/material';
import { Person, AdminPanelSettings } from '@mui/icons-material';
import { Role } from '@/types/navigation';
import { cn } from '@/lib/utils';

interface RoleSwitcherProps {
  currentRole: Role;
  onRoleChange: (role: Role) => void;
  showLabels?: boolean;
  size?: 'small' | 'medium' | 'large';
  orientation?: 'horizontal' | 'vertical';
}

export function RoleSwitcher({
  currentRole,
  onRoleChange,
  showLabels = true,
  size = 'medium',
  orientation = 'horizontal',
}: RoleSwitcherProps) {
  const theme = useTheme();

  const handleRoleChange = (
    _event: React.MouseEvent<HTMLElement>,
    newRole: Role | null
  ) => {
    if (newRole !== null) {
      console.log('🔄 Role switching from', currentRole, 'to', newRole);
      onRoleChange(newRole);
    }
  };

  const getRoleIcon = (role: Role) => {
    const iconSize = size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium';

    switch (role) {
      case 'user':
        return <Person fontSize={iconSize} />;
      case 'admin':
        return <AdminPanelSettings fontSize={iconSize} />;
      default:
        return <Person fontSize={iconSize} />;
    }
  };

  const getRoleLabel = (role: Role) => {
    switch (role) {
      case 'user':
        return 'User';
      case 'admin':
        return 'Admin';
      default:
        return 'User';
    }
  };

  const buttonSizeClass = {
    small: 'px-8 py-4 text-sm',
    medium: 'px-12 py-6 text-base',
    large: 'px-16 py-8 text-lg',
  }[size];

  return (
    <Box
      className={cn(
        'flex items-center',
        orientation === 'vertical' ? 'flex-col gap-8' : 'flex-row gap-16'
      )}
    >
      {showLabels && (
        <Typography
          variant="caption"
          className="text-on-surface font-medium uppercase tracking-wider min-w-fit"
          sx={{
            fontSize: '0.75rem',
            fontWeight: 600,
            color: 'var(--md-sys-color-on-surface)',
            marginBottom: '8px',
            letterSpacing: '0.1em'
          }}
        >
          Role
        </Typography>
      )}

      <ToggleButtonGroup
        value={currentRole}
        exclusive
        onChange={handleRoleChange}
        orientation={orientation}
        size={size}
        className="m3-shape-sm"
        sx={{
          gap: '4px',
          '& .MuiToggleButton-root': {
            border: `1px solid var(--md-sys-color-outline)`,
            borderRadius: '12px',
            color: 'var(--md-sys-color-on-surface-variant)',
            backgroundColor: 'var(--md-sys-color-surface)',
            padding: '8px 16px',
            minWidth: '64px',
            fontWeight: 500,
            fontSize: '0.875rem',
            textTransform: 'capitalize',
            transition: theme.transitions.create(
              ['background-color', 'color', 'border-color', 'box-shadow'],
              {
                duration: theme.transitions.duration.short,
              }
            ),
            '&:hover': {
              backgroundColor: 'var(--md-sys-color-surface-variant)',
              borderColor: 'var(--md-sys-color-primary)',
              color: 'var(--md-sys-color-on-surface)',
            },
            '&.Mui-selected': {
              backgroundColor: 'var(--md-sys-color-primary-container)',
              borderColor: 'var(--md-sys-color-primary)',
              color: 'var(--md-sys-color-on-primary-container)',
              boxShadow: `0 1px 3px rgba(0, 0, 0, 0.12)`,
              '&:hover': {
                backgroundColor: 'var(--md-sys-color-primary-container)',
                filter: 'brightness(0.95)',
              },
            },
          },
        }}
      >
        {(['user', 'admin'] as Role[]).map((role) => (
          <ToggleButton
            key={role}
            value={role}
            aria-label={`${getRoleLabel(role)} role`}
            className={cn(
              buttonSizeClass,
              'flex items-center transition-m3',
              showLabels ? 'gap-8' : 'gap-0'
            )}
          >
            {getRoleIcon(role)}
            {showLabels && (
              <Typography
                variant="caption"
                className="font-medium"
                sx={{
                  fontSize: size === 'small' ? '0.625rem' : size === 'large' ? '0.875rem' : '0.75rem',
                  fontWeight: 500,
                }}
              >
                {getRoleLabel(role)}
              </Typography>
            )}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Box>
  );
}
