/**
 * Navigation Drawer Component
 * Material Design 3 compliant navigation drawer with role-based filtering
 */

'use client';

import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Badge,
  useTheme,
  Collapse,
  IconButton,
} from '@mui/material';
import { ExpandLess, ExpandMore, ChevronRight as ChevronRightIcon } from '@mui/icons-material';
import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useNavigation } from '@/components/providers/NavigationProvider';
import { getNavigationByRole } from '@/config/navigation';
import { cn } from '@/lib/utils';

interface NavigationDrawerProps {
  open: boolean;
  onClose: () => void;
  permanent: boolean;
}

const DRAWER_WIDTH = 280;
const MINI_DRAWER_WIDTH = 64;

export function NavigationDrawer({ open, onClose, permanent }: NavigationDrawerProps) {
  const theme = useTheme();
  const pathname = usePathname();
  const router = useRouter();
  const { currentRole, activeItem, setActiveItem, isMiniMode, toggleDrawer } = useNavigation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [manuallyCollapsed, setManuallyCollapsed] = useState<string[]>([]);

  const navigationItems = getNavigationByRole(currentRole);

  // Auto-expand items that contain the current active path (only for sub-pages, not main pages)
  // But don't interfere with manual collapse actions
  useEffect(() => {
    const currentItem = navigationItems.find(item => {
      if (!item.children || item.children.length === 0) return false;

      // Check if we're on a sub-page, not the main page
      const isOnSubPage = item.children.some(child => pathname === child.path);
      const isOnMainPage = pathname === item.path;

      // Only auto-expand if we're on a sub-page, not the main page
      return isOnSubPage && !isOnMainPage;
    });

    if (currentItem &&
        !expandedItems.includes(currentItem.key) &&
        !manuallyCollapsed.includes(currentItem.key)) {
      setExpandedItems(prev => [...prev, currentItem.key]);
    }
  }, [pathname, navigationItems, expandedItems, manuallyCollapsed]);

  const handleItemClick = (itemKey: string, itemPath: string, hasChildren: boolean) => {
    if (hasChildren) {
      const isCurrentlyExpanded = expandedItems.includes(itemKey);

      if (isCurrentlyExpanded) {
        // If clicking on an expanded item, collapse it and mark as manually collapsed
        setExpandedItems(prev => prev.filter(key => key !== itemKey));
        setManuallyCollapsed(prev => [...prev.filter(key => key !== itemKey), itemKey]);
        // Also navigate to the main page
        setActiveItem(itemKey);
        router.push(itemPath);
      } else {
        // If clicking on a collapsed item, expand it and remove from manually collapsed
        setExpandedItems(prev => [...prev, itemKey]);
        setManuallyCollapsed(prev => prev.filter(key => key !== itemKey));
      }
    } else {
      // Navigate for items without children
      setActiveItem(itemKey);
      router.push(itemPath);

      // Close drawer on mobile
      if (!permanent) {
        onClose();
      }
    }
  };

  const handleSubItemClick = (subItemPath: string) => {
    router.push(subItemPath);

    // Close drawer on mobile
    if (!permanent) {
      onClose();
    }
  };

  const isItemActive = (itemPath: string) => {
    return pathname === itemPath || pathname.startsWith(itemPath + '/');
  };

  const isSubItemActive = (subItemPath: string) => {
    return pathname === subItemPath || pathname.startsWith(subItemPath + '/');
  };

  const miniDrawerContent = (
    <Box className="h-full flex flex-col bg-surface">
      {/* Mini Header with Expand Button */}
      <Box className="p-8 border-b border-outline-variant flex justify-center">
        <IconButton
          onClick={toggleDrawer}
          size="small"
          sx={{ color: 'text.primary' }}
          aria-label="expand navigation"
        >
          <ChevronRightIcon />
        </IconButton>
      </Box>

      {/* Mini Navigation Items */}
      <Box className="flex-1 overflow-y-auto">
        <List sx={{ padding: '8px 4px' }}>
          {navigationItems.map((item) => (
            <ListItem key={item.key} disablePadding sx={{ display: 'block', mb: 1 }}>
              <ListItemButton
                onClick={() => handleItemClick(item.key, item.path)}
                sx={{
                  minHeight: 48,
                  justifyContent: 'center',
                  px: 1,
                  borderRadius: 1,
                  backgroundColor: activeItem === item.key ? 'primary.main' : 'transparent',
                  color: activeItem === item.key ? 'primary.contrastText' : 'text.primary',
                  '&:hover': {
                    backgroundColor: activeItem === item.key ? 'primary.dark' : 'action.hover',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 0,
                    justifyContent: 'center',
                    color: 'inherit',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Mini Footer */}
      <Box className="p-8 border-t border-outline-variant text-center">
        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '10px' }}>
          M3
        </Typography>
      </Box>
    </Box>
  );

  const drawerContent = (
    <Box className="h-full flex flex-col bg-surface">
      {/* Navigation Header */}
      <Box className="p-24 border-b border-outline-variant">
        <Typography
          variant="h6"
          className="text-on-surface font-medium"
          sx={{ fontSize: '1.125rem' }}
        >
          {currentRole === 'admin' ? 'Organization' : 'My Workspace'}
        </Typography>
        <Typography
          variant="caption"
          className="text-on-surface-variant"
        >
          {currentRole.charAt(0).toUpperCase() + currentRole.slice(1)} Dashboard
        </Typography>
      </Box>

      {/* Navigation Items */}
      <List
        component="nav"
        role="navigation"
        aria-label={`${currentRole === 'admin' ? 'Organization' : 'My Workspace'} navigation`}
        className="flex-1 py-8"
      >
        {navigationItems.map((item) => {
          const isActive = isItemActive(item.path);
          const isExpanded = expandedItems.includes(item.key);
          const hasChildren = Boolean(item.children && item.children.length > 0);

          return (
            <Box key={item.key}>
              {/* Primary Navigation Item */}
              <ListItem disablePadding className="mb-4">
                <ListItemButton
                  onClick={() => handleItemClick(item.key, item.path, hasChildren)}
                  role="button"
                  aria-selected={isActive}
                  aria-expanded={hasChildren ? isExpanded : undefined}
                  aria-label={`Navigate to ${item.label}`}
                  className={cn(
                    'm3-shape-lg mx-12 py-12 px-16',
                    'min-h-56 transition-all duration-200 ease-in-out',
                    isActive
                      ? 'bg-secondary-container text-on-secondary-container shadow-sm'
                      : 'text-on-surface hover:bg-surface-variant hover:shadow-sm',
                    hasChildren && isExpanded && 'bg-surface-variant'
                  )}
                >
                  <ListItemIcon
                    className={cn(
                      'min-w-40 mr-12',
                      isActive ? 'text-on-secondary-container' : 'text-on-surface-variant'
                    )}
                  >
                    {item.badge && item.badge > 0 ? (
                      <Badge badgeContent={item.badge} color="error" variant="standard" max={99}>
                        {item.icon}
                      </Badge>
                    ) : (
                      item.icon
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.label}
                    primaryTypographyProps={{
                      variant: 'body2',
                      className: cn(
                        'font-medium',
                        isActive ? 'text-on-secondary-container' : 'text-on-surface'
                      ),
                      sx: { fontSize: '0.875rem', fontWeight: 500 },
                    }}
                  />
                  {hasChildren && (
                    <Box
                      className="ml-8 transition-transform duration-200 ease-in-out"
                      sx={{
                        transform: isExpanded ? 'rotate(0deg)' : 'rotate(0deg)',
                        color: isActive ? 'var(--md-sys-color-on-secondary-container)' : 'var(--md-sys-color-on-surface-variant)',
                      }}
                    >
                      {isExpanded ? <ExpandLess /> : <ExpandMore />}
                    </Box>
                  )}
                </ListItemButton>
              </ListItem>

              {/* Sub Navigation Items */}
              {hasChildren && (
                <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.children!.filter(subItem =>
                      subItem.roles.includes(currentRole)
                    ).map((subItem) => {
                      const isSubActive = isSubItemActive(subItem.path);

                      return (
                        <ListItem key={subItem.key} disablePadding className="mb-2">
                          <ListItemButton
                            onClick={() => handleSubItemClick(subItem.path)}
                            role="button"
                            aria-selected={isSubActive}
                            aria-label={`Navigate to ${subItem.label}`}
                            className={cn(
                              'm3-shape-sm mx-24 py-8 px-16 ml-56',
                              'min-h-40 transition-m3',
                              isSubActive
                                ? 'bg-tertiary-container text-on-tertiary-container'
                                : 'text-on-surface-variant hover:bg-surface-variant'
                            )}
                          >
                            {subItem.icon && (
                              <ListItemIcon
                                className={cn(
                                  'min-w-24 mr-8',
                                  isSubActive ? 'text-on-tertiary-container' : 'text-on-surface-variant'
                                )}
                              >
                                {subItem.badge && subItem.badge > 0 ? (
                                  <Badge badgeContent={subItem.badge} color="error">
                                    {subItem.icon}
                                  </Badge>
                                ) : (
                                  subItem.icon
                                )}
                              </ListItemIcon>
                            )}
                            <ListItemText
                              primary={subItem.label}
                              primaryTypographyProps={{
                                variant: 'body2',
                                className: cn(
                                  isSubActive ? 'text-on-tertiary-container font-medium' : 'text-on-surface-variant'
                                ),
                                sx: { fontSize: '0.875rem' },
                              }}
                            />
                            {subItem.badge && subItem.badge > 0 && !subItem.icon && (
                              <Badge badgeContent={subItem.badge} color="error" />
                            )}
                          </ListItemButton>
                        </ListItem>
                      );
                    })}
                  </List>
                </Collapse>
              )}
            </Box>
          );
        })}
      </List>

      {/* Footer */}
      <Box className="p-16 border-t border-outline-variant">
        <Typography
          variant="caption"
          className="text-on-surface-variant text-center"
        >
          oneHRMS M3 v1.0
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={permanent ? 'permanent' : 'temporary'} // Use permanent for desktop, temporary for mobile
      open={open}
      onClose={onClose}
      ModalProps={{
        keepMounted: true, // Better open performance on mobile
      }}
      sx={{
        width: permanent && isMiniMode ? MINI_DRAWER_WIDTH : DRAWER_WIDTH,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: permanent && isMiniMode ? MINI_DRAWER_WIDTH : DRAWER_WIDTH,
          boxSizing: 'border-box',
          top: permanent ? 64 : 0, // Account for top app bar
          height: permanent ? 'calc(100vh - 64px)' : '100vh',
          border: 'none',
          boxShadow: permanent ? 'none' : theme.shadows[16],
          transition: 'width 0.3s ease-in-out',
          overflowX: 'hidden',
        },
      }}
      className={cn(
        permanent && 'border-r border-outline-variant'
      )}
    >
      {permanent && isMiniMode ? miniDrawerContent : drawerContent}
    </Drawer>
  );
}
