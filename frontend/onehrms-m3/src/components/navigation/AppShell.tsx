/**
 * App Shell Component
 * Material Design 3 compliant application shell with navigation
 */

'use client';

import { useState } from 'react';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  Settings,
  AccountCircle,
  Person,
  Logout,
  DarkMode,
  LightMode,
} from '@mui/icons-material';
import { useAuth } from '@/components/providers/AuthProvider';
import { useTheme as useCustomTheme } from '@/components/providers/ThemeProvider';
import { useNavigation } from '@/components/providers/NavigationProvider';
import { NavigationDrawer } from './NavigationDrawer';
import { RoleSwitcher } from './RoleSwitcher';
import { cn } from '@/lib/utils';

interface AppShellProps {
  children: React.ReactNode;
}

export function AppShell({ children }: AppShellProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  const { isDark, toggleTheme } = useCustomTheme();
  const { isDrawerOpen, isMiniMode, toggleDrawer, closeDrawer, currentRole, setRole } = useNavigation();

  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
  };

  const handleThemeToggle = () => {
    toggleTheme();
    handleUserMenuClose();
  };

  return (
    <Box className="flex min-h-screen">
      {/* App Bar */}
      <AppBar
        position="fixed"
        className={cn(
          'z-50 transition-m3',
          'bg-surface border-b border-outline-variant',
          'text-on-surface shadow-elevation-2'
        )}
        sx={{
          zIndex: theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar className="min-h-[64px] px-16 md:px-24">
          {/* Menu button for both mobile and desktop */}
          <IconButton
            color="inherit"
            aria-label="toggle navigation drawer"
            edge="start"
            onClick={toggleDrawer}
            className="mr-16"
            sx={{
              color: 'var(--md-sys-color-on-surface)',
              '&:hover': {
                backgroundColor: 'var(--md-sys-color-surface-variant)',
              },
            }}
          >
            <MenuIcon />
          </IconButton>

          {/* Logo */}
          <Typography
            variant="h6"
            component="div"
            className="font-bold text-primary mr-32"
          >
            oneHRMS
          </Typography>

          {/* Role Switcher */}
          <Box className="mr-auto">
            <RoleSwitcher
              currentRole={currentRole}
              onRoleChange={setRole}
              size="small"
            />
          </Box>

          {/* Header Actions */}
          <Box className="flex items-center gap-8">
            {/* Notifications */}
            <IconButton
              color="inherit"
              aria-label="notifications"
              className="transition-m3 hover:bg-primary/10"
            >
              <Badge badgeContent={3} color="error" variant="dot">
                <Notifications />
              </Badge>
            </IconButton>

            {/* Settings */}
            <IconButton
              color="inherit"
              aria-label="settings"
              className="transition-m3 hover:bg-primary/10"
            >
              <Settings />
            </IconButton>

            {/* User Menu */}
            <IconButton
              color="inherit"
              aria-label="user menu"
              onClick={handleUserMenuOpen}
              className="transition-m3 hover:bg-primary/10"
            >
              {user?.avatar ? (
                <Avatar
                  src={user.avatar}
                  alt={user.name}
                  className="w-32 h-32"
                />
              ) : (
                <AccountCircle />
              )}
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Navigation Drawer */}
      <NavigationDrawer
        open={isDrawerOpen}
        onClose={closeDrawer}
        permanent={!isMobile}
      />

      {/* Main Content */}
      <Box
        component="main"
        className={cn(
          'flex-1 transition-all duration-300 ease-in-out',
          'bg-surface min-h-screen'
        )}
        sx={{
          paddingTop: '64px', // Exact app bar height
          // Remove manual margin - permanent drawer handles positioning automatically
          transition: 'margin-left 0.3s ease-in-out',
        }}
      >
        {children}
      </Box>

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        onClick={handleUserMenuClose}
        className="mt-8"
        PaperProps={{
          className: cn(
            'm3-elevation-3 m3-shape-md',
            'bg-surface-container text-on-surface',
            'min-w-200'
          ),
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* User Info */}
        <Box className="px-16 py-12">
          <Typography variant="subtitle2" className="font-medium">
            {user?.name || 'User'}
          </Typography>
          <Typography variant="caption" className="text-on-surface-variant">
            {user?.email || '<EMAIL>'}
          </Typography>
          {user?.department && (
            <Typography variant="caption" className="text-on-surface-variant block">
              {user.department}
            </Typography>
          )}
        </Box>

        <Divider />

        {/* Menu Items */}
        <MenuItem onClick={handleUserMenuClose}>
          <ListItemIcon>
            <Person fontSize="small" />
          </ListItemIcon>
          <ListItemText>Profile</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleUserMenuClose}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          <ListItemText>Settings</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleThemeToggle}>
          <ListItemIcon>
            {isDark ? <LightMode fontSize="small" /> : <DarkMode fontSize="small" />}
          </ListItemIcon>
          <ListItemText>{isDark ? 'Light Mode' : 'Dark Mode'}</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          <ListItemText>Logout</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
}
