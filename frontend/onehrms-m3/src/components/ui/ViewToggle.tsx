/**
 * ViewToggle Component
 * Toggle between grid and list views
 */

'use client';

import { ToggleButton, ToggleButtonGroup } from '@mui/material';
import { ViewModule, ViewList } from '@mui/icons-material';

export type ViewType = 'grid' | 'list';

interface ViewToggleProps {
  view: ViewType;
  onViewChange: (view: ViewType) => void;
  className?: string;
}

export default function ViewToggle({ view, onViewChange, className }: ViewToggleProps) {
  const handleChange = (event: React.MouseEvent<HTMLElement>, newView: ViewType | null) => {
    if (newView !== null) {
      onViewChange(newView);
    }
  };

  return (
    <ToggleButtonGroup
      value={view}
      exclusive
      onChange={handleChange}
      aria-label="view toggle"
      className={className}
      sx={{
        '& .MuiToggleButton-root': {
          border: '1px solid var(--md-sys-color-outline-variant)',
          color: 'var(--md-sys-color-on-surface-variant)',
          '&.Mui-selected': {
            backgroundColor: 'var(--md-sys-color-primary-container)',
            color: 'var(--md-sys-color-on-primary-container)',
            '&:hover': {
              backgroundColor: 'var(--md-sys-color-primary-container)',
            },
          },
          '&:hover': {
            backgroundColor: 'var(--md-sys-color-surface-container-high)',
          },
        },
      }}
    >
      <ToggleButton value="grid" aria-label="grid view">
        <ViewModule className="w-5 h-5" />
      </ToggleButton>
      <ToggleButton value="list" aria-label="list view">
        <ViewList className="w-5 h-5" />
      </ToggleButton>
    </ToggleButtonGroup>
  );
}
