/**
 * Navigation Types for oneHRMS M3 Dashboard
 * Material Design 3 compliant navigation system
 */

import { ReactNode } from 'react';

export type Role = 'user' | 'admin';

export interface NavigationItem {
  key: string;
  label: string;
  icon: ReactNode;
  path: string;
  roles: Role[];
  badge?: number;
  children?: NavigationSubItem[];
}

export interface NavigationSubItem {
  key: string;
  label: string;
  path: string;
  roles: Role[];
  icon?: ReactNode;
  badge?: number;
}

export interface NavigationState {
  currentRole: Role;
  activeItem: string;
  activeSubItem: string;
  isDrawerOpen: boolean;
  isMiniMode: boolean;
}

export interface NavigationActions {
  setRole: (role: Role) => void;
  setActiveItem: (key: string) => void;
  setActiveSubItem: (key: string) => void;
  toggleDrawer: () => void;
  closeDrawer: () => void;
}

export interface NavigationConfig {
  items: NavigationItem[];
  defaultRoutes: Record<Role, string>;
}

// Navigation item types for better organization
export type NavigationItemType =
  | 'dashboard'
  | 'attendance'
  | 'leave'
  | 'salary'
  | 'employees'
  | 'payroll'
  | 'recruitment'
  | 'reports'
  | 'settings';

// Navigation section types
export type NavigationSectionType =
  | 'personal'
  | 'management'
  | 'administration'
  | 'system';

export interface NavigationSection {
  key: NavigationSectionType;
  label: string;
  items: NavigationItem[];
  roles: Role[];
}
