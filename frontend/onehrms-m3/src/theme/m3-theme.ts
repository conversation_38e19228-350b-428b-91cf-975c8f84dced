/**
 * Material Design 3 Theme Configuration
 * Implements M3 design tokens and color system
 */

import { createTheme, ThemeOptions } from '@mui/material/styles';
import { argbFromHex, themeFromSourceColor, applyTheme } from '@material/material-color-utilities';

// M3 Color Tokens
const M3_COLORS = {
  // Primary brand color
  primary: '#6750A4',

  // Surface colors
  surface: '#FEF7FF',
  surfaceDim: '#DED8E1',
  surfaceBright: '#FEF7FF',
  surfaceContainerLowest: '#FFFFFF',
  surfaceContainerLow: '#F7F2FA',
  surfaceContainer: '#F3EDF7',
  surfaceContainerHigh: '#ECE6F0',
  surfaceContainerHighest: '#E6E0E9',

  // On-surface colors
  onSurface: '#1D1B20',
  onSurfaceVariant: '#49454F',

  // Outline colors
  outline: '#79747E',
  outlineVariant: '#CAC4D0',

  // Error colors
  error: '#BA1A1A',
  onError: '#FFFFFF',
  errorContainer: '#FFDAD6',
  onErrorContainer: '#410002',

  // Warning colors (custom)
  warning: '#F57C00',
  onWarning: '#FFFFFF',
  warningContainer: '#FFE0B2',
  onWarningContainer: '#E65100',

  // Success colors (custom)
  success: '#2E7D32',
  onSuccess: '#FFFFFF',
  successContainer: '#C8E6C9',
  onSuccessContainer: '#1B5E20',

  // Info colors (custom)
  info: '#1976D2',
  onInfo: '#FFFFFF',
  infoContainer: '#BBDEFB',
  onInfoContainer: '#0D47A1',
};

// M3 Typography Scale
const M3_TYPOGRAPHY = {
  // Display styles
  displayLarge: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '57px',
    lineHeight: '64px',
    fontWeight: 400,
    letterSpacing: '-0.25px',
  },
  displayMedium: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '45px',
    lineHeight: '52px',
    fontWeight: 400,
    letterSpacing: '0px',
  },
  displaySmall: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '36px',
    lineHeight: '44px',
    fontWeight: 400,
    letterSpacing: '0px',
  },

  // Headline styles
  headlineLarge: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '32px',
    lineHeight: '40px',
    fontWeight: 400,
    letterSpacing: '0px',
  },
  headlineMedium: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '28px',
    lineHeight: '36px',
    fontWeight: 400,
    letterSpacing: '0px',
  },
  headlineSmall: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '24px',
    lineHeight: '32px',
    fontWeight: 400,
    letterSpacing: '0px',
  },

  // Title styles
  titleLarge: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '22px',
    lineHeight: '28px',
    fontWeight: 400,
    letterSpacing: '0px',
  },
  titleMedium: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: 500,
    letterSpacing: '0.15px',
  },
  titleSmall: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 500,
    letterSpacing: '0.1px',
  },

  // Body styles
  bodyLarge: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: 400,
    letterSpacing: '0.5px',
  },
  bodyMedium: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 400,
    letterSpacing: '0.25px',
  },
  bodySmall: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '12px',
    lineHeight: '16px',
    fontWeight: 400,
    letterSpacing: '0.4px',
  },

  // Label styles
  labelLarge: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 500,
    letterSpacing: '0.1px',
  },
  labelMedium: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '12px',
    lineHeight: '16px',
    fontWeight: 500,
    letterSpacing: '0.5px',
  },
  labelSmall: {
    fontFamily: 'Roboto, system-ui, sans-serif',
    fontSize: '11px',
    lineHeight: '16px',
    fontWeight: 500,
    letterSpacing: '0.5px',
  },
};

// M3 Shape Scale
const M3_SHAPE = {
  borderRadius: 12, // Default border radius
  borderRadiusExtraSmall: 4,
  borderRadiusSmall: 8,
  borderRadiusMedium: 12,
  borderRadiusLarge: 16,
  borderRadiusExtraLarge: 28,
  borderRadiusFull: 9999,
};

// M3 Elevation Scale
const M3_ELEVATION = {
  level0: 'none',
  level1: '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
  level2: '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)',
  level3: '0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)',
  level4: '0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)',
  level5: '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
};

/**
 * Create Material Design 3 MUI Theme
 */
export function createM3Theme(isDark: boolean = false): ThemeOptions {
  const m3Colors = generateM3Theme(M3_COLORS.primary, isDark);

  return {
    palette: {
      mode: isDark ? 'dark' : 'light',
      primary: {
        main: m3Colors.primary,
        contrastText: m3Colors.onPrimary,
      },
      secondary: {
        main: m3Colors.secondary,
        contrastText: m3Colors.onSecondary,
      },
      error: {
        main: m3Colors.error,
        contrastText: m3Colors.onError,
      },
      warning: {
        main: M3_COLORS.warning,
        contrastText: M3_COLORS.onWarning,
      },
      info: {
        main: M3_COLORS.info,
        contrastText: M3_COLORS.onInfo,
      },
      success: {
        main: M3_COLORS.success,
        contrastText: M3_COLORS.onSuccess,
      },
      background: {
        default: m3Colors.surface,
        paper: M3_COLORS.surfaceContainer,
      },
      text: {
        primary: m3Colors.onSurface,
        secondary: m3Colors.onSurfaceVariant,
      },
      divider: m3Colors.outlineVariant,
    },
    typography: {
      fontFamily: 'Roboto, system-ui, sans-serif',
      h1: M3_TYPOGRAPHY.displayLarge,
      h2: M3_TYPOGRAPHY.displayMedium,
      h3: M3_TYPOGRAPHY.displaySmall,
      h4: M3_TYPOGRAPHY.headlineLarge,
      h5: M3_TYPOGRAPHY.headlineMedium,
      h6: M3_TYPOGRAPHY.headlineSmall,
      subtitle1: M3_TYPOGRAPHY.titleLarge,
      subtitle2: M3_TYPOGRAPHY.titleMedium,
      body1: M3_TYPOGRAPHY.bodyLarge,
      body2: M3_TYPOGRAPHY.bodyMedium,
      caption: M3_TYPOGRAPHY.bodySmall,
      button: M3_TYPOGRAPHY.labelLarge,
      overline: M3_TYPOGRAPHY.labelSmall,
    },
    shape: {
      borderRadius: M3_SHAPE.borderRadius,
    },
    shadows: [
      M3_ELEVATION.level0,
      M3_ELEVATION.level1,
      M3_ELEVATION.level2,
      M3_ELEVATION.level3,
      M3_ELEVATION.level4,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
      M3_ELEVATION.level5,
    ] as any,
  };
}

/**
 * Generate M3 theme from source color
 */
function generateM3Theme(sourceColor: string = M3_COLORS.primary, isDark: boolean = false) {
  // For now, return static colors. In production, use @material/material-color-utilities
  return isDark ? {
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',
    secondary: '#CCC2DC',
    onSecondary: '#332D41',
    secondaryContainer: '#4A4458',
    onSecondaryContainer: '#E8DEF8',
    tertiary: '#EFB8C8',
    onTertiary: '#492532',
    tertiaryContainer: '#633B48',
    onTertiaryContainer: '#FFD8E4',
    surface: '#1D1B20',
    onSurface: '#E6E0E9',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    outline: '#938F99',
    outlineVariant: '#49454F',
    error: '#FFB4AB',
    onError: '#690005',
    errorContainer: '#93000A',
    onErrorContainer: '#FFDAD6',
  } : {
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',
    surface: '#FEF7FF',
    onSurface: '#1D1B20',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',
  };
}

export { M3_COLORS, M3_TYPOGRAPHY, M3_SHAPE, M3_ELEVATION };
