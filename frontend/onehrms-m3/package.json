{"name": "onehrms-m3-dashboard", "version": "1.0.0", "description": "oneHRMS Dashboard with Material Design 3, Tailwind CSS, and Modern Architecture", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.0", "@material/material-color-utilities": "^0.2.7", "@mui/icons-material": "^6.0.0", "@mui/lab": "^6.0.0-beta.0", "@mui/material": "^6.0.0", "@mui/material-nextjs": "^6.0.0", "@tanstack/react-query": "^5.0.0", "autoprefixer": "^10.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "jwt-decode": "^4.0.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "zod": "^3.22.0"}, "devDependencies": {"@playwright/test": "^1.39.0", "@tanstack/react-query-devtools": "^5.84.2", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}