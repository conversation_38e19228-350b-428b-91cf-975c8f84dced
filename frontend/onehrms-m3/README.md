# 🎨 oneHRMS Material Design 3 Dashboard

A modern HR management system built with **Material Design 3**, **Next.js 14**, **Tailwind CSS**, **React Hook Form**, and **MUI v6**.

## ✨ **Features**

### 🎨 **Material Design 3**
- **M3 Color System**: Dynamic color tokens with light/dark theme support
- **M3 Typography**: Complete typography scale (Display, Headline, Title, Body, Label)
- **M3 Components**: Cards, buttons, navigation with M3 styling
- **M3 Elevation**: 5-level elevation system with proper shadows
- **M3 Shape**: Rounded corners following M3 specifications

### 🧭 **Navigation Architecture**
- **Role-Based Navigation**: User/Manager/Admin with filtered menu items
- **Expandable Sidebar**: Collapsible navigation with sub-items
- **URL Synchronization**: Role switching updates routes automatically
- **Mobile Responsive**: Drawer navigation on mobile, persistent on desktop

### 🎯 **Modern UI/UX**
- **Card-Based Layout**: KPI cards and content cards with proper spacing
- **Responsive Grid**: 12-column grid system with breakpoint-aware spacing
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- **Touch-Friendly**: 44×44px minimum touch targets
- **Smooth Animations**: M3-compliant transitions and micro-interactions

### 🛠 **Technical Stack**
- **Next.js 14**: App Router with server components
- **TypeScript**: Full type safety throughout
- **Tailwind CSS**: Utility-first styling with M3 design tokens
- **Material-UI v6**: Latest MUI components with M3 theming
- **React Hook Form**: Performant forms with validation
- **React Query**: Data fetching and caching
- **Framer Motion**: Advanced animations

## 🚀 **Getting Started**

### Prerequisites
- Node.js 18+
- npm 9+

### Installation

```bash
# Navigate to the M3 project
cd frontend/onehrms-m3

# Install dependencies
npm install --legacy-peer-deps

# Start development server
npm run dev

# Open browser
open http://localhost:3001
```

### Development Scripts

```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run start           # Start production server

# Code Quality
npm run lint            # ESLint
npm run type-check      # TypeScript check

# Testing
npm test               # Run Jest tests
npm run test:watch     # Watch mode
npm run test:coverage  # Coverage report
npm run test:e2e       # Playwright E2E tests
```

## 🏗 **Architecture**

### Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (dashboard)/       # Dashboard routes
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Root page
│   └── globals.css       # Global styles
├── components/
│   ├── navigation/       # Navigation components
│   └── providers/        # Context providers
├── config/
│   └── navigation.tsx    # Navigation configuration
├── hooks/               # Custom hooks
├── lib/
│   └── utils.ts         # Utility functions
├── theme/
│   └── m3-theme.ts      # M3 theme configuration
└── types/
    └── navigation.ts    # Type definitions
```

### Navigation Schema

```typescript
interface NavigationItem {
  key: string;
  label: string;
  icon: ReactNode;
  path: string;
  roles: Role[];
  children?: NavigationSubItem[];
  badge?: number;
}
```

### M3 Design Tokens

```css
/* Color System */
--md-sys-color-primary: #6750A4;
--md-sys-color-on-primary: #FFFFFF;
--md-sys-color-primary-container: #EADDFF;
--md-sys-color-on-primary-container: #21005D;

/* Typography Scale */
--md-sys-typescale-display-large: 57px/64px;
--md-sys-typescale-headline-medium: 28px/36px;
--md-sys-typescale-body-large: 16px/24px;

/* Elevation */
--md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3);
--md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3);
```

## 🎨 **Styling Approach**

### Tailwind + M3 Integration

```tsx
// M3 utility classes
<Card className="kpi-card m3-elevation-1 m3-shape-lg">
  <CardContent className="p-24">
    <Typography className="text-on-surface">
      Content
    </Typography>
  </CardContent>
</Card>

// Responsive spacing
<Box className="content-padding"> {/* 24px on desktop, 16px on mobile */}
  <Grid container spacing={3}>
    {/* Content */}
  </Grid>
</Box>
```

### Custom M3 Classes

```css
.kpi-card {
  @apply m3-elevation-1 m3-shape-lg p-24 min-h-[120px] bg-surface-container;
}

.content-card {
  @apply m3-elevation-1 m3-shape-lg p-24 bg-surface-container;
}

.transition-m3 {
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
}
```

## 🧭 **Navigation System**

### Role-Based Access

```typescript
// Navigation filtered by role
const userItems = getNavigationByRole('user');
const adminItems = getNavigationByRole('admin');

// Role switching
const { currentRole, setRole } = useNavigation();
setRole('admin'); // Navigates to /admin/dashboard
```

### Route Structure

```
/dashboard              # User dashboard
/attendance            # User attendance
/leave                 # User leave management
/salary               # User salary information

/admin/dashboard      # Admin dashboard
/admin/employees      # Employee management
/admin/attendance     # Attendance management
/admin/payroll        # Payroll management
/admin/recruitment    # Recruitment management
```

## 📱 **Responsive Design**

### Breakpoints

```typescript
// Tailwind breakpoints
xs: 0px      // Mobile
sm: 600px    // Small tablet
md: 900px    // Tablet (drawer breakpoint)
lg: 1200px   // Desktop
xl: 1536px   // Large desktop
```

### Mobile Adaptations

- **Navigation**: Drawer overlay on mobile, persistent sidebar on desktop
- **Spacing**: Reduced padding on mobile (16px vs 24px)
- **Touch Targets**: Minimum 44×44px for all interactive elements
- **Typography**: Responsive font sizes

## ♿ **Accessibility**

### WCAG 2.1 AA Compliance

- **Color Contrast**: ≥4.5:1 for normal text, ≥3:1 for large text
- **Keyboard Navigation**: Full keyboard support with focus indicators
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Touch Targets**: Minimum 44×44px interactive areas
- **Motion**: Respects `prefers-reduced-motion`

### Implementation

```tsx
<ListItemButton
  role="button"
  aria-selected={isActive}
  aria-label={`Navigate to ${item.label}`}
  className="min-h-56 transition-m3" // 56px = 14 * 4 (44px+ touch target)
>
```

## 🎯 **Performance**

### Optimizations

- **Route-based Code Splitting**: Automatic with Next.js App Router
- **Component Lazy Loading**: Dynamic imports for heavy components
- **Image Optimization**: Next.js built-in image optimization
- **Bundle Analysis**: Webpack bundle analyzer integration
- **Tree Shaking**: Unused code elimination

### Bundle Size

```bash
# Analyze bundle
npm run build && npm run analyze
```

## 🧪 **Testing Strategy**

### Unit Tests (Jest)

```bash
# Component testing
npm test

# Coverage report
npm run test:coverage
```

### E2E Tests (Playwright)

```bash
# End-to-end testing
npm run test:e2e
```

### Testing Approach

- **Component Tests**: React Testing Library for UI components
- **Hook Tests**: Custom hooks testing
- **Integration Tests**: Navigation and routing
- **E2E Tests**: Complete user workflows

## 🚀 **Deployment**

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables

```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onehrms
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onehrms-frontend
```

## 📊 **Success Metrics**

- ✅ **Material Design 3**: Complete M3 implementation
- ✅ **Modern Stack**: Next.js 14 + TypeScript + Tailwind
- ✅ **Accessibility**: WCAG 2.1 AA compliant
- ✅ **Performance**: Optimized bundle and loading
- ✅ **Developer Experience**: Hot reload, TypeScript, ESLint
- ✅ **Mobile-First**: Responsive design with touch support

## 🔄 **Next Steps**

1. **Form Implementation**: React Hook Form integration
2. **API Integration**: Connect to backend services
3. **Authentication**: Keycloak integration
4. **Testing**: Comprehensive test coverage
5. **Deployment**: CI/CD pipeline setup

## 📝 **License**

Private - oneHRMS Team

---

**Built with ❤️ using Material Design 3 and modern web technologies**
