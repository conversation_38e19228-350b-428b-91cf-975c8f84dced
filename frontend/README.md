# oneHRMS Frontend - Consolidated React Application

## Overview

The oneHRMS frontend has been consolidated into a single, modern React application built with TypeScript, Material-UI, and Vite. This consolidation eliminates the complexity of multiple frontend frameworks and provides a unified development experience.

---

## 🏗️ Architecture

### **Single Frontend Application**
```
frontend/
└── dashboard-ui/          # Single React TypeScript application
    ├── src/
    │   ├── components/    # Shared React components
    │   ├── features/      # Feature-based modules
    │   ├── contexts/      # React contexts (Auth, Theme, etc.)
    │   ├── utils/         # Utility functions
    │   ├── generated/     # Auto-generated API clients
    │   └── types/         # TypeScript type definitions
    ├── public/            # Static assets
    ├── tests/             # Jest + Playwright tests
    ├── docs/              # Component documentation
    └── scripts/           # Build and deployment scripts
```

### **Technology Stack**
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI v5
- **Build Tool**: Vite
- **State Management**: React Context + Hooks
- **Routing**: React Router v6
- **Authentication**: Keycloak OIDC
- **Testing**: Jest + React Testing Library + Playwright
- **API Integration**: OpenAPI generated clients

---

## 🚀 Quick Start

### **Prerequisites**
- Node.js 18+ and npm
- Java Runtime Environment (JRE) 17+ for API client generation

### **Installation**
```bash
# Navigate to the frontend application
cd frontend/dashboard-ui

# Install dependencies
npm install

# Generate API clients
npm run generate:api

# Start development server
npm run dev
```

### **Development Server**
```bash
# Start with hot reload
npm run dev

# Start with specific environment
npm run dev:staging

# Start with mock data
npm run dev:mock
```

---

## 🔧 Environment Configuration

### **Environment Files**
- `.env.development` - Development environment
- `.env.staging` - Staging environment
- `.env.production` - Production environment

### **Setup Environment**
```bash
# Initialize environment configuration
npm run env:init development
npm run env:init staging
npm run env:init production

# Validate environment
npm run env:validate production

# List environment variables
npm run env:list
```

### **Required Environment Variables**
```bash
# Application
VITE_APP_NAME=oneHRMS
VITE_APP_VERSION=1.0.0

# Authentication
VITE_AUTH_PROVIDER=keycloak
VITE_KEYCLOAK_URL=https://auth.onehrms.com
VITE_KEYCLOAK_REALM=oneHRMS
VITE_KEYCLOAK_CLIENT_ID=onehrms-frontend

# API
VITE_API_BASE_URL=https://api.onehrms.com/api
```

---

## 🏗️ Build Process

### **Build Commands**
```bash
# Production build
npm run build

# Environment-specific builds
npm run build:dev
npm run build:staging
npm run build:production

# Build with bundle analysis
npm run build:analyze
```

### **Build Features**
- **Environment Validation**: Validates required environment variables
- **Pre-build Checks**: Runs linting and tests before building
- **API Client Generation**: Automatically generates API clients
- **Bundle Analysis**: Optional bundle size analysis
- **Production Safety**: Prevents development configurations in production

---

## 🚀 Deployment

### **Deployment Commands**
```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:production

# Dry run deployment
npm run deploy:dry-run

# Rollback deployment
npm run deploy:rollback v1.2.3
```

### **Deployment Targets**
| Environment | URL | Description |
|-------------|-----|-------------|
| Development | https://dev.onehrms.com | Development environment |
| Staging | https://staging.onehrms.com | Staging environment |
| Production | https://app.onehrms.com | Production environment |

---

## 🧪 Testing

### **Test Commands**
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:contract

# Run end-to-end tests
npm run test:e2e
```

### **Testing Strategy**
- **Unit Tests**: Component and hook testing with React Testing Library
- **Integration Tests**: Feature workflow testing
- **Contract Tests**: API contract validation
- **End-to-End Tests**: Full user journey testing with Playwright

---

## 🔌 API Integration

### **OpenAPI Client Generation**
```bash
# Generate API clients for all microservices
npm run generate:api

# Watch for changes and auto-regenerate
npm run generate:api:watch
```

### **Generated API Clients**
- **Employee API**: Employee management
- **Payroll API**: Payroll processing
- **Attendance API**: Attendance tracking
- **ESS API**: Employee self-service
- **Recruitment API**: Recruitment management

### **Usage Example**
```typescript
import { generatedApiService, Employee } from '@/services/generated-api-service';

// Type-safe API calls
const employees: Employee[] = await generatedApiService.getEmployees();
const employee: Employee = await generatedApiService.getEmployee(id);
```

---

## 🎨 Component Development

### **Component Structure**
```typescript
// Feature-based component organization
src/features/recruitment/
├── components/
│   ├── JobRequisitionList.tsx
│   ├── JobApplicationForm.tsx
│   └── InterviewScheduler.tsx
├── hooks/
│   ├── useJobRequisitions.ts
│   └── useInterviewScheduling.ts
└── services/
    └── recruitmentApi.ts
```

### **Component Guidelines**
- Use TypeScript for all components
- Follow Material-UI design system
- Implement proper error boundaries
- Include comprehensive tests
- Document component APIs

---

## 🔒 Authentication

### **Keycloak Integration**
- **OIDC Authentication**: Industry-standard authentication
- **Role-Based Access Control**: Fine-grained permissions
- **Multi-Tenant Support**: Tenant-aware authentication
- **Session Management**: Automatic token refresh

### **Authentication Context**
```typescript
import { useAuth } from '@/contexts/AuthContext';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <LoginButton onClick={login} />;
  }
  
  return <UserProfile user={user} onLogout={logout} />;
}
```

---

## 📊 Performance

### **Optimization Features**
- **Code Splitting**: Lazy loading of routes and components
- **Bundle Analysis**: Webpack bundle analyzer integration
- **Tree Shaking**: Automatic dead code elimination
- **Asset Optimization**: Image and asset optimization
- **Caching**: Intelligent caching strategies

### **Performance Monitoring**
```bash
# Analyze bundle size
npm run build:analyze

# Performance testing
npm run test:performance

# Lighthouse audit
npm run audit:lighthouse
```

---

## 🛠️ Development Tools

### **Code Quality**
```bash
# Linting
npm run lint
npm run lint:fix

# Type checking
npm run type-check

# Code formatting
npm run format
```

### **Development Utilities**
```bash
# Check environment
npm run env:check

# Health check
npm run dev:health

# Clean development environment
npm run dev:clean
```

---

## 📚 Documentation

### **Available Documentation**
- **Component Documentation**: Storybook integration
- **API Documentation**: Auto-generated from OpenAPI specs
- **Build Documentation**: Build and deployment guides
- **Testing Documentation**: Testing strategies and examples

### **Generate Documentation**
```bash
# Generate component documentation
npm run docs:components

# Generate API documentation
npm run docs:api

# Start documentation server
npm run docs:serve
```

---

## 🔄 Migration from Vue.js

### **Migration Status**: ✅ **COMPLETED**

The oneHRMS frontend has been successfully migrated from multiple Vue.js applications to a single React application:

- **✅ Vue.js Applications Removed**: All Vue.js code has been removed
- **✅ React Application Complete**: Full feature parity achieved
- **✅ Build Process Unified**: Single build and deployment process
- **✅ Documentation Updated**: All documentation reflects new architecture

### **Benefits Achieved**
- **Simplified Architecture**: Single frontend framework
- **Better Performance**: Optimized React application
- **Improved Developer Experience**: Unified tooling and patterns
- **Enhanced Maintainability**: Single codebase to maintain

---

**Last Updated**: August 2025  
**Version**: 1.0.0  
**Team**: oneHRMS Development Team
