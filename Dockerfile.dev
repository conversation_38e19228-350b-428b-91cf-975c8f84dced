# Development Dockerfile for oneHRMS
FROM python:3.10-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV TESTING=false

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libmariadb-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast package management
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# Create app directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml .
COPY pytest.ini .
COPY mypy.ini .
COPY .flake8 .
COPY .env.example .

# Create virtual environment and install dependencies
RUN uv venv .venv
ENV PATH="/app/.venv/bin:$PATH"

# Install Python dependencies
RUN uv pip install pytest pytest-cov black flake8 mypy

# Copy source code
COPY hrms/ ./hrms/
COPY tests/ ./tests/

# Create necessary directories
RUN mkdir -p logs uploads

# Set up development user (non-root)
RUN useradd -m -u 1000 developer && \
    chown -R developer:developer /app
USER developer

# Expose ports
EXPOSE 8000 8001 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command for development
CMD ["python", "-m", "pytest", "tests/", "-v"]
