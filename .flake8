[flake8]
# Flake8 configuration for oneHRMS

# Maximum line length
max-line-length = 110

# Exclude directories and files
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    htmlcov,
    .coverage,
    migrations,
    node_modules

# Error codes to ignore
ignore =
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    W504,  # line break after binary operator
    F401,  # imported but unused (handled by isort)
    F403,  # star import used
    F405,  # name may be undefined from star import
    D100,  # Missing docstring in public module
    D101,  # Missing docstring in public class
    D102,  # Missing docstring in public method
    D103,  # Missing docstring in public function
    D104,  # Missing docstring in public package
    D105   # Missing docstring in magic method

# Error codes to select
select = E,W,F,C

# Maximum complexity
max-complexity = 10

# Per-file ignores
per-file-ignores =
    __init__.py:F401
    tests/*:F401,F811
    */migrations/*:E501,F401

# Show source code for each error
show-source = True

# Show pep8 violation statistics
statistics = True

# Count errors and warnings
count = True
