#!/usr/bin/env python3
"""
Service startup script for oneHRMS microservices.

This script starts all the microservices in development mode
and provides a unified interface for managing them.
"""

import asyncio
import os
import signal
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import psutil
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.spinner import Spinner

console = Console()

# Service configurations
SERVICES = {
    "employee": {
        "name": "Employee Service",
        "port": 8001,
        "path": "hrms/employee",
        "main_file": "main.py",
        "process": None,
        "status": "stopped"
    },
    "attendance": {
        "name": "Attendance Service", 
        "port": 8002,
        "path": "hrms/attendance",
        "main_file": "main.py",
        "process": None,
        "status": "stopped"
    }
}

# Environment variables
ENV_VARS = {
    "ENVIRONMENT": "development",
    "LOG_LEVEL": "INFO",
    "DATABASE_URL": "sqlite:///./hrms_dev.db",  # Using SQLite for development
    "SECRET_KEY": "dev-secret-key-change-in-production",
    "ACCESS_TOKEN_EXPIRE_MINUTES": "60",
    "ALLOWED_ORIGINS": "*",
    "TRUSTED_HOSTS": "localhost,127.0.0.1"
}


class ServiceManager:
    """Manages microservice lifecycle."""
    
    def __init__(self):
        self.services = SERVICES.copy()
        self.running = False
    
    def setup_environment(self):
        """Setup environment variables."""
        for key, value in ENV_VARS.items():
            os.environ[key] = value
        
        console.print("✅ Environment variables configured", style="green")
    
    def check_port_availability(self, port: int) -> bool:
        """Check if a port is available."""
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return False
        return True
    
    def start_service(self, service_name: str) -> bool:
        """Start a single service."""
        service = self.services[service_name]
        
        # Check if port is available
        if not self.check_port_availability(service["port"]):
            console.print(f"❌ Port {service['port']} is already in use", style="red")
            return False
        
        # Check if service directory exists
        service_path = Path(service["path"])
        if not service_path.exists():
            console.print(f"❌ Service directory not found: {service_path}", style="red")
            return False
        
        main_file = service_path / service["main_file"]
        if not main_file.exists():
            console.print(f"❌ Main file not found: {main_file}", style="red")
            return False
        
        try:
            # Start the service
            cmd = [
                sys.executable, "-m", "uvicorn",
                f"{service['path'].replace('/', '.')}.main:app",
                "--host", "0.0.0.0",
                "--port", str(service["port"]),
                "--reload",
                "--log-level", "info"
            ]
            
            console.print(f"🚀 Starting {service['name']} on port {service['port']}...")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            service["process"] = process
            service["status"] = "starting"
            
            # Wait a moment to check if it started successfully
            time.sleep(2)
            
            if process.poll() is None:  # Process is still running
                service["status"] = "running"
                console.print(f"✅ {service['name']} started successfully", style="green")
                return True
            else:
                # Process died
                stdout, stderr = process.communicate()
                console.print(f"❌ {service['name']} failed to start", style="red")
                console.print(f"Error: {stderr.decode()}", style="red")
                service["status"] = "failed"
                return False
                
        except Exception as e:
            console.print(f"❌ Failed to start {service['name']}: {e}", style="red")
            service["status"] = "failed"
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """Stop a single service."""
        service = self.services[service_name]
        
        if service["process"] and service["process"].poll() is None:
            try:
                console.print(f"🛑 Stopping {service['name']}...")
                service["process"].terminate()
                
                # Wait for graceful shutdown
                try:
                    service["process"].wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't stop gracefully
                    service["process"].kill()
                    service["process"].wait()
                
                service["status"] = "stopped"
                service["process"] = None
                console.print(f"✅ {service['name']} stopped", style="green")
                return True
                
            except Exception as e:
                console.print(f"❌ Failed to stop {service['name']}: {e}", style="red")
                return False
        else:
            console.print(f"⚠️ {service['name']} is not running", style="yellow")
            return True
    
    def start_all_services(self) -> bool:
        """Start all services."""
        console.print(Panel.fit("🚀 Starting oneHRMS Microservices", style="bold magenta"))
        
        self.setup_environment()
        
        success_count = 0
        total_services = len(self.services)
        
        for service_name in self.services:
            if self.start_service(service_name):
                success_count += 1
        
        if success_count == total_services:
            console.print(f"\n🎉 All {total_services} services started successfully!", style="bold green")
            self.running = True
            return True
        else:
            console.print(f"\n⚠️ {success_count}/{total_services} services started", style="yellow")
            return False
    
    def stop_all_services(self):
        """Stop all services."""
        console.print("\n🛑 Stopping all services...", style="yellow")
        
        for service_name in self.services:
            self.stop_service(service_name)
        
        self.running = False
        console.print("✅ All services stopped", style="green")
    
    def get_service_status(self) -> Table:
        """Get current status of all services."""
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("Service", style="cyan")
        table.add_column("Port", justify="center")
        table.add_column("Status", justify="center")
        table.add_column("URL", style="dim")
        
        for service_name, service in self.services.items():
            status_style = {
                "running": "green",
                "starting": "yellow", 
                "stopped": "red",
                "failed": "red"
            }.get(service["status"], "white")
            
            status_icon = {
                "running": "🟢",
                "starting": "🟡",
                "stopped": "🔴", 
                "failed": "❌"
            }.get(service["status"], "⚪")
            
            url = f"http://localhost:{service['port']}" if service["status"] == "running" else "-"
            
            table.add_row(
                service["name"],
                str(service["port"]),
                f"{status_icon} {service['status'].upper()}",
                url
            )
        
        return table
    
    def monitor_services(self):
        """Monitor services and display status."""
        try:
            with Live(self.get_service_status(), refresh_per_second=1) as live:
                while self.running:
                    # Update service statuses
                    for service_name, service in self.services.items():
                        if service["process"]:
                            if service["process"].poll() is None:
                                service["status"] = "running"
                            else:
                                service["status"] = "failed"
                                console.print(f"❌ {service['name']} has crashed!", style="red")
                    
                    live.update(self.get_service_status())
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            pass
    
    def handle_shutdown(self, signum, frame):
        """Handle shutdown signal."""
        console.print("\n🛑 Received shutdown signal...", style="yellow")
        self.stop_all_services()
        sys.exit(0)


def main():
    """Main function."""
    manager = ServiceManager()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, manager.handle_shutdown)
    signal.signal(signal.SIGTERM, manager.handle_shutdown)
    
    try:
        # Start all services
        if manager.start_all_services():
            console.print("\n📊 Service Status Dashboard:", style="bold blue")
            console.print("Press Ctrl+C to stop all services\n")
            
            # Print service URLs
            console.print("🌐 Service URLs:", style="bold cyan")
            for service_name, service in manager.services.items():
                if service["status"] == "running":
                    console.print(f"   {service['name']}: http://localhost:{service['port']}")
                    console.print(f"   {service['name']} Docs: http://localhost:{service['port']}/docs")
            
            console.print("\n📚 Testing:")
            console.print("   Run: python test_microservices.py")
            console.print()
            
            # Monitor services
            manager.monitor_services()
        else:
            console.print("❌ Failed to start all services", style="red")
            manager.stop_all_services()
            sys.exit(1)
            
    except KeyboardInterrupt:
        manager.stop_all_services()
    except Exception as e:
        console.print(f"❌ Unexpected error: {e}", style="red")
        manager.stop_all_services()
        sys.exit(1)


if __name__ == "__main__":
    main()
