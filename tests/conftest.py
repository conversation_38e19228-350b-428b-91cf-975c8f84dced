"""
Pytest configuration and fixtures for oneHRMS test suite.
"""

import os
import shutil
import tempfile
from datetime import date, datetime
from typing import Any, Dict, Generator, List
from unittest.mock import MagicMock, Mock

import pytest
from fastapi.testclient import TestClient


@pytest.fixture(scope="session")
def test_config() -> Dict[str, Any]:
    """Test configuration fixture."""
    return {
        "testing": True,
        "database_url": "sqlite:///:memory:",
        "tenant_id": "test_tenant",
        "debug": False,
    }


@pytest.fixture(scope="function")
def sample_employee_data() -> Dict[str, Any]:
    """Sample employee data for testing."""
    return {
        "employee_id": "EMP001",
        "first_name": "<PERSON>",
        "last_name": "Do<PERSON>",
        "email": "<EMAIL>",
        "department": "Engineering",
        "designation": "Software Engineer",
        "date_of_joining": "2023-01-01",
        "tenant_id": "test_tenant",
    }


@pytest.fixture(scope="function")
def sample_payroll_data() -> Dict[str, Any]:
    """Sample payroll data for testing."""
    return {
        "employee_id": "EMP001",
        "salary_structure": "Standard",
        "basic_salary": 50000,
        "allowances": 10000,
        "deductions": 5000,
        "pay_period": "2023-01",
        "tenant_id": "test_tenant",
    }


@pytest.fixture(scope="function")
def sample_attendance_data() -> Dict[str, Any]:
    """Sample attendance data for testing."""
    return {
        "employee_id": "EMP001",
        "date": "2023-01-15",
        "check_in": "09:00:00",
        "check_out": "18:00:00",
        "status": "Present",
        "working_hours": 8.0,
        "tenant_id": "test_tenant",
    }


@pytest.fixture(scope="function")
def sample_leave_data() -> Dict[str, Any]:
    """Sample leave data for testing."""
    return {
        "employee_id": "EMP001",
        "leave_type": "Annual Leave",
        "from_date": "2023-02-01",
        "to_date": "2023-02-05",
        "total_days": 5,
        "reason": "Family vacation",
        "status": "Pending",
        "tenant_id": "test_tenant",
    }


@pytest.fixture(scope="function")
def sample_recruitment_data() -> Dict[str, Any]:
    """Sample recruitment data for testing."""
    return {
        "job_opening_id": "JOB001",
        "job_title": "Software Engineer",
        "department": "Engineering",
        "candidate_name": "Jane Smith",
        "candidate_email": "<EMAIL>",
        "application_date": "2023-01-10",
        "status": "Applied",
        "tenant_id": "test_tenant",
    }


@pytest.fixture(scope="function")
def mock_tenant_context():
    """Mock tenant context for multi-tenancy testing."""

    class MockTenantContext:
        def __init__(self, tenant_id: str = "test_tenant"):
            self.tenant_id = tenant_id

        def __enter__(self):
            os.environ["CURRENT_TENANT_ID"] = self.tenant_id
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            if "CURRENT_TENANT_ID" in os.environ:
                del os.environ["CURRENT_TENANT_ID"]

    return MockTenantContext


@pytest.fixture(scope="function")
def temp_directory():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture(scope="function")
def mock_database():
    """Mock database connection for testing."""
    mock_db = MagicMock()
    mock_db.execute.return_value = Mock()
    mock_db.fetchall.return_value = []
    mock_db.fetchone.return_value = None
    mock_db.commit.return_value = None
    mock_db.rollback.return_value = None
    return mock_db


@pytest.fixture(scope="function")
def mock_cache():
    """Mock cache for testing."""
    cache_data = {}

    class MockCache:
        def get(self, key):
            return cache_data.get(key)

        def set(self, key, value, timeout=None):
            cache_data[key] = value

        def delete(self, key):
            cache_data.pop(key, None)

        def clear(self):
            cache_data.clear()

    return MockCache()


@pytest.fixture(scope="function")
def sample_employees_list() -> List[Dict[str, Any]]:
    """Sample list of employees for testing."""
    return [
        {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "department": "Engineering",
            "tenant_id": "test_tenant",
        },
        {
            "employee_id": "EMP002",
            "first_name": "Jane",
            "last_name": "Smith",
            "email": "<EMAIL>",
            "department": "HR",
            "tenant_id": "test_tenant",
        },
        {
            "employee_id": "EMP003",
            "first_name": "Bob",
            "last_name": "Johnson",
            "email": "<EMAIL>",
            "department": "Finance",
            "tenant_id": "test_tenant",
        },
    ]


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Automatically set up test environment for each test."""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "ERROR"  # Reduce log noise during testing

    yield

    # Cleanup after test
    test_vars = ["TESTING", "LOG_LEVEL", "CURRENT_TENANT_ID"]
    for var in test_vars:
        if var in os.environ:
            del os.environ[var]


# Pytest markers for test categorization
pytest_plugins = []


# Custom markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line("markers", "integration: mark test as an integration test")
    config.addinivalue_line("markers", "e2e: mark test as an end-to-end test")
    config.addinivalue_line("markers", "slow: mark test as slow running")
    config.addinivalue_line("markers", "payroll: mark test as payroll module test")
    config.addinivalue_line("markers", "hr: mark test as HR module test")
    config.addinivalue_line("markers", "attendance: mark test as attendance module test")
    config.addinivalue_line("markers", "tenant: mark test as multi-tenancy test")
    config.addinivalue_line("markers", "api: mark test as API test")
    config.addinivalue_line("markers", "auth: mark test as authentication test")


@pytest.fixture(scope="function")
def client():
    """FastAPI test client fixture for attendance service."""
    from hrms.microservices.attendance.api import app

    return TestClient(app)
