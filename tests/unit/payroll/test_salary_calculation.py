"""
Unit tests for payroll salary calculation functionality.
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict

import pytest


class TestSalaryCalculation:
    """Test salary calculation logic."""

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_basic_salary_calculation(self, sample_payroll_data):
        """Test basic salary calculation."""
        # Mock salary calculation logic
        basic_salary = sample_payroll_data["basic_salary"]
        allowances = sample_payroll_data["allowances"]
        deductions = sample_payroll_data["deductions"]

        gross_salary = basic_salary + allowances
        net_salary = gross_salary - deductions

        assert gross_salary == 60000  # 50000 + 10000
        assert net_salary == 55000  # 60000 - 5000

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_tax_calculation(self):
        """Test tax calculation based on salary brackets."""
        test_cases = [
            {"annual_salary": 300000, "expected_tax": 0},  # Below tax threshold
            {"annual_salary": 600000, "expected_tax": 10000},  # 10% bracket: (600000-500000)*0.1
            {"annual_salary": 1200000, "expected_tax": 90000},  # Higher bracket
        ]

        for case in test_cases:
            # Mock tax calculation
            annual_salary = case["annual_salary"]
            if annual_salary <= 500000:
                tax = 0
            elif annual_salary <= 1000000:
                tax = (annual_salary - 500000) * 0.1
            else:
                tax = 50000 + (annual_salary - 1000000) * 0.2

            assert tax == case["expected_tax"]

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_overtime_calculation(self):
        """Test overtime calculation."""
        base_hourly_rate = Decimal("500")
        overtime_hours = Decimal("10")
        overtime_multiplier = Decimal("1.5")

        overtime_pay = base_hourly_rate * overtime_hours * overtime_multiplier
        expected_overtime = Decimal("7500")  # 500 * 10 * 1.5

        assert overtime_pay == expected_overtime

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_bonus_calculation(self):
        """Test bonus calculation based on performance."""
        base_salary = 50000
        performance_ratings = {
            "excellent": 0.20,
            "good": 0.10,
            "average": 0.05,
            "poor": 0.00,
        }

        for rating, multiplier in performance_ratings.items():
            bonus = base_salary * multiplier
            if rating == "excellent":
                assert bonus == 10000
            elif rating == "good":
                assert bonus == 5000
            elif rating == "average":
                assert bonus == 2500
            else:
                assert bonus == 0

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_provident_fund_calculation(self):
        """Test provident fund calculation."""
        basic_salary = 50000
        pf_employee_rate = 0.12
        pf_employer_rate = 0.12

        employee_pf = basic_salary * pf_employee_rate
        employer_pf = basic_salary * pf_employer_rate

        assert employee_pf == 6000
        assert employer_pf == 6000

    @pytest.mark.unit
    @pytest.mark.payroll
    @pytest.mark.tenant
    def test_salary_calculation_tenant_isolation(self, mock_tenant_context):
        """Test that salary calculations are tenant-isolated."""
        # Test with tenant 1
        with mock_tenant_context("tenant1"):
            salary_data_t1 = {
                "employee_id": "EMP001",
                "basic_salary": 50000,
                "tenant_id": "tenant1",
            }
            # Mock calculation would include tenant_id validation
            assert salary_data_t1["tenant_id"] == "tenant1"

        # Test with tenant 2
        with mock_tenant_context("tenant2"):
            salary_data_t2 = {
                "employee_id": "EMP001",  # Same employee ID, different tenant
                "basic_salary": 60000,
                "tenant_id": "tenant2",
            }
            assert salary_data_t2["tenant_id"] == "tenant2"

        # Verify different salaries for same employee in different tenants
        assert salary_data_t1["basic_salary"] != salary_data_t2["basic_salary"]

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_salary_slip_generation(self, sample_payroll_data):
        """Test salary slip data generation."""
        # Mock salary slip generation
        salary_slip = {
            "employee_id": sample_payroll_data["employee_id"],
            "pay_period": sample_payroll_data["pay_period"],
            "basic_salary": sample_payroll_data["basic_salary"],
            "allowances": sample_payroll_data["allowances"],
            "deductions": sample_payroll_data["deductions"],
            "gross_salary": sample_payroll_data["basic_salary"] + sample_payroll_data["allowances"],
            "net_salary": (sample_payroll_data["basic_salary"] + sample_payroll_data["allowances"])
            - sample_payroll_data["deductions"],
            "generated_date": datetime.now().isoformat(),
            "tenant_id": sample_payroll_data["tenant_id"],
        }

        # Validate salary slip structure
        required_fields = [
            "employee_id",
            "pay_period",
            "basic_salary",
            "allowances",
            "deductions",
            "gross_salary",
            "net_salary",
            "generated_date",
            "tenant_id",
        ]

        for field in required_fields:
            assert field in salary_slip

        # Validate calculations
        assert salary_slip["gross_salary"] == 60000
        assert salary_slip["net_salary"] == 55000

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_payroll_period_validation(self):
        """Test payroll period validation."""
        valid_periods = ["2023-01", "2023-12", "2024-06"]
        invalid_periods = ["2023-13", "2022", "invalid", ""]

        def validate_pay_period(period: str) -> bool:
            """Mock validation function."""
            if not period:
                return False
            try:
                year, month = period.split("-")
                year_int = int(year)
                month_int = int(month)
                return 2020 <= year_int <= 2030 and 1 <= month_int <= 12
            except (ValueError, AttributeError):
                return False

        # Test valid periods
        for period in valid_periods:
            assert validate_pay_period(period) is True

        # Test invalid periods
        for period in invalid_periods:
            assert validate_pay_period(period) is False

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_currency_precision(self):
        """Test currency calculation precision."""
        # Test with Decimal for precise currency calculations
        salary = Decimal("50000.00")
        tax_rate = Decimal("0.125")  # 12.5%

        tax_amount = salary * tax_rate
        net_salary = salary - tax_amount

        # Verify precision
        assert tax_amount == Decimal("6250.00")
        assert net_salary == Decimal("43750.00")

        # Test rounding
        irregular_amount = Decimal("50000.123456")
        rounded_amount = irregular_amount.quantize(Decimal("0.01"))
        assert rounded_amount == Decimal("50000.12")

    @pytest.mark.unit
    @pytest.mark.payroll
    def test_payroll_error_handling(self):
        """Test error handling in payroll calculations."""
        # Test division by zero
        with pytest.raises(ZeroDivisionError):
            result = 1000 / 0

        # Test negative salary
        def validate_salary(amount):
            if amount < 0:
                raise ValueError("Salary cannot be negative")
            return amount

        with pytest.raises(ValueError, match="Salary cannot be negative"):
            validate_salary(-1000)

        # Test missing required fields
        incomplete_data = {"employee_id": "EMP001"}  # Missing salary

        def process_payroll(data):
            required_fields = ["employee_id", "basic_salary", "tenant_id"]
            for field in required_fields:
                if field not in data:
                    raise KeyError(f"Missing required field: {field}")
            return data

        with pytest.raises(KeyError, match="Missing required field: basic_salary"):
            process_payroll(incomplete_data)
