"""
Unit tests for attendance time tracking functionality.
"""

from datetime import datetime, time, timedelta
from typing import Any, Dict, List

import pytest


class TestTimeTracking:
    """Test time tracking and attendance logic."""

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_check_in_out_calculation(self, sample_attendance_data):
        """Test check-in/check-out time calculation."""
        check_in_str = sample_attendance_data["check_in"]
        check_out_str = sample_attendance_data["check_out"]

        # Parse time strings
        check_in = datetime.strptime(check_in_str, "%H:%M:%S").time()
        check_out = datetime.strptime(check_out_str, "%H:%M:%S").time()

        # Calculate working hours
        check_in_dt = datetime.combine(datetime.today(), check_in)
        check_out_dt = datetime.combine(datetime.today(), check_out)

        working_duration = check_out_dt - check_in_dt
        working_hours = working_duration.total_seconds() / 3600

        assert working_hours == 9.0  # 18:00 - 09:00 = 9 hours
        assert sample_attendance_data["working_hours"] == 8.0  # Excluding lunch break

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_attendance_status_determination(self):
        """Test attendance status determination logic."""
        test_cases = [
            {
                "check_in": "09:00:00",
                "check_out": "18:00:00",
                "expected_status": "Present",
                "expected_hours": 8.0,  # Excluding 1-hour lunch
            },
            {
                "check_in": "10:30:00",  # Late arrival
                "check_out": "18:00:00",
                "expected_status": "Late",
                "expected_hours": 6.5,
            },
            {
                "check_in": "09:00:00",
                "check_out": "15:00:00",  # Early departure
                "expected_status": "Half Day",
                "expected_hours": 5.0,
            },
            {
                "check_in": None,
                "check_out": None,
                "expected_status": "Absent",
                "expected_hours": 0.0,
            },
        ]

        def determine_attendance_status(check_in: str, check_out: str) -> Dict[str, Any]:
            """Mock attendance status determination."""
            if not check_in or not check_out:
                return {"status": "Absent", "working_hours": 0.0}

            check_in_time = datetime.strptime(check_in, "%H:%M:%S").time()
            check_out_time = datetime.strptime(check_out, "%H:%M:%S").time()

            # Calculate working hours (excluding 1-hour lunch break)
            check_in_dt = datetime.combine(datetime.today(), check_in_time)
            check_out_dt = datetime.combine(datetime.today(), check_out_time)
            total_hours = (check_out_dt - check_in_dt).total_seconds() / 3600
            working_hours = max(0, total_hours - 1)  # Subtract lunch break

            # Determine status
            if check_in_time > time(9, 30):  # Late if after 9:30 AM
                status = "Late"
            elif working_hours < 6:
                status = "Half Day"
            else:
                status = "Present"

            return {"status": status, "working_hours": working_hours}

        for case in test_cases:
            result = determine_attendance_status(case["check_in"], case["check_out"])
            assert result["status"] == case["expected_status"]
            assert result["working_hours"] == case["expected_hours"]

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_overtime_calculation(self):
        """Test overtime calculation."""
        standard_hours = 8.0
        overtime_multiplier = 1.5

        test_cases = [
            {"working_hours": 8.0, "expected_overtime": 0.0},
            {"working_hours": 9.5, "expected_overtime": 1.5},
            {"working_hours": 12.0, "expected_overtime": 4.0},
        ]

        def calculate_overtime(working_hours: float) -> float:
            """Calculate overtime hours."""
            return max(0, working_hours - standard_hours)

        for case in test_cases:
            overtime = calculate_overtime(case["working_hours"])
            assert overtime == case["expected_overtime"]

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_shift_management(self):
        """Test shift management functionality."""
        shifts = {
            "morning": {"start": "06:00:00", "end": "14:00:00"},
            "day": {"start": "09:00:00", "end": "18:00:00"},
            "evening": {"start": "14:00:00", "end": "22:00:00"},
            "night": {"start": "22:00:00", "end": "06:00:00"},
        }

        def get_shift_for_time(check_in_time: str) -> str:
            """Determine shift based on check-in time."""
            check_in = datetime.strptime(check_in_time, "%H:%M:%S").time()

            if time(6, 0) <= check_in < time(9, 0):
                return "morning"
            elif time(9, 0) <= check_in < time(16, 0):
                return "day"
            elif time(16, 0) <= check_in < time(22, 0):
                return "evening"
            else:
                return "night"

        test_cases = [
            {"check_in": "07:30:00", "expected_shift": "morning"},
            {"check_in": "12:15:00", "expected_shift": "day"},  # Fixed: 12:15 is in day shift
            {"check_in": "17:45:00", "expected_shift": "evening"},  # Fixed: 17:45 is in evening shift
            {"check_in": "23:30:00", "expected_shift": "night"},
        ]

        for case in test_cases:
            shift = get_shift_for_time(case["check_in"])
            assert shift == case["expected_shift"]

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_break_time_tracking(self):
        """Test break time tracking."""
        attendance_record = {
            "check_in": "09:00:00",
            "break_start": "12:00:00",
            "break_end": "13:00:00",
            "check_out": "18:00:00",
        }

        def calculate_effective_working_hours(record: Dict[str, str]) -> float:
            """Calculate effective working hours excluding breaks."""
            check_in = datetime.strptime(record["check_in"], "%H:%M:%S")
            check_out = datetime.strptime(record["check_out"], "%H:%M:%S")

            total_time = check_out - check_in
            total_hours = total_time.total_seconds() / 3600

            # Subtract break time
            if record.get("break_start") and record.get("break_end"):
                break_start = datetime.strptime(record["break_start"], "%H:%M:%S")
                break_end = datetime.strptime(record["break_end"], "%H:%M:%S")
                break_duration = break_end - break_start
                break_hours = break_duration.total_seconds() / 3600
                total_hours -= break_hours

            return total_hours

        effective_hours = calculate_effective_working_hours(attendance_record)
        assert effective_hours == 8.0  # 9 hours total - 1 hour break

    @pytest.mark.unit
    @pytest.mark.attendance
    @pytest.mark.tenant
    def test_attendance_tenant_isolation(self, mock_tenant_context):
        """Test attendance data tenant isolation."""
        # Create attendance records for different tenants
        with mock_tenant_context("tenant1"):
            attendance_t1 = {
                "employee_id": "EMP001",
                "date": "2023-01-15",
                "status": "Present",
                "tenant_id": "tenant1",
            }

        with mock_tenant_context("tenant2"):
            attendance_t2 = {
                "employee_id": "EMP001",  # Same employee, different tenant
                "date": "2023-01-15",
                "status": "Absent",
                "tenant_id": "tenant2",
            }

        # Verify tenant isolation
        assert attendance_t1["tenant_id"] != attendance_t2["tenant_id"]
        assert attendance_t1["status"] != attendance_t2["status"]

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_monthly_attendance_summary(self):
        """Test monthly attendance summary calculation."""
        monthly_records = [
            {"date": "2023-01-01", "status": "Present", "working_hours": 8.0},
            {"date": "2023-01-02", "status": "Present", "working_hours": 8.5},
            {"date": "2023-01-03", "status": "Late", "working_hours": 7.5},
            {"date": "2023-01-04", "status": "Half Day", "working_hours": 4.0},
            {"date": "2023-01-05", "status": "Absent", "working_hours": 0.0},
        ]

        def calculate_monthly_summary(records: List[Dict[str, Any]]) -> Dict[str, Any]:
            """Calculate monthly attendance summary."""
            total_days = len(records)
            present_days = len([r for r in records if r["status"] in ["Present", "Late", "Half Day"]])
            absent_days = len([r for r in records if r["status"] == "Absent"])
            late_days = len([r for r in records if r["status"] == "Late"])
            half_days = len([r for r in records if r["status"] == "Half Day"])

            total_hours = sum(r["working_hours"] for r in records)
            average_hours = total_hours / present_days if present_days > 0 else 0

            attendance_percentage = (present_days / total_days) * 100 if total_days > 0 else 0

            return {
                "total_days": total_days,
                "present_days": present_days,
                "absent_days": absent_days,
                "late_days": late_days,
                "half_days": half_days,
                "total_hours": total_hours,
                "average_hours": round(average_hours, 2),
                "attendance_percentage": round(attendance_percentage, 2),
            }

        summary = calculate_monthly_summary(monthly_records)

        assert summary["total_days"] == 5
        assert summary["present_days"] == 4
        assert summary["absent_days"] == 1
        assert summary["late_days"] == 1
        assert summary["half_days"] == 1
        assert summary["total_hours"] == 28.0
        assert summary["average_hours"] == 7.0
        assert summary["attendance_percentage"] == 80.0

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_geolocation_validation(self):
        """Test geolocation-based attendance validation."""
        office_location = {"latitude": 40.7128, "longitude": -74.0060}  # NYC
        allowed_radius = 100  # meters

        test_locations = [
            {"latitude": 40.7128, "longitude": -74.0060, "expected_valid": True},  # Exact location
            {"latitude": 40.7130, "longitude": -74.0062, "expected_valid": True},  # Within radius
            {"latitude": 40.7200, "longitude": -74.0200, "expected_valid": False},  # Outside radius
        ]

        def is_location_valid(user_lat: float, user_lon: float) -> bool:
            """Validate if user location is within allowed radius."""
            import math

            # Simplified distance calculation (Haversine formula approximation)
            lat_diff = abs(user_lat - office_location["latitude"])
            lon_diff = abs(user_lon - office_location["longitude"])

            # Convert to approximate meters (rough calculation)
            distance = math.sqrt(lat_diff**2 + lon_diff**2) * 111000  # 1 degree ≈ 111km

            return distance <= allowed_radius

        for location in test_locations:
            is_valid = is_location_valid(location["latitude"], location["longitude"])
            assert is_valid == location["expected_valid"]

    @pytest.mark.unit
    @pytest.mark.attendance
    def test_attendance_policy_validation(self):
        """Test attendance policy validation."""
        attendance_policy = {
            "max_late_arrivals_per_month": 3,
            "max_early_departures_per_month": 2,
            "minimum_working_hours_per_day": 8.0,
            "maximum_continuous_absent_days": 3,
        }

        def validate_attendance_policy(records: List[Dict[str, Any]], policy: Dict[str, Any]) -> List[str]:
            """Validate attendance against policy and return violations."""
            violations = []

            late_count = len([r for r in records if r.get("status") == "Late"])
            if late_count > policy["max_late_arrivals_per_month"]:
                violations.append(f"Exceeded maximum late arrivals: {late_count}")

            insufficient_hours = [
                r for r in records if r.get("working_hours", 0) < policy["minimum_working_hours_per_day"]
            ]
            if insufficient_hours:
                violations.append(f"Insufficient working hours on {len(insufficient_hours)} days")

            return violations

        # Test compliant records
        compliant_records = [
            {"status": "Present", "working_hours": 8.0},
            {"status": "Present", "working_hours": 8.5},
            {"status": "Late", "working_hours": 8.0},
        ]

        violations = validate_attendance_policy(compliant_records, attendance_policy)
        assert len(violations) == 0

        # Test non-compliant records
        non_compliant_records = [
            {"status": "Late", "working_hours": 8.0},
            {"status": "Late", "working_hours": 8.0},
            {"status": "Late", "working_hours": 8.0},
            {"status": "Late", "working_hours": 8.0},  # 4 late arrivals (exceeds limit of 3)
            {"status": "Present", "working_hours": 6.0},  # Insufficient hours
        ]

        violations = validate_attendance_policy(non_compliant_records, attendance_policy)
        assert len(violations) > 0
        assert any("late arrivals" in violation for violation in violations)
