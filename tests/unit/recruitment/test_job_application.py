"""
Unit tests for recruitment job application functionality.
"""

from datetime import date, datetime
from typing import Any, Dict, List

import pytest


class TestJobApplication:
    """Test job application processing logic."""

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_job_application_creation(self, sample_recruitment_data):
        """Test job application creation."""
        application = {
            "application_id": "APP001",
            "job_opening_id": sample_recruitment_data["job_opening_id"],
            "candidate_name": sample_recruitment_data["candidate_name"],
            "candidate_email": sample_recruitment_data["candidate_email"],
            "application_date": sample_recruitment_data["application_date"],
            "status": sample_recruitment_data["status"],
            "tenant_id": sample_recruitment_data["tenant_id"],
        }

        # Validate application structure
        required_fields = [
            "application_id",
            "job_opening_id",
            "candidate_name",
            "candidate_email",
            "application_date",
            "status",
            "tenant_id",
        ]

        for field in required_fields:
            assert field in application

        assert application["status"] == "Applied"
        assert application["tenant_id"] == "test_tenant"

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_application_status_workflow(self):
        """Test application status workflow transitions."""
        valid_transitions = {
            "Applied": ["Under Review", "Rejected"],
            "Under Review": ["Interview Scheduled", "Rejected"],
            "Interview Scheduled": ["Interview Completed", "Cancelled"],
            "Interview Completed": ["Selected", "Rejected"],
            "Selected": ["Offer Extended"],
            "Offer Extended": ["Offer Accepted", "Offer Declined"],
            "Offer Accepted": ["Hired"],
            "Rejected": [],  # Terminal state
            "Cancelled": [],  # Terminal state
            "Hired": [],  # Terminal state
        }

        def can_transition(current_status: str, new_status: str) -> bool:
            """Check if status transition is valid."""
            return new_status in valid_transitions.get(current_status, [])

        # Test valid transitions
        assert can_transition("Applied", "Under Review") is True
        assert can_transition("Under Review", "Interview Scheduled") is True
        assert can_transition("Interview Completed", "Selected") is True

        # Test invalid transitions
        assert can_transition("Applied", "Hired") is False
        assert can_transition("Rejected", "Selected") is False
        assert can_transition("Hired", "Applied") is False

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_candidate_scoring(self):
        """Test candidate scoring algorithm."""
        candidate_profile = {
            "experience_years": 5,
            "education_level": "Masters",
            "skills_match": 0.8,  # 80% match
            "interview_score": 85,
            "reference_check": "Positive",
        }

        def calculate_candidate_score(profile: Dict[str, Any]) -> float:
            """Mock candidate scoring algorithm."""
            score = 0.0

            # Experience scoring (max 30 points)
            exp_years = profile.get("experience_years", 0)
            score += min(exp_years * 5, 30)

            # Education scoring (max 20 points)
            education_scores = {
                "PhD": 20,
                "Masters": 15,
                "Bachelors": 10,
                "Diploma": 5,
            }
            score += education_scores.get(profile.get("education_level"), 0)

            # Skills match (max 25 points)
            skills_match = profile.get("skills_match", 0)
            score += skills_match * 25

            # Interview score (max 25 points)
            interview_score = profile.get("interview_score", 0)
            score += (interview_score / 100) * 25

            return round(score, 2)

        total_score = calculate_candidate_score(candidate_profile)
        expected_score = 25 + 15 + 20 + 21.25  # 81.25
        assert total_score == expected_score

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_job_posting_validation(self):
        """Test job posting validation."""
        valid_job_posting = {
            "job_title": "Software Engineer",
            "department": "Engineering",
            "location": "Remote",
            "employment_type": "Full-time",
            "salary_range": {"min": 80000, "max": 120000},
            "required_skills": ["Python", "Django", "PostgreSQL"],
            "experience_required": "3-5 years",
            "posting_date": "2023-01-01",
            "application_deadline": "2023-02-01",
        }

        def validate_job_posting(posting: Dict[str, Any]) -> List[str]:
            """Validate job posting and return list of errors."""
            errors = []

            required_fields = [
                "job_title",
                "department",
                "employment_type",
                "posting_date",
                "application_deadline",
            ]

            for field in required_fields:
                if not posting.get(field):
                    errors.append(f"Missing required field: {field}")

            # Validate salary range
            salary_range = posting.get("salary_range", {})
            if salary_range:
                min_salary = salary_range.get("min", 0)
                max_salary = salary_range.get("max", 0)
                if min_salary >= max_salary:
                    errors.append("Minimum salary must be less than maximum salary")

            # Validate dates
            posting_date = posting.get("posting_date")
            deadline = posting.get("application_deadline")
            if posting_date and deadline and posting_date >= deadline:
                errors.append("Application deadline must be after posting date")

            return errors

        # Test valid posting
        errors = validate_job_posting(valid_job_posting)
        assert len(errors) == 0

        # Test invalid posting
        invalid_posting = {
            "job_title": "",  # Empty title
            "salary_range": {"min": 100000, "max": 80000},  # Invalid range
            "posting_date": "2023-02-01",
            "application_deadline": "2023-01-01",  # Before posting date
        }

        errors = validate_job_posting(invalid_posting)
        assert len(errors) > 0
        assert any("Missing required field" in error for error in errors)

    @pytest.mark.unit
    @pytest.mark.recruitment
    @pytest.mark.tenant
    def test_recruitment_tenant_isolation(self, mock_tenant_context):
        """Test recruitment data tenant isolation."""
        # Create applications for different tenants
        with mock_tenant_context("tenant1"):
            app_t1 = {
                "application_id": "APP001",
                "candidate_email": "<EMAIL>",
                "tenant_id": "tenant1",
            }

        with mock_tenant_context("tenant2"):
            app_t2 = {
                "application_id": "APP001",  # Same ID, different tenant
                "candidate_email": "<EMAIL>",
                "tenant_id": "tenant2",
            }

        # Verify tenant isolation
        assert app_t1["tenant_id"] != app_t2["tenant_id"]
        assert app_t1["tenant_id"] == "tenant1"
        assert app_t2["tenant_id"] == "tenant2"

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_interview_scheduling(self):
        """Test interview scheduling logic."""
        interview_data = {
            "application_id": "APP001",
            "interview_type": "Technical",
            "scheduled_date": "2023-02-15",
            "scheduled_time": "10:00",
            "duration_minutes": 60,
            "interviewer_ids": ["INT001", "INT002"],
            "location": "Conference Room A",
        }

        def schedule_interview(data: Dict[str, Any]) -> Dict[str, Any]:
            """Mock interview scheduling function."""
            # Validate required fields
            required_fields = [
                "application_id",
                "interview_type",
                "scheduled_date",
                "scheduled_time",
                "interviewer_ids",
            ]

            for field in required_fields:
                if field not in data:
                    raise ValueError(f"Missing required field: {field}")

            # Create interview record
            interview = {
                **data,
                "interview_id": f"INT_{data['application_id']}_{data['interview_type']}",
                "status": "Scheduled",
                "created_date": datetime.now().isoformat(),
            }

            return interview

        interview = schedule_interview(interview_data)

        assert interview["interview_id"] == "INT_APP001_Technical"
        assert interview["status"] == "Scheduled"
        assert "created_date" in interview

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_offer_generation(self):
        """Test job offer generation."""
        offer_data = {
            "application_id": "APP001",
            "candidate_name": "John Doe",
            "position": "Software Engineer",
            "salary": 100000,
            "start_date": "2023-03-01",
            "benefits": ["Health Insurance", "401k", "PTO"],
            "offer_valid_until": "2023-02-28",
        }

        def generate_offer(data: Dict[str, Any]) -> Dict[str, Any]:
            """Mock offer generation function."""
            offer = {
                "offer_id": f"OFFER_{data['application_id']}",
                "generated_date": datetime.now().isoformat(),
                "status": "Pending",
                **data,
            }

            return offer

        offer = generate_offer(offer_data)

        assert offer["offer_id"] == "OFFER_APP001"
        assert offer["status"] == "Pending"
        assert offer["salary"] == 100000
        assert len(offer["benefits"]) == 3

    @pytest.mark.unit
    @pytest.mark.recruitment
    def test_recruitment_analytics(self):
        """Test recruitment analytics calculations."""
        applications_data = [
            {"status": "Applied", "application_date": "2023-01-01"},
            {"status": "Under Review", "application_date": "2023-01-02"},
            {"status": "Interview Scheduled", "application_date": "2023-01-03"},
            {"status": "Selected", "application_date": "2023-01-04"},
            {"status": "Rejected", "application_date": "2023-01-05"},
            {"status": "Hired", "application_date": "2023-01-06"},
        ]

        def calculate_recruitment_metrics(applications: List[Dict[str, Any]]) -> Dict[str, Any]:
            """Calculate recruitment metrics."""
            total_applications = len(applications)
            status_counts = {}

            for app in applications:
                status = app["status"]
                status_counts[status] = status_counts.get(status, 0) + 1

            hired_count = status_counts.get("Hired", 0)
            conversion_rate = (hired_count / total_applications) * 100 if total_applications > 0 else 0

            return {
                "total_applications": total_applications,
                "status_breakdown": status_counts,
                "conversion_rate": round(conversion_rate, 2),
                "hired_count": hired_count,
            }

        metrics = calculate_recruitment_metrics(applications_data)

        assert metrics["total_applications"] == 6
        assert metrics["hired_count"] == 1
        assert metrics["conversion_rate"] == 16.67  # 1/6 * 100
