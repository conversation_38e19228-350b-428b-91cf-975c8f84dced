"""
Unit tests for Recruitment service business logic.

This module tests the RecruitmentService class which contains all business
logic for recruitment operations including job management, candidate tracking,
application processing, interview scheduling, and offer management.
"""

from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock

import pytest

from hrms.microservices.recruitment.models import ApplicationStatus, InterviewStatus, JobStatus, OfferStatus
from hrms.microservices.recruitment.service import RecruitmentService
from hrms.microservices.shared.exceptions import (
    BusinessLogicError,
    ConflictError,
    NotFoundError,
    ValidationError,
)

from . import get_test_tenant_id, get_test_user_id


class TestJobManagement:
    """Test job management functionality."""

    @pytest.mark.asyncio
    async def test_create_job_success(self, recruitment_service, sample_job_data):
        """Test successful job creation."""
        # Act
        job = await recruitment_service.create_job(get_test_tenant_id(), sample_job_data)

        # Assert
        assert job.title == sample_job_data["title"]
        assert job.description == sample_job_data["description"]
        assert job.department == sample_job_data["department"]
        assert job.status == JobStatus.DRAFT
        assert job.tenant_id == get_test_tenant_id()

    @pytest.mark.asyncio
    async def test_create_job_validation_error(self, recruitment_service):
        """Test job creation with invalid data."""
        # Arrange
        invalid_data = {
            "title": "",  # Empty title should fail
            "description": "Valid description",
            "department": "Engineering",
        }

        # Act & Assert
        with pytest.raises(ValidationError):
            await recruitment_service.create_job(get_test_tenant_id(), invalid_data)

    @pytest.mark.asyncio
    async def test_publish_job_success(self, recruitment_service, sample_job):
        """Test successful job publishing."""
        # Act
        published_job = await recruitment_service.publish_job(get_test_tenant_id(), sample_job.id)

        # Assert
        assert published_job.status == JobStatus.PUBLISHED
        assert published_job.posted_date is not None

    @pytest.mark.asyncio
    async def test_publish_job_not_found(self, recruitment_service):
        """Test publishing non-existent job."""
        # Act & Assert
        with pytest.raises(NotFoundError):
            await recruitment_service.publish_job(get_test_tenant_id(), "non-existent-id")

    @pytest.mark.asyncio
    async def test_close_job_success(self, recruitment_service, sample_job):
        """Test successful job closing."""
        # Arrange - First publish the job
        await recruitment_service.publish_job(get_test_tenant_id(), sample_job.id)

        # Act
        closed_job = await recruitment_service.close_job(get_test_tenant_id(), sample_job.id)

        # Assert
        assert closed_job.status == JobStatus.CLOSED

    @pytest.mark.asyncio
    async def test_get_jobs_with_filters(self, recruitment_service, sample_job):
        """Test getting jobs with status filter."""
        # Act
        jobs = await recruitment_service.get_jobs(get_test_tenant_id(), status=JobStatus.PUBLISHED)

        # Assert
        assert len(jobs) >= 1
        assert all(job.status == JobStatus.PUBLISHED for job in jobs)

    @pytest.mark.asyncio
    async def test_get_job_by_id_success(self, recruitment_service, sample_job):
        """Test getting job by ID."""
        # Act
        job = await recruitment_service.get_job_by_id(get_test_tenant_id(), sample_job.id)

        # Assert
        assert job.id == sample_job.id
        assert job.title == sample_job.title

    @pytest.mark.asyncio
    async def test_get_job_by_id_not_found(self, recruitment_service):
        """Test getting non-existent job."""
        # Act & Assert
        with pytest.raises(NotFoundError):
            await recruitment_service.get_job_by_id(get_test_tenant_id(), "non-existent-id")


class TestCandidateManagement:
    """Test candidate management functionality."""

    @pytest.mark.asyncio
    async def test_create_candidate_success(self, recruitment_service, sample_candidate_data):
        """Test successful candidate creation."""
        # Act
        candidate = await recruitment_service.create_candidate(get_test_tenant_id(), sample_candidate_data)

        # Assert
        assert candidate.first_name == sample_candidate_data["first_name"]
        assert candidate.last_name == sample_candidate_data["last_name"]
        assert candidate.email == sample_candidate_data["email"]
        assert candidate.tenant_id == get_test_tenant_id()

    @pytest.mark.asyncio
    async def test_create_candidate_duplicate_email(self, recruitment_service, sample_candidate):
        """Test creating candidate with duplicate email."""
        # Arrange
        duplicate_data = {
            "first_name": "Jane",
            "last_name": "Smith",
            "email": sample_candidate.email,  # Same email as existing candidate
        }

        # Act & Assert
        with pytest.raises(ConflictError):
            await recruitment_service.create_candidate(get_test_tenant_id(), duplicate_data)

    @pytest.mark.asyncio
    async def test_update_candidate_success(self, recruitment_service, sample_candidate):
        """Test successful candidate update."""
        # Arrange
        update_data = {
            "phone": "******-9999",
            "current_company": "New Company",
        }

        # Act
        updated_candidate = await recruitment_service.update_candidate(
            get_test_tenant_id(), sample_candidate.id, update_data
        )

        # Assert
        assert updated_candidate.phone == update_data["phone"]
        assert updated_candidate.current_company == update_data["current_company"]

    @pytest.mark.asyncio
    async def test_search_candidates(self, recruitment_service, sample_candidate):
        """Test candidate search functionality."""
        # Act
        candidates = await recruitment_service.search_candidates(get_test_tenant_id(), search_term="John")

        # Assert
        assert len(candidates) >= 1
        assert any("John" in candidate.first_name for candidate in candidates)

    @pytest.mark.asyncio
    async def test_upload_resume_success(self, recruitment_service, sample_candidate, mock_resume_upload):
        """Test successful resume upload."""
        # Act
        updated_candidate = await recruitment_service.upload_resume(
            get_test_tenant_id(), sample_candidate.id, mock_resume_upload
        )

        # Assert
        assert updated_candidate.resume_file_path is not None
        assert "resume.pdf" in updated_candidate.resume_file_path


class TestApplicationManagement:
    """Test application management functionality."""

    @pytest.mark.asyncio
    async def test_submit_application_success(self, recruitment_service, sample_job, sample_candidate):
        """Test successful application submission."""
        # Arrange
        application_data = {
            "job_id": sample_job.id,
            "candidate_id": sample_candidate.id,
            "cover_letter": "I am very interested in this position...",
        }

        # Act
        application = await recruitment_service.submit_application(get_test_tenant_id(), application_data)

        # Assert
        assert application.job_id == sample_job.id
        assert application.candidate_id == sample_candidate.id
        assert application.status == ApplicationStatus.SUBMITTED
        assert application.applied_date is not None

    @pytest.mark.asyncio
    async def test_submit_duplicate_application(self, recruitment_service, sample_application):
        """Test submitting duplicate application."""
        # Arrange
        duplicate_data = {
            "job_id": sample_application.job_id,
            "candidate_id": sample_application.candidate_id,
            "cover_letter": "Another application...",
        }

        # Act & Assert
        with pytest.raises(ConflictError):
            await recruitment_service.submit_application(get_test_tenant_id(), duplicate_data)

    @pytest.mark.asyncio
    async def test_update_application_status_success(self, recruitment_service, sample_application):
        """Test successful application status update."""
        # Act
        updated_application = await recruitment_service.update_application_status(
            get_test_tenant_id(),
            sample_application.id,
            ApplicationStatus.UNDER_REVIEW,
            reviewer_id=get_test_user_id(),
            notes="Initial review completed",
        )

        # Assert
        assert updated_application.status == ApplicationStatus.UNDER_REVIEW
        assert updated_application.reviewer_id == get_test_user_id()
        assert updated_application.review_notes == "Initial review completed"

    @pytest.mark.asyncio
    async def test_get_applications_by_job(self, recruitment_service, sample_application):
        """Test getting applications by job."""
        # Act
        applications = await recruitment_service.get_applications_by_job(
            get_test_tenant_id(), sample_application.job_id
        )

        # Assert
        assert len(applications) >= 1
        assert all(app.job_id == sample_application.job_id for app in applications)

    @pytest.mark.asyncio
    async def test_get_applications_by_candidate(self, recruitment_service, sample_application):
        """Test getting applications by candidate."""
        # Act
        applications = await recruitment_service.get_applications_by_candidate(
            get_test_tenant_id(), sample_application.candidate_id
        )

        # Assert
        assert len(applications) >= 1
        assert all(app.candidate_id == sample_application.candidate_id for app in applications)


class TestInterviewManagement:
    """Test interview management functionality."""

    @pytest.mark.asyncio
    async def test_schedule_interview_success(self, recruitment_service, sample_application):
        """Test successful interview scheduling."""
        # Arrange
        interview_data = {
            "application_id": sample_application.id,
            "interviewer_id": get_test_user_id(),
            "interview_type": "Technical",
            "scheduled_date": datetime.now() + timedelta(days=2),
            "duration_minutes": 60,
            "location": "Conference Room A",
        }

        # Act
        interview = await recruitment_service.schedule_interview(get_test_tenant_id(), interview_data)

        # Assert
        assert interview.application_id == sample_application.id
        assert interview.interviewer_id == get_test_user_id()
        assert interview.status == InterviewStatus.SCHEDULED
        assert interview.interview_type == "Technical"

    @pytest.mark.asyncio
    async def test_schedule_interview_past_date(self, recruitment_service, sample_application):
        """Test scheduling interview with past date."""
        # Arrange
        interview_data = {
            "application_id": sample_application.id,
            "interviewer_id": get_test_user_id(),
            "interview_type": "Technical",
            "scheduled_date": datetime.now() - timedelta(days=1),  # Past date
            "duration_minutes": 60,
        }

        # Act & Assert
        with pytest.raises(ValidationError):
            await recruitment_service.schedule_interview(get_test_tenant_id(), interview_data)

    @pytest.mark.asyncio
    async def test_complete_interview_success(self, recruitment_service, sample_interview):
        """Test successful interview completion."""
        # Arrange
        feedback_data = {
            "feedback": "Candidate performed well in technical questions",
            "rating": Decimal("4.5"),
            "recommendation": "Proceed to next round",
        }

        # Act
        completed_interview = await recruitment_service.complete_interview(
            get_test_tenant_id(), sample_interview.id, feedback_data
        )

        # Assert
        assert completed_interview.status == InterviewStatus.COMPLETED
        assert completed_interview.feedback == feedback_data["feedback"]
        assert completed_interview.rating == feedback_data["rating"]
        assert completed_interview.recommendation == feedback_data["recommendation"]

    @pytest.mark.asyncio
    async def test_cancel_interview_success(self, recruitment_service, sample_interview):
        """Test successful interview cancellation."""
        # Act
        cancelled_interview = await recruitment_service.cancel_interview(
            get_test_tenant_id(), sample_interview.id, "Candidate unavailable"
        )

        # Assert
        assert cancelled_interview.status == InterviewStatus.CANCELLED
        assert cancelled_interview.cancellation_reason == "Candidate unavailable"


class TestOfferManagement:
    """Test offer management functionality."""

    @pytest.mark.asyncio
    async def test_create_offer_success(self, recruitment_service, sample_application):
        """Test successful offer creation."""
        # Arrange
        offer_data = {
            "application_id": sample_application.id,
            "salary": Decimal("150000"),
            "currency": "USD",
            "start_date": date.today() + timedelta(days=30),
            "benefits": "Health insurance, 401k, flexible PTO",
            "terms_conditions": "Standard employment terms apply",
            "response_deadline": date.today() + timedelta(days=7),
        }

        # Act
        offer = await recruitment_service.create_offer(
            get_test_tenant_id(), offer_data, created_by=get_test_user_id()
        )

        # Assert
        assert offer.application_id == sample_application.id
        assert offer.salary == offer_data["salary"]
        assert offer.status == OfferStatus.DRAFT
        assert offer.created_by == get_test_user_id()

    @pytest.mark.asyncio
    async def test_send_offer_success(self, recruitment_service, sample_offer):
        """Test successful offer sending."""
        # Act
        sent_offer = await recruitment_service.send_offer(get_test_tenant_id(), sample_offer.id)

        # Assert
        assert sent_offer.status == OfferStatus.SENT
        assert sent_offer.sent_date is not None

    @pytest.mark.asyncio
    async def test_accept_offer_success(self, recruitment_service, sample_offer):
        """Test successful offer acceptance."""
        # Arrange - First send the offer
        await recruitment_service.send_offer(get_test_tenant_id(), sample_offer.id)

        # Act
        accepted_offer = await recruitment_service.accept_offer(get_test_tenant_id(), sample_offer.id)

        # Assert
        assert accepted_offer.status == OfferStatus.ACCEPTED
        assert accepted_offer.accepted_date is not None

    @pytest.mark.asyncio
    async def test_reject_offer_success(self, recruitment_service, sample_offer):
        """Test successful offer rejection."""
        # Arrange - First send the offer
        await recruitment_service.send_offer(get_test_tenant_id(), sample_offer.id)

        # Act
        rejected_offer = await recruitment_service.reject_offer(
            get_test_tenant_id(), sample_offer.id, "Salary not competitive"
        )

        # Assert
        assert rejected_offer.status == OfferStatus.REJECTED
        assert rejected_offer.rejected_date is not None
        assert rejected_offer.rejection_reason == "Salary not competitive"

    @pytest.mark.asyncio
    async def test_withdraw_offer_success(self, recruitment_service, sample_offer):
        """Test successful offer withdrawal."""
        # Arrange - First send the offer
        await recruitment_service.send_offer(get_test_tenant_id(), sample_offer.id)

        # Act
        withdrawn_offer = await recruitment_service.withdraw_offer(
            get_test_tenant_id(), sample_offer.id, "Position no longer available"
        )

        # Assert
        assert withdrawn_offer.status == OfferStatus.WITHDRAWN
        assert withdrawn_offer.rejection_reason == "Position no longer available"


class TestBusinessLogic:
    """Test business logic and validation rules."""

    @pytest.mark.asyncio
    async def test_tenant_isolation(self, recruitment_service, sample_job_data):
        """Test that data is properly isolated by tenant."""
        # Arrange
        tenant1_id = "tenant-1"
        tenant2_id = "tenant-2"

        # Act - Create job for tenant 1
        job1 = await recruitment_service.create_job(tenant1_id, sample_job_data)

        # Try to access from tenant 2
        with pytest.raises(NotFoundError):
            await recruitment_service.get_job_by_id(tenant2_id, job1.id)

    @pytest.mark.asyncio
    async def test_application_workflow_validation(self, recruitment_service, sample_job, sample_candidate):
        """Test application workflow validation."""
        # Arrange - Create application
        application_data = {
            "job_id": sample_job.id,
            "candidate_id": sample_candidate.id,
            "cover_letter": "Test application",
        }
        application = await recruitment_service.submit_application(get_test_tenant_id(), application_data)

        # Act & Assert - Try to move from SUBMITTED to OFFERED (should fail)
        with pytest.raises(BusinessLogicError):
            await recruitment_service.update_application_status(
                get_test_tenant_id(),
                application.id,
                ApplicationStatus.OFFERED,  # Invalid transition
                reviewer_id=get_test_user_id(),
            )

    @pytest.mark.asyncio
    async def test_salary_validation(self, recruitment_service, sample_job_data):
        """Test salary range validation."""
        # Arrange
        invalid_job_data = sample_job_data.copy()
        invalid_job_data["salary_min"] = 200000
        invalid_job_data["salary_max"] = 100000  # Max less than min

        # Act & Assert
        with pytest.raises(ValidationError):
            await recruitment_service.create_job(get_test_tenant_id(), invalid_job_data)

    @pytest.mark.asyncio
    async def test_interview_scheduling_conflicts(self, recruitment_service, sample_application):
        """Test interview scheduling conflict detection."""
        # Arrange
        interview_time = datetime.now() + timedelta(days=2)

        # Create first interview
        interview_data1 = {
            "application_id": sample_application.id,
            "interviewer_id": get_test_user_id(),
            "interview_type": "Technical",
            "scheduled_date": interview_time,
            "duration_minutes": 60,
        }
        await recruitment_service.schedule_interview(get_test_tenant_id(), interview_data1)

        # Try to create overlapping interview
        interview_data2 = {
            "application_id": sample_application.id,
            "interviewer_id": get_test_user_id(),
            "interview_type": "Behavioral",
            "scheduled_date": interview_time + timedelta(minutes=30),  # Overlapping
            "duration_minutes": 60,
        }

        # Act & Assert
        with pytest.raises(ConflictError):
            await recruitment_service.schedule_interview(get_test_tenant_id(), interview_data2)
