"""
Integration tests for Recruitment API endpoints.

This module tests the FastAPI endpoints for the Recruitment service including
HTTP request/response handling, authentication, validation, and error handling.
"""

from datetime import date, timedelta

import pytest
from fastapi import status
from httpx import AsyncClient

from . import get_test_tenant_id


class TestJobEndpoints:
    """Test job management API endpoints."""

    @pytest.mark.asyncio
    async def test_create_job_success(self, async_client: AsyncClient, sample_job_data):
        """Test successful job creation via API."""
        # Act
        response = await async_client.post("/api/v1/recruitment/jobs", json=sample_job_data)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        job_data = response.json()
        assert job_data["title"] == sample_job_data["title"]
        assert job_data["department"] == sample_job_data["department"]
        assert job_data["status"] == "draft"

    @pytest.mark.asyncio
    async def test_create_job_validation_error(self, async_client: AsyncClient):
        """Test job creation with invalid data."""
        # Arrange
        invalid_data = {
            "title": "",  # Empty title
            "description": "Valid description",
            # Missing required department
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/jobs", json=invalid_data)

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_get_jobs_success(self, async_client: AsyncClient, sample_job):
        """Test getting jobs list."""
        # Act
        response = await async_client.get("/api/v1/recruitment/jobs")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        jobs = response.json()
        assert isinstance(jobs, list)
        assert len(jobs) >= 1

    @pytest.mark.asyncio
    async def test_get_jobs_with_filters(self, async_client: AsyncClient, sample_job):
        """Test getting jobs with status filter."""
        # Act
        response = await async_client.get("/api/v1/recruitment/jobs", params={"status": "published"})

        # Assert
        assert response.status_code == status.HTTP_200_OK
        jobs = response.json()
        assert isinstance(jobs, list)

    @pytest.mark.asyncio
    async def test_get_job_by_id_success(self, async_client: AsyncClient, sample_job):
        """Test getting specific job by ID."""
        # Act
        response = await async_client.get(f"/api/v1/recruitment/jobs/{sample_job.id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        job_data = response.json()
        assert job_data["id"] == str(sample_job.id)
        assert job_data["title"] == sample_job.title

    @pytest.mark.asyncio
    async def test_get_job_by_id_not_found(self, async_client: AsyncClient):
        """Test getting non-existent job."""
        # Act
        response = await async_client.get("/api/v1/recruitment/jobs/non-existent-id")

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_update_job_success(self, async_client: AsyncClient, sample_job):
        """Test successful job update."""
        # Arrange
        update_data = {
            "title": "Updated Job Title",
            "location": "Remote",
        }

        # Act
        response = await async_client.put(f"/api/v1/recruitment/jobs/{sample_job.id}", json=update_data)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        job_data = response.json()
        assert job_data["title"] == update_data["title"]
        assert job_data["location"] == update_data["location"]

    @pytest.mark.asyncio
    async def test_publish_job_success(self, async_client: AsyncClient, sample_job):
        """Test successful job publishing."""
        # Act
        response = await async_client.patch(f"/api/v1/recruitment/jobs/{sample_job.id}/publish")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        job_data = response.json()
        assert job_data["status"] == "published"
        assert job_data["posted_date"] is not None

    @pytest.mark.asyncio
    async def test_close_job_success(self, async_client: AsyncClient, sample_job):
        """Test successful job closing."""
        # Arrange - First publish the job
        await async_client.patch(f"/api/v1/recruitment/jobs/{sample_job.id}/publish")

        # Act
        response = await async_client.patch(f"/api/v1/recruitment/jobs/{sample_job.id}/close")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        job_data = response.json()
        assert job_data["status"] == "closed"


class TestCandidateEndpoints:
    """Test candidate management API endpoints."""

    @pytest.mark.asyncio
    async def test_create_candidate_success(self, async_client: AsyncClient, sample_candidate_data):
        """Test successful candidate creation via API."""
        # Act
        response = await async_client.post("/api/v1/recruitment/candidates", json=sample_candidate_data)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        candidate_data = response.json()
        assert candidate_data["first_name"] == sample_candidate_data["first_name"]
        assert candidate_data["email"] == sample_candidate_data["email"]

    @pytest.mark.asyncio
    async def test_create_candidate_duplicate_email(self, async_client: AsyncClient, sample_candidate):
        """Test creating candidate with duplicate email."""
        # Arrange
        duplicate_data = {
            "first_name": "Jane",
            "last_name": "Smith",
            "email": sample_candidate.email,  # Same email
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/candidates", json=duplicate_data)

        # Assert
        assert response.status_code == status.HTTP_409_CONFLICT

    @pytest.mark.asyncio
    async def test_get_candidates_success(self, async_client: AsyncClient, sample_candidate):
        """Test getting candidates list."""
        # Act
        response = await async_client.get("/api/v1/recruitment/candidates")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        candidates = response.json()
        assert isinstance(candidates, list)
        assert len(candidates) >= 1

    @pytest.mark.asyncio
    async def test_get_candidates_with_search(self, async_client: AsyncClient, sample_candidate):
        """Test getting candidates with search."""
        # Act
        response = await async_client.get("/api/v1/recruitment/candidates", params={"search": "John"})

        # Assert
        assert response.status_code == status.HTTP_200_OK
        candidates = response.json()
        assert isinstance(candidates, list)

    @pytest.mark.asyncio
    async def test_get_candidate_by_id_success(self, async_client: AsyncClient, sample_candidate):
        """Test getting specific candidate by ID."""
        # Act
        response = await async_client.get(f"/api/v1/recruitment/candidates/{sample_candidate.id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        candidate_data = response.json()
        assert candidate_data["id"] == str(sample_candidate.id)
        assert candidate_data["email"] == sample_candidate.email

    @pytest.mark.asyncio
    async def test_update_candidate_success(self, async_client: AsyncClient, sample_candidate):
        """Test successful candidate update."""
        # Arrange
        update_data = {
            "phone": "******-9999",
            "current_company": "New Company",
        }

        # Act
        response = await async_client.put(
            f"/api/v1/recruitment/candidates/{sample_candidate.id}", json=update_data
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        candidate_data = response.json()
        assert candidate_data["phone"] == update_data["phone"]
        assert candidate_data["current_company"] == update_data["current_company"]

    @pytest.mark.asyncio
    async def test_upload_resume_success(self, async_client: AsyncClient, sample_candidate, temp_resume_file):
        """Test successful resume upload."""
        # Act
        with open(temp_resume_file, "rb") as resume_file:
            response = await async_client.post(
                f"/api/v1/recruitment/candidates/{sample_candidate.id}/resume",
                files={"file": ("resume.pdf", resume_file, "application/pdf")},
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        candidate_data = response.json()
        assert candidate_data["resume_file_path"] is not None

    @pytest.mark.asyncio
    async def test_upload_resume_invalid_file_type(self, async_client: AsyncClient, sample_candidate):
        """Test resume upload with invalid file type."""
        # Act
        response = await async_client.post(
            f"/api/v1/recruitment/candidates/{sample_candidate.id}/resume",
            files={"file": ("resume.txt", b"text content", "text/plain")},
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST


class TestApplicationEndpoints:
    """Test application management API endpoints."""

    @pytest.mark.asyncio
    async def test_submit_application_success(self, async_client: AsyncClient, sample_job, sample_candidate):
        """Test successful application submission."""
        # Arrange
        application_data = {
            "job_id": str(sample_job.id),
            "candidate_id": str(sample_candidate.id),
            "cover_letter": "I am very interested in this position...",
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/applications", json=application_data)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        app_data = response.json()
        assert app_data["job_id"] == application_data["job_id"]
        assert app_data["candidate_id"] == application_data["candidate_id"]
        assert app_data["status"] == "submitted"

    @pytest.mark.asyncio
    async def test_submit_duplicate_application(self, async_client: AsyncClient, sample_application):
        """Test submitting duplicate application."""
        # Arrange
        duplicate_data = {
            "job_id": str(sample_application.job_id),
            "candidate_id": str(sample_application.candidate_id),
            "cover_letter": "Another application...",
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/applications", json=duplicate_data)

        # Assert
        assert response.status_code == status.HTTP_409_CONFLICT

    @pytest.mark.asyncio
    async def test_get_applications_success(self, async_client: AsyncClient, sample_application):
        """Test getting applications list."""
        # Act
        response = await async_client.get("/api/v1/recruitment/applications")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        applications = response.json()
        assert isinstance(applications, list)
        assert len(applications) >= 1

    @pytest.mark.asyncio
    async def test_get_application_by_id_success(self, async_client: AsyncClient, sample_application):
        """Test getting specific application by ID."""
        # Act
        response = await async_client.get(f"/api/v1/recruitment/applications/{sample_application.id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        app_data = response.json()
        assert app_data["id"] == str(sample_application.id)

    @pytest.mark.asyncio
    async def test_update_application_status_success(self, async_client: AsyncClient, sample_application):
        """Test successful application status update."""
        # Arrange
        status_data = {
            "status": "under_review",
            "notes": "Initial review completed",
        }

        # Act
        response = await async_client.patch(
            f"/api/v1/recruitment/applications/{sample_application.id}/status", json=status_data
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        app_data = response.json()
        assert app_data["status"] == status_data["status"]
        assert app_data["review_notes"] == status_data["notes"]


class TestInterviewEndpoints:
    """Test interview management API endpoints."""

    @pytest.mark.asyncio
    async def test_schedule_interview_success(self, async_client: AsyncClient, sample_application):
        """Test successful interview scheduling."""
        # Arrange
        interview_data = {
            "application_id": str(sample_application.id),
            "interviewer_id": "interviewer-123",
            "interview_type": "Technical",
            "scheduled_date": (date.today() + timedelta(days=2)).isoformat() + "T10:00:00",
            "duration_minutes": 60,
            "location": "Conference Room A",
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/interviews", json=interview_data)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        interview_resp = response.json()
        assert interview_resp["application_id"] == interview_data["application_id"]
        assert interview_resp["interview_type"] == interview_data["interview_type"]
        assert interview_resp["status"] == "scheduled"

    @pytest.mark.asyncio
    async def test_get_interviews_success(self, async_client: AsyncClient, sample_interview):
        """Test getting interviews list."""
        # Act
        response = await async_client.get("/api/v1/recruitment/interviews")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        interviews = response.json()
        assert isinstance(interviews, list)
        assert len(interviews) >= 1

    @pytest.mark.asyncio
    async def test_get_interview_by_id_success(self, async_client: AsyncClient, sample_interview):
        """Test getting specific interview by ID."""
        # Act
        response = await async_client.get(f"/api/v1/recruitment/interviews/{sample_interview.id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        interview_data = response.json()
        assert interview_data["id"] == str(sample_interview.id)

    @pytest.mark.asyncio
    async def test_complete_interview_success(self, async_client: AsyncClient, sample_interview):
        """Test successful interview completion."""
        # Arrange
        completion_data = {
            "feedback": "Candidate performed well",
            "rating": 4.5,
            "recommendation": "Proceed to next round",
        }

        # Act
        response = await async_client.patch(
            f"/api/v1/recruitment/interviews/{sample_interview.id}/complete", json=completion_data
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        interview_data = response.json()
        assert interview_data["status"] == "completed"
        assert interview_data["feedback"] == completion_data["feedback"]


class TestOfferEndpoints:
    """Test offer management API endpoints."""

    @pytest.mark.asyncio
    async def test_create_offer_success(self, async_client: AsyncClient, sample_application):
        """Test successful offer creation."""
        # Arrange
        offer_data = {
            "application_id": str(sample_application.id),
            "salary": 150000,
            "currency": "USD",
            "start_date": (date.today() + timedelta(days=30)).isoformat(),
            "benefits": "Health insurance, 401k, flexible PTO",
            "terms_conditions": "Standard employment terms apply",
            "response_deadline": (date.today() + timedelta(days=7)).isoformat(),
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/offers", json=offer_data)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        offer_resp = response.json()
        assert offer_resp["application_id"] == offer_data["application_id"]
        assert offer_resp["salary"] == offer_data["salary"]
        assert offer_resp["status"] == "draft"

    @pytest.mark.asyncio
    async def test_get_offer_by_id_success(self, async_client: AsyncClient, sample_offer):
        """Test getting specific offer by ID."""
        # Act
        response = await async_client.get(f"/api/v1/recruitment/offers/{sample_offer.id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        offer_data = response.json()
        assert offer_data["id"] == str(sample_offer.id)

    @pytest.mark.asyncio
    async def test_send_offer_success(self, async_client: AsyncClient, sample_offer):
        """Test successful offer sending."""
        # Act
        response = await async_client.patch(f"/api/v1/recruitment/offers/{sample_offer.id}/send")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        offer_data = response.json()
        assert offer_data["status"] == "sent"
        assert offer_data["sent_date"] is not None

    @pytest.mark.asyncio
    async def test_accept_offer_success(self, async_client: AsyncClient, sample_offer):
        """Test successful offer acceptance."""
        # Arrange - First send the offer
        await async_client.patch(f"/api/v1/recruitment/offers/{sample_offer.id}/send")

        # Act
        response = await async_client.patch(f"/api/v1/recruitment/offers/{sample_offer.id}/accept")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        offer_data = response.json()
        assert offer_data["status"] == "accepted"
        assert offer_data["accepted_date"] is not None

    @pytest.mark.asyncio
    async def test_reject_offer_success(self, async_client: AsyncClient, sample_offer):
        """Test successful offer rejection."""
        # Arrange - First send the offer
        await async_client.patch(f"/api/v1/recruitment/offers/{sample_offer.id}/send")

        rejection_data = {"reason": "Salary not competitive"}

        # Act
        response = await async_client.patch(
            f"/api/v1/recruitment/offers/{sample_offer.id}/reject", json=rejection_data
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        offer_data = response.json()
        assert offer_data["status"] == "rejected"
        assert offer_data["rejection_reason"] == rejection_data["reason"]


class TestHealthEndpoint:
    """Test health check endpoint."""

    @pytest.mark.asyncio
    async def test_health_check_success(self, async_client: AsyncClient):
        """Test health check endpoint."""
        # Act
        response = await async_client.get("/health")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        health_data = response.json()
        assert health_data["status"] == "healthy"
        assert health_data["service"] == "recruitment-service"


class TestErrorHandling:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_invalid_uuid_format(self, async_client: AsyncClient):
        """Test handling of invalid UUID format."""
        # Act
        response = await async_client.get("/api/v1/recruitment/jobs/invalid-uuid")

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, async_client: AsyncClient):
        """Test handling of missing required fields."""
        # Arrange
        incomplete_data = {
            "title": "Test Job",
            # Missing required description and department
        }

        # Act
        response = await async_client.post("/api/v1/recruitment/jobs", json=incomplete_data)

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_large_file_upload(self, async_client: AsyncClient, sample_candidate):
        """Test handling of large file upload."""
        # Arrange - Create a large file (> 5MB)
        large_content = b"x" * (6 * 1024 * 1024)  # 6MB

        # Act
        response = await async_client.post(
            f"/api/v1/recruitment/candidates/{sample_candidate.id}/resume",
            files={"file": ("large_resume.pdf", large_content, "application/pdf")},
        )

        # Assert
        assert response.status_code == status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
