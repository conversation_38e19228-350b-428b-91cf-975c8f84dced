"""
Test configuration and fixtures for Recruitment microservice tests.

This module provides pytest fixtures and configuration for testing the
Recruitment microservice including database setup, authentication
mocking, and test data generation.
"""

import asyncio
import os
import tempfile
from datetime import date, datetime, timedelta
from typing import Async<PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from hrms.microservices.recruitment.api import app
from hrms.microservices.recruitment.models import (
    Application,
    ApplicationStatus,
    Candidate,
    Interview,
    InterviewStatus,
    Job,
    JobStatus,
    Offer,
    OfferStatus,
)
from hrms.microservices.recruitment.repository import (
    ApplicationRepository,
    CandidateRepository,
    InterviewRepository,
    JobRepository,
    OfferRepository,
)
from hrms.microservices.recruitment.service import RecruitmentService
from hrms.microservices.shared.auth import Tenant, User
from hrms.microservices.shared.database import DatabaseManager
from hrms.microservices.shared.models import Base

from . import get_test_hiring_manager_id, get_test_tenant_id, get_test_user_id

# Test database configuration
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_db_engine():
    """Create test database engine."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)

    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # Clean up
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session = sessionmaker(test_db_engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def test_db_manager(test_db_engine) -> DatabaseManager:
    """Create test database manager."""
    db_manager = DatabaseManager(TEST_DATABASE_URL)
    db_manager.engine = test_db_engine
    return db_manager


@pytest_asyncio.fixture(scope="function")
async def job_repo(test_db_manager) -> JobRepository:
    """Create job repository."""
    return JobRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def candidate_repo(test_db_manager) -> CandidateRepository:
    """Create candidate repository."""
    return CandidateRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def application_repo(test_db_manager) -> ApplicationRepository:
    """Create application repository."""
    return ApplicationRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def interview_repo(test_db_manager) -> InterviewRepository:
    """Create interview repository."""
    return InterviewRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def offer_repo(test_db_manager) -> OfferRepository:
    """Create offer repository."""
    return OfferRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def recruitment_service(
    job_repo, candidate_repo, application_repo, interview_repo, offer_repo
) -> RecruitmentService:
    """Create recruitment service."""
    return RecruitmentService(job_repo, candidate_repo, application_repo, interview_repo, offer_repo)


@pytest.fixture
def mock_user() -> User:
    """Create mock user for testing."""
    return User(
        id=get_test_user_id(),
        email="<EMAIL>",
        username="testuser",
        roles=["hr_admin"],
        tenant_id=get_test_tenant_id(),
    )


@pytest.fixture
def mock_tenant() -> Tenant:
    """Create mock tenant for testing."""
    return Tenant(
        id=get_test_tenant_id(),
        name="Test Tenant",
        domain="test.example.com",
        settings={},
    )


@pytest.fixture
def test_client() -> TestClient:
    """Create test client for API testing."""
    return TestClient(app)


@pytest_asyncio.fixture(scope="function")
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create async test client for API testing."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def sample_job_data() -> dict:
    """Create sample job data."""
    return {
        "title": "Senior Software Engineer",
        "description": "We are looking for a senior software engineer to join our team.",
        "department": "Engineering",
        "location": "San Francisco, CA",
        "employment_type": "Full-time",
        "experience_level": "Senior",
        "salary_min": 120000,
        "salary_max": 180000,
        "currency": "USD",
        "requirements": "5+ years of Python experience",
        "responsibilities": "Design and develop software solutions",
        "benefits": "Health insurance, 401k, flexible PTO",
        "closing_date": date.today() + timedelta(days=30),
        "hiring_manager_id": get_test_hiring_manager_id(),
        "is_remote": True,
    }


@pytest.fixture
def sample_candidate_data() -> dict:
    """Create sample candidate data."""
    return {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "phone": "******-0123",
        "linkedin_url": "https://linkedin.com/in/johndoe",
        "portfolio_url": "https://johndoe.dev",
        "current_company": "Tech Corp",
        "current_position": "Software Engineer",
        "experience_years": 5,
        "education": "BS Computer Science",
        "skills": '["Python", "JavaScript", "React", "PostgreSQL"]',
        "cover_letter": "I am excited to apply for this position...",
        "source": "LinkedIn",
    }


@pytest.fixture
def sample_application_data() -> dict:
    """Create sample application data."""
    return {
        "job_id": "job-123",
        "candidate_id": "candidate-123",
        "cover_letter": "I am very interested in this position...",
        "custom_responses": '{"question1": "answer1", "question2": "answer2"}',
    }


@pytest.fixture
def sample_interview_data() -> dict:
    """Create sample interview data."""
    return {
        "application_id": "application-123",
        "interviewer_id": get_test_hiring_manager_id(),
        "interview_type": "Technical",
        "scheduled_date": datetime.now() + timedelta(days=2),
        "duration_minutes": 60,
        "location": "Conference Room A",
        "meeting_link": "https://zoom.us/j/123456789",
    }


@pytest.fixture
def sample_offer_data() -> dict:
    """Create sample offer data."""
    return {
        "application_id": "application-123",
        "salary": 150000,
        "currency": "USD",
        "start_date": date.today() + timedelta(days=30),
        "benefits": "Health insurance, 401k, flexible PTO",
        "terms_conditions": "Standard employment terms apply",
        "response_deadline": date.today() + timedelta(days=7),
    }


@pytest_asyncio.fixture(scope="function")
async def sample_job(job_repo: JobRepository, sample_job_data: dict) -> Job:
    """Create sample job in database."""
    job_data = sample_job_data.copy()
    job_data["status"] = JobStatus.PUBLISHED

    return await job_repo.create(get_test_tenant_id(), job_data)


@pytest_asyncio.fixture(scope="function")
async def sample_candidate(candidate_repo: CandidateRepository, sample_candidate_data: dict) -> Candidate:
    """Create sample candidate in database."""
    return await candidate_repo.create(get_test_tenant_id(), sample_candidate_data)


@pytest_asyncio.fixture(scope="function")
async def sample_application(
    application_repo: ApplicationRepository,
    sample_job: Job,
    sample_candidate: Candidate,
) -> Application:
    """Create sample application in database."""
    application_data = {
        "job_id": sample_job.id,
        "candidate_id": sample_candidate.id,
        "cover_letter": "I am very interested in this position...",
        "status": ApplicationStatus.SUBMITTED,
    }

    return await application_repo.create(get_test_tenant_id(), application_data)


@pytest_asyncio.fixture(scope="function")
async def sample_interview(
    interview_repo: InterviewRepository,
    sample_application: Application,
) -> Interview:
    """Create sample interview in database."""
    interview_data = {
        "application_id": sample_application.id,
        "interviewer_id": get_test_hiring_manager_id(),
        "interview_type": "Technical",
        "scheduled_date": datetime.now() + timedelta(days=2),
        "duration_minutes": 60,
        "status": InterviewStatus.SCHEDULED,
    }

    return await interview_repo.create(get_test_tenant_id(), interview_data)


@pytest_asyncio.fixture(scope="function")
async def sample_offer(
    offer_repo: OfferRepository,
    sample_application: Application,
) -> Offer:
    """Create sample offer in database."""
    offer_data = {
        "application_id": sample_application.id,
        "salary": 150000,
        "currency": "USD",
        "start_date": date.today() + timedelta(days=30),
        "status": OfferStatus.DRAFT,
        "created_by": get_test_user_id(),
    }

    return await offer_repo.create(get_test_tenant_id(), offer_data)


@pytest.fixture
def temp_resume_file():
    """Create temporary resume file for testing."""
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
        tmp_file.write(b"Test PDF resume content")
        tmp_file.flush()
        yield tmp_file.name

    # Clean up
    if os.path.exists(tmp_file.name):
        os.unlink(tmp_file.name)


@pytest.fixture
def mock_resume_upload():
    """Create mock resume upload for testing."""
    mock_file = MagicMock()
    mock_file.filename = "resume.pdf"
    mock_file.content_type = "application/pdf"
    mock_file.size = 1024 * 1024  # 1MB
    mock_file.read = AsyncMock(return_value=b"Test PDF resume content")
    return mock_file


# Mock authentication dependencies
@pytest.fixture(autouse=True)
def mock_auth_dependencies(mock_user, mock_tenant):
    """Mock authentication dependencies for all tests."""
    from hrms.microservices.shared.auth import get_current_tenant, get_current_user

    # Override dependencies
    app.dependency_overrides[get_current_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant] = lambda: mock_tenant

    yield

    # Clean up overrides
    app.dependency_overrides.clear()


# Test data generators
def generate_jobs(count: int = 5) -> list:
    """Generate multiple job test data."""
    jobs = []
    for i in range(count):
        jobs.append(
            {
                "title": f"Software Engineer {i}",
                "description": f"Job description {i}",
                "department": f"Department {i}",
                "location": f"Location {i}",
                "employment_type": "Full-time",
                "experience_level": "Mid",
                "salary_min": 80000 + (i * 10000),
                "salary_max": 120000 + (i * 10000),
                "is_remote": i % 2 == 0,
            }
        )
    return jobs


def generate_candidates(count: int = 5) -> list:
    """Generate multiple candidate test data."""
    candidates = []
    for i in range(count):
        candidates.append(
            {
                "first_name": f"John{i}",
                "last_name": f"Doe{i}",
                "email": f"john{i}.doe{i}@example.com",
                "phone": f"******-012{i}",
                "current_company": f"Company {i}",
                "experience_years": i + 1,
            }
        )
    return candidates


# Utility functions for tests
def assert_job_equal(actual: dict, expected: dict):
    """Assert job data equality."""
    assert actual["title"] == expected["title"]
    assert actual["description"] == expected["description"]
    assert actual["department"] == expected["department"]
    assert actual["location"] == expected["location"]


def assert_candidate_equal(actual: dict, expected: dict):
    """Assert candidate data equality."""
    assert actual["first_name"] == expected["first_name"]
    assert actual["last_name"] == expected["last_name"]
    assert actual["email"] == expected["email"]
    assert actual["phone"] == expected["phone"]
