"""
Test package for Recruitment microservice.

This package contains comprehensive tests for the Recruitment service including:
- Unit tests for service layer business logic
- Integration tests for API endpoints
- Repository layer tests with database operations
- Multi-tenant isolation tests
- Authentication and authorization tests
- File upload and resume management tests
- Job posting and application workflow tests
- Interview scheduling and management tests
- Offer management and acceptance tests

Test Structure:
- test_api.py: API endpoint integration tests
- test_service.py: Business logic unit tests
- test_repository.py: Data access layer tests
- test_models.py: Model validation tests
- conftest.py: Test fixtures and configuration
"""

__version__ = "1.0.0"
__test_package__ = "recruitment-service-tests"

# Test configuration
TEST_CONFIG = {
    "database_url": "postgresql://test:test@localhost:5432/test_hrms",
    "test_tenant_id": "test-tenant-001",
    "test_user_id": "test-user-001",
    "test_hiring_manager_id": "test-manager-001",
    "mock_auth": True,
    "cleanup_after_tests": True,
}

# Test data constants
TEST_DATA = {
    "job_statuses": ["draft", "published", "closed", "cancelled"],
    "application_statuses": [
        "submitted",
        "under_review",
        "shortlisted",
        "interviewed",
        "offered",
        "hired",
        "rejected",
    ],
    "interview_statuses": ["scheduled", "confirmed", "completed", "cancelled", "no_show"],
    "offer_statuses": ["draft", "sent", "accepted", "rejected", "expired", "withdrawn"],
    "employment_types": ["Full-time", "Part-time", "Contract", "Internship"],
    "experience_levels": ["Entry", "Mid", "Senior", "Lead", "Executive"],
    "max_resume_size_mb": 5,
    "allowed_resume_types": [".pdf", ".doc", ".docx"],
}


# Test utilities
def get_test_tenant_id() -> str:
    """Get test tenant ID."""
    return TEST_CONFIG["test_tenant_id"]


def get_test_user_id() -> str:
    """Get test user ID."""
    return TEST_CONFIG["test_user_id"]


def get_test_hiring_manager_id() -> str:
    """Get test hiring manager ID."""
    return TEST_CONFIG["test_hiring_manager_id"]


__all__ = [
    "TEST_CONFIG",
    "TEST_DATA",
    "get_test_tenant_id",
    "get_test_user_id",
    "get_test_hiring_manager_id",
]
