"""
Test configuration and fixtures for ESS microservice tests.

This module provides pytest fixtures and configuration for testing the
Employee Self-Service microservice including database setup, authentication
mocking, and test data generation.
"""

import asyncio
import os
import tempfile
from datetime import date, datetime, timedelta
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from hrms.microservices.ess.api import app
from hrms.microservices.ess.models import (
    Document,
    DocumentType,
    LeaveApplication,
    LeaveApplicationStatus,
    Timesheet,
    TimesheetStatus,
)
from hrms.microservices.ess.repository import (
    DocumentRepository,
    LeaveApplicationRepository,
    TimesheetRepository,
)
from hrms.microservices.ess.service import ESSService
from hrms.microservices.shared.auth import Tenant, User
from hrms.microservices.shared.database import DatabaseManager
from hrms.microservices.shared.models import Base

from . import get_test_employee_id, get_test_tenant_id, get_test_user_id

# Test database configuration
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_db_engine():
    """Create test database engine."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)

    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # Clean up
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session = sessionmaker(test_db_engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def test_db_manager(test_db_engine) -> DatabaseManager:
    """Create test database manager."""
    db_manager = DatabaseManager(TEST_DATABASE_URL)
    db_manager.engine = test_db_engine
    return db_manager


@pytest_asyncio.fixture(scope="function")
async def leave_repo(test_db_manager) -> LeaveApplicationRepository:
    """Create leave application repository."""
    return LeaveApplicationRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def document_repo(test_db_manager) -> DocumentRepository:
    """Create document repository."""
    return DocumentRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def timesheet_repo(test_db_manager) -> TimesheetRepository:
    """Create timesheet repository."""
    return TimesheetRepository(test_db_manager)


@pytest_asyncio.fixture(scope="function")
async def ess_service(leave_repo, document_repo, timesheet_repo) -> ESSService:
    """Create ESS service."""
    return ESSService(leave_repo, document_repo, timesheet_repo)


@pytest.fixture
def mock_user() -> User:
    """Create mock user for testing."""
    return User(
        id=get_test_user_id(),
        email="<EMAIL>",
        username="testuser",
        roles=["employee"],
        tenant_id=get_test_tenant_id(),
    )


@pytest.fixture
def mock_tenant() -> Tenant:
    """Create mock tenant for testing."""
    return Tenant(
        id=get_test_tenant_id(),
        name="Test Tenant",
        domain="test.example.com",
        settings={},
    )


@pytest.fixture
def test_client() -> TestClient:
    """Create test client for API testing."""
    return TestClient(app)


@pytest_asyncio.fixture(scope="function")
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create async test client for API testing."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def sample_leave_application_data() -> dict:
    """Create sample leave application data."""
    return {
        "leave_type": "Annual Leave",
        "from_date": date.today() + timedelta(days=7),
        "to_date": date.today() + timedelta(days=10),
        "reason": "Family vacation",
        "is_half_day": False,
    }


@pytest.fixture
def sample_document_data() -> dict:
    """Create sample document data."""
    return {
        "title": "Test Document",
        "description": "Test document description",
        "document_type": DocumentType.PERSONAL,
        "is_confidential": False,
    }


@pytest.fixture
def sample_timesheet_data() -> dict:
    """Create sample timesheet data."""
    # Get next Monday
    today = date.today()
    days_ahead = 0 - today.weekday()  # Monday is 0
    if days_ahead <= 0:  # Target day already happened this week
        days_ahead += 7
    next_monday = today + timedelta(days_ahead)

    return {
        "week_start_date": next_monday,
        "notes": "Test timesheet notes",
    }


@pytest_asyncio.fixture(scope="function")
async def sample_leave_application(
    leave_repo: LeaveApplicationRepository, sample_leave_application_data: dict
) -> LeaveApplication:
    """Create sample leave application in database."""
    leave_data = sample_leave_application_data.copy()
    leave_data["employee_id"] = get_test_employee_id()
    leave_data["status"] = LeaveApplicationStatus.SUBMITTED

    return await leave_repo.create(get_test_tenant_id(), leave_data)


@pytest_asyncio.fixture(scope="function")
async def sample_document(document_repo: DocumentRepository, sample_document_data: dict) -> Document:
    """Create sample document in database."""
    document_data = sample_document_data.copy()
    document_data.update(
        {
            "employee_id": get_test_employee_id(),
            "file_name": "test_document.pdf",
            "file_path": "/tmp/test_document.pdf",
            "file_size": 1024,
            "mime_type": "application/pdf",
        }
    )

    return await document_repo.create(get_test_tenant_id(), document_data)


@pytest_asyncio.fixture(scope="function")
async def sample_timesheet(timesheet_repo: TimesheetRepository, sample_timesheet_data: dict) -> Timesheet:
    """Create sample timesheet in database."""
    timesheet_data = sample_timesheet_data.copy()
    timesheet_data["employee_id"] = get_test_employee_id()
    timesheet_data["status"] = TimesheetStatus.DRAFT

    return await timesheet_repo.create(get_test_tenant_id(), timesheet_data)


@pytest.fixture
def temp_file():
    """Create temporary file for testing file uploads."""
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
        tmp_file.write(b"Test PDF content")
        tmp_file.flush()
        yield tmp_file.name

    # Clean up
    if os.path.exists(tmp_file.name):
        os.unlink(tmp_file.name)


@pytest.fixture
def mock_file_upload():
    """Create mock file upload for testing."""
    mock_file = MagicMock()
    mock_file.filename = "test_document.pdf"
    mock_file.content_type = "application/pdf"
    mock_file.size = 1024
    mock_file.read = AsyncMock(return_value=b"Test PDF content")
    return mock_file


# Mock authentication dependencies
@pytest.fixture(autouse=True)
def mock_auth_dependencies(mock_user, mock_tenant):
    """Mock authentication dependencies for all tests."""
    from hrms.microservices.shared.auth import get_current_tenant, get_current_user

    # Override dependencies
    app.dependency_overrides[get_current_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant] = lambda: mock_tenant

    yield

    # Clean up overrides
    app.dependency_overrides.clear()


# Test data generators
def generate_leave_applications(count: int = 5) -> list:
    """Generate multiple leave application test data."""
    applications = []
    for i in range(count):
        applications.append(
            {
                "leave_type": f"Leave Type {i}",
                "from_date": date.today() + timedelta(days=i * 7),
                "to_date": date.today() + timedelta(days=i * 7 + 3),
                "reason": f"Test reason {i}",
                "is_half_day": i % 2 == 0,
            }
        )
    return applications


def generate_documents(count: int = 5) -> list:
    """Generate multiple document test data."""
    documents = []
    doc_types = list(DocumentType)
    for i in range(count):
        documents.append(
            {
                "title": f"Test Document {i}",
                "description": f"Test document description {i}",
                "document_type": doc_types[i % len(doc_types)],
                "is_confidential": i % 2 == 0,
            }
        )
    return documents


def generate_timesheets(count: int = 5) -> list:
    """Generate multiple timesheet test data."""
    timesheets = []
    for i in range(count):
        # Get Monday for each week
        today = date.today()
        days_ahead = 0 - today.weekday() + (i * 7)
        if days_ahead <= 0:
            days_ahead += 7
        monday = today + timedelta(days_ahead)

        timesheets.append(
            {
                "week_start_date": monday,
                "notes": f"Test timesheet notes {i}",
            }
        )
    return timesheets


# Utility functions for tests
def assert_leave_application_equal(actual: dict, expected: dict):
    """Assert leave application data equality."""
    assert actual["leave_type"] == expected["leave_type"]
    assert actual["from_date"] == expected["from_date"]
    assert actual["to_date"] == expected["to_date"]
    assert actual["reason"] == expected["reason"]
    assert actual["is_half_day"] == expected["is_half_day"]


def assert_document_equal(actual: dict, expected: dict):
    """Assert document data equality."""
    assert actual["title"] == expected["title"]
    assert actual["description"] == expected["description"]
    assert actual["document_type"] == expected["document_type"]
    assert actual["is_confidential"] == expected["is_confidential"]


def assert_timesheet_equal(actual: dict, expected: dict):
    """Assert timesheet data equality."""
    assert actual["week_start_date"] == expected["week_start_date"]
    assert actual["notes"] == expected["notes"]
