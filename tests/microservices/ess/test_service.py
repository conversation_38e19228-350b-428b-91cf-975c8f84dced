"""
Unit tests for ESS service layer.

This module contains comprehensive unit tests for the Employee Self-Service
business logic layer, testing all service methods, business rules, and
error handling scenarios.
"""

import os
import tempfile
from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from hrms.microservices.ess.models import (
    DashboardResponse,
    DocumentCreate,
    DocumentType,
    LeaveApplicationCreate,
    LeaveApplicationStatus,
    ProfileUpdate,
    TimesheetCreate,
    TimesheetStatus,
)
from hrms.microservices.ess.service import ESSService
from hrms.microservices.shared.exceptions import BusinessLogicError, NotFoundError, ValidationError

from . import get_test_employee_id, get_test_tenant_id


class TestESSService:
    """Test cases for ESS service layer."""

    @pytest.mark.asyncio
    async def test_get_employee_dashboard(self, ess_service: ESSService):
        """Test getting employee dashboard."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Mock repository methods
        ess_service.leave_repo.count_by_employee_and_status = AsyncMock(return_value=2)
        ess_service.document_repo.get_by_employee = AsyncMock(return_value=[])
        ess_service.timesheet_repo.count_by_employee_and_status = AsyncMock(return_value=1)

        dashboard = await ess_service.get_employee_dashboard(tenant_id, employee_id)

        assert isinstance(dashboard, DashboardResponse)
        assert dashboard.pending_leave_applications == 2
        assert dashboard.pending_timesheets == 1
        assert isinstance(dashboard.remaining_leave_balance, dict)
        assert "Annual Leave" in dashboard.remaining_leave_balance

    @pytest.mark.asyncio
    async def test_get_employee_profile(self, ess_service: ESSService):
        """Test getting employee profile."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        profile = await ess_service.get_employee_profile(tenant_id, employee_id)

        assert profile.employee_id == employee_id
        assert profile.first_name == "John"
        assert profile.last_name == "Doe"
        assert profile.email == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_update_employee_profile(self, ess_service: ESSService):
        """Test updating employee profile."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        profile_data = ProfileUpdate(
            phone="******-9999",
            emergency_contact_name="Jane Doe Updated",
            emergency_contact_phone="******-8888",
            address="456 Updated St, City, State 54321",
        )

        profile = await ess_service.update_employee_profile(tenant_id, employee_id, profile_data)

        assert profile.employee_id == employee_id
        # In the mock implementation, it returns the same profile
        # In a real implementation, this would reflect the updates

    @pytest.mark.asyncio
    async def test_create_leave_application_success(self, ess_service: ESSService):
        """Test successful leave application creation."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        leave_data = LeaveApplicationCreate(
            leave_type="Annual Leave",
            from_date=date.today() + timedelta(days=7),
            to_date=date.today() + timedelta(days=10),
            reason="Family vacation",
            is_half_day=False,
        )

        # Mock repository create method
        mock_leave = MagicMock()
        mock_leave.id = "leave-123"
        mock_leave.employee_id = employee_id
        mock_leave.leave_type = leave_data.leave_type
        mock_leave.from_date = leave_data.from_date
        mock_leave.to_date = leave_data.to_date
        mock_leave.total_days = Decimal("4.0")
        mock_leave.reason = leave_data.reason
        mock_leave.status = LeaveApplicationStatus.SUBMITTED
        mock_leave.is_half_day = leave_data.is_half_day
        mock_leave.created_at = datetime.utcnow()
        mock_leave.updated_at = datetime.utcnow()

        ess_service.leave_repo.create = AsyncMock(return_value=mock_leave)

        result = await ess_service.create_leave_application(tenant_id, employee_id, leave_data)

        assert result.leave_type == leave_data.leave_type
        assert result.from_date == leave_data.from_date
        assert result.to_date == leave_data.to_date
        assert result.status == LeaveApplicationStatus.SUBMITTED
        ess_service.leave_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_leave_application_validation_error(self, ess_service: ESSService):
        """Test leave application creation with validation error."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Leave application for yesterday (should fail validation)
        leave_data = LeaveApplicationCreate(
            leave_type="Annual Leave",
            from_date=date.today() - timedelta(days=1),
            to_date=date.today(),
            reason="Past leave",
            is_half_day=False,
        )

        with pytest.raises(ValidationError, match="at least 1 day in advance"):
            await ess_service.create_leave_application(tenant_id, employee_id, leave_data)

    @pytest.mark.asyncio
    async def test_create_leave_application_too_many_days(self, ess_service: ESSService):
        """Test leave application creation with too many days."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Leave application for 35 days (should fail validation)
        leave_data = LeaveApplicationCreate(
            leave_type="Annual Leave",
            from_date=date.today() + timedelta(days=7),
            to_date=date.today() + timedelta(days=42),
            reason="Long vacation",
            is_half_day=False,
        )

        with pytest.raises(ValidationError, match="Cannot apply for more than 30 days"):
            await ess_service.create_leave_application(tenant_id, employee_id, leave_data)

    @pytest.mark.asyncio
    async def test_get_leave_applications(self, ess_service: ESSService):
        """Test getting leave applications."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Mock repository method
        mock_applications = [MagicMock() for _ in range(3)]
        for i, app in enumerate(mock_applications):
            app.id = f"leave-{i}"
            app.employee_id = employee_id
            app.leave_type = f"Leave Type {i}"
            app.status = LeaveApplicationStatus.SUBMITTED
            app.created_at = datetime.utcnow()
            app.updated_at = datetime.utcnow()

        ess_service.leave_repo.get_by_employee = AsyncMock(return_value=mock_applications)

        result = await ess_service.get_leave_applications(tenant_id, employee_id)

        assert len(result) == 3
        assert all(app.employee_id == employee_id for app in result)
        ess_service.leave_repo.get_by_employee.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_leave_application_not_found(self, ess_service: ESSService):
        """Test getting non-existent leave application."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        application_id = "non-existent"

        ess_service.leave_repo.get_by_id = AsyncMock(return_value=None)

        with pytest.raises(NotFoundError, match="Leave application not found"):
            await ess_service.get_leave_application(tenant_id, employee_id, application_id)

    @pytest.mark.asyncio
    async def test_cancel_leave_application_success(self, ess_service: ESSService):
        """Test successful leave application cancellation."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        application_id = "leave-123"

        # Mock existing application
        mock_application = MagicMock()
        mock_application.id = application_id
        mock_application.employee_id = employee_id
        mock_application.status = LeaveApplicationStatus.SUBMITTED

        # Mock updated application
        mock_updated = MagicMock()
        mock_updated.id = application_id
        mock_updated.status = LeaveApplicationStatus.CANCELLED
        mock_updated.created_at = datetime.utcnow()
        mock_updated.updated_at = datetime.utcnow()

        ess_service.leave_repo.get_by_id = AsyncMock(return_value=mock_application)
        ess_service.leave_repo.update_status = AsyncMock(return_value=mock_updated)

        result = await ess_service.cancel_leave_application(tenant_id, employee_id, application_id)

        assert result.status == LeaveApplicationStatus.CANCELLED
        ess_service.leave_repo.update_status.assert_called_once_with(
            tenant_id, application_id, LeaveApplicationStatus.CANCELLED
        )

    @pytest.mark.asyncio
    async def test_cancel_leave_application_wrong_status(self, ess_service: ESSService):
        """Test cancelling leave application with wrong status."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        application_id = "leave-123"

        # Mock application with approved status
        mock_application = MagicMock()
        mock_application.id = application_id
        mock_application.employee_id = employee_id
        mock_application.status = LeaveApplicationStatus.APPROVED

        ess_service.leave_repo.get_by_id = AsyncMock(return_value=mock_application)

        with pytest.raises(BusinessLogicError, match="Cannot cancel leave application"):
            await ess_service.cancel_leave_application(tenant_id, employee_id, application_id)

    @pytest.mark.asyncio
    async def test_upload_document_success(self, ess_service: ESSService, mock_file_upload):
        """Test successful document upload."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        document_data = DocumentCreate(
            title="Test Document",
            description="Test description",
            document_type=DocumentType.PERSONAL,
            is_confidential=False,
        )

        # Mock repository create method
        mock_document = MagicMock()
        mock_document.id = "doc-123"
        mock_document.employee_id = employee_id
        mock_document.title = document_data.title
        mock_document.document_type = document_data.document_type
        mock_document.file_name = "test_document.pdf"
        mock_document.created_at = datetime.utcnow()
        mock_document.updated_at = datetime.utcnow()

        ess_service.document_repo.create = AsyncMock(return_value=mock_document)

        # Mock file saving
        with patch.object(ess_service, "_save_document_file", return_value="/tmp/test_document.pdf"):
            result = await ess_service.upload_document(
                tenant_id, employee_id, document_data, mock_file_upload
            )

        assert result.title == document_data.title
        assert result.document_type == document_data.document_type
        ess_service.document_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_upload_document_file_too_large(self, ess_service: ESSService):
        """Test document upload with file too large."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        document_data = DocumentCreate(
            title="Large Document",
            document_type=DocumentType.PERSONAL,
        )

        # Mock large file
        mock_file = MagicMock()
        mock_file.filename = "large_document.pdf"
        mock_file.size = 15 * 1024 * 1024  # 15MB (exceeds 10MB limit)

        with pytest.raises(ValidationError, match="File size cannot exceed 10MB"):
            await ess_service.upload_document(tenant_id, employee_id, document_data, mock_file)

    @pytest.mark.asyncio
    async def test_upload_document_invalid_file_type(self, ess_service: ESSService):
        """Test document upload with invalid file type."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        document_data = DocumentCreate(
            title="Invalid Document",
            document_type=DocumentType.PERSONAL,
        )

        # Mock file with invalid type
        mock_file = MagicMock()
        mock_file.filename = "document.exe"
        mock_file.size = 1024

        with pytest.raises(ValidationError, match="File type .exe not allowed"):
            await ess_service.upload_document(tenant_id, employee_id, document_data, mock_file)

    @pytest.mark.asyncio
    async def test_get_documents(self, ess_service: ESSService):
        """Test getting documents."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Mock repository method
        mock_documents = [MagicMock() for _ in range(3)]
        for i, doc in enumerate(mock_documents):
            doc.id = f"doc-{i}"
            doc.employee_id = employee_id
            doc.title = f"Document {i}"
            doc.document_type = DocumentType.PERSONAL
            doc.created_at = datetime.utcnow()
            doc.updated_at = datetime.utcnow()

        ess_service.document_repo.get_by_employee = AsyncMock(return_value=mock_documents)

        result = await ess_service.get_documents(tenant_id, employee_id)

        assert len(result) == 3
        assert all(doc.employee_id == employee_id for doc in result)
        ess_service.document_repo.get_by_employee.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_document_success(self, ess_service: ESSService):
        """Test successful document deletion."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        document_id = "doc-123"

        # Mock existing document
        mock_document = MagicMock()
        mock_document.id = document_id
        mock_document.employee_id = employee_id
        mock_document.file_path = "/tmp/test_document.pdf"

        ess_service.document_repo.get_by_id = AsyncMock(return_value=mock_document)
        ess_service.document_repo.delete = AsyncMock(return_value=True)

        # Mock file system operations
        with patch("os.path.exists", return_value=True), patch("os.remove") as mock_remove:

            result = await ess_service.delete_document(tenant_id, employee_id, document_id)

        assert result is True
        mock_remove.assert_called_once_with("/tmp/test_document.pdf")
        ess_service.document_repo.delete.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_timesheet_success(self, ess_service: ESSService):
        """Test successful timesheet creation."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Get next Monday
        today = date.today()
        days_ahead = 0 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days_ahead)

        timesheet_data = TimesheetCreate(
            week_start_date=next_monday,
            notes="Test timesheet",
        )

        # Mock repository methods
        ess_service.timesheet_repo.get_by_employee = AsyncMock(return_value=[])  # No existing timesheets

        mock_timesheet = MagicMock()
        mock_timesheet.id = "timesheet-123"
        mock_timesheet.employee_id = employee_id
        mock_timesheet.week_start_date = timesheet_data.week_start_date
        mock_timesheet.status = TimesheetStatus.DRAFT
        mock_timesheet.created_at = datetime.utcnow()
        mock_timesheet.updated_at = datetime.utcnow()

        ess_service.timesheet_repo.create = AsyncMock(return_value=mock_timesheet)

        result = await ess_service.create_timesheet(tenant_id, employee_id, timesheet_data)

        assert result.week_start_date == timesheet_data.week_start_date
        assert result.status == TimesheetStatus.DRAFT
        ess_service.timesheet_repo.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_timesheet_duplicate_week(self, ess_service: ESSService):
        """Test timesheet creation for duplicate week."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Get next Monday
        today = date.today()
        days_ahead = 0 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days_ahead)

        timesheet_data = TimesheetCreate(
            week_start_date=next_monday,
            notes="Test timesheet",
        )

        # Mock existing timesheet for the same week
        mock_existing = MagicMock()
        mock_existing.week_start_date = next_monday

        ess_service.timesheet_repo.get_by_employee = AsyncMock(return_value=[mock_existing])

        with pytest.raises(BusinessLogicError, match="Timesheet already exists for this week"):
            await ess_service.create_timesheet(tenant_id, employee_id, timesheet_data)

    @pytest.mark.asyncio
    async def test_submit_timesheet_success(self, ess_service: ESSService):
        """Test successful timesheet submission."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        timesheet_id = "timesheet-123"

        # Mock existing timesheet
        mock_timesheet = MagicMock()
        mock_timesheet.id = timesheet_id
        mock_timesheet.employee_id = employee_id
        mock_timesheet.status = TimesheetStatus.DRAFT

        # Mock updated timesheet
        mock_updated = MagicMock()
        mock_updated.id = timesheet_id
        mock_updated.status = TimesheetStatus.SUBMITTED
        mock_updated.created_at = datetime.utcnow()
        mock_updated.updated_at = datetime.utcnow()

        ess_service.timesheet_repo.get_by_id = AsyncMock(return_value=mock_timesheet)
        ess_service.timesheet_repo.update_status = AsyncMock(return_value=mock_updated)

        result = await ess_service.submit_timesheet(tenant_id, employee_id, timesheet_id)

        assert result.status == TimesheetStatus.SUBMITTED
        ess_service.timesheet_repo.update_status.assert_called_once_with(
            tenant_id, timesheet_id, TimesheetStatus.SUBMITTED
        )

    @pytest.mark.asyncio
    async def test_submit_timesheet_wrong_status(self, ess_service: ESSService):
        """Test submitting timesheet with wrong status."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()
        timesheet_id = "timesheet-123"

        # Mock timesheet with submitted status
        mock_timesheet = MagicMock()
        mock_timesheet.id = timesheet_id
        mock_timesheet.employee_id = employee_id
        mock_timesheet.status = TimesheetStatus.SUBMITTED

        ess_service.timesheet_repo.get_by_id = AsyncMock(return_value=mock_timesheet)

        with pytest.raises(BusinessLogicError, match="Can only submit draft timesheets"):
            await ess_service.submit_timesheet(tenant_id, employee_id, timesheet_id)

    @pytest.mark.asyncio
    async def test_get_timesheets(self, ess_service: ESSService):
        """Test getting timesheets."""
        tenant_id = get_test_tenant_id()
        employee_id = get_test_employee_id()

        # Mock repository method
        mock_timesheets = [MagicMock() for _ in range(3)]
        for i, ts in enumerate(mock_timesheets):
            ts.id = f"timesheet-{i}"
            ts.employee_id = employee_id
            ts.status = TimesheetStatus.DRAFT
            ts.created_at = datetime.utcnow()
            ts.updated_at = datetime.utcnow()

        ess_service.timesheet_repo.get_by_employee = AsyncMock(return_value=mock_timesheets)

        result = await ess_service.get_timesheets(tenant_id, employee_id)

        assert len(result) == 3
        assert all(ts.employee_id == employee_id for ts in result)
        ess_service.timesheet_repo.get_by_employee.assert_called_once()
