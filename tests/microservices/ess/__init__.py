"""
Test package for Employee Self-Service (ESS) microservice.

This package contains comprehensive tests for the ESS service including:
- Unit tests for service layer business logic
- Integration tests for API endpoints
- Repository layer tests with database operations
- Multi-tenant isolation tests
- Authentication and authorization tests
- File upload and document management tests
- Leave application workflow tests
- Timesheet management tests

Test Structure:
- test_api.py: API endpoint integration tests
- test_service.py: Business logic unit tests
- test_repository.py: Data access layer tests
- test_models.py: Model validation tests
- conftest.py: Test fixtures and configuration
"""

__version__ = "1.0.0"
__test_package__ = "ess-service-tests"

# Test configuration
TEST_CONFIG = {
    "database_url": "postgresql://test:test@localhost:5432/test_hrms",
    "test_tenant_id": "test-tenant-001",
    "test_employee_id": "test-employee-001",
    "test_user_id": "test-user-001",
    "mock_auth": True,
    "cleanup_after_tests": True,
}

# Test data constants
TEST_DATA = {
    "leave_types": ["Annual Leave", "Sick Leave", "Personal Leave", "Maternity Leave"],
    "document_types": ["personal", "official", "certificate", "contract"],
    "timesheet_statuses": ["draft", "submitted", "approved", "rejected"],
    "max_file_size_mb": 10,
    "allowed_file_types": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"],
}


# Test utilities
def get_test_tenant_id() -> str:
    """Get test tenant ID."""
    return TEST_CONFIG["test_tenant_id"]


def get_test_employee_id() -> str:
    """Get test employee ID."""
    return TEST_CONFIG["test_employee_id"]


def get_test_user_id() -> str:
    """Get test user ID."""
    return TEST_CONFIG["test_user_id"]


__all__ = [
    "TEST_CONFIG",
    "TEST_DATA",
    "get_test_tenant_id",
    "get_test_employee_id",
    "get_test_user_id",
]
