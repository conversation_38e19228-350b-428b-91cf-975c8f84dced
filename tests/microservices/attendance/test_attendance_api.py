"""
Integration tests for Attendance Management API endpoints.

Tests API endpoints, request/response validation, and HTTP status codes.
Following TDD principles - tests written before implementation.
"""

from datetime import date, datetime, time
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from hrms.microservices.attendance.api import app
from hrms.microservices.attendance.models import AttendanceStatus, CheckInType


class TestAttendanceAPI:
    """Test suite for Attendance API endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer mock-jwt-token", "X-Tenant-ID": "test_tenant"}

    @pytest.fixture
    def sample_attendance_payload(self):
        """Sample attendance creation payload."""
        return {
            "employee_id": "emp-123",
            "attendance_date": date.today().isoformat(),
            "status": "PRESENT",
            "shift_id": "shift-123",
            "working_hours": "8.0",
            "late_entry": False,
            "early_exit": False,
        }

    @pytest.fixture
    def sample_checkin_payload(self):
        """Sample check-in creation payload."""
        return {
            "employee_id": "emp-123",
            "check_time": datetime.now().isoformat(),
            "check_type": "CHECK_IN",
            "shift_id": "shift-123",
            "latitude": 37.7749,
            "longitude": -122.4194,
        }


class TestAttendanceEndpoints:
    """Test attendance CRUD endpoints."""

    @pytest.mark.asyncio
    async def test_create_attendance_success(self, client, auth_headers, sample_attendance_payload):
        """Test successful attendance creation via API."""
        response = client.post(
            "/api/v1/attendance/records", json=sample_attendance_payload, headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["data"]["employee_id"] == "emp-123"

    @pytest.mark.asyncio
    async def test_create_attendance_validation_error(self, client, auth_headers):
        """Test attendance creation with invalid data."""
        invalid_payload = {
            "employee_id": "",  # Invalid: empty employee_id
            "attendance_date": "invalid-date",  # Invalid: bad date format
            "status": "INVALID_STATUS",  # Invalid: not in enum
        }

        response = client.post("/api/v1/attendance/records", json=invalid_payload, headers=auth_headers)

        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_get_attendance_success(self, client, auth_headers):
        """Test successful attendance retrieval."""
        attendance_id = "att-123"

        response = client.get(f"/api/v1/attendance/records/{attendance_id}", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data

    @pytest.mark.asyncio
    async def test_get_attendance_not_found(self, client, auth_headers):
        """Test attendance retrieval with non-existent ID."""
        attendance_id = "att-999"

        response = client.get(f"/api/v1/attendance/records/{attendance_id}", headers=auth_headers)

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "error" in data

    @pytest.mark.asyncio
    async def test_list_attendance_records(self, client, auth_headers):
        """Test listing attendance records with pagination."""
        response = client.get(
            "/api/v1/attendance/records",
            params={
                "skip": 0,
                "limit": 10,
                "employee_id": "emp-123",
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
            },
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "pagination" in data

    @pytest.mark.asyncio
    async def test_update_attendance_success(self, client, auth_headers):
        """Test successful attendance update."""
        attendance_id = "att-123"
        update_payload = {"status": "HALF_DAY", "working_hours": "4.0", "notes": "Updated to half day"}

        response = client.put(
            f"/api/v1/attendance/records/{attendance_id}", json=update_payload, headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["status"] == "HALF_DAY"

    @pytest.mark.asyncio
    async def test_delete_attendance_success(self, client, auth_headers):
        """Test successful attendance deletion."""
        attendance_id = "att-123"

        response = client.delete(f"/api/v1/attendance/records/{attendance_id}", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestCheckInEndpoints:
    """Test check-in/check-out endpoints."""

    @pytest.mark.asyncio
    async def test_create_checkin_success(self, client, auth_headers, sample_checkin_payload):
        """Test successful check-in creation."""
        response = client.post(
            "/api/v1/attendance/checkins", json=sample_checkin_payload, headers=auth_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["check_type"] == "CHECK_IN"

    @pytest.mark.asyncio
    async def test_get_employee_checkins(self, client, auth_headers):
        """Test retrieving employee check-ins."""
        employee_id = "emp-123"

        response = client.get(
            f"/api/v1/attendance/checkins/employee/{employee_id}",
            params={"start_date": "2024-01-01", "end_date": "2024-01-31"},
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)

    @pytest.mark.asyncio
    async def test_process_auto_attendance(self, client, auth_headers):
        """Test automatic attendance processing from check-ins."""
        response = client.post(
            "/api/v1/attendance/process-auto",
            json={"employee_id": "emp-123", "attendance_date": date.today().isoformat()},
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestShiftEndpoints:
    """Test shift management endpoints."""

    @pytest.mark.asyncio
    async def test_create_shift_type_success(self, client, auth_headers):
        """Test successful shift type creation."""
        shift_payload = {
            "name": "Morning Shift",
            "start_time": "09:00:00",
            "end_time": "17:00:00",
            "break_duration_minutes": 60,
            "grace_period_minutes": 15,
            "working_hours_threshold": "7.5",
            "is_active": True,
        }

        response = client.post("/api/v1/attendance/shifts", json=shift_payload, headers=auth_headers)

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "Morning Shift"

    @pytest.mark.asyncio
    async def test_list_shift_types(self, client, auth_headers):
        """Test listing shift types."""
        response = client.get("/api/v1/attendance/shifts", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)

    @pytest.mark.asyncio
    async def test_get_shift_type_success(self, client, auth_headers):
        """Test successful shift type retrieval."""
        shift_id = "shift-123"

        response = client.get(f"/api/v1/attendance/shifts/{shift_id}", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestReportingEndpoints:
    """Test attendance reporting endpoints."""

    @pytest.mark.asyncio
    async def test_get_attendance_summary(self, client, auth_headers):
        """Test attendance summary endpoint."""
        response = client.get(
            "/api/v1/attendance/reports/summary",
            params={"employee_id": "emp-123", "start_date": "2024-01-01", "end_date": "2024-01-31"},
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "total_days" in data["data"]
        assert "present_days" in data["data"]
        assert "attendance_percentage" in data["data"]

    @pytest.mark.asyncio
    async def test_get_team_attendance_report(self, client, auth_headers):
        """Test team attendance report endpoint."""
        response = client.get(
            "/api/v1/attendance/reports/team",
            params={"department_id": "dept-123", "start_date": "2024-01-01", "end_date": "2024-01-31"},
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)

    @pytest.mark.asyncio
    async def test_export_attendance_report(self, client, auth_headers):
        """Test attendance report export."""
        response = client.get(
            "/api/v1/attendance/reports/export",
            params={
                "format": "csv",
                "employee_id": "emp-123",
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
            },
            headers=auth_headers,
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"


class TestAuthenticationAndAuthorization:
    """Test authentication and authorization."""

    @pytest.mark.asyncio
    async def test_missing_auth_header(self, client):
        """Test request without authentication header."""
        response = client.get("/api/v1/attendance/records")

        assert response.status_code == 401
        data = response.json()
        assert "error" in data

    @pytest.mark.asyncio
    async def test_invalid_tenant_access(self, client):
        """Test request with invalid tenant access."""
        headers = {"Authorization": "Bearer mock-jwt-token", "X-Tenant-ID": "unauthorized_tenant"}

        response = client.get("/api/v1/attendance/records", headers=headers)

        assert response.status_code == 403
        data = response.json()
        assert "error" in data


class TestHealthAndStatus:
    """Test health check and status endpoints."""

    @pytest.mark.asyncio
    async def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data

    @pytest.mark.asyncio
    async def test_service_info(self, client):
        """Test service information endpoint."""
        response = client.get("/info")

        assert response.status_code == 200
        data = response.json()
        assert data["service_name"] == "attendance-service"
        assert "version" in data
        assert "description" in data
