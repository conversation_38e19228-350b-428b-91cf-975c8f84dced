"""
Unit tests for Attendance Management Service.

Tests business logic, validation, and service operations with comprehensive coverage.
Following TDD principles - tests written before implementation.
"""

from datetime import date, datetime, time, timedelta
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock

import pytest

from hrms.microservices.attendance.models import (
    AttendanceCreate,
    AttendanceResponse,
    AttendanceStatus,
    AttendanceUpdate,
    CheckInCreate,
    CheckInResponse,
    CheckInType,
    ShiftStatus,
    ShiftTypeCreate,
    ShiftTypeResponse,
)
from hrms.microservices.attendance.service import AttendanceService
from hrms.microservices.shared.exceptions import (
    BusinessLogicError,
    ConflictError,
    NotFoundError,
    ValidationError,
)


class TestAttendanceService:
    """Test suite for AttendanceService business logic."""

    @pytest.fixture
    def mock_attendance_repo(self):
        """Mock attendance repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_checkin_repo(self):
        """Mock check-in repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_shift_repo(self):
        """Mock shift repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_employee_service(self):
        """Mock employee service client."""
        return AsyncMock()

    @pytest.fixture
    def attendance_service(
        self, mock_attendance_repo, mock_checkin_repo, mock_shift_repo, mock_employee_service
    ):
        """Create AttendanceService instance with mocked dependencies."""
        return AttendanceService(
            attendance_repo=mock_attendance_repo,
            checkin_repo=mock_checkin_repo,
            shift_repo=mock_shift_repo,
            employee_service=mock_employee_service,
        )

    @pytest.fixture
    def sample_attendance_data(self):
        """Sample attendance creation data."""
        return AttendanceCreate(
            employee_id="emp-123",
            attendance_date=date.today(),
            status=AttendanceStatus.PRESENT,
            shift_id="shift-123",
            working_hours=Decimal("8.0"),
            late_entry=False,
            early_exit=False,
        )

    @pytest.fixture
    def sample_checkin_data(self):
        """Sample check-in creation data."""
        return CheckInCreate(
            employee_id="emp-123",
            check_time=datetime.now(),
            check_type=CheckInType.CHECK_IN,
            shift_id="shift-123",
            latitude=37.7749,
            longitude=-122.4194,
        )

    @pytest.fixture
    def sample_shift_data(self):
        """Sample shift type creation data."""
        return ShiftTypeCreate(
            name="Morning Shift",
            start_time=time(9, 0),
            end_time=time(17, 0),
            break_duration_minutes=60,
            grace_period_minutes=15,
            working_hours_threshold=Decimal("7.5"),
            is_active=True,
        )


class TestAttendanceOperations:
    """Test attendance CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_attendance_success(self, attendance_service, sample_attendance_data):
        """Test successful attendance creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock employee validation
        attendance_service.employee_service.get_employee.return_value = {
            "id": "emp-123",
            "employee_id": "EMP001",
            "status": "ACTIVE",
        }

        # Mock shift validation
        mock_shift = MagicMock()
        mock_shift.id = "shift-123"
        mock_shift.name = "Morning Shift"
        attendance_service.shift_repo.get_by_id.return_value = mock_shift

        # Mock duplicate check
        attendance_service.attendance_repo.check_duplicate.return_value = False

        # Mock attendance creation
        mock_attendance = MagicMock()
        mock_attendance.id = "att-123"
        mock_attendance.employee_id = "emp-123"
        mock_attendance.status = AttendanceStatus.PRESENT
        attendance_service.attendance_repo.create.return_value = mock_attendance

        # Execute
        result = await attendance_service.create_attendance(tenant_id, sample_attendance_data, created_by)

        # Verify
        attendance_service.employee_service.get_employee.assert_called_once_with(tenant_id, "emp-123")
        attendance_service.shift_repo.get_by_id.assert_called_once_with(tenant_id, "shift-123")
        attendance_service.attendance_repo.check_duplicate.assert_called_once()
        attendance_service.attendance_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_create_attendance_duplicate_error(self, attendance_service, sample_attendance_data):
        """Test attendance creation with duplicate record."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock employee validation
        attendance_service.employee_service.get_employee.return_value = {"id": "emp-123", "status": "ACTIVE"}

        # Mock shift validation
        mock_shift = MagicMock()
        attendance_service.shift_repo.get_by_id.return_value = mock_shift

        # Mock duplicate check - return True for duplicate
        attendance_service.attendance_repo.check_duplicate.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError, match="Attendance already exists"):
            await attendance_service.create_attendance(tenant_id, sample_attendance_data, created_by)

    @pytest.mark.asyncio
    async def test_create_attendance_invalid_employee(self, attendance_service, sample_attendance_data):
        """Test attendance creation with invalid employee."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock employee not found
        attendance_service.employee_service.get_employee.side_effect = NotFoundError("Employee not found")

        # Execute and verify exception
        with pytest.raises(NotFoundError, match="Employee not found"):
            await attendance_service.create_attendance(tenant_id, sample_attendance_data, created_by)

    @pytest.mark.asyncio
    async def test_get_attendance_success(self, attendance_service):
        """Test successful attendance retrieval."""
        tenant_id = "test_tenant"
        attendance_id = "att-123"

        mock_attendance = MagicMock()
        mock_attendance.id = attendance_id
        mock_attendance.employee_id = "emp-123"
        mock_attendance.status = AttendanceStatus.PRESENT

        attendance_service.attendance_repo.get_by_id.return_value = mock_attendance

        # Execute
        result = await attendance_service.get_attendance(tenant_id, attendance_id)

        # Verify
        attendance_service.attendance_repo.get_by_id.assert_called_once_with(tenant_id, attendance_id)
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_attendance_not_found(self, attendance_service):
        """Test attendance retrieval with non-existent record."""
        tenant_id = "test_tenant"
        attendance_id = "att-999"

        attendance_service.attendance_repo.get_by_id.return_value = None

        # Execute and verify exception
        with pytest.raises(NotFoundError, match="Attendance not found"):
            await attendance_service.get_attendance(tenant_id, attendance_id)


class TestCheckInOperations:
    """Test check-in/check-out operations."""

    @pytest.mark.asyncio
    async def test_create_checkin_success(self, attendance_service, sample_checkin_data):
        """Test successful check-in creation."""
        tenant_id = "test_tenant"

        # Mock employee validation
        attendance_service.employee_service.get_employee.return_value = {"id": "emp-123", "status": "ACTIVE"}

        # Mock shift validation
        mock_shift = MagicMock()
        mock_shift.id = "shift-123"
        attendance_service.shift_repo.get_by_id.return_value = mock_shift

        # Mock check-in creation
        mock_checkin = MagicMock()
        mock_checkin.id = "checkin-123"
        mock_checkin.employee_id = "emp-123"
        mock_checkin.check_type = CheckInType.CHECK_IN
        attendance_service.checkin_repo.create.return_value = mock_checkin

        # Execute
        result = await attendance_service.create_checkin(tenant_id, sample_checkin_data)

        # Verify
        attendance_service.employee_service.get_employee.assert_called_once()
        attendance_service.shift_repo.get_by_id.assert_called_once()
        attendance_service.checkin_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_auto_attendance_from_checkins(self, attendance_service):
        """Test automatic attendance creation from check-ins."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"
        attendance_date = date.today()

        # Mock check-ins for the day
        mock_checkins = [
            MagicMock(
                check_time=datetime.combine(attendance_date, time(9, 0)), check_type=CheckInType.CHECK_IN
            ),
            MagicMock(
                check_time=datetime.combine(attendance_date, time(17, 0)), check_type=CheckInType.CHECK_OUT
            ),
        ]

        attendance_service.checkin_repo.get_by_employee_date.return_value = mock_checkins

        # Mock shift details
        mock_shift = MagicMock()
        mock_shift.working_hours_threshold = Decimal("7.5")
        attendance_service.shift_repo.get_by_id.return_value = mock_shift

        # Mock attendance creation
        mock_attendance = MagicMock()
        attendance_service.attendance_repo.create.return_value = mock_attendance

        # Execute
        result = await attendance_service.process_auto_attendance(tenant_id, employee_id, attendance_date)

        # Verify
        attendance_service.checkin_repo.get_by_employee_date.assert_called_once()
        attendance_service.attendance_repo.create.assert_called_once()
        assert result is not None


class TestShiftManagement:
    """Test shift type management operations."""

    @pytest.mark.asyncio
    async def test_create_shift_type_success(self, attendance_service, sample_shift_data):
        """Test successful shift type creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock name uniqueness check
        attendance_service.shift_repo.check_name_exists.return_value = False

        # Mock shift creation
        mock_shift = MagicMock()
        mock_shift.id = "shift-123"
        mock_shift.name = "Morning Shift"
        attendance_service.shift_repo.create.return_value = mock_shift

        # Execute
        result = await attendance_service.create_shift_type(tenant_id, sample_shift_data, created_by)

        # Verify
        attendance_service.shift_repo.check_name_exists.assert_called_once()
        attendance_service.shift_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_create_shift_type_duplicate_name(self, attendance_service, sample_shift_data):
        """Test shift type creation with duplicate name."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock name exists
        attendance_service.shift_repo.check_name_exists.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError, match="Shift type name already exists"):
            await attendance_service.create_shift_type(tenant_id, sample_shift_data, created_by)


class TestAttendanceReporting:
    """Test attendance reporting and analytics."""

    @pytest.mark.asyncio
    async def test_get_attendance_summary(self, attendance_service):
        """Test attendance summary generation."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()

        # Mock attendance records
        mock_summary = {
            "total_days": 30,
            "present_days": 25,
            "absent_days": 3,
            "half_days": 2,
            "total_working_hours": Decimal("200.0"),
            "average_working_hours": Decimal("8.0"),
        }

        attendance_service.attendance_repo.get_summary.return_value = mock_summary

        # Execute
        result = await attendance_service.get_attendance_summary(tenant_id, employee_id, start_date, end_date)

        # Verify
        attendance_service.attendance_repo.get_summary.assert_called_once_with(
            tenant_id, employee_id, start_date, end_date
        )
        assert result["total_days"] == 30
        assert result["present_days"] == 25
        assert result["attendance_percentage"] == 83.33  # 25/30 * 100


class TestBusinessLogicValidation:
    """Test business logic validation rules."""

    @pytest.mark.asyncio
    async def test_validate_working_hours_threshold(self, attendance_service):
        """Test working hours threshold validation."""
        # Test case: 6 hours worked, threshold 7.5 hours = Half Day
        working_hours = Decimal("6.0")
        threshold = Decimal("7.5")

        result = attendance_service._calculate_attendance_status(working_hours, threshold)

        assert result == AttendanceStatus.HALF_DAY

    @pytest.mark.asyncio
    async def test_validate_late_entry_detection(self, attendance_service):
        """Test late entry detection logic."""
        shift_start = time(9, 0)
        actual_checkin = time(9, 20)
        grace_period = 15  # minutes

        is_late = attendance_service._is_late_entry(shift_start, actual_checkin, grace_period)

        assert is_late is True

    @pytest.mark.asyncio
    async def test_validate_early_exit_detection(self, attendance_service):
        """Test early exit detection logic."""
        shift_end = time(17, 0)
        actual_checkout = time(16, 30)
        grace_period = 15  # minutes

        is_early = attendance_service._is_early_exit(shift_end, actual_checkout, grace_period)

        assert is_early is True
