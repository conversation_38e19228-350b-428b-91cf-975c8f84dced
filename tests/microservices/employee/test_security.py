"""
Security tests for Employee Management Service.

Tests authentication, authorization, data access controls, and security vulnerabilities.
"""

import json
from datetime import date
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from hrms.microservices.employee.api import app


@pytest.fixture
def client():
    """Test client for security tests."""
    return TestClient(app)


@pytest.fixture
def valid_employee_payload():
    """Valid employee creation payload."""
    return {
        "employee_id": "EMP001",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "hire_date": str(date.today()),
        "department_id": "dept-123",
        "position_id": "pos-123",
    }


class TestAuthentication:
    """Test authentication mechanisms."""

    def test_no_authentication_header(self, client):
        """Test API access without authentication header."""
        response = client.get("/employees/")

        # Should return 401 Unauthorized
        assert response.status_code == 401

    def test_invalid_authentication_scheme(self, client):
        """Test API access with invalid authentication scheme."""
        headers = {"Authorization": "Basic invalid-token"}

        response = client.get("/employees/", headers=headers)

        # Should return 401 Unauthorized
        assert response.status_code == 401

    def test_malformed_jwt_token(self, client):
        """Test API access with malformed JWT token."""
        headers = {"Authorization": "Bearer invalid.jwt.token"}

        response = client.get("/employees/", headers=headers)

        # Should return 401 Unauthorized
        assert response.status_code == 401

    def test_expired_jwt_token(self, client):
        """Test API access with expired JWT token."""
        # Simulate expired token
        expired_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid"
        headers = {"Authorization": f"Bearer {expired_token}"}

        response = client.get("/employees/", headers=headers)

        # Should return 401 Unauthorized
        assert response.status_code == 401

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_valid_authentication(self, mock_get_tenant, mock_get_user, client):
        """Test API access with valid authentication."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_service.list_employees.return_value = []
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}
            response = client.get("/employees/", headers=headers)

            # Should return 200 OK
            assert response.status_code == 200


class TestAuthorization:
    """Test authorization and role-based access control."""

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_insufficient_permissions_create(
        self, mock_get_tenant, mock_get_user, client, valid_employee_payload
    ):
        """Test employee creation with insufficient permissions."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {
            "id": "user123",
            "username": "readonly_user",
            "roles": ["viewer"],
            "permissions": ["read"],  # No write permission
        }

        headers = {"Authorization": "Bearer valid-jwt-token"}
        response = client.post("/employees/", json=valid_employee_payload, headers=headers)

        # Should return 403 Forbidden
        assert response.status_code == 403

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_insufficient_permissions_delete(self, mock_get_tenant, mock_get_user, client):
        """Test employee deletion with insufficient permissions."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {
            "id": "user123",
            "username": "editor_user",
            "roles": ["editor"],
            "permissions": ["read", "write"],  # No delete permission
        }

        headers = {"Authorization": "Bearer valid-jwt-token"}
        response = client.delete("/employees/emp-123", headers=headers)

        # Should return 403 Forbidden
        assert response.status_code == 403

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_admin_full_access(self, mock_get_tenant, mock_get_user, client, valid_employee_payload):
        """Test admin user has full access to all operations."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {
            "id": "admin123",
            "username": "admin_user",
            "roles": ["admin"],
            "permissions": ["read", "write", "delete", "admin"],
        }

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_employee = MagicMock()
            mock_employee.dict.return_value = {"id": "emp-123", "employee_id": "EMP001"}

            mock_service = AsyncMock()
            mock_service.create_employee.return_value = mock_employee
            mock_service.get_employee.return_value = mock_employee
            mock_service.delete_employee.return_value = True
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer admin-jwt-token"}

            # Test create
            response = client.post("/employees/", json=valid_employee_payload, headers=headers)
            assert response.status_code == 201

            # Test read
            response = client.get("/employees/emp-123", headers=headers)
            assert response.status_code == 200

            # Test delete
            response = client.delete("/employees/emp-123", headers=headers)
            assert response.status_code == 204


class TestTenantIsolation:
    """Test tenant isolation and data access controls."""

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_cross_tenant_data_access_prevention(self, mock_get_tenant, mock_get_user, client):
        """Test that users cannot access data from other tenants."""
        from unittest.mock import patch

        from hrms.microservices.shared.exceptions import NotFoundError

        mock_get_tenant.return_value = "tenant_a"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            # Simulate employee not found (because it belongs to different tenant)
            mock_service = AsyncMock()
            mock_service.get_employee.side_effect = NotFoundError("Employee not found")
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}

            # Try to access employee from tenant_b while authenticated as tenant_a
            response = client.get("/employees/emp-from-tenant-b", headers=headers)

            # Should return 404 Not Found (not 403 to avoid information disclosure)
            assert response.status_code == 404

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_tenant_data_isolation_in_listing(self, mock_get_tenant, mock_get_user, client):
        """Test that employee listing only returns data for the current tenant."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "tenant_a"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            # Mock employees only from tenant_a
            mock_employees = [
                MagicMock(dict=lambda: {"id": "emp-1", "tenant_id": "tenant_a"}),
                MagicMock(dict=lambda: {"id": "emp-2", "tenant_id": "tenant_a"}),
            ]

            mock_service = AsyncMock()
            mock_service.list_employees.return_value = mock_employees
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}
            response = client.get("/employees/", headers=headers)

            assert response.status_code == 200
            data = response.json()

            # Verify all returned employees belong to tenant_a
            for employee in data:
                assert employee["tenant_id"] == "tenant_a"


class TestInputValidation:
    """Test input validation and injection prevention."""

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_sql_injection_prevention(self, mock_get_tenant, mock_get_user, client):
        """Test SQL injection prevention in search parameters."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_service.list_employees.return_value = []
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}

            # Attempt SQL injection in search parameter
            malicious_search = "'; DROP TABLE employees; --"
            response = client.get(f"/employees/?search={malicious_search}", headers=headers)

            # Should handle gracefully without error
            assert response.status_code in [200, 400]  # Either success or validation error

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_xss_prevention_in_input(self, mock_get_tenant, mock_get_user, client):
        """Test XSS prevention in employee data input."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}

            # Attempt XSS in employee data
            xss_payload = {
                "employee_id": "EMP001",
                "first_name": "<script>alert('xss')</script>",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "hire_date": str(date.today()),
                "department_id": "dept-123",
                "position_id": "pos-123",
            }

            response = client.post("/employees/", json=xss_payload, headers=headers)

            # Should either reject the input or sanitize it
            assert response.status_code in [400, 422]  # Validation error

    def test_oversized_payload_rejection(self, client):
        """Test rejection of oversized payloads."""
        headers = {"Authorization": "Bearer valid-jwt-token"}

        # Create oversized payload (simulate very large description)
        oversized_payload = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "hire_date": str(date.today()),
            "department_id": "dept-123",
            "position_id": "pos-123",
            "notes": "A" * 100000,  # 100KB of data
        }

        response = client.post("/employees/", json=oversized_payload, headers=headers)

        # Should reject oversized payload
        assert response.status_code in [400, 413, 422]


class TestDataSecurity:
    """Test data security and sensitive information handling."""

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_sensitive_data_not_in_logs(self, mock_get_tenant, mock_get_user, client, valid_employee_payload):
        """Test that sensitive data is not logged."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_employee = MagicMock()
            mock_employee.dict.return_value = {"id": "emp-123", "employee_id": "EMP001"}

            mock_service = AsyncMock()
            mock_service.create_employee.return_value = mock_employee
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}

            # Add sensitive data to payload
            sensitive_payload = valid_employee_payload.copy()
            sensitive_payload.update({"ssn": "***********", "bank_account": "**********", "salary": 75000})

            response = client.post("/employees/", json=sensitive_payload, headers=headers)

            # Response should be successful but not contain sensitive data in logs
            assert response.status_code == 201

    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_password_field_exclusion(self, mock_get_tenant, mock_get_user, client):
        """Test that password fields are excluded from responses."""
        from unittest.mock import patch

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        with patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service:
            mock_employee = MagicMock()
            mock_employee.dict.return_value = {
                "id": "emp-123",
                "employee_id": "EMP001",
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                # Note: no password field should be included
            }

            mock_service = AsyncMock()
            mock_service.get_employee.return_value = mock_employee
            mock_get_service.return_value = mock_service

            headers = {"Authorization": "Bearer valid-jwt-token"}
            response = client.get("/employees/emp-123", headers=headers)

            assert response.status_code == 200
            data = response.json()

            # Ensure no password-related fields are in response
            sensitive_fields = ["password", "password_hash", "secret", "token"]
            for field in sensitive_fields:
                assert field not in data, f"Sensitive field '{field}' found in response"
