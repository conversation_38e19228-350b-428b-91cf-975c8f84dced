"""
Integration tests for Employee Management Service.

Tests end-to-end functionality including database operations, API endpoints,
and multi-tenancy isolation.
"""

import asyncio
import os
import tempfile
from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from hrms.microservices.employee.api import app
from hrms.microservices.employee.models import (
    Department,
    Employee,
    EmploymentStatus,
    Gender,
    MaritalStatus,
    Position,
)
from hrms.microservices.employee.repository import (
    DepartmentRepository,
    EmployeeRepository,
    PositionRepository,
)
from hrms.microservices.employee.service import EmployeeService
from hrms.microservices.shared.database import db_manager
from hrms.microservices.shared.models import Base

# Test database setup - use in-memory SQLite for faster tests
TEST_DATABASE_URL = "sqlite:///:memory:"
test_engine = create_engine(
    TEST_DATABASE_URL, connect_args={"check_same_thread": False}, echo=False  # Set to True for SQL debugging
)
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="function")
def test_db():
    """Create test database for each test."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)

    # Create a test session
    session = TestSessionLocal()

    try:
        yield session
    finally:
        session.close()
        # Clean up tables after each test
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def mock_auth():
    """Mock authentication dependencies."""
    mock_user = MagicMock()
    mock_user.id = "test_user_123"
    mock_user.tenant_id = "test_tenant"
    mock_user.roles = ["admin"]
    mock_user.permissions = ["read", "write", "delete"]

    mock_tenant = MagicMock()
    mock_tenant.id = "test_tenant"
    mock_tenant.name = "Test Tenant"

    return mock_user, mock_tenant


@pytest.fixture
def client(test_db, mock_auth):
    """Test client with mocked authentication and real database."""
    mock_user, mock_tenant = mock_auth

    # Override database dependency
    def override_get_db():
        try:
            yield test_db
        finally:
            pass

    app.dependency_overrides[db_manager.get_session] = override_get_db

    with (
        patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
        patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
    ):

        mock_get_user.return_value = mock_user
        mock_get_tenant.return_value = "test_tenant"

        yield TestClient(app)

    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def sample_department_data():
    """Sample department data for testing."""
    return {
        "name": "Engineering",
        "code": "ENG",
        "description": "Software Engineering Department",
        "is_active": True,
    }


@pytest.fixture
def sample_position_data():
    """Sample position data for testing."""
    return {
        "title": "Software Engineer",
        "code": "SE",
        "description": "Software Engineer Position",
        "department_id": "dept-123",
        "level": 2,
        "is_active": True,
    }


@pytest.fixture
def sample_employee_data():
    """Sample employee data for testing."""
    return {
        "employee_id": "EMP001",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "date_of_birth": "1990-01-01",
        "gender": "male",
        "marital_status": "single",
        "nationality": "US",
        "hire_date": str(date.today()),
        "status": "active",
        "department_id": "dept-123",
        "position_id": "pos-123",
        "address_line1": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postal_code": "10001",
        "country": "USA",
        "emergency_contact_name": "Jane Doe",
        "emergency_contact_phone": "+1234567891",
        "emergency_contact_relationship": "Spouse",
        "is_active": True,
    }


@pytest.fixture
def department_in_db(test_db):
    """Create a department in the test database."""
    department = Department(
        id="dept-123",
        tenant_id="test_tenant",
        name="Engineering",
        code="ENG",
        description="Software Engineering Department",
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        created_by="test_user_123",
        updated_by="test_user_123",
    )
    test_db.add(department)
    test_db.commit()
    test_db.refresh(department)
    return department


@pytest.fixture
def position_in_db(test_db, department_in_db):
    """Create a position in the test database."""
    position = Position(
        id="pos-123",
        tenant_id="test_tenant",
        title="Software Engineer",
        code="SE",
        description="Software Engineer Position",
        department_id=department_in_db.id,
        level=2,
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        created_by="test_user_123",
        updated_by="test_user_123",
    )
    test_db.add(position)
    test_db.commit()
    test_db.refresh(position)
    return position


class TestEmployeeIntegration:
    """Integration tests for Employee service."""

    @pytest.mark.integration
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data

    @pytest.mark.integration
    def test_create_department_success(self, client, sample_department_data):
        """Test successful department creation."""
        response = client.post("/departments/", json=sample_department_data)

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Engineering"
        assert data["code"] == "ENG"
        assert data["tenant_id"] == "test_tenant"
        assert data["is_active"] is True

    @pytest.mark.integration
    def test_create_position_success(self, client, department_in_db, sample_position_data):
        """Test successful position creation."""
        # Update sample data to use the created department
        sample_position_data["department_id"] = department_in_db.id

        response = client.post("/positions/", json=sample_position_data)

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Software Engineer"
        assert data["code"] == "SE"
        assert data["level"] == 2
        assert data["department_id"] == department_in_db.id

    @pytest.mark.integration
    def test_create_employee_success(self, client, department_in_db, position_in_db, sample_employee_data):
        """Test successful employee creation."""
        # Update sample data to use the created department and position
        sample_employee_data["department_id"] = department_in_db.id
        sample_employee_data["position_id"] = position_in_db.id

        response = client.post("/employees/", json=sample_employee_data)

        assert response.status_code == 201
        data = response.json()
        assert data["employee_id"] == "EMP001"
        assert data["first_name"] == "John"
        assert data["last_name"] == "Doe"
        assert data["tenant_id"] == "test_tenant"
        assert data["department_id"] == department_in_db.id
        assert data["position_id"] == position_in_db.id

    @pytest.mark.integration
    def test_list_employees_with_pagination(self, client):
        """Test employee listing with pagination."""
        # Test empty list first
        response = client.get("/employees/?skip=0&limit=10")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0

    @pytest.mark.integration
    def test_get_employee_by_id_not_found(self, client):
        """Test getting employee by ID when not found."""
        response = client.get("/employees/nonexistent-id")

        assert response.status_code == 404

    @pytest.mark.integration
    def test_list_departments(self, client, department_in_db):
        """Test listing departments."""
        response = client.get("/departments/")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

        # Check if our department is in the list
        dept_found = any(dept["id"] == department_in_db.id for dept in data)
        assert dept_found

    @pytest.mark.integration
    def test_list_positions(self, client, position_in_db):
        """Test listing positions."""
        response = client.get("/positions/")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

        # Check if our position is in the list
        pos_found = any(pos["id"] == position_in_db.id for pos in data)
        assert pos_found


class TestTenantIsolation:
    """Test tenant isolation in the Employee service."""

    @pytest.mark.integration
    def test_tenant_isolation_in_employee_creation(self, test_db, mock_auth):
        """Test that employees are created with correct tenant isolation."""
        mock_user, mock_tenant = mock_auth

        # Test that tenant_id is properly set in database entities
        department = Department(
            id="dept-123",
            tenant_id="test_tenant",
            name="Engineering",
            code="ENG",
            description="Engineering Department",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            created_by="test_user",
            updated_by="test_user",
        )
        test_db.add(department)
        test_db.commit()

        # Verify tenant isolation
        assert department.tenant_id == "test_tenant"

        # Query should only return departments for this tenant
        tenant_departments = test_db.query(Department).filter(Department.tenant_id == "test_tenant").all()
        assert len(tenant_departments) == 1
        assert tenant_departments[0].id == department.id


class TestErrorHandling:
    """Test error handling in the Employee service."""

    @pytest.mark.integration
    def test_validation_error_handling(self, client):
        """Test validation error handling."""
        # Send invalid employee data
        invalid_data = {
            "employee_id": "",  # Empty employee ID
            "first_name": "",  # Empty first name
            "email": "invalid-email",  # Invalid email
        }

        response = client.post("/employees/", json=invalid_data)

        assert response.status_code == 422  # Validation error

    @pytest.mark.integration
    def test_not_found_error_handling(self, client):
        """Test not found error handling."""
        response = client.get("/employees/nonexistent-id")

        assert response.status_code == 404
