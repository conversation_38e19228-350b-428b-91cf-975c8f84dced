"""
Performance tests for Employee Management Service.

Tests performance with large datasets, concurrent operations, and stress testing.
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient

from hrms.microservices.employee.api import app
from hrms.microservices.employee.models import EmploymentStatus, Gender, MaritalStatus


@pytest.fixture
def client():
    """Test client for performance tests."""
    return TestClient(app)


@pytest.fixture
def large_employee_dataset():
    """Generate large dataset of employee data."""
    employees = []
    for i in range(1000):  # 1000 employees
        employee = {
            "id": f"emp-{i:04d}",
            "employee_id": f"EMP{i:04d}",
            "first_name": f"Employee{i}",
            "last_name": "Test",
            "email": f"employee{i}@company.com",
            "phone": f"+123456{i:04d}",
            "date_of_birth": date(1990, 1, 1),
            "gender": Gender.MALE if i % 2 == 0 else Gender.FEMALE,
            "marital_status": MaritalStatus.SINGLE,
            "nationality": "US",
            "hire_date": date.today(),
            "status": EmploymentStatus.ACTIVE,
            "department_id": f"dept-{i % 10}",  # 10 departments
            "position_id": f"pos-{i % 20}",  # 20 positions
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": "system",
            "updated_by": "system",
        }
        employees.append(employee)
    return employees


class TestEmployeePerformance:
    """Performance tests for Employee operations."""

    @pytest.mark.performance
    def test_list_employees_large_dataset_performance(self, client, large_employee_dataset):
        """Test performance of listing employees with large dataset."""
        from unittest.mock import patch

        with (
            patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
            patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
            patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
        ):

            mock_get_tenant.return_value = "test_tenant"
            mock_get_user.return_value = {"id": "user123", "username": "testuser"}

            # Mock large dataset
            mock_employees = [MagicMock(dict=lambda emp=emp: emp) for emp in large_employee_dataset]

            mock_service = AsyncMock()
            mock_service.list_employees.return_value = mock_employees
            mock_get_service.return_value = mock_service

            # Measure performance
            start_time = time.time()

            response = client.get(
                "/employees/?skip=0&limit=1000", headers={"Authorization": "Bearer test_token"}
            )

            end_time = time.time()
            execution_time = end_time - start_time

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1000

            # Performance assertion - should complete within 2 seconds
            assert execution_time < 2.0, f"List operation took {execution_time:.2f}s, expected < 2.0s"

    @pytest.mark.performance
    def test_search_employees_performance(self, client, large_employee_dataset):
        """Test performance of employee search operations."""
        from unittest.mock import patch

        with (
            patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
            patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
            patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
        ):

            mock_get_tenant.return_value = "test_tenant"
            mock_get_user.return_value = {"id": "user123", "username": "testuser"}

            # Mock filtered results (simulate search)
            filtered_employees = [emp for emp in large_employee_dataset if "Employee1" in emp["first_name"]]
            mock_employees = [MagicMock(dict=lambda emp=emp: emp) for emp in filtered_employees]

            mock_service = AsyncMock()
            mock_service.list_employees.return_value = mock_employees
            mock_get_service.return_value = mock_service

            # Measure search performance
            start_time = time.time()

            response = client.get(
                "/employees/?search=Employee1&skip=0&limit=100",
                headers={"Authorization": "Bearer test_token"},
            )

            end_time = time.time()
            execution_time = end_time - start_time

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert len(data) > 0

            # Performance assertion - search should complete within 1 second
            assert execution_time < 1.0, f"Search operation took {execution_time:.2f}s, expected < 1.0s"

    @pytest.mark.performance
    def test_concurrent_employee_creation(self, client):
        """Test concurrent employee creation performance."""
        from unittest.mock import patch

        def create_employee(employee_id):
            """Create a single employee."""
            with (
                patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
                patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
                patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
            ):

                mock_get_tenant.return_value = "test_tenant"
                mock_get_user.return_value = {"id": "user123", "username": "testuser"}

                mock_employee = MagicMock()
                mock_employee.dict.return_value = {
                    "id": f"emp-{employee_id}",
                    "employee_id": f"EMP{employee_id:03d}",
                    "first_name": f"Employee{employee_id}",
                    "last_name": "Test",
                }

                mock_service = AsyncMock()
                mock_service.create_employee.return_value = mock_employee
                mock_get_service.return_value = mock_service

                employee_payload = {
                    "employee_id": f"EMP{employee_id:03d}",
                    "first_name": f"Employee{employee_id}",
                    "last_name": "Test",
                    "email": f"employee{employee_id}@company.com",
                    "hire_date": str(date.today()),
                    "department_id": "dept-123",
                    "position_id": "pos-123",
                }

                response = client.post(
                    "/employees/", json=employee_payload, headers={"Authorization": "Bearer test_token"}
                )
                return response.status_code == 201

        # Test concurrent creation of 50 employees
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_employee, i) for i in range(50)]
            results = [future.result() for future in futures]

        end_time = time.time()
        execution_time = end_time - start_time

        # Assertions
        assert all(results), "All employee creations should succeed"

        # Performance assertion - 50 concurrent creations should complete within 5 seconds
        assert execution_time < 5.0, f"Concurrent creation took {execution_time:.2f}s, expected < 5.0s"

    @pytest.mark.performance
    def test_bulk_employee_update_performance(self, client):
        """Test bulk employee update performance."""
        from unittest.mock import patch

        with (
            patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
            patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
            patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
        ):

            mock_get_tenant.return_value = "test_tenant"
            mock_get_user.return_value = {"id": "user123", "username": "testuser"}

            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            # Simulate bulk update of 100 employees
            start_time = time.time()

            successful_updates = 0
            for i in range(100):
                mock_employee = MagicMock()
                mock_employee.dict.return_value = {
                    "id": f"emp-{i}",
                    "first_name": f"UpdatedEmployee{i}",
                    "phone": f"+987654{i:04d}",
                }
                mock_service.update_employee.return_value = mock_employee

                update_payload = {"first_name": f"UpdatedEmployee{i}", "phone": f"+987654{i:04d}"}

                response = client.put(
                    f"/employees/emp-{i}", json=update_payload, headers={"Authorization": "Bearer test_token"}
                )

                if response.status_code == 200:
                    successful_updates += 1

            end_time = time.time()
            execution_time = end_time - start_time

            # Assertions
            assert successful_updates == 100, "All updates should succeed"

            # Performance assertion - 100 updates should complete within 3 seconds
            assert execution_time < 3.0, f"Bulk update took {execution_time:.2f}s, expected < 3.0s"

    @pytest.mark.performance
    def test_pagination_performance(self, client, large_employee_dataset):
        """Test pagination performance with large dataset."""
        from unittest.mock import patch

        with (
            patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
            patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
            patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
        ):

            mock_get_tenant.return_value = "test_tenant"
            mock_get_user.return_value = {"id": "user123", "username": "testuser"}

            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service

            # Test multiple pages
            page_size = 50
            total_pages = 20  # 1000 employees / 50 per page

            start_time = time.time()

            for page in range(total_pages):
                skip = page * page_size
                page_data = large_employee_dataset[skip : skip + page_size]
                mock_employees = [MagicMock(dict=lambda emp=emp: emp) for emp in page_data]
                mock_service.list_employees.return_value = mock_employees

                response = client.get(
                    f"/employees/?skip={skip}&limit={page_size}",
                    headers={"Authorization": "Bearer test_token"},
                )

                assert response.status_code == 200
                data = response.json()
                assert len(data) == page_size

            end_time = time.time()
            execution_time = end_time - start_time

            # Performance assertion - paginating through 1000 records should complete within 10 seconds
            assert execution_time < 10.0, f"Pagination took {execution_time:.2f}s, expected < 10.0s"


class TestEmployeeStressTest:
    """Stress tests for Employee service."""

    @pytest.mark.stress
    def test_high_load_employee_retrieval(self, client):
        """Test high load employee retrieval."""
        from unittest.mock import patch

        with (
            patch("hrms.microservices.employee.api.get_employee_service") as mock_get_service,
            patch("hrms.microservices.employee.api.get_current_user") as mock_get_user,
            patch("hrms.microservices.employee.api.get_tenant_id") as mock_get_tenant,
        ):

            mock_get_tenant.return_value = "test_tenant"
            mock_get_user.return_value = {"id": "user123", "username": "testuser"}

            mock_employee = MagicMock()
            mock_employee.dict.return_value = {
                "id": "emp-123",
                "employee_id": "EMP001",
                "first_name": "John",
                "last_name": "Doe",
            }

            mock_service = AsyncMock()
            mock_service.get_employee.return_value = mock_employee
            mock_get_service.return_value = mock_service

            # Simulate 500 concurrent requests
            def get_employee():
                response = client.get("/employees/emp-123", headers={"Authorization": "Bearer test_token"})
                return response.status_code == 200

            start_time = time.time()

            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = [executor.submit(get_employee) for _ in range(500)]
                results = [future.result() for future in futures]

            end_time = time.time()
            execution_time = end_time - start_time

            # Assertions
            success_rate = sum(results) / len(results)
            assert success_rate >= 0.95, f"Success rate {success_rate:.2%} should be >= 95%"

            # Performance assertion - 500 concurrent requests should complete within 15 seconds
            assert execution_time < 15.0, f"High load test took {execution_time:.2f}s, expected < 15.0s"
