"""
Unit tests for Employee Management Service.

Tests business logic, validation, and service operations with comprehensive coverage.
"""

from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest

from hrms.microservices.employee.models import (
    DepartmentCreate,
    EmployeeCreate,
    EmployeeUpdate,
    EmploymentStatus,
    Gender,
    MaritalStatus,
    PositionCreate,
)
from hrms.microservices.employee.service import EmployeeService
from hrms.microservices.shared.exceptions import (
    BusinessLogicError,
    ConflictError,
    NotFoundError,
    ValidationError,
)


@pytest.fixture
def mock_db_manager():
    """Mock database manager."""
    return MagicMock()


@pytest.fixture
def employee_service(mock_db_manager):
    """Employee service with mocked dependencies."""
    service = EmployeeService(mock_db_manager)
    service.employee_repo = AsyncMock()
    service.department_repo = AsyncMock()
    service.position_repo = AsyncMock()
    service.document_repo = AsyncMock()
    return service


@pytest.fixture
def sample_department_data():
    """Sample department data."""
    return DepartmentCreate(
        name="Engineering", code="ENG", description="Software Engineering Department", is_active=True
    )


@pytest.fixture
def sample_position_data():
    """Sample position data."""
    return PositionCreate(
        title="Software Engineer",
        code="SE",
        description="Software Engineer Position",
        department_id="dept-123",
        level=2,
        is_active=True,
    )


@pytest.fixture
def sample_employee_data():
    """Sample employee data."""
    return EmployeeCreate(
        employee_id="EMP001",
        first_name="John",
        last_name="Doe",
        email="<EMAIL>",
        phone="+1234567890",
        date_of_birth=date(1990, 1, 1),
        gender=Gender.MALE,
        marital_status=MaritalStatus.SINGLE,
        nationality="US",
        hire_date=date.today(),
        status=EmploymentStatus.ACTIVE,
        department_id="dept-123",
        position_id="pos-123",
        address_line1="123 Main St",
        city="New York",
        state="NY",
        postal_code="10001",
        country="USA",
        emergency_contact_name="Jane Doe",
        emergency_contact_phone="+1234567891",
        emergency_contact_relationship="Spouse",
        is_active=True,
    )


class TestEmployeeService:
    """Test cases for Employee Service."""

    @pytest.mark.asyncio
    async def test_create_employee_success(self, employee_service, sample_employee_data):
        """Test successful employee creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock repository responses
        employee_service.employee_repo.check_employee_id_exists.return_value = False
        employee_service.employee_repo.check_email_exists.return_value = False
        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Create complete mock employee with all required fields
        mock_employee = MagicMock()
        mock_employee.id = "emp-123"
        mock_employee.tenant_id = tenant_id
        mock_employee.employee_id = "EMP001"
        mock_employee.first_name = "John"
        mock_employee.last_name = "Doe"
        mock_employee.middle_name = None
        mock_employee.email = "<EMAIL>"
        mock_employee.phone = "+1234567890"
        mock_employee.date_of_birth = date(1990, 1, 1)
        mock_employee.gender = Gender.MALE
        mock_employee.marital_status = MaritalStatus.SINGLE
        mock_employee.nationality = "US"
        mock_employee.hire_date = date.today()
        mock_employee.termination_date = None
        mock_employee.status = EmploymentStatus.ACTIVE
        mock_employee.department_id = "dept-123"
        mock_employee.position_id = "pos-123"
        mock_employee.manager_id = None
        mock_employee.address_line1 = "123 Main St"
        mock_employee.address_line2 = None
        mock_employee.city = "New York"
        mock_employee.state = "NY"
        mock_employee.postal_code = "10001"
        mock_employee.country = "USA"
        mock_employee.emergency_contact_name = "Jane Doe"
        mock_employee.emergency_contact_phone = "+1234567891"
        mock_employee.emergency_contact_relationship = "Spouse"
        mock_employee.is_active = True
        mock_employee.created_at = datetime.now()
        mock_employee.updated_at = datetime.now()
        mock_employee.created_by = created_by
        mock_employee.updated_by = created_by
        # Set related objects to None to avoid Pydantic validation issues
        mock_employee.department = None
        mock_employee.position = None

        employee_service.employee_repo.create.return_value = mock_employee

        # Execute
        result = await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        # Verify
        employee_service.employee_repo.check_employee_id_exists.assert_called_once_with(tenant_id, "EMP001")
        employee_service.employee_repo.check_email_exists.assert_called_once_with(
            tenant_id, "<EMAIL>"
        )
        employee_service.employee_repo.create.assert_called_once()

        assert result is not None

    @pytest.mark.asyncio
    async def test_create_employee_duplicate_employee_id(self, employee_service, sample_employee_data):
        """Test employee creation with duplicate employee ID."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate employee ID
        employee_service.employee_repo.check_employee_id_exists.return_value = True
        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Employee ID EMP001 already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_employee_duplicate_email(self, employee_service, sample_employee_data):
        """Test employee creation with duplicate email."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate email
        employee_service.employee_repo.check_employee_id_exists.return_value = False
        employee_service.employee_repo.check_email_exists.return_value = True
        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Email <EMAIL> already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_employee_invalid_department(self, employee_service, sample_employee_data):
        """Test employee creation with invalid department."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock invalid department
        employee_service.employee_repo.check_employee_id_exists.return_value = False
        employee_service.employee_repo.check_email_exists.return_value = False
        employee_service.department_repo.get_by_id.return_value = None
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Department not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_employee_invalid_position(self, employee_service, sample_employee_data):
        """Test employee creation with invalid position."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock invalid position
        employee_service.employee_repo.check_employee_id_exists.return_value = False
        employee_service.employee_repo.check_email_exists.return_value = False
        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = None

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Position not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_employee_success(self, employee_service):
        """Test successful employee retrieval."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"

        # Create complete mock employee with all required fields
        mock_employee = MagicMock()
        mock_employee.id = employee_id
        mock_employee.tenant_id = tenant_id
        mock_employee.employee_id = "EMP001"
        mock_employee.first_name = "John"
        mock_employee.last_name = "Doe"
        mock_employee.middle_name = None
        mock_employee.email = "<EMAIL>"
        mock_employee.phone = "+1234567890"
        mock_employee.date_of_birth = date(1990, 1, 1)
        mock_employee.gender = Gender.MALE
        mock_employee.marital_status = MaritalStatus.SINGLE
        mock_employee.nationality = "US"
        mock_employee.hire_date = date.today()
        mock_employee.termination_date = None
        mock_employee.status = EmploymentStatus.ACTIVE
        mock_employee.department_id = "dept-123"
        mock_employee.position_id = "pos-123"
        mock_employee.manager_id = None
        mock_employee.address_line1 = "123 Main St"
        mock_employee.address_line2 = None
        mock_employee.city = "New York"
        mock_employee.state = "NY"
        mock_employee.postal_code = "10001"
        mock_employee.country = "USA"
        mock_employee.emergency_contact_name = "Jane Doe"
        mock_employee.emergency_contact_phone = "+1234567891"
        mock_employee.emergency_contact_relationship = "Spouse"
        mock_employee.is_active = True
        mock_employee.created_at = datetime.now()
        mock_employee.updated_at = datetime.now()
        mock_employee.created_by = "user123"
        mock_employee.updated_by = "user123"
        # Set related objects to None to avoid Pydantic validation issues
        mock_employee.department = None
        mock_employee.position = None

        employee_service.employee_repo.get_by_id.return_value = mock_employee

        # Execute
        result = await employee_service.get_employee(tenant_id, employee_id)

        # Verify
        employee_service.employee_repo.get_by_id.assert_called_once_with(tenant_id, employee_id)
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_employee_not_found(self, employee_service):
        """Test employee retrieval when not found."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"

        employee_service.employee_repo.get_by_id.return_value = None

        # Execute and verify exception
        with pytest.raises(NotFoundError) as exc_info:
            await employee_service.get_employee(tenant_id, employee_id)

        assert f"Employee not found: {employee_id}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_employee_success(self, employee_service):
        """Test successful employee update."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"
        updated_by = "user123"

        update_data = EmployeeUpdate(first_name="Jane", phone="+1234567892")

        # Create complete mock employee with all required fields
        mock_employee = MagicMock()
        mock_employee.id = employee_id
        mock_employee.tenant_id = tenant_id
        mock_employee.employee_id = "EMP001"
        mock_employee.first_name = "Jane"  # Updated name
        mock_employee.last_name = "Doe"
        mock_employee.middle_name = None
        mock_employee.email = "<EMAIL>"
        mock_employee.phone = "+1234567892"  # Updated phone
        mock_employee.date_of_birth = date(1990, 1, 1)
        mock_employee.gender = Gender.FEMALE
        mock_employee.marital_status = MaritalStatus.SINGLE
        mock_employee.nationality = "US"
        mock_employee.hire_date = date.today()
        mock_employee.termination_date = None
        mock_employee.status = EmploymentStatus.ACTIVE
        mock_employee.department_id = "dept-123"
        mock_employee.position_id = "pos-123"
        mock_employee.manager_id = None
        mock_employee.address_line1 = "123 Main St"
        mock_employee.address_line2 = None
        mock_employee.city = "New York"
        mock_employee.state = "NY"
        mock_employee.postal_code = "10001"
        mock_employee.country = "USA"
        mock_employee.emergency_contact_name = "John Doe"
        mock_employee.emergency_contact_phone = "+1234567891"
        mock_employee.emergency_contact_relationship = "Spouse"
        mock_employee.is_active = True
        mock_employee.created_at = datetime.now()
        mock_employee.updated_at = datetime.now()
        mock_employee.created_by = "user123"
        mock_employee.updated_by = updated_by
        # Set related objects to None to avoid Pydantic validation issues
        mock_employee.department = None
        mock_employee.position = None

        employee_service.employee_repo.get_by_id.return_value = mock_employee
        employee_service.employee_repo.check_email_exists.return_value = False
        employee_service.employee_repo.update.return_value = mock_employee

        # Execute
        result = await employee_service.update_employee(tenant_id, employee_id, update_data, updated_by)

        # Verify
        employee_service.employee_repo.get_by_id.assert_called_once_with(tenant_id, employee_id)
        employee_service.employee_repo.update.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_delete_employee_success(self, employee_service):
        """Test successful employee deletion."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"

        mock_employee = MagicMock()
        mock_employee.id = employee_id

        employee_service.employee_repo.get_by_id.return_value = mock_employee
        employee_service.employee_repo.get_by_manager.return_value = []  # No subordinates
        employee_service.employee_repo.delete.return_value = True

        # Execute
        result = await employee_service.delete_employee(tenant_id, employee_id)

        # Verify
        employee_service.employee_repo.get_by_id.assert_called_once_with(tenant_id, employee_id)
        employee_service.employee_repo.get_by_manager.assert_called_once_with(tenant_id, employee_id)
        employee_service.employee_repo.delete.assert_called_once_with(tenant_id, employee_id)
        assert result is True

    @pytest.mark.asyncio
    async def test_delete_employee_with_subordinates(self, employee_service):
        """Test employee deletion when employee has subordinates."""
        tenant_id = "test_tenant"
        employee_id = "emp-123"

        mock_employee = MagicMock()
        mock_employee.id = employee_id

        mock_subordinate = MagicMock()
        mock_subordinate.id = "emp-456"

        employee_service.employee_repo.get_by_id.return_value = mock_employee
        employee_service.employee_repo.get_by_manager.return_value = [mock_subordinate]

        # Execute and verify exception
        with pytest.raises(BusinessLogicError) as exc_info:
            await employee_service.delete_employee(tenant_id, employee_id)

        assert "Cannot delete employee with subordinates" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_department_success(self, employee_service, sample_department_data):
        """Test successful department creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        employee_service.department_repo.get_by_code.return_value = None

        # Create complete mock department with all required fields
        mock_department = MagicMock()
        mock_department.id = "dept-123"
        mock_department.tenant_id = tenant_id
        mock_department.name = "Engineering"
        mock_department.code = "ENG"
        mock_department.description = "Engineering Department"
        mock_department.parent_department_id = None
        mock_department.manager_id = None
        mock_department.is_active = True
        mock_department.created_at = datetime.now()
        mock_department.updated_at = datetime.now()
        mock_department.created_by = created_by
        mock_department.updated_by = created_by

        employee_service.department_repo.create.return_value = mock_department

        # Execute
        result = await employee_service.create_department(tenant_id, sample_department_data, created_by)

        # Verify
        employee_service.department_repo.get_by_code.assert_called_once_with(tenant_id, "ENG")
        employee_service.department_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_create_department_duplicate_code(self, employee_service, sample_department_data):
        """Test department creation with duplicate code."""
        tenant_id = "test_tenant"
        created_by = "user123"

        mock_existing_dept = MagicMock()
        mock_existing_dept.code = "ENG"

        employee_service.department_repo.get_by_code.return_value = mock_existing_dept

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await employee_service.create_department(tenant_id, sample_department_data, created_by)

        assert "Department code ENG already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_position_success(self, employee_service, sample_position_data):
        """Test successful position creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        employee_service.position_repo.get_by_code.return_value = None
        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")

        # Create complete mock position with all required fields
        mock_position = MagicMock()
        mock_position.id = "pos-123"
        mock_position.tenant_id = tenant_id
        mock_position.title = "Software Engineer"
        mock_position.code = "SE"
        mock_position.description = "Software Engineer Position"
        mock_position.department_id = "dept-123"
        mock_position.level = 2
        mock_position.is_active = True
        mock_position.created_at = datetime.now()
        mock_position.updated_at = datetime.now()
        mock_position.created_by = created_by
        mock_position.updated_by = created_by

        employee_service.position_repo.create.return_value = mock_position

        # Execute
        result = await employee_service.create_position(tenant_id, sample_position_data, created_by)

        # Verify
        employee_service.position_repo.get_by_code.assert_called_once_with(tenant_id, "SE")
        employee_service.department_repo.get_by_id.assert_called_once_with(tenant_id, "dept-123")
        employee_service.position_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_list_employees_with_filters(self, employee_service):
        """Test listing employees with filters."""
        tenant_id = "test_tenant"
        skip = 0
        limit = 50
        filters = {"department_id": "dept-123", "status": "active"}

        # Create complete mock employees with all required fields
        mock_employee_1 = MagicMock()
        mock_employee_1.id = "emp-1"
        mock_employee_1.tenant_id = tenant_id
        mock_employee_1.employee_id = "EMP001"
        mock_employee_1.first_name = "John"
        mock_employee_1.last_name = "Doe"
        mock_employee_1.middle_name = None
        mock_employee_1.email = "<EMAIL>"
        mock_employee_1.phone = "+1234567890"
        mock_employee_1.date_of_birth = date(1990, 1, 1)
        mock_employee_1.gender = Gender.MALE
        mock_employee_1.marital_status = MaritalStatus.SINGLE
        mock_employee_1.nationality = "US"
        mock_employee_1.hire_date = date.today()
        mock_employee_1.termination_date = None
        mock_employee_1.status = EmploymentStatus.ACTIVE
        mock_employee_1.department_id = "dept-123"
        mock_employee_1.position_id = "pos-123"
        mock_employee_1.manager_id = None
        mock_employee_1.address_line1 = "123 Main St"
        mock_employee_1.address_line2 = None
        mock_employee_1.city = "New York"
        mock_employee_1.state = "NY"
        mock_employee_1.postal_code = "10001"
        mock_employee_1.country = "USA"
        mock_employee_1.emergency_contact_name = "Jane Doe"
        mock_employee_1.emergency_contact_phone = "+1234567891"
        mock_employee_1.emergency_contact_relationship = "Spouse"
        mock_employee_1.is_active = True
        mock_employee_1.created_at = datetime.now()
        mock_employee_1.updated_at = datetime.now()
        mock_employee_1.created_by = "user123"
        mock_employee_1.updated_by = "user123"
        # Set related objects to None to avoid Pydantic validation issues
        mock_employee_1.department = None
        mock_employee_1.position = None

        mock_employee_2 = MagicMock()
        mock_employee_2.id = "emp-2"
        mock_employee_2.tenant_id = tenant_id
        mock_employee_2.employee_id = "EMP002"
        mock_employee_2.first_name = "Jane"
        mock_employee_2.last_name = "Smith"
        mock_employee_2.middle_name = None
        mock_employee_2.email = "<EMAIL>"
        mock_employee_2.phone = "+1234567892"
        mock_employee_2.date_of_birth = date(1992, 2, 2)
        mock_employee_2.gender = Gender.FEMALE
        mock_employee_2.marital_status = MaritalStatus.MARRIED
        mock_employee_2.nationality = "US"
        mock_employee_2.hire_date = date.today()
        mock_employee_2.termination_date = None
        mock_employee_2.status = EmploymentStatus.ACTIVE
        mock_employee_2.department_id = "dept-123"
        mock_employee_2.position_id = "pos-124"
        mock_employee_2.manager_id = None
        mock_employee_2.address_line1 = "456 Oak Ave"
        mock_employee_2.address_line2 = None
        mock_employee_2.city = "Boston"
        mock_employee_2.state = "MA"
        mock_employee_2.postal_code = "02101"
        mock_employee_2.country = "USA"
        mock_employee_2.emergency_contact_name = "John Smith"
        mock_employee_2.emergency_contact_phone = "+1234567893"
        mock_employee_2.emergency_contact_relationship = "Spouse"
        mock_employee_2.is_active = True
        mock_employee_2.created_at = datetime.now()
        mock_employee_2.updated_at = datetime.now()
        mock_employee_2.created_by = "user123"
        mock_employee_2.updated_by = "user123"
        # Set related objects to None to avoid Pydantic validation issues
        mock_employee_2.department = None
        mock_employee_2.position = None

        mock_employees = [mock_employee_1, mock_employee_2]

        employee_service.employee_repo.list_with_details.return_value = mock_employees

        # Execute
        result = await employee_service.list_employees(tenant_id, skip, limit, filters)

        # Verify
        employee_service.employee_repo.list_with_details.assert_called_once_with(
            tenant_id, skip, limit, filters
        )
        assert len(result) == 2

    @pytest.mark.asyncio
    async def test_get_employee_statistics(self, employee_service):
        """Test getting employee statistics."""
        tenant_id = "test_tenant"

        mock_stats = {
            "total_employees": 100,
            "active_employees": 95,
            "inactive_employees": 5,
            "by_department": {"Engineering": 50, "Sales": 30, "HR": 20},
        }

        employee_service.employee_repo.get_statistics.return_value = mock_stats

        # Execute
        result = await employee_service.get_employee_statistics(tenant_id)

        # Verify
        employee_service.employee_repo.get_statistics.assert_called_once_with(tenant_id)
        assert result["total_employees"] == 100
        assert result["active_employees"] == 95
        assert "by_department" in result


class TestEmployeeValidation:
    """Test cases for employee validation logic."""

    def test_validate_invalid_email(self):
        """Test validation with invalid email."""
        from pydantic import ValidationError as PydanticValidationError

        # Test that Pydantic validation catches invalid email
        with pytest.raises(PydanticValidationError) as exc_info:
            EmployeeCreate(
                employee_id="EMP001",
                first_name="John",
                last_name="Doe",
                email="invalid-email",  # Invalid email format
                hire_date=date.today(),
                department_id="dept-123",
                position_id="pos-123",
            )

        assert "value is not a valid email address" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_future_hire_date(self, employee_service, sample_employee_data):
        """Test validation with future hire date."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Set future hire date
        from datetime import timedelta

        sample_employee_data.hire_date = date.today() + timedelta(days=30)

        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Hire date cannot be in the future" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_termination_before_hire(self, employee_service, sample_employee_data):
        """Test validation with termination date before hire date."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Set termination date before hire date
        sample_employee_data.termination_date = sample_employee_data.hire_date

        employee_service.department_repo.get_by_id.return_value = MagicMock(id="dept-123")
        employee_service.position_repo.get_by_id.return_value = MagicMock(id="pos-123")

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await employee_service.create_employee(tenant_id, sample_employee_data, created_by)

        assert "Termination date must be after hire date" in str(exc_info.value)
