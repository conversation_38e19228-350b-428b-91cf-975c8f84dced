"""
Unit tests for Employee Repository.

Tests database operations and data access layer.
"""

from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.exc import IntegrityError

from hrms.microservices.employee.models import (
    Department,
    Employee,
    EmploymentStatus,
    Gender,
    MaritalStatus,
    Position,
)
from hrms.microservices.employee.repository import (
    DepartmentRepository,
    EmployeeRepository,
    PositionRepository,
)
from hrms.microservices.shared.exceptions import ConflictError, NotFoundError


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    return AsyncMock()


@pytest.fixture
def employee_repo(mock_db_session):
    """Employee repository with mocked session."""
    return EmployeeRepository(mock_db_session)


@pytest.fixture
def department_repo(mock_db_session):
    """Department repository with mocked session."""
    return DepartmentRepository(mock_db_session)


@pytest.fixture
def position_repo(mock_db_session):
    """Position repository with mocked session."""
    return PositionRepository(mock_db_session)


@pytest.fixture
def sample_employee():
    """Sample employee model."""
    employee = Employee()
    employee.id = "emp-123"
    employee.tenant_id = "tenant-123"
    employee.employee_id = "EMP001"
    employee.first_name = "John"
    employee.last_name = "Doe"
    employee.email = "<EMAIL>"
    employee.phone = "+1234567890"
    employee.date_of_birth = date(1990, 1, 1)
    employee.gender = Gender.MALE
    employee.marital_status = MaritalStatus.SINGLE
    employee.nationality = "US"
    employee.hire_date = date.today()
    employee.status = EmploymentStatus.ACTIVE
    employee.department_id = "dept-123"
    employee.position_id = "pos-123"
    employee.is_active = True
    employee.created_at = datetime.now()
    employee.updated_at = datetime.now()
    employee.created_by = "user123"
    employee.updated_by = "user123"
    return employee


@pytest.fixture
def sample_department():
    """Sample department model."""
    department = Department()
    department.id = "dept-123"
    department.tenant_id = "tenant-123"
    department.name = "Engineering"
    department.code = "ENG"
    department.description = "Engineering Department"
    department.is_active = True
    department.created_at = datetime.now()
    department.updated_at = datetime.now()
    department.created_by = "user123"
    department.updated_by = "user123"
    return department


@pytest.fixture
def sample_position():
    """Sample position model."""
    position = Position()
    position.id = "pos-123"
    position.tenant_id = "tenant-123"
    position.title = "Software Engineer"
    position.code = "SE"
    position.description = "Software Engineer Position"
    position.department_id = "dept-123"
    position.level = 2
    position.is_active = True
    position.created_at = datetime.now()
    position.updated_at = datetime.now()
    position.created_by = "user123"
    position.updated_by = "user123"
    return position


class TestEmployeeRepository:
    """Test cases for Employee Repository."""

    @pytest.mark.asyncio
    async def test_create_employee_success(self, employee_repo, mock_db_session, sample_employee):
        """Test successful employee creation."""
        tenant_id = "tenant-123"
        employee_data = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "hire_date": date.today(),
            "department_id": "dept-123",
            "position_id": "pos-123",
        }
        created_by = "user123"

        # Mock session behavior
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # Execute
        result = await employee_repo.create(tenant_id, employee_data, created_by)

        # Verify
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_employee_by_id_success(self, employee_repo, mock_db_session, sample_employee):
        """Test successful employee retrieval by ID."""
        tenant_id = "tenant-123"
        employee_id = "emp-123"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_employee
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.get_by_id(tenant_id, employee_id)

        # Verify
        assert result == sample_employee
        mock_db_session.query.assert_called_once_with(Employee)

    @pytest.mark.asyncio
    async def test_get_employee_by_id_not_found(self, employee_repo, mock_db_session):
        """Test employee retrieval when not found."""
        tenant_id = "tenant-123"
        employee_id = "nonexistent"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = None
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.get_by_id(tenant_id, employee_id)

        # Verify
        assert result is None

    @pytest.mark.asyncio
    async def test_check_employee_id_exists_true(self, employee_repo, mock_db_session, sample_employee):
        """Test checking if employee ID exists (returns True)."""
        tenant_id = "tenant-123"
        employee_id = "EMP001"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_employee
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.check_employee_id_exists(tenant_id, employee_id)

        # Verify
        assert result is True

    @pytest.mark.asyncio
    async def test_check_employee_id_exists_false(self, employee_repo, mock_db_session):
        """Test checking if employee ID exists (returns False)."""
        tenant_id = "tenant-123"
        employee_id = "NONEXISTENT"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = None
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.check_employee_id_exists(tenant_id, employee_id)

        # Verify
        assert result is False

    @pytest.mark.asyncio
    async def test_check_email_exists_true(self, employee_repo, mock_db_session, sample_employee):
        """Test checking if email exists (returns True)."""
        tenant_id = "tenant-123"
        email = "<EMAIL>"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_employee
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.check_email_exists(tenant_id, email)

        # Verify
        assert result is True

    @pytest.mark.asyncio
    async def test_update_employee_success(self, employee_repo, mock_db_session, sample_employee):
        """Test successful employee update."""
        tenant_id = "tenant-123"
        employee_id = "emp-123"
        update_data = {"first_name": "Jane", "phone": "+1234567892"}
        updated_by = "user123"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_employee
        mock_db_session.query.return_value = mock_query
        mock_db_session.commit = AsyncMock()

        # Execute
        result = await employee_repo.update(tenant_id, employee_id, update_data, updated_by)

        # Verify
        mock_db_session.commit.assert_called_once()
        assert result == sample_employee
        assert sample_employee.first_name == "Jane"
        assert sample_employee.phone == "+1234567892"

    @pytest.mark.asyncio
    async def test_delete_employee_success(self, employee_repo, mock_db_session, sample_employee):
        """Test successful employee deletion."""
        tenant_id = "tenant-123"
        employee_id = "emp-123"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_employee
        mock_db_session.query.return_value = mock_query
        mock_db_session.delete = MagicMock()
        mock_db_session.commit = AsyncMock()

        # Execute
        result = await employee_repo.delete(tenant_id, employee_id)

        # Verify
        mock_db_session.delete.assert_called_once_with(sample_employee)
        mock_db_session.commit.assert_called_once()
        assert result is True

    @pytest.mark.asyncio
    async def test_list_employees_with_filters(self, employee_repo, mock_db_session, sample_employee):
        """Test listing employees with filters."""
        tenant_id = "tenant-123"
        skip = 0
        limit = 50
        filters = {"department_id": "dept-123", "status": "active"}

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.filter.return_value.offset.return_value.limit.return_value.all.return_value = [
            sample_employee
        ]
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.list_with_details(tenant_id, skip, limit, filters)

        # Verify
        assert len(result) == 1
        assert result[0] == sample_employee

    @pytest.mark.asyncio
    async def test_get_by_manager(self, employee_repo, mock_db_session, sample_employee):
        """Test getting employees by manager."""
        tenant_id = "tenant-123"
        manager_id = "mgr-123"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.all.return_value = [sample_employee]
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await employee_repo.get_by_manager(tenant_id, manager_id)

        # Verify
        assert len(result) == 1
        assert result[0] == sample_employee

    @pytest.mark.asyncio
    async def test_get_statistics(self, employee_repo, mock_db_session):
        """Test getting employee statistics."""
        tenant_id = "tenant-123"

        # Mock query results for statistics
        mock_db_session.execute = AsyncMock()
        mock_db_session.execute.return_value.scalar.return_value = 100

        # Execute
        result = await employee_repo.get_statistics(tenant_id)

        # Verify
        assert isinstance(result, dict)
        mock_db_session.execute.assert_called()


class TestDepartmentRepository:
    """Test cases for Department Repository."""

    @pytest.mark.asyncio
    async def test_create_department_success(self, department_repo, mock_db_session, sample_department):
        """Test successful department creation."""
        tenant_id = "tenant-123"
        department_data = {"name": "Engineering", "code": "ENG", "description": "Engineering Department"}
        created_by = "user123"

        # Mock session behavior
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # Execute
        result = await department_repo.create(tenant_id, department_data, created_by)

        # Verify
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_department_by_code(self, department_repo, mock_db_session, sample_department):
        """Test getting department by code."""
        tenant_id = "tenant-123"
        code = "ENG"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_department
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await department_repo.get_by_code(tenant_id, code)

        # Verify
        assert result == sample_department


class TestPositionRepository:
    """Test cases for Position Repository."""

    @pytest.mark.asyncio
    async def test_create_position_success(self, position_repo, mock_db_session, sample_position):
        """Test successful position creation."""
        tenant_id = "tenant-123"
        position_data = {"title": "Software Engineer", "code": "SE", "department_id": "dept-123", "level": 2}
        created_by = "user123"

        # Mock session behavior
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # Execute
        result = await position_repo.create(tenant_id, position_data, created_by)

        # Verify
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_position_by_code(self, position_repo, mock_db_session, sample_position):
        """Test getting position by code."""
        tenant_id = "tenant-123"
        code = "SE"

        # Mock query result
        mock_query = MagicMock()
        mock_query.filter.return_value.filter.return_value.first.return_value = sample_position
        mock_db_session.query.return_value = mock_query

        # Execute
        result = await position_repo.get_by_code(tenant_id, code)

        # Verify
        assert result == sample_position
