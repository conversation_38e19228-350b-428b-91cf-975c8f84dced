"""
Unit tests for Employee models and validation.

Tests Pydantic models, validation rules, and data serialization.
"""

from datetime import date, datetime
from typing import Optional

import pytest
from pydantic import ValidationError

from hrms.microservices.employee.models import (
    DepartmentBase,
    DepartmentCreate,
    DepartmentResponse,
    DepartmentUpdate,
    EmployeeBase,
    EmployeeCreate,
    EmployeeDocumentBase,
    EmployeeDocumentCreate,
    EmployeeDocumentResponse,
    EmployeeResponse,
    EmployeeUpdate,
    EmploymentStatus,
    Gender,
    MaritalStatus,
    PositionBase,
    PositionCreate,
    PositionResponse,
    PositionUpdate,
)


class TestEmployeeModels:
    """Test cases for Employee models."""

    def test_employee_create_valid(self):
        """Test valid employee creation model."""
        employee_data = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "<PERSON>e",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "date_of_birth": date(1990, 1, 1),
            "gender": Gender.MALE,
            "marital_status": MaritalStatus.SINGLE,
            "nationality": "US",
            "hire_date": date.today(),
            "status": EmploymentStatus.ACTIVE,
            "department_id": "dept-123",
            "position_id": "pos-123",
            "address_line1": "123 Main St",
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "USA",
            "emergency_contact_name": "Jane Doe",
            "emergency_contact_phone": "+1234567891",
            "emergency_contact_relationship": "Spouse",
            "is_active": True,
        }

        employee = EmployeeCreate(**employee_data)

        assert employee.employee_id == "EMP001"
        assert employee.first_name == "John"
        assert employee.last_name == "Doe"
        assert employee.email == "<EMAIL>"
        assert employee.gender == Gender.MALE
        assert employee.status == EmploymentStatus.ACTIVE

    def test_employee_create_minimal_required(self):
        """Test employee creation with minimal required fields."""
        employee_data = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "hire_date": date.today(),
            "department_id": "dept-123",
            "position_id": "pos-123",
        }

        employee = EmployeeCreate(**employee_data)

        assert employee.employee_id == "EMP001"
        assert employee.status == EmploymentStatus.ACTIVE  # Default value
        assert employee.is_active is True  # Default value

    def test_employee_create_invalid_email(self):
        """Test employee creation with invalid email."""
        employee_data = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "invalid-email",  # Invalid email format
            "hire_date": date.today(),
            "department_id": "dept-123",
            "position_id": "pos-123",
        }

        with pytest.raises(ValidationError) as exc_info:
            EmployeeCreate(**employee_data)

        assert "value is not a valid email address" in str(exc_info.value)

    def test_employee_create_empty_required_fields(self):
        """Test employee creation with empty required fields."""
        with pytest.raises(ValidationError) as exc_info:
            EmployeeCreate(
                employee_id="",  # Empty employee ID
                first_name="",  # Empty first name
                last_name="",  # Empty last name
                email="<EMAIL>",
                hire_date=date.today(),
                department_id="dept-123",
                position_id="pos-123",
            )

        errors = str(exc_info.value)
        assert "String should have at least 1 character" in errors

    def test_employee_create_field_length_validation(self):
        """Test employee creation with field length validation."""
        with pytest.raises(ValidationError) as exc_info:
            EmployeeCreate(
                employee_id="A" * 25,  # Too long (max 20)
                first_name="B" * 55,  # Too long (max 50)
                last_name="C" * 55,  # Too long (max 50)
                email="<EMAIL>",
                hire_date=date.today(),
                department_id="dept-123",
                position_id="pos-123",
            )

        errors = str(exc_info.value)
        assert "String should have at most" in errors

    def test_employee_update_partial(self):
        """Test employee update with partial data."""
        update_data = {"first_name": "Jane", "phone": "+1234567892", "gender": Gender.FEMALE}

        employee_update = EmployeeUpdate(**update_data)

        assert employee_update.first_name == "Jane"
        assert employee_update.phone == "+1234567892"
        assert employee_update.gender == Gender.FEMALE
        assert employee_update.last_name is None  # Not provided

    def test_employee_response_complete(self):
        """Test employee response model with complete data."""
        response_data = {
            "id": "emp-123",
            "tenant_id": "tenant-123",
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "middle_name": None,
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "date_of_birth": date(1990, 1, 1),
            "gender": Gender.MALE,
            "marital_status": MaritalStatus.SINGLE,
            "nationality": "US",
            "hire_date": date.today(),
            "termination_date": None,
            "status": EmploymentStatus.ACTIVE,
            "department_id": "dept-123",
            "position_id": "pos-123",
            "manager_id": None,
            "address_line1": "123 Main St",
            "address_line2": None,
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "USA",
            "emergency_contact_name": "Jane Doe",
            "emergency_contact_phone": "+1234567891",
            "emergency_contact_relationship": "Spouse",
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": "user123",
            "updated_by": "user123",
        }

        employee_response = EmployeeResponse(**response_data)

        assert employee_response.id == "emp-123"
        assert employee_response.tenant_id == "tenant-123"
        assert employee_response.employee_id == "EMP001"


class TestDepartmentModels:
    """Test cases for Department models."""

    def test_department_create_valid(self):
        """Test valid department creation model."""
        department_data = {
            "name": "Engineering",
            "code": "ENG",
            "description": "Engineering Department",
            "parent_department_id": None,
            "manager_id": None,
            "is_active": True,
        }

        department = DepartmentCreate(**department_data)

        assert department.name == "Engineering"
        assert department.code == "ENG"
        assert department.description == "Engineering Department"
        assert department.is_active is True

    def test_department_create_minimal(self):
        """Test department creation with minimal required fields."""
        department_data = {"name": "Engineering", "code": "ENG"}

        department = DepartmentCreate(**department_data)

        assert department.name == "Engineering"
        assert department.code == "ENG"
        assert department.description is None
        assert department.is_active is True  # Default value

    def test_department_create_invalid_length(self):
        """Test department creation with invalid field lengths."""
        with pytest.raises(ValidationError) as exc_info:
            DepartmentCreate(name="A" * 105, code="B" * 25)  # Too long (max 100)  # Too long (max 20)

        errors = str(exc_info.value)
        assert "String should have at most" in errors

    def test_department_response_complete(self):
        """Test department response model."""
        response_data = {
            "id": "dept-123",
            "tenant_id": "tenant-123",
            "name": "Engineering",
            "code": "ENG",
            "description": "Engineering Department",
            "parent_department_id": None,
            "manager_id": None,
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": "user123",
            "updated_by": "user123",
        }

        department_response = DepartmentResponse(**response_data)

        assert department_response.id == "dept-123"
        assert department_response.name == "Engineering"
        assert department_response.code == "ENG"


class TestPositionModels:
    """Test cases for Position models."""

    def test_position_create_valid(self):
        """Test valid position creation model."""
        position_data = {
            "title": "Software Engineer",
            "code": "SE",
            "description": "Software Engineer Position",
            "department_id": "dept-123",
            "level": 2,
            "is_active": True,
        }

        position = PositionCreate(**position_data)

        assert position.title == "Software Engineer"
        assert position.code == "SE"
        assert position.department_id == "dept-123"
        assert position.level == 2

    def test_position_create_level_validation(self):
        """Test position creation with level validation."""
        # Test valid level range
        position_data = {"title": "Software Engineer", "code": "SE", "department_id": "dept-123", "level": 5}

        position = PositionCreate(**position_data)
        assert position.level == 5

        # Test invalid level (too high)
        with pytest.raises(ValidationError) as exc_info:
            PositionCreate(
                title="Software Engineer", code="SE", department_id="dept-123", level=15  # Too high (max 10)
            )

        assert "Input should be less than or equal to 10" in str(exc_info.value)

        # Test invalid level (too low)
        with pytest.raises(ValidationError) as exc_info:
            PositionCreate(
                title="Software Engineer", code="SE", department_id="dept-123", level=0  # Too low (min 1)
            )

        assert "Input should be greater than or equal to 1" in str(exc_info.value)

    def test_position_response_complete(self):
        """Test position response model."""
        response_data = {
            "id": "pos-123",
            "tenant_id": "tenant-123",
            "title": "Software Engineer",
            "code": "SE",
            "description": "Software Engineer Position",
            "department_id": "dept-123",
            "level": 2,
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": "user123",
            "updated_by": "user123",
        }

        position_response = PositionResponse(**response_data)

        assert position_response.id == "pos-123"
        assert position_response.title == "Software Engineer"
        assert position_response.level == 2


class TestEmployeeDocumentModels:
    """Test cases for Employee Document models."""

    def test_employee_document_create_valid(self):
        """Test valid employee document creation model."""
        document_data = {
            "document_type": "ID_CARD",
            "document_name": "National ID Card",
            "file_content": b"fake_file_content",
            "mime_type": "image/jpeg",
        }

        document = EmployeeDocumentCreate(**document_data)

        assert document.document_type == "ID_CARD"
        assert document.document_name == "National ID Card"
        assert document.file_content == b"fake_file_content"
        assert document.mime_type == "image/jpeg"

    def test_employee_document_create_invalid_length(self):
        """Test employee document creation with invalid field lengths."""
        with pytest.raises(ValidationError) as exc_info:
            EmployeeDocumentCreate(
                document_type="A" * 55,  # Too long (max 50)
                document_name="B" * 205,  # Too long (max 200)
                file_content=b"content",
                mime_type="image/jpeg",
            )

        errors = str(exc_info.value)
        assert "String should have at most" in errors

    def test_employee_document_response_complete(self):
        """Test employee document response model."""
        response_data = {
            "id": "doc-123",
            "employee_id": "emp-123",
            "tenant_id": "tenant-123",
            "document_type": "ID_CARD",
            "document_name": "National ID Card",
            "file_path": "/path/to/document.pdf",
            "file_size": 1024,
            "mime_type": "application/pdf",
            "uploaded_by": "user123",
            "created_at": datetime.now(),
        }

        document_response = EmployeeDocumentResponse(**response_data)

        assert document_response.id == "doc-123"
        assert document_response.employee_id == "emp-123"
        assert document_response.document_type == "ID_CARD"


class TestEnumModels:
    """Test cases for Enum models."""

    def test_employment_status_enum(self):
        """Test EmploymentStatus enum values."""
        assert EmploymentStatus.ACTIVE == "active"
        assert EmploymentStatus.INACTIVE == "inactive"
        assert EmploymentStatus.TERMINATED == "terminated"
        assert EmploymentStatus.ON_LEAVE == "on_leave"
        assert EmploymentStatus.PROBATION == "probation"

    def test_gender_enum(self):
        """Test Gender enum values."""
        assert Gender.MALE == "male"
        assert Gender.FEMALE == "female"
        assert Gender.OTHER == "other"
        assert Gender.PREFER_NOT_TO_SAY == "prefer_not_to_say"

    def test_marital_status_enum(self):
        """Test MaritalStatus enum values."""
        assert MaritalStatus.SINGLE == "single"
        assert MaritalStatus.MARRIED == "married"
        assert MaritalStatus.DIVORCED == "divorced"
        assert MaritalStatus.WIDOWED == "widowed"
        assert MaritalStatus.SEPARATED == "separated"
