"""
Integration tests for Employee Management Service API.

Tests API endpoints, authentication, authorization, and data flow.
"""

from datetime import date
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from hrms.microservices.employee.api import app
from hrms.microservices.employee.models import EmploymentStatus, Gender, MaritalStatus


@pytest.fixture
def client():
    """Test client for Employee API."""
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Mock authentication headers."""
    return {"Authorization": "Bearer mock-jwt-token"}


@pytest.fixture
def sample_employee_payload():
    """Sample employee creation payload."""
    return {
        "employee_id": "EMP001",
        "first_name": "<PERSON>",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "date_of_birth": "1990-01-01",
        "gender": "male",
        "marital_status": "single",
        "nationality": "US",
        "hire_date": str(date.today()),
        "status": "active",
        "department_id": "dept-123",
        "position_id": "pos-123",
        "address_line1": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postal_code": "10001",
        "country": "USA",
        "emergency_contact_name": "Jane Doe",
        "emergency_contact_phone": "+1234567891",
        "emergency_contact_relationship": "Spouse",
        "is_active": True,
    }


@pytest.fixture
def sample_department_payload():
    """Sample department creation payload."""
    return {
        "name": "Engineering",
        "code": "ENG",
        "description": "Software Engineering Department",
        "is_active": True,
    }


@pytest.fixture
def sample_position_payload():
    """Sample position creation payload."""
    return {
        "title": "Software Engineer",
        "code": "SE",
        "description": "Software Engineer Position",
        "department_id": "dept-123",
        "level": 2,
        "is_active": True,
    }


class TestEmployeeAPI:
    """Test cases for Employee API endpoints."""

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_create_employee_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_employee_payload
    ):
        """Test successful employee creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_employee_response = MagicMock()
        mock_employee_response.dict.return_value = {
            "id": "emp-123",
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "tenant_id": "test_tenant",
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00",
        }
        mock_service.create_employee = AsyncMock(return_value=mock_employee_response)

        # Execute
        response = client.post("/api/v1/employees", json=sample_employee_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["employee_id"] == "EMP001"
        assert data["first_name"] == "John"
        assert data["last_name"] == "Doe"
        assert data["tenant_id"] == "test_tenant"

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_create_employee_validation_error(
        self, mock_tenant, mock_user, mock_service, client, auth_headers
    ):
        """Test employee creation with validation error."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Invalid payload (missing required fields)
        invalid_payload = {
            "first_name": "John",
            "last_name": "Doe",
            # Missing required fields
        }

        # Execute
        response = client.post("/api/v1/employees", json=invalid_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 422  # Validation error

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_get_employee_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful employee retrieval via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_employee_response = MagicMock()
        mock_employee_response.dict.return_value = {
            "id": "emp-123",
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "tenant_id": "test_tenant",
        }
        mock_service.get_employee = AsyncMock(return_value=mock_employee_response)

        # Execute
        response = client.get("/api/v1/employees/emp-123", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "emp-123"
        assert data["employee_id"] == "EMP001"

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_get_employee_not_found(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test employee retrieval when not found."""
        from hrms.microservices.shared.exceptions import NotFoundError

        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service to raise NotFoundError
        mock_service.get_employee = AsyncMock(side_effect=NotFoundError("Employee not found"))

        # Execute
        response = client.get("/api/v1/employees/nonexistent", headers=auth_headers)

        # Verify
        assert response.status_code == 404

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_list_employees_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful employee listing via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_employees = [
            MagicMock(dict=lambda: {"id": "emp-1", "first_name": "John", "last_name": "Doe"}),
            MagicMock(dict=lambda: {"id": "emp-2", "first_name": "Jane", "last_name": "Smith"}),
        ]
        mock_service.list_employees = AsyncMock(return_value=mock_employees)

        # Execute
        response = client.get("/api/v1/employees?page=1&size=10", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_update_employee_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful employee update via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_employee_response = MagicMock()
        mock_employee_response.dict.return_value = {
            "id": "emp-123",
            "first_name": "Jane",  # Updated name
            "last_name": "Doe",
            "email": "<EMAIL>",
        }
        mock_service.update_employee = AsyncMock(return_value=mock_employee_response)

        update_payload = {"first_name": "Jane", "email": "<EMAIL>"}

        # Execute
        response = client.put("/api/v1/employees/emp-123", json=update_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["first_name"] == "Jane"

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_delete_employee_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful employee deletion via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_service.delete_employee = AsyncMock(return_value=True)

        # Execute
        response = client.delete("/api/v1/employees/emp-123", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "deleted successfully" in data["message"]

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_get_employee_statistics(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test employee statistics endpoint."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_stats = {
            "total_employees": 100,
            "active_employees": 95,
            "inactive_employees": 5,
            "by_department": {"Engineering": 50, "Sales": 30, "HR": 20},
        }
        mock_service.get_employee_statistics = AsyncMock(return_value=mock_stats)

        # Execute
        response = client.get("/api/v1/employees/statistics", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["total_employees"] == 100
        assert data["active_employees"] == 95
        assert "by_department" in data


class TestDepartmentAPI:
    """Test cases for Department API endpoints."""

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_create_department_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_department_payload
    ):
        """Test successful department creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_department_response = MagicMock()
        mock_department_response.dict.return_value = {
            "id": "dept-123",
            "name": "Engineering",
            "code": "ENG",
            "description": "Software Engineering Department",
            "tenant_id": "test_tenant",
        }
        mock_service.create_department = AsyncMock(return_value=mock_department_response)

        # Execute
        response = client.post("/api/v1/departments", json=sample_department_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Engineering"
        assert data["code"] == "ENG"

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_list_departments_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful department listing via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_departments = [
            MagicMock(dict=lambda: {"id": "dept-1", "name": "Engineering", "code": "ENG"}),
            MagicMock(dict=lambda: {"id": "dept-2", "name": "Sales", "code": "SALES"}),
        ]
        mock_service.list_departments = AsyncMock(return_value=mock_departments)

        # Execute
        response = client.get("/api/v1/departments", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["name"] == "Engineering"


class TestPositionAPI:
    """Test cases for Position API endpoints."""

    @patch("hrms.microservices.employee.api.employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_current_tenant")
    def test_create_position_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_position_payload
    ):
        """Test successful position creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_position_response = MagicMock()
        mock_position_response.dict.return_value = {
            "id": "pos-123",
            "title": "Software Engineer",
            "code": "SE",
            "department_id": "dept-123",
            "level": 2,
            "tenant_id": "test_tenant",
        }
        mock_service.create_position = AsyncMock(return_value=mock_position_response)

        # Execute
        response = client.post("/api/v1/positions", json=sample_position_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Software Engineer"
        assert data["code"] == "SE"
        assert data["level"] == 2


class TestHealthCheck:
    """Test cases for health check endpoint."""

    def test_health_check_success(self, client):
        """Test health check endpoint."""
        with patch("hrms.microservices.employee.api.db_manager") as mock_db:
            mock_db.health_check = AsyncMock(return_value={"connected": True, "response_time_ms": 5.2})

            response = client.get("/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "employee-service"
            assert "dependencies" in data


class TestAuthentication:
    """Test cases for authentication and authorization."""

    def test_unauthorized_access(self, client):
        """Test API access without authentication."""
        response = client.get("/api/v1/employees")

        # Should return 401 or 403 depending on implementation
        assert response.status_code in [401, 403]

    def test_invalid_token(self, client):
        """Test API access with invalid token."""
        headers = {"Authorization": "Bearer invalid-token"}

        response = client.get("/api/v1/employees", headers=headers)

        # Should return 401 or 403 depending on implementation
        assert response.status_code in [401, 403]


class TestEmployeeAPIEnhanced:
    """Enhanced API tests with comprehensive coverage."""

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_create_employee_validation_errors(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test employee creation with validation errors."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        # Invalid payload - missing required fields
        invalid_payload = {
            "employee_id": "",  # Empty employee ID
            "first_name": "",  # Empty first name
            "email": "invalid-email",  # Invalid email format
        }

        response = client.post("/employees/", json=invalid_payload, headers=auth_headers)

        assert response.status_code == 422  # Validation error
        error_detail = response.json()["detail"]
        assert isinstance(error_detail, list)
        assert len(error_detail) > 0

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_create_employee_conflict_error(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers, sample_employee_payload
    ):
        """Test employee creation with conflict error (duplicate employee ID)."""
        from hrms.microservices.shared.exceptions import ConflictError

        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_service = AsyncMock()
        mock_service.create_employee.side_effect = ConflictError("Employee ID already exists")
        mock_get_service.return_value = mock_service

        response = client.post("/employees/", json=sample_employee_payload, headers=auth_headers)

        assert response.status_code == 409  # Conflict error

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_update_employee_partial(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test partial employee update."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_employee = MagicMock()
        mock_employee.id = "emp-123"
        mock_employee.first_name = "Jane"  # Updated
        mock_employee.phone = "+1234567892"  # Updated
        mock_employee.dict.return_value = {"id": "emp-123", "first_name": "Jane", "phone": "+1234567892"}

        mock_service = AsyncMock()
        mock_service.update_employee.return_value = mock_employee
        mock_get_service.return_value = mock_service

        update_payload = {"first_name": "Jane", "phone": "+1234567892"}

        response = client.put("/employees/emp-123", json=update_payload, headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["first_name"] == "Jane"
        assert data["phone"] == "+1234567892"

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_list_employees_with_filters(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test employee listing with filters."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_employees = [
            MagicMock(dict=lambda: {"id": "emp-1", "first_name": "John", "department_id": "dept-123"}),
            MagicMock(dict=lambda: {"id": "emp-2", "first_name": "Jane", "department_id": "dept-123"}),
        ]

        mock_service = AsyncMock()
        mock_service.list_employees.return_value = mock_employees
        mock_get_service.return_value = mock_service

        response = client.get(
            "/employees/?department_id=dept-123&status=active&skip=0&limit=10", headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        mock_service.list_employees.assert_called_once()

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_get_employee_statistics(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test employee statistics endpoint."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_stats = {
            "total_employees": 100,
            "active_employees": 95,
            "inactive_employees": 5,
            "by_department": {"Engineering": 50, "Sales": 30, "HR": 20},
            "by_status": {"active": 95, "inactive": 5},
        }

        mock_service = AsyncMock()
        mock_service.get_employee_statistics.return_value = mock_stats
        mock_get_service.return_value = mock_service

        response = client.get("/employees/statistics", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert data["total_employees"] == 100
        assert data["active_employees"] == 95
        assert "by_department" in data
        assert "by_status" in data


class TestDepartmentAPI:
    """Test cases for Department API endpoints."""

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_create_department_success(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test successful department creation."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_department = MagicMock()
        mock_department.dict.return_value = {
            "id": "dept-123",
            "name": "Engineering",
            "code": "ENG",
            "description": "Engineering Department",
            "is_active": True,
        }

        mock_service = AsyncMock()
        mock_service.create_department.return_value = mock_department
        mock_get_service.return_value = mock_service

        department_payload = {
            "name": "Engineering",
            "code": "ENG",
            "description": "Engineering Department",
            "is_active": True,
        }

        response = client.post("/departments/", json=department_payload, headers=auth_headers)

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Engineering"
        assert data["code"] == "ENG"

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_list_departments(self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers):
        """Test department listing."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_departments = [
            MagicMock(dict=lambda: {"id": "dept-1", "name": "Engineering", "code": "ENG"}),
            MagicMock(dict=lambda: {"id": "dept-2", "name": "Sales", "code": "SALES"}),
        ]

        mock_service = AsyncMock()
        mock_service.list_departments.return_value = mock_departments
        mock_get_service.return_value = mock_service

        response = client.get("/departments/", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["name"] == "Engineering"


class TestPositionAPI:
    """Test cases for Position API endpoints."""

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_create_position_success(
        self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers
    ):
        """Test successful position creation."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_position = MagicMock()
        mock_position.dict.return_value = {
            "id": "pos-123",
            "title": "Software Engineer",
            "code": "SE",
            "department_id": "dept-123",
            "level": 2,
            "is_active": True,
        }

        mock_service = AsyncMock()
        mock_service.create_position.return_value = mock_position
        mock_get_service.return_value = mock_service

        position_payload = {
            "title": "Software Engineer",
            "code": "SE",
            "department_id": "dept-123",
            "level": 2,
            "is_active": True,
        }

        response = client.post("/positions/", json=position_payload, headers=auth_headers)

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Software Engineer"
        assert data["code"] == "SE"
        assert data["level"] == 2

    @patch("hrms.microservices.employee.api.get_employee_service")
    @patch("hrms.microservices.employee.api.get_current_user")
    @patch("hrms.microservices.employee.api.get_tenant_id")
    def test_list_positions(self, mock_get_tenant, mock_get_user, mock_get_service, client, auth_headers):
        """Test position listing."""
        mock_get_tenant.return_value = "test_tenant"
        mock_get_user.return_value = {"id": "user123", "username": "testuser"}

        mock_positions = [
            MagicMock(dict=lambda: {"id": "pos-1", "title": "Software Engineer", "level": 2}),
            MagicMock(dict=lambda: {"id": "pos-2", "title": "Senior Engineer", "level": 3}),
        ]

        mock_service = AsyncMock()
        mock_service.list_positions.return_value = mock_positions
        mock_get_service.return_value = mock_service

        response = client.get("/positions/", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["title"] == "Software Engineer"
