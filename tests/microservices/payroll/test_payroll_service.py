"""
Unit tests for Payroll Management Service.

Tests business logic, validation, and service operations with comprehensive coverage.
"""

from datetime import date, datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock

import pytest

from hrms.microservices.payroll.models import (
    ComponentType,
    PayrollEntryCreate,
    PayrollFrequency,
    SalaryComponentCreate,
    SalaryComponentUpdate,
    SalarySlipCreate,
    SalarySlipStatus,
    SalaryStructureCreate,
    SalaryStructureDetailBase,
)
from hrms.microservices.payroll.service import PayrollService
from hrms.microservices.shared.exceptions import (
    BusinessLogicError,
    ConflictError,
    NotFoundError,
    ValidationError,
)


@pytest.fixture
def mock_db_manager():
    """Mock database manager."""
    return MagicMock()


@pytest.fixture
def payroll_service(mock_db_manager):
    """Payroll service with mocked dependencies."""
    service = PayrollService(mock_db_manager)
    service.component_repo = AsyncMock()
    service.structure_repo = AsyncMock()
    service.slip_repo = AsyncMock()
    service.entry_repo = AsyncMock()
    service.detail_repo = AsyncMock()
    return service


@pytest.fixture
def sample_component_data():
    """Sample salary component data."""
    return SalaryComponentCreate(
        name="Basic Salary",
        abbr="BS",
        type=ComponentType.EARNING,
        description="Basic salary component",
        depends_on_payment_days=True,
        is_tax_applicable=True,
        amount=Decimal("5000.00"),
        is_active=True,
    )


@pytest.fixture
def sample_structure_data():
    """Sample salary structure data."""
    return SalaryStructureCreate(
        name="Software Engineer Structure",
        company="Tech Corp",
        currency="USD",
        payroll_frequency=PayrollFrequency.MONTHLY,
        is_active=True,
        earnings=[
            SalaryStructureDetailBase(
                salary_component_id="comp-1", component_type=ComponentType.EARNING, amount=Decimal("5000.00")
            )
        ],
        deductions=[
            SalaryStructureDetailBase(
                salary_component_id="comp-2", component_type=ComponentType.DEDUCTION, amount=Decimal("500.00")
            )
        ],
    )


@pytest.fixture
def sample_slip_data():
    """Sample salary slip data."""
    return SalarySlipCreate(
        employee_id="emp-123",
        employee_name="John Doe",
        company="Tech Corp",
        department="Engineering",
        designation="Software Engineer",
        payroll_frequency=PayrollFrequency.MONTHLY,
        start_date=date(2024, 1, 1),
        end_date=date(2024, 1, 31),
        salary_structure_id="struct-123",
        total_working_days=22,
        payment_days=Decimal("22.00"),
        currency="USD",
    )


@pytest.fixture
def sample_entry_data():
    """Sample payroll entry data."""
    return PayrollEntryCreate(
        name="January 2024 Payroll",
        company="Tech Corp",
        payroll_frequency=PayrollFrequency.MONTHLY,
        start_date=date(2024, 1, 1),
        end_date=date(2024, 1, 31),
        currency="USD",
    )


class TestSalaryComponentService:
    """Test cases for Salary Component Service."""

    @pytest.mark.asyncio
    async def test_create_salary_component_success(self, payroll_service, sample_component_data):
        """Test successful salary component creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock repository responses
        payroll_service.component_repo.check_name_exists.return_value = False
        payroll_service.component_repo.check_abbr_exists.return_value = False

        mock_component = MagicMock()
        mock_component.id = "comp-123"
        mock_component.name = "Basic Salary"
        mock_component.abbr = "BS"
        mock_component.type = ComponentType.EARNING

        payroll_service.component_repo.create.return_value = mock_component

        # Execute
        result = await payroll_service.create_salary_component(tenant_id, sample_component_data, created_by)

        # Verify
        payroll_service.component_repo.check_name_exists.assert_called_once_with(tenant_id, "Basic Salary")
        payroll_service.component_repo.check_abbr_exists.assert_called_once_with(tenant_id, "BS")
        payroll_service.component_repo.create.assert_called_once()

        assert result is not None

    @pytest.mark.asyncio
    async def test_create_salary_component_duplicate_name(self, payroll_service, sample_component_data):
        """Test salary component creation with duplicate name."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate name
        payroll_service.component_repo.check_name_exists.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await payroll_service.create_salary_component(tenant_id, sample_component_data, created_by)

        assert "Basic Salary" in str(exc_info.value)
        assert "already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_salary_component_duplicate_abbr(self, payroll_service, sample_component_data):
        """Test salary component creation with duplicate abbreviation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate abbreviation
        payroll_service.component_repo.check_name_exists.return_value = False
        payroll_service.component_repo.check_abbr_exists.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await payroll_service.create_salary_component(tenant_id, sample_component_data, created_by)

        assert "BS" in str(exc_info.value)
        assert "already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_salary_component_success(self, payroll_service):
        """Test successful salary component retrieval."""
        tenant_id = "test_tenant"
        component_id = "comp-123"

        mock_component = MagicMock()
        mock_component.id = component_id
        mock_component.name = "Basic Salary"

        payroll_service.component_repo.get_by_id.return_value = mock_component

        # Execute
        result = await payroll_service.get_salary_component(tenant_id, component_id)

        # Verify
        payroll_service.component_repo.get_by_id.assert_called_once_with(tenant_id, component_id)
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_salary_component_not_found(self, payroll_service):
        """Test salary component retrieval when not found."""
        tenant_id = "test_tenant"
        component_id = "comp-123"

        payroll_service.component_repo.get_by_id.return_value = None

        # Execute and verify exception
        with pytest.raises(NotFoundError) as exc_info:
            await payroll_service.get_salary_component(tenant_id, component_id)

        assert f"Salary component not found: {component_id}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_salary_component_success(self, payroll_service):
        """Test successful salary component update."""
        tenant_id = "test_tenant"
        component_id = "comp-123"
        updated_by = "user123"

        update_data = SalaryComponentUpdate(name="Updated Basic Salary", amount=Decimal("6000.00"))

        mock_component = MagicMock()
        mock_component.id = component_id

        payroll_service.component_repo.get_by_id.return_value = mock_component
        payroll_service.component_repo.check_name_exists.return_value = False
        payroll_service.component_repo.update.return_value = mock_component

        # Execute
        result = await payroll_service.update_salary_component(
            tenant_id, component_id, update_data, updated_by
        )

        # Verify
        payroll_service.component_repo.get_by_id.assert_called_once_with(tenant_id, component_id)
        payroll_service.component_repo.update.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_list_salary_components_by_type(self, payroll_service):
        """Test listing salary components by type."""
        tenant_id = "test_tenant"
        component_type = ComponentType.EARNING

        mock_components = [
            MagicMock(id="comp-1", name="Basic Salary", type=ComponentType.EARNING),
            MagicMock(id="comp-2", name="HRA", type=ComponentType.EARNING),
        ]

        payroll_service.component_repo.get_by_type.return_value = mock_components

        # Execute
        result = await payroll_service.list_salary_components(tenant_id, component_type)

        # Verify
        payroll_service.component_repo.get_by_type.assert_called_once_with(tenant_id, component_type)
        assert len(result) == 2


class TestSalaryStructureService:
    """Test cases for Salary Structure Service."""

    @pytest.mark.asyncio
    async def test_create_salary_structure_success(self, payroll_service, sample_structure_data):
        """Test successful salary structure creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock repository responses
        payroll_service.structure_repo.check_name_exists.return_value = False

        # Mock component validation
        mock_earning_component = MagicMock()
        mock_earning_component.type = ComponentType.EARNING
        mock_deduction_component = MagicMock()
        mock_deduction_component.type = ComponentType.DEDUCTION

        payroll_service.component_repo.get_by_id.side_effect = [
            mock_earning_component,
            mock_deduction_component,
        ]

        mock_structure = MagicMock()
        mock_structure.id = "struct-123"
        mock_structure.name = "Software Engineer Structure"

        payroll_service.structure_repo.create.return_value = mock_structure

        # Execute
        result = await payroll_service.create_salary_structure(tenant_id, sample_structure_data, created_by)

        # Verify
        payroll_service.structure_repo.check_name_exists.assert_called_once()
        payroll_service.structure_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_create_salary_structure_duplicate_name(self, payroll_service, sample_structure_data):
        """Test salary structure creation with duplicate name."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate name
        payroll_service.structure_repo.check_name_exists.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await payroll_service.create_salary_structure(tenant_id, sample_structure_data, created_by)

        assert "Software Engineer Structure" in str(exc_info.value)
        assert "already exists" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_salary_structure_invalid_component(self, payroll_service, sample_structure_data):
        """Test salary structure creation with invalid component."""
        tenant_id = "test_tenant"
        created_by = "user123"

        payroll_service.structure_repo.check_name_exists.return_value = False

        # Mock invalid component (earning component in deduction)
        mock_earning_component = MagicMock()
        mock_earning_component.type = ComponentType.EARNING
        mock_earning_component.name = "Basic Salary"

        payroll_service.component_repo.get_by_id.side_effect = [
            mock_earning_component,
            mock_earning_component,  # Both earnings
        ]

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await payroll_service.create_salary_structure(tenant_id, sample_structure_data, created_by)

        assert "not a deduction component" in str(exc_info.value)


class TestSalarySlipService:
    """Test cases for Salary Slip Service."""

    @pytest.mark.asyncio
    async def test_generate_salary_slip_success(self, payroll_service, sample_slip_data):
        """Test successful salary slip generation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock repository responses
        payroll_service.slip_repo.get_by_employee_and_period.return_value = None

        mock_structure = MagicMock()
        mock_structure.id = "struct-123"
        mock_structure.earnings = []
        mock_structure.deductions = []

        payroll_service.structure_repo.get_with_details.return_value = mock_structure

        mock_slip = MagicMock()
        mock_slip.id = "slip-123"
        mock_slip.employee_id = "emp-123"

        payroll_service.slip_repo.create.return_value = mock_slip
        payroll_service.slip_repo.get_with_details.return_value = mock_slip

        # Execute
        result = await payroll_service.generate_salary_slip(tenant_id, sample_slip_data, created_by)

        # Verify
        payroll_service.slip_repo.get_by_employee_and_period.assert_called_once()
        payroll_service.structure_repo.get_with_details.assert_called_once()
        payroll_service.slip_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_generate_salary_slip_duplicate_period(self, payroll_service, sample_slip_data):
        """Test salary slip generation with duplicate period."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock existing slip
        mock_existing_slip = MagicMock()
        payroll_service.slip_repo.get_by_employee_and_period.return_value = mock_existing_slip

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await payroll_service.generate_salary_slip(tenant_id, sample_slip_data, created_by)

        assert "already exists" in str(exc_info.value)
        assert "emp-123" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_submit_salary_slip_success(self, payroll_service):
        """Test successful salary slip submission."""
        tenant_id = "test_tenant"
        slip_id = "slip-123"
        submitted_by = "user123"

        mock_slip = MagicMock()
        mock_slip.status = SalarySlipStatus.DRAFT

        payroll_service.slip_repo.get_by_id.return_value = mock_slip
        payroll_service.slip_repo.update.return_value = mock_slip

        # Execute
        result = await payroll_service.submit_salary_slip(tenant_id, slip_id, submitted_by)

        # Verify
        payroll_service.slip_repo.get_by_id.assert_called_once_with(tenant_id, slip_id)
        payroll_service.slip_repo.update.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_submit_salary_slip_invalid_status(self, payroll_service):
        """Test salary slip submission with invalid status."""
        tenant_id = "test_tenant"
        slip_id = "slip-123"
        submitted_by = "user123"

        mock_slip = MagicMock()
        mock_slip.status = SalarySlipStatus.SUBMITTED  # Already submitted

        payroll_service.slip_repo.get_by_id.return_value = mock_slip

        # Execute and verify exception
        with pytest.raises(BusinessLogicError) as exc_info:
            await payroll_service.submit_salary_slip(tenant_id, slip_id, submitted_by)

        assert "Cannot submit salary slip" in str(exc_info.value)


class TestPayrollEntryService:
    """Test cases for Payroll Entry Service."""

    @pytest.mark.asyncio
    async def test_create_payroll_entry_success(self, payroll_service, sample_entry_data):
        """Test successful payroll entry creation."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock repository responses
        payroll_service.entry_repo.check_name_exists.return_value = False

        mock_entry = MagicMock()
        mock_entry.id = "entry-123"
        mock_entry.name = "January 2024 Payroll"

        payroll_service.entry_repo.create.return_value = mock_entry

        # Execute
        result = await payroll_service.create_payroll_entry(tenant_id, sample_entry_data, created_by)

        # Verify
        payroll_service.entry_repo.check_name_exists.assert_called_once()
        payroll_service.entry_repo.create.assert_called_once()
        assert result is not None

    @pytest.mark.asyncio
    async def test_create_payroll_entry_duplicate_name(self, payroll_service, sample_entry_data):
        """Test payroll entry creation with duplicate name."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Mock duplicate name
        payroll_service.entry_repo.check_name_exists.return_value = True

        # Execute and verify exception
        with pytest.raises(ConflictError) as exc_info:
            await payroll_service.create_payroll_entry(tenant_id, sample_entry_data, created_by)

        assert "January 2024 Payroll" in str(exc_info.value)
        assert "already exists" in str(exc_info.value)


class TestPayrollValidation:
    """Test cases for payroll validation logic."""

    @pytest.mark.asyncio
    async def test_validate_invalid_date_range(self, payroll_service):
        """Test validation with invalid date range."""
        tenant_id = "test_tenant"
        created_by = "user123"

        # Invalid slip data (start date after end date)
        invalid_slip_data = SalarySlipCreate(
            employee_id="emp-123",
            employee_name="John Doe",
            company="Tech Corp",
            payroll_frequency=PayrollFrequency.MONTHLY,
            start_date=date(2024, 1, 31),  # After end date
            end_date=date(2024, 1, 1),
            salary_structure_id="struct-123",
        )

        payroll_service.slip_repo.get_by_employee_and_period.return_value = None

        mock_structure = MagicMock()
        payroll_service.structure_repo.get_with_details.return_value = mock_structure

        # Execute and verify exception
        with pytest.raises(ValidationError) as exc_info:
            await payroll_service.generate_salary_slip(tenant_id, invalid_slip_data, created_by)

        assert "Start date must be before end date" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_dangerous_formula(self, payroll_service):
        """Test validation with dangerous formula."""
        # Test formula validation
        dangerous_formula = "import os; os.system('rm -rf /')"

        with pytest.raises(ValidationError) as exc_info:
            payroll_service._validate_formula(dangerous_formula)

        assert "dangerous keyword" in str(exc_info.value)

    def test_calculate_working_days(self, payroll_service):
        """Test working days calculation."""
        start_date = date(2024, 1, 1)  # Monday
        end_date = date(2024, 1, 7)  # Sunday

        working_days = payroll_service._calculate_working_days(start_date, end_date)

        # Should be 5 working days (Monday to Friday)
        assert working_days == 5
