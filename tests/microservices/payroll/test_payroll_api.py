"""
Integration tests for Payroll Management Service API.

Tests API endpoints, authentication, authorization, and data flow.
"""

from datetime import date
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from hrms.microservices.payroll.api import app
from hrms.microservices.payroll.models import ComponentType, PayrollFrequency


@pytest.fixture
def client():
    """Test client for Payroll API."""
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Mock authentication headers."""
    return {"Authorization": "Bearer mock-jwt-token"}


@pytest.fixture
def sample_component_payload():
    """Sample salary component creation payload."""
    return {
        "name": "Basic Salary",
        "abbr": "BS",
        "type": "earning",
        "description": "Basic salary component",
        "depends_on_payment_days": True,
        "is_tax_applicable": True,
        "amount": "5000.00",
        "is_active": True,
    }


@pytest.fixture
def sample_structure_payload():
    """Sample salary structure creation payload."""
    return {
        "name": "Software Engineer Structure",
        "company": "Tech Corp",
        "currency": "USD",
        "payroll_frequency": "monthly",
        "is_active": True,
        "earnings": [{"salary_component_id": "comp-1", "component_type": "earning", "amount": "5000.00"}],
        "deductions": [{"salary_component_id": "comp-2", "component_type": "deduction", "amount": "500.00"}],
    }


@pytest.fixture
def sample_slip_payload():
    """Sample salary slip creation payload."""
    return {
        "employee_id": "emp-123",
        "employee_name": "John Doe",
        "company": "Tech Corp",
        "department": "Engineering",
        "designation": "Software Engineer",
        "payroll_frequency": "monthly",
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "salary_structure_id": "struct-123",
        "total_working_days": 22,
        "payment_days": "22.00",
        "currency": "USD",
    }


@pytest.fixture
def sample_entry_payload():
    """Sample payroll entry creation payload."""
    return {
        "name": "January 2024 Payroll",
        "company": "Tech Corp",
        "payroll_frequency": "monthly",
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "currency": "USD",
    }


class TestSalaryComponentAPI:
    """Test cases for Salary Component API endpoints."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_create_salary_component_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_component_payload
    ):
        """Test successful salary component creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_component_response = MagicMock()
        mock_component_response.dict.return_value = {
            "id": "comp-123",
            "name": "Basic Salary",
            "abbr": "BS",
            "type": "earning",
            "amount": "5000.00",
            "tenant_id": "test_tenant",
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00",
        }
        mock_service.create_salary_component = AsyncMock(return_value=mock_component_response)

        # Execute
        response = client.post(
            "/api/v1/payroll/components", json=sample_component_payload, headers=auth_headers
        )

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Basic Salary"
        assert data["abbr"] == "BS"
        assert data["type"] == "earning"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_list_salary_components_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful salary component listing via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_components = [
            MagicMock(dict=lambda: {"id": "comp-1", "name": "Basic Salary", "type": "earning"}),
            MagicMock(dict=lambda: {"id": "comp-2", "name": "HRA", "type": "earning"}),
        ]
        mock_service.list_salary_components = AsyncMock(return_value=mock_components)

        # Execute
        response = client.get("/api/v1/payroll/components?component_type=earning", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["name"] == "Basic Salary"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_get_salary_component_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful salary component retrieval via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_component_response = MagicMock()
        mock_component_response.dict.return_value = {
            "id": "comp-123",
            "name": "Basic Salary",
            "abbr": "BS",
            "type": "earning",
            "tenant_id": "test_tenant",
        }
        mock_service.get_salary_component = AsyncMock(return_value=mock_component_response)

        # Execute
        response = client.get("/api/v1/payroll/components/comp-123", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "comp-123"
        assert data["name"] == "Basic Salary"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_update_salary_component_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers
    ):
        """Test successful salary component update via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_component_response = MagicMock()
        mock_component_response.dict.return_value = {
            "id": "comp-123",
            "name": "Updated Basic Salary",
            "amount": "6000.00",
        }
        mock_service.update_salary_component = AsyncMock(return_value=mock_component_response)

        update_payload = {"name": "Updated Basic Salary", "amount": "6000.00"}

        # Execute
        response = client.put(
            "/api/v1/payroll/components/comp-123", json=update_payload, headers=auth_headers
        )

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Basic Salary"


class TestSalaryStructureAPI:
    """Test cases for Salary Structure API endpoints."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_create_salary_structure_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_structure_payload
    ):
        """Test successful salary structure creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_structure_response = MagicMock()
        mock_structure_response.dict.return_value = {
            "id": "struct-123",
            "name": "Software Engineer Structure",
            "company": "Tech Corp",
            "currency": "USD",
            "tenant_id": "test_tenant",
        }
        mock_service.create_salary_structure = AsyncMock(return_value=mock_structure_response)

        # Execute
        response = client.post(
            "/api/v1/payroll/salary-structures", json=sample_structure_payload, headers=auth_headers
        )

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Software Engineer Structure"
        assert data["company"] == "Tech Corp"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_list_salary_structures_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful salary structure listing via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_structures = [
            MagicMock(dict=lambda: {"id": "struct-1", "name": "Engineer Structure", "company": "Tech Corp"}),
            MagicMock(dict=lambda: {"id": "struct-2", "name": "Manager Structure", "company": "Tech Corp"}),
        ]
        mock_service.list_salary_structures = AsyncMock(return_value=mock_structures)

        # Execute
        response = client.get("/api/v1/payroll/salary-structures?company=Tech Corp", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["name"] == "Engineer Structure"


class TestSalarySlipAPI:
    """Test cases for Salary Slip API endpoints."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_generate_salary_slip_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_slip_payload
    ):
        """Test successful salary slip generation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_slip_response = MagicMock()
        mock_slip_response.dict.return_value = {
            "id": "slip-123",
            "employee_id": "emp-123",
            "employee_name": "John Doe",
            "gross_pay": "5000.00",
            "total_deduction": "500.00",
            "net_pay": "4500.00",
            "status": "draft",
            "tenant_id": "test_tenant",
        }
        mock_service.generate_salary_slip = AsyncMock(return_value=mock_slip_response)

        # Execute
        response = client.post("/api/v1/payroll/salary-slips", json=sample_slip_payload, headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["employee_id"] == "emp-123"
        assert data["employee_name"] == "John Doe"
        assert data["status"] == "draft"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_list_salary_slips_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful salary slip listing via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_slips = [
            MagicMock(dict=lambda: {"id": "slip-1", "employee_name": "John Doe", "net_pay": "4500.00"}),
            MagicMock(dict=lambda: {"id": "slip-2", "employee_name": "Jane Smith", "net_pay": "5500.00"}),
        ]
        mock_service.list_salary_slips = AsyncMock(return_value=mock_slips)

        # Execute
        response = client.get("/api/v1/payroll/salary-slips?employee_id=emp-123", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["employee_name"] == "John Doe"

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_submit_salary_slip_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test successful salary slip submission via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_slip_response = MagicMock()
        mock_slip_response.dict.return_value = {"id": "slip-123", "status": "submitted"}
        mock_service.submit_salary_slip = AsyncMock(return_value=mock_slip_response)

        # Execute
        response = client.post("/api/v1/payroll/salary-slips/slip-123/submit", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "submitted"


class TestPayrollEntryAPI:
    """Test cases for Payroll Entry API endpoints."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_create_payroll_entry_success(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_entry_payload
    ):
        """Test successful payroll entry creation via API."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_entry_response = MagicMock()
        mock_entry_response.dict.return_value = {
            "id": "entry-123",
            "name": "January 2024 Payroll",
            "company": "Tech Corp",
            "status": "draft",
            "number_of_employees": 0,
            "tenant_id": "test_tenant",
        }
        mock_service.create_payroll_entry = AsyncMock(return_value=mock_entry_response)

        # Execute
        response = client.post(
            "/api/v1/payroll/payroll-entries", json=sample_entry_payload, headers=auth_headers
        )

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "January 2024 Payroll"
        assert data["company"] == "Tech Corp"
        assert data["status"] == "draft"


class TestPayrollStatisticsAPI:
    """Test cases for Payroll Statistics API endpoints."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_get_payroll_statistics_success(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test payroll statistics endpoint."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_stats = {
            "total_slips": 100,
            "total_gross_pay": 500000.0,
            "total_net_pay": 450000.0,
            "by_status": {"draft": 20, "submitted": 80},
        }
        mock_service.get_payroll_statistics = AsyncMock(return_value=mock_stats)

        # Execute
        response = client.get("/api/v1/payroll/statistics?company=Tech Corp", headers=auth_headers)

        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["total_slips"] == 100
        assert data["total_gross_pay"] == 500000.0
        assert "by_status" in data


class TestHealthCheck:
    """Test cases for health check endpoint."""

    def test_health_check_success(self, client):
        """Test health check endpoint."""
        with patch("hrms.microservices.payroll.api.db_manager") as mock_db:
            mock_db.health_check = AsyncMock(return_value={"connected": True, "response_time_ms": 5.2})

            response = client.get("/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "payroll-service"
            assert "dependencies" in data


class TestTenantIsolation:
    """Test multi-tenant isolation."""

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_tenant_isolation_in_component_creation(
        self, mock_tenant, mock_user, mock_service, client, auth_headers, sample_component_payload
    ):
        """Test that salary components are created with correct tenant isolation."""
        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service response
        mock_service.create_salary_component.return_value = MagicMock(
            dict=lambda: {"id": "comp-123", "tenant_id": "test_tenant"}
        )

        response = client.post(
            "/api/v1/payroll/components", json=sample_component_payload, headers=auth_headers
        )

        assert response.status_code == 200
        # Verify service was called with correct tenant_id
        mock_service.create_salary_component.assert_called_once()
        call_args = mock_service.create_salary_component.call_args
        assert call_args[0][0] == "test_tenant"  # First argument should be tenant_id


class TestErrorHandling:
    """Test error handling scenarios."""

    def test_validation_error_handling(self, client, auth_headers):
        """Test validation error handling."""
        # Send invalid data (missing required fields)
        invalid_data = {
            "name": "Basic Salary"
            # Missing required fields
        }

        response = client.post("/api/v1/payroll/components", json=invalid_data, headers=auth_headers)

        assert response.status_code == 422  # Validation error

    @patch("hrms.microservices.payroll.api.payroll_service")
    @patch("hrms.microservices.payroll.api.get_current_user")
    @patch("hrms.microservices.payroll.api.get_current_tenant")
    def test_not_found_error_handling(self, mock_tenant, mock_user, mock_service, client, auth_headers):
        """Test not found error handling."""
        from hrms.microservices.shared.exceptions import NotFoundError

        # Mock authentication
        mock_user.return_value = MagicMock(id="user123", tenant_id="test_tenant")
        mock_tenant.return_value = MagicMock(id="test_tenant", name="Test Tenant")

        # Mock service to raise NotFoundError
        mock_service.get_salary_component.side_effect = NotFoundError("Salary component not found")

        response = client.get("/api/v1/payroll/components/nonexistent", headers=auth_headers)

        assert response.status_code == 404
