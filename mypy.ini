[mypy]
# MyPy configuration for oneHRMS

# Python version
python_version = 3.10

# Strictness settings
strict = True
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# Error output
show_error_codes = True
show_column_numbers = True
show_error_context = True
pretty = True

# Import discovery
namespace_packages = True
explicit_package_bases = True

# Exclude patterns
exclude = (?x)(
    ^build/
    | ^dist/
    | ^\.venv/
    | ^venv/
    | ^\.env/
    | ^env/
    | ^node_modules/
    | ^migrations/
    | ^htmlcov/
    | ^\.pytest_cache/
    | /__pycache__/
)

# Per-module options
[mypy-tests.*]
ignore_errors = True

[mypy-migrations.*]
ignore_errors = True

[mypy-frappe.*]
ignore_missing_imports = True

[mypy-erpnext.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-coverage.*]
ignore_missing_imports = True

# Third-party libraries without stubs
[mypy-requests.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-uvicorn.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-passlib.*]
ignore_missing_imports = True
