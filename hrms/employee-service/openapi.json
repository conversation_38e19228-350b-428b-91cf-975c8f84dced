{"openapi": "3.0.3", "info": {"title": "Employee Service API", "description": "API for managing employees in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/employee", "description": "Production server"}, {"url": "http://localhost:8100/employee", "description": "Development server"}], "paths": {"/employees": {"get": {"summary": "Get employees list", "description": "Retrieve a paginated list of employees", "operationId": "getEmployees", "tags": ["Employees"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "search", "in": "query", "description": "Search term for employee name or email", "required": false, "schema": {"type": "string"}}, {"name": "department", "in": "query", "description": "Filter by department", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "Filter by employee status", "required": false, "schema": {"$ref": "#/components/schemas/EmployeeStatus"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create employee", "description": "Create a new employee record", "operationId": "createEmployee", "tags": ["Employees"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmployeeRequest"}}}}, "responses": {"201": {"description": "Employee created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Employee already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/employees/{employeeId}": {"get": {"summary": "Get employee by ID", "description": "Retrieve a specific employee by their ID", "operationId": "getEmployeeById", "tags": ["Employees"], "parameters": [{"name": "employeeId", "in": "path", "description": "Employee ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}, "404": {"description": "Employee not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update employee", "description": "Update an existing employee record", "operationId": "updateEmployee", "tags": ["Employees"], "parameters": [{"name": "employeeId", "in": "path", "description": "Employee ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEmployeeRequest"}}}}, "responses": {"200": {"description": "Employee updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Employee not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Delete employee", "description": "Delete an employee record", "operationId": "deleteEmployee", "tags": ["Employees"], "parameters": [{"name": "employeeId", "in": "path", "description": "Employee ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Employee deleted successfully"}, "404": {"description": "Employee not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"Employee": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique employee identifier"}, "employeeId": {"type": "string", "description": "Employee ID (e.g., EMP001)"}, "firstName": {"type": "string", "description": "Employee first name"}, "lastName": {"type": "string", "description": "Employee last name"}, "email": {"type": "string", "format": "email", "description": "Employee email address"}, "phone": {"type": "string", "description": "Employee phone number"}, "department": {"type": "string", "description": "Employee department"}, "designation": {"type": "string", "description": "Employee designation/title"}, "manager": {"$ref": "#/components/schemas/EmployeeReference"}, "dateOfJoining": {"type": "string", "format": "date", "description": "Date when employee joined"}, "dateOfBirth": {"type": "string", "format": "date", "description": "Employee date of birth"}, "status": {"$ref": "#/components/schemas/EmployeeStatus"}, "address": {"$ref": "#/components/schemas/Address"}, "salary": {"$ref": "#/components/schemas/SalaryInfo"}, "createdAt": {"type": "string", "format": "date-time", "description": "Record creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Record last update timestamp"}}, "required": ["id", "employeeId", "firstName", "lastName", "email", "department", "designation", "dateOfJoining", "status"]}, "EmployeeReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}}, "required": ["id", "employeeId", "firstName", "lastName"]}, "EmployeeStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "TERMINATED", "ON_LEAVE", "PROBATION"], "description": "Employee status"}, "Address": {"type": "object", "properties": {"street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}}, "required": ["street", "city", "state", "country", "postalCode"]}, "SalaryInfo": {"type": "object", "properties": {"basic": {"type": "number", "format": "double", "description": "Basic salary amount"}, "currency": {"type": "string", "description": "Salary currency code"}, "effectiveDate": {"type": "string", "format": "date", "description": "Date when salary is effective from"}}, "required": ["basic", "currency", "effectiveDate"]}, "CreateEmployeeRequest": {"type": "object", "properties": {"employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "department": {"type": "string"}, "designation": {"type": "string"}, "managerId": {"type": "string", "format": "uuid"}, "dateOfJoining": {"type": "string", "format": "date"}, "dateOfBirth": {"type": "string", "format": "date"}, "address": {"$ref": "#/components/schemas/Address"}, "salary": {"$ref": "#/components/schemas/SalaryInfo"}}, "required": ["employeeId", "firstName", "lastName", "email", "department", "designation", "dateOfJoining"]}, "UpdateEmployeeRequest": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "department": {"type": "string"}, "designation": {"type": "string"}, "managerId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/EmployeeStatus"}, "address": {"$ref": "#/components/schemas/Address"}, "salary": {"$ref": "#/components/schemas/SalaryInfo"}}}, "EmployeeListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}