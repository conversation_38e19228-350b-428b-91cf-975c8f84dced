"""
Shared utilities and components for oneHRMS microservices.

This module provides common functionality used across all microservices including:
- Authentication and authorization
- Database management
- Logging configuration
- Exception handling
- Common models and schemas
- Utility functions
"""

__version__ = "1.0.0"
__author__ = "oneHRMS Team"

from .auth import (
    get_current_user,
    verify_token,
    create_access_token,
    AuthenticationError,
    AuthorizationError,
)
from .database import (
    get_database,
    DatabaseManager,
    get_db_session,
)
from .exceptions import (
    HRMSException,
    ValidationError,
    NotFoundError,
    ConflictError,
)
from .models import (
    BaseModel,
    BaseResponse,
    ErrorResponse,
    PaginatedResponse,
    User,
    Employee,
)
from .utils import (
    generate_uuid,
    format_datetime,
    validate_email,
    hash_password,
    verify_password,
)
from .logging import setup_logging, get_logger

__all__ = [
    # Authentication
    "get_current_user",
    "verify_token", 
    "create_access_token",
    "AuthenticationError",
    "AuthorizationError",
    
    # Database
    "get_database",
    "DatabaseManager",
    "get_db_session",
    
    # Exceptions
    "HRMSException",
    "ValidationError",
    "NotFoundError",
    "ConflictError",
    
    # Models
    "BaseModel",
    "BaseResponse",
    "ErrorResponse",
    "PaginatedResponse",
    "User",
    "Employee",
    
    # Utils
    "generate_uuid",
    "format_datetime",
    "validate_email",
    "hash_password",
    "verify_password",
    
    # Logging
    "setup_logging",
    "get_logger",
]
