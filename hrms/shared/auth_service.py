"""
Authentication service for oneHRMS microservices.

This module provides authentication endpoints and services for
user login, logout, token refresh, and user management.
"""

from datetime import datetime
from typing import Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from .auth import (
    authenticate_and_create_token, 
    refresh_access_token, 
    logout_user, 
    get_current_user,
    security
)
from .models import User
from .logging import get_logger
from .rate_limiting import rate_limit
from .audit import audit_service, AuditAction, AuditSeverity
from .validation import SecureBaseModel

logger = get_logger(__name__)

# Create authentication router
auth_router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


class LoginRequest(SecureBaseModel):
    """Login request model with validation."""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8, max_length=128)


class LoginResponse(BaseModel):
    """Login response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class RefreshTokenRequest(SecureBaseModel):
    """Refresh token request model."""
    refresh_token: str = Field(..., min_length=10)


class RefreshTokenResponse(BaseModel):
    """Refresh token response model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class LogoutResponse(BaseModel):
    """Logout response model."""
    message: str
    success: bool = True


class UserProfileResponse(BaseModel):
    """User profile response model."""
    user: User
    last_login: Optional[datetime] = None
    permissions: list[str]
    roles: list[str]


@auth_router.post("/login", response_model=LoginResponse)
@rate_limit("auth")
async def login(
    request: Request,
    login_data: LoginRequest
):
    """
    Authenticate user and return access and refresh tokens.
    
    This endpoint:
    - Validates user credentials
    - Creates JWT access and refresh tokens
    - Logs authentication attempt
    - Returns user information and tokens
    """
    try:
        # Log authentication attempt
        logger.info(f"Authentication attempt for user: {login_data.username}")
        
        # Authenticate user and create tokens
        auth_result = await authenticate_and_create_token(
            username=login_data.username,
            password=login_data.password,
            request=request
        )
        
        # Log successful authentication
        audit_service.log_authentication(
            username=login_data.username,
            success=True,
            request=request
        )
        
        return LoginResponse(**auth_result)
        
    except Exception as e:
        # Log failed authentication
        audit_service.log_authentication(
            username=login_data.username,
            success=False,
            request=request,
            error_message=str(e)
        )
        
        logger.warning(f"Authentication failed for user: {login_data.username} - {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )


@auth_router.post("/refresh", response_model=RefreshTokenResponse)
@rate_limit("auth")
async def refresh_token(
    request: Request,
    refresh_data: RefreshTokenRequest
):
    """
    Refresh access token using refresh token.
    
    This endpoint:
    - Validates refresh token
    - Creates new access token
    - Logs token refresh attempt
    """
    try:
        # Refresh access token
        refresh_result = await refresh_access_token(refresh_data.refresh_token)
        
        logger.info("Token refresh successful")
        
        return RefreshTokenResponse(**refresh_result)
        
    except Exception as e:
        logger.warning(f"Token refresh failed: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )


@auth_router.post("/logout", response_model=LogoutResponse)
@rate_limit("auth")
async def logout(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: User = Depends(get_current_user)
):
    """
    Logout user by revoking their token.
    
    This endpoint:
    - Revokes the current access token
    - Logs logout event
    - Returns success message
    """
    try:
        # Logout user (revoke token)
        logout_result = await logout_user(credentials.credentials)
        
        # Log logout event
        audit_service.log_action(
            action=AuditAction.LOGOUT,
            resource_type="authentication",
            user=current_user,
            description=f"User {current_user.username} logged out",
            severity=AuditSeverity.LOW,
            request=request
        )
        
        logger.info(f"User {current_user.username} logged out successfully")
        
        return LogoutResponse(**logout_result)
        
    except Exception as e:
        logger.error(f"Logout failed for user {current_user.username}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@auth_router.get("/profile", response_model=UserProfileResponse)
@rate_limit("read")
async def get_user_profile(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """
    Get current user profile information.
    
    This endpoint:
    - Returns current user information
    - Includes permissions and roles
    - Logs profile access
    """
    try:
        # Log profile access
        audit_service.log_data_access(
            user=current_user,
            resource_type="user_profile",
            resource_id=str(current_user.id),
            action=AuditAction.READ,
            request=request
        )
        
        return UserProfileResponse(
            user=current_user,
            permissions=current_user.permissions,
            roles=current_user.roles
        )
        
    except Exception as e:
        logger.error(f"Failed to get profile for user {current_user.username}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


class TokenVerificationResponse(BaseModel):
    """Token verification response model."""
    valid: bool
    user_id: str
    username: str
    is_active: bool


@auth_router.get("/verify", response_model=TokenVerificationResponse)
@rate_limit("read")
async def verify_token(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """
    Verify if the current token is valid.

    This endpoint:
    - Validates the current token
    - Returns token validity status
    - Can be used by frontend to check authentication status
    """
    return TokenVerificationResponse(
        valid=True,
        user_id=str(current_user.id),
        username=current_user.username,
        is_active=current_user.is_active
    )


# Health check for authentication service
@auth_router.get("/health")
async def auth_health_check():
    """Authentication service health check."""
    return {
        "service": "authentication",
        "status": "healthy",
        "version": "1.0.0"
    }


# Development endpoints (only available in development mode)
import os

if os.getenv("ENVIRONMENT", "development") == "development":
    
    @auth_router.get("/dev/users")
    @rate_limit("read")
    async def get_development_users():
        """Get list of development users (development only)."""
        return {
            "users": [
                {
                    "username": "admin",
                    "password": "DevAdmin123!",
                    "roles": ["admin", "hr_manager"],
                    "description": "Development administrator"
                },
                {
                    "username": "hr_manager",
                    "password": "DevHR123!",
                    "roles": ["hr_manager"],
                    "description": "Development HR manager"
                },
                {
                    "username": "employee",
                    "password": "DevEmp123!",
                    "roles": ["employee"],
                    "description": "Development employee"
                }
            ],
            "note": "These are development-only credentials. Do not use in production."
        }
