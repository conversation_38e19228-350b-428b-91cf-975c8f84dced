"""
Audit logging service for oneHRMS microservices.

This module provides comprehensive audit logging for security monitoring,
compliance requirements, and operational tracking.
"""

import json
import os
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional
from uuid import UUID, uuid4
from functools import wraps

from fastapi import Request
from sqlalchemy import Column, String, DateTime, Text, JSON, create_engine
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func

from .logging import get_logger
from .models import User

logger = get_logger(__name__)

# Database configuration for audit logs
AUDIT_DATABASE_URL = os.getenv("AUDIT_DATABASE_URL", os.getenv("DATABASE_URL", "sqlite:///audit.db"))

# Create audit database engine
audit_engine = create_engine(AUDIT_DATABASE_URL)
AuditSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=audit_engine)
AuditBase = declarative_base()


class AuditAction(str, Enum):
    """Audit action types."""
    CREATE = "CREATE"
    READ = "READ"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    EXPORT = "EXPORT"
    IMPORT = "IMPORT"
    APPROVE = "APPROVE"
    REJECT = "REJECT"
    SUBMIT = "SUBMIT"
    CANCEL = "CANCEL"
    RESET_PASSWORD = "RESET_PASSWORD"
    CHANGE_ROLE = "CHANGE_ROLE"
    BULK_OPERATION = "BULK_OPERATION"


class AuditSeverity(str, Enum):
    """Audit severity levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class AuditLog(AuditBase):
    """Audit log database model."""
    __tablename__ = "audit_logs"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # User information
    user_id = Column(String(255), nullable=True)
    username = Column(String(255), nullable=True)
    user_email = Column(String(255), nullable=True)
    
    # Request information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    request_id = Column(String(255), nullable=True)
    
    # Action details
    action = Column(String(50), nullable=False)
    resource_type = Column(String(100), nullable=False)
    resource_id = Column(String(255), nullable=True)
    service_name = Column(String(100), nullable=True)
    
    # Audit details
    severity = Column(String(20), nullable=False, default=AuditSeverity.LOW)
    description = Column(Text, nullable=True)
    details = Column(JSON, nullable=True)
    
    # Changes tracking
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    
    # Status
    success = Column(String(10), nullable=False, default="true")
    error_message = Column(Text, nullable=True)


class AuditService:
    """Audit logging service."""
    
    def __init__(self):
        self.service_name = os.getenv("SERVICE_NAME", "unknown")
        self._ensure_audit_table()
    
    def _ensure_audit_table(self):
        """Ensure audit table exists."""
        try:
            AuditBase.metadata.create_all(bind=audit_engine)
            logger.info("Audit table initialized")
        except Exception as e:
            logger.error(f"Failed to initialize audit table: {e}")
    
    def _get_audit_session(self) -> Session:
        """Get audit database session."""
        return AuditSessionLocal()
    
    def log_action(
        self,
        action: AuditAction,
        resource_type: str,
        user: Optional[User] = None,
        resource_id: Optional[str] = None,
        description: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.LOW,
        request: Optional[Request] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Log an audit event."""
        try:
            session = self._get_audit_session()
            
            # Extract request information
            ip_address = None
            user_agent = None
            request_id = None
            
            if request:
                ip_address = request.client.host
                # Check for forwarded IP
                forwarded_for = request.headers.get("X-Forwarded-For")
                if forwarded_for:
                    ip_address = forwarded_for.split(",")[0].strip()
                
                user_agent = request.headers.get("User-Agent")
                request_id = request.headers.get("X-Request-ID")
            
            # Create audit log entry
            audit_log = AuditLog(
                user_id=str(user.id) if user else None,
                username=user.username if user else None,
                user_email=user.email if user else None,
                ip_address=ip_address,
                user_agent=user_agent,
                request_id=request_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                service_name=self.service_name,
                severity=severity,
                description=description,
                details=details,
                old_values=old_values,
                new_values=new_values,
                success="true" if success else "false",
                error_message=error_message
            )
            
            session.add(audit_log)
            session.commit()
            
            # Log to application logs as well
            log_level = "info" if success else "warning"
            log_message = f"AUDIT: {action} {resource_type}"
            if resource_id:
                log_message += f" {resource_id}"
            if user:
                log_message += f" by {user.username}"
            if description:
                log_message += f" - {description}"
            
            getattr(logger, log_level)(log_message)
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
        finally:
            session.close()
    
    def log_authentication(
        self,
        username: str,
        success: bool,
        request: Optional[Request] = None,
        error_message: Optional[str] = None
    ):
        """Log authentication events."""
        action = AuditAction.LOGIN if success else AuditAction.LOGIN
        severity = AuditSeverity.LOW if success else AuditSeverity.HIGH
        
        self.log_action(
            action=action,
            resource_type="authentication",
            description=f"Authentication {'successful' if success else 'failed'} for {username}",
            details={"username": username},
            severity=severity,
            request=request,
            success=success,
            error_message=error_message
        )
    
    def log_data_access(
        self,
        user: User,
        resource_type: str,
        resource_id: str,
        action: AuditAction,
        request: Optional[Request] = None
    ):
        """Log data access events."""
        self.log_action(
            action=action,
            resource_type=resource_type,
            user=user,
            resource_id=resource_id,
            description=f"Data {action.lower()} operation",
            request=request
        )
    
    def log_data_modification(
        self,
        user: User,
        resource_type: str,
        resource_id: str,
        action: AuditAction,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None
    ):
        """Log data modification events."""
        self.log_action(
            action=action,
            resource_type=resource_type,
            user=user,
            resource_id=resource_id,
            description=f"Data {action.lower()} operation",
            old_values=old_values,
            new_values=new_values,
            severity=AuditSeverity.MEDIUM,
            request=request
        )
    
    def log_security_event(
        self,
        event_type: str,
        description: str,
        user: Optional[User] = None,
        details: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None
    ):
        """Log security-related events."""
        self.log_action(
            action=AuditAction.READ,  # Generic action for security events
            resource_type="security",
            user=user,
            description=f"Security event: {event_type} - {description}",
            details=details,
            severity=AuditSeverity.HIGH,
            request=request
        )


# Global audit service instance
audit_service = AuditService()


def audit_log(
    action: AuditAction,
    resource_type: str,
    severity: AuditSeverity = AuditSeverity.LOW,
    description: Optional[str] = None
):
    """
    Decorator for automatic audit logging.
    
    Usage:
        @router.post("/employees")
        @audit_log(AuditAction.CREATE, "employee", AuditSeverity.MEDIUM)
        async def create_employee(request: Request, current_user: User = Depends(get_current_user)):
            # Implementation
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request and user from function arguments
            request = None
            current_user = None
            
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                elif hasattr(arg, 'username'):  # User object
                    current_user = arg
            
            # Check kwargs as well
            if not request:
                request = kwargs.get('request')
            if not current_user:
                current_user = kwargs.get('current_user')
            
            try:
                # Execute the function
                result = await func(*args, **kwargs)
                
                # Log successful operation
                resource_id = None
                if hasattr(result, 'data') and hasattr(result.data, 'id'):
                    resource_id = str(result.data.id)
                
                audit_service.log_action(
                    action=action,
                    resource_type=resource_type,
                    user=current_user,
                    resource_id=resource_id,
                    description=description or f"{action.value} {resource_type}",
                    severity=severity,
                    request=request,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # Log failed operation
                audit_service.log_action(
                    action=action,
                    resource_type=resource_type,
                    user=current_user,
                    description=description or f"Failed {action.value} {resource_type}",
                    severity=AuditSeverity.HIGH,
                    request=request,
                    success=False,
                    error_message=str(e)
                )
                
                # Re-raise the exception
                raise
        
        return wrapper
    return decorator
