"""
Utility functions for oneHRMS microservices.

This module provides common utility functions used across all microservices
including validation, formatting, and helper functions.
"""

import re
import uuid
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Union
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import os

from passlib.context import CryptContext

from .logging import get_logger

logger = get_logger(__name__)

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def generate_uuid() -> str:
    """Generate a new UUID string."""
    return str(uuid.uuid4())


def generate_employee_id(prefix: str = "EMP", length: int = 6) -> str:
    """
    Generate a unique employee ID.
    
    Args:
        prefix: Prefix for the employee ID
        length: Length of the numeric part
    
    Returns:
        Generated employee ID (e.g., EMP001234)
    """
    import random
    import string
    
    numeric_part = ''.join(random.choices(string.digits, k=length))
    return f"{prefix}{numeric_part}"


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format datetime object to string.
    
    Args:
        dt: Datetime object to format
        format_str: Format string
    
    Returns:
        Formatted datetime string
    """
    if dt is None:
        return ""
    
    # Ensure timezone awareness
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    return dt.strftime(format_str)


def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """
    Parse datetime string to datetime object.
    
    Args:
        dt_str: Datetime string to parse
        format_str: Format string
    
    Returns:
        Parsed datetime object or None if parsing fails
    """
    if not dt_str:
        return None
    
    try:
        dt = datetime.strptime(dt_str, format_str)
        # Make timezone aware
        return dt.replace(tzinfo=timezone.utc)
    except ValueError as e:
        logger.warning(f"Failed to parse datetime '{dt_str}': {e}")
        return None


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
    
    Returns:
        True if email is valid, False otherwise
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_phone(phone: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone: Phone number to validate
    
    Returns:
        True if phone is valid, False otherwise
    """
    if not phone:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's between 10-15 digits
    return 10 <= len(digits_only) <= 15


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt.
    
    Args:
        password: Plain text password
    
    Returns:
        Hashed password
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
    
    Returns:
        True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def sanitize_string(text: str, max_length: int = None) -> str:
    """
    Sanitize string input by removing dangerous characters.
    
    Args:
        text: Text to sanitize
        max_length: Maximum length to truncate to
    
    Returns:
        Sanitized text
    """
    if not text:
        return ""
    
    # Remove HTML tags and dangerous characters
    sanitized = re.sub(r'<[^>]*>', '', text)
    sanitized = re.sub(r'[<>"\']', '', sanitized)
    
    # Truncate if needed
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized.strip()


def format_currency(amount: float, currency: str = "USD") -> str:
    """
    Format amount as currency.
    
    Args:
        amount: Amount to format
        currency: Currency code
    
    Returns:
        Formatted currency string
    """
    if currency == "USD":
        return f"${amount:,.2f}"
    elif currency == "EUR":
        return f"€{amount:,.2f}"
    elif currency == "INR":
        return f"₹{amount:,.2f}"
    else:
        return f"{currency} {amount:,.2f}"


def calculate_age(birth_date: datetime) -> int:
    """
    Calculate age from birth date.
    
    Args:
        birth_date: Date of birth
    
    Returns:
        Age in years
    """
    if not birth_date:
        return 0
    
    today = datetime.now().date()
    birth_date = birth_date.date() if isinstance(birth_date, datetime) else birth_date
    
    age = today.year - birth_date.year
    
    # Adjust if birthday hasn't occurred this year
    if today < birth_date.replace(year=today.year):
        age -= 1
    
    return age


def calculate_work_days(start_date: datetime, end_date: datetime, exclude_weekends: bool = True) -> int:
    """
    Calculate number of work days between two dates.
    
    Args:
        start_date: Start date
        end_date: End date
        exclude_weekends: Whether to exclude weekends
    
    Returns:
        Number of work days
    """
    if not start_date or not end_date:
        return 0
    
    if start_date > end_date:
        start_date, end_date = end_date, start_date
    
    total_days = (end_date - start_date).days + 1
    
    if not exclude_weekends:
        return total_days
    
    # Count weekdays only
    work_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
            work_days += 1
        current_date += timedelta(days=1)
    
    return work_days


def send_email(
    to_email: str,
    subject: str,
    body: str,
    from_email: str = None,
    is_html: bool = False
) -> bool:
    """
    Send email notification.
    
    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body
        from_email: Sender email address
        is_html: Whether body is HTML
    
    Returns:
        True if email sent successfully, False otherwise
    """
    try:
        # Email configuration from environment
        smtp_server = os.getenv("SMTP_SERVER", "localhost")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME", "")
        smtp_password = os.getenv("SMTP_PASSWORD", "")
        from_email = from_email or os.getenv("FROM_EMAIL", "<EMAIL>")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Subject'] = subject
        
        # Attach body
        msg.attach(MIMEText(body, 'html' if is_html else 'plain'))
        
        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            if smtp_username and smtp_password:
                server.starttls()
                server.login(smtp_username, smtp_password)
            
            server.send_message(msg)
        
        logger.info(f"Email sent successfully to {to_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {e}")
        return False


def paginate_query(query, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
    """
    Paginate a SQLAlchemy query.
    
    Args:
        query: SQLAlchemy query object
        page: Page number (1-based)
        page_size: Number of items per page
    
    Returns:
        Dictionary with pagination info and data
    """
    # Ensure minimum values
    page = max(1, page)
    page_size = max(1, min(100, page_size))  # Limit max page size
    
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Get total count
    total = query.count()
    
    # Get paginated data
    items = query.offset(offset).limit(page_size).all()
    
    # Calculate total pages
    total_pages = (total + page_size - 1) // page_size if total > 0 else 0
    
    return {
        "data": items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    Mask sensitive data like phone numbers, emails, etc.
    
    Args:
        data: Data to mask
        mask_char: Character to use for masking
        visible_chars: Number of characters to keep visible at the end
    
    Returns:
        Masked data
    """
    if not data or len(data) <= visible_chars:
        return data
    
    masked_length = len(data) - visible_chars
    return mask_char * masked_length + data[-visible_chars:]


def generate_random_password(length: int = 12) -> str:
    """
    Generate a random password.
    
    Args:
        length: Password length
    
    Returns:
        Generated password
    """
    import random
    import string
    
    # Ensure password has at least one character from each category
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"
    
    # Start with one character from each category
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special)
    ]
    
    # Fill the rest randomly
    all_chars = lowercase + uppercase + digits + special
    for _ in range(length - 4):
        password.append(random.choice(all_chars))
    
    # Shuffle the password
    random.shuffle(password)
    
    return ''.join(password)


def convert_to_dict(obj: Any, exclude_fields: List[str] = None) -> Dict[str, Any]:
    """
    Convert object to dictionary, handling various types.
    
    Args:
        obj: Object to convert
        exclude_fields: Fields to exclude from conversion
    
    Returns:
        Dictionary representation
    """
    exclude_fields = exclude_fields or []
    
    if hasattr(obj, '__dict__'):
        result = {}
        for key, value in obj.__dict__.items():
            if key.startswith('_') or key in exclude_fields:
                continue
            
            if isinstance(value, datetime):
                result[key] = format_datetime(value)
            elif hasattr(value, '__dict__'):
                result[key] = convert_to_dict(value, exclude_fields)
            else:
                result[key] = value
        
        return result
    
    return obj
