"""
Database connection and session management for oneHRMS microservices.

This module provides database connectivity, session management, and
common database operations using SQLAlchemy.
"""

import os
from contextlib import contextmanager
from typing import Generator, Optional

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from .exceptions import DatabaseError
from .logging import get_logger

logger = get_logger(__name__)

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "sqlite:///./hrms_dev.db"  # Use SQLite for development
)

# SQLAlchemy setup
if DATABASE_URL.startswith("sqlite"):
    # SQLite configuration
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=os.getenv("SQL_DEBUG", "false").lower() == "true"
    )
else:
    # PostgreSQL configuration
    engine = create_engine(
        DATABASE_URL,
        poolclass=QueuePool,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        echo=os.getenv("SQL_DEBUG", "false").lower() == "true"
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for SQLAlchemy models
Base = declarative_base()

# Metadata for table creation
metadata = MetaData()


class DatabaseManager:
    """Database manager for handling connections and sessions."""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise DatabaseError(f"Failed to create database tables: {e}")
    
    def drop_tables(self):
        """Drop all database tables."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise DatabaseError(f"Failed to drop database tables: {e}")
    
    def get_session(self) -> Session:
        """Get a new database session."""
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """Provide a transactional scope around a series of operations."""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database transaction failed: {e}")
            raise DatabaseError(f"Database transaction failed: {e}")
        finally:
            session.close()
    
    def health_check(self) -> bool:
        """Check database connectivity."""
        try:
            with self.session_scope() as session:
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


# Global database manager instance
db_manager = DatabaseManager()


def get_database() -> DatabaseManager:
    """Get the database manager instance."""
    return db_manager


def get_db_session() -> Generator[Session, None, None]:
    """
    Dependency function for FastAPI to get database session.
    
    Usage:
        @app.get("/users/")
        def get_users(db: Session = Depends(get_db_session)):
            return db.query(User).all()
    """
    session = db_manager.get_session()
    try:
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise DatabaseError(f"Database session error: {e}")
    finally:
        session.close()


class BaseRepository:
    """Base repository class with common database operations."""
    
    def __init__(self, session: Session, model_class):
        self.session = session
        self.model_class = model_class
    
    def create(self, **kwargs):
        """Create a new record."""
        try:
            instance = self.model_class(**kwargs)
            self.session.add(instance)
            self.session.commit()
            self.session.refresh(instance)
            return instance
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to create {self.model_class.__name__}: {e}")
            raise DatabaseError(f"Failed to create {self.model_class.__name__}: {e}")
    
    def get_by_id(self, id):
        """Get a record by ID."""
        try:
            return self.session.query(self.model_class).filter(
                self.model_class.id == id
            ).first()
        except Exception as e:
            logger.error(f"Failed to get {self.model_class.__name__} by ID: {e}")
            raise DatabaseError(f"Failed to get {self.model_class.__name__} by ID: {e}")
    
    def get_all(self, skip: int = 0, limit: int = 100):
        """Get all records with pagination."""
        try:
            return self.session.query(self.model_class).offset(skip).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get {self.model_class.__name__} list: {e}")
            raise DatabaseError(f"Failed to get {self.model_class.__name__} list: {e}")
    
    def update(self, id, **kwargs):
        """Update a record by ID."""
        try:
            instance = self.get_by_id(id)
            if not instance:
                return None
            
            for key, value in kwargs.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            
            self.session.commit()
            self.session.refresh(instance)
            return instance
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to update {self.model_class.__name__}: {e}")
            raise DatabaseError(f"Failed to update {self.model_class.__name__}: {e}")
    
    def delete(self, id):
        """Delete a record by ID."""
        try:
            instance = self.get_by_id(id)
            if not instance:
                return False
            
            self.session.delete(instance)
            self.session.commit()
            return True
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to delete {self.model_class.__name__}: {e}")
            raise DatabaseError(f"Failed to delete {self.model_class.__name__}: {e}")
    
    def count(self):
        """Count total records."""
        try:
            return self.session.query(self.model_class).count()
        except Exception as e:
            logger.error(f"Failed to count {self.model_class.__name__}: {e}")
            raise DatabaseError(f"Failed to count {self.model_class.__name__}: {e}")


# Database initialization function
def init_database():
    """Initialize the database with tables and basic data."""
    try:
        db_manager.create_tables()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise DatabaseError(f"Failed to initialize database: {e}")


# Health check function
def check_database_health() -> dict:
    """Check database health and return status."""
    try:
        is_healthy = db_manager.health_check()
        return {
            "database": "healthy" if is_healthy else "unhealthy",
            "url": DATABASE_URL.split("@")[-1] if "@" in DATABASE_URL else "unknown"
        }
    except Exception as e:
        logger.error(f"Database health check error: {e}")
        return {
            "database": "unhealthy",
            "error": str(e)
        }


def get_db():
    """FastAPI dependency to get database session."""
    db_manager = DatabaseManager()
    with db_manager.session_scope() as session:
        yield session
