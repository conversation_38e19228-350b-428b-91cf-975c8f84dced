"""
Common Pydantic models and schemas for oneHRMS microservices.

This module defines base models, response schemas, and common data structures
used across all microservices for consistent API interfaces.
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar
from uuid import UUID, uuid4

from pydantic import BaseModel as PydanticBaseModel, Field, validator


T = TypeVar('T')


class BaseModel(PydanticBaseModel):
    """Base model with common configuration."""
    
    class Config:
        # Enable ORM mode for SQLAlchemy integration
        from_attributes = True
        # Use enum values instead of enum names
        use_enum_values = True
        # Validate assignment
        validate_assignment = True
        # Allow population by field name (Pydantic v2)
        populate_by_name = True


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None


class BaseResponse(BaseModel):
    """Base response model for all API responses."""
    
    success: bool = True
    message: str = "Operation completed successfully"
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseModel):
    """Error response model for API errors."""
    
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginatedResponse(BaseResponse, Generic[T]):
    """Paginated response model for list endpoints."""
    
    data: List[T]
    total: int
    page: int = 1
    page_size: int = 20
    total_pages: int
    
    @validator('total_pages', always=True)
    def calculate_total_pages(cls, v, values):
        total = values.get('total', 0)
        page_size = values.get('page_size', 20)
        return (total + page_size - 1) // page_size if total > 0 else 0


class User(BaseModel):
    """User model for authentication and authorization."""
    
    id: UUID = Field(default_factory=uuid4)
    email: str = Field(..., description="User email address")
    username: str = Field(..., description="Username")
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    roles: List[str] = Field(default_factory=list)
    permissions: List[str] = Field(default_factory=list)
    
    @validator('email')
    def validate_email(cls, v):
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, v):
            raise ValueError('Invalid email format')
        return v.lower()


class Employee(TimestampMixin):
    """Employee model for basic employee information."""
    
    id: UUID = Field(default_factory=uuid4)
    employee_id: str = Field(..., description="Unique employee identifier")
    first_name: str = Field(..., description="Employee first name")
    last_name: str = Field(..., description="Employee last name")
    email: str = Field(..., description="Employee email address")
    phone: Optional[str] = None
    department_id: Optional[UUID] = None
    position_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None
    hire_date: Optional[datetime] = None
    status: str = Field(default="active", description="Employee status")
    
    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"
    
    @validator('email')
    def validate_email(cls, v):
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, v):
            raise ValueError('Invalid email format')
        return v.lower()
    
    @validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['active', 'inactive', 'terminated', 'on_leave']
        if v not in allowed_statuses:
            raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v


class Department(TimestampMixin):
    """Department model."""
    
    id: UUID = Field(default_factory=uuid4)
    name: str = Field(..., description="Department name")
    code: str = Field(..., description="Department code")
    description: Optional[str] = None
    manager_id: Optional[UUID] = None
    parent_department_id: Optional[UUID] = None
    is_active: bool = True


class Position(TimestampMixin):
    """Position/Job Title model."""
    
    id: UUID = Field(default_factory=uuid4)
    title: str = Field(..., description="Position title")
    code: str = Field(..., description="Position code")
    description: Optional[str] = None
    department_id: Optional[UUID] = None
    level: Optional[int] = None
    is_active: bool = True


# Request/Response models for common operations
class CreateEmployeeRequest(BaseModel):
    """Request model for creating an employee."""
    
    employee_id: str
    first_name: str
    last_name: str
    email: str
    phone: Optional[str] = None
    department_id: Optional[UUID] = None
    position_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None
    hire_date: Optional[datetime] = None


class UpdateEmployeeRequest(BaseModel):
    """Request model for updating an employee."""
    
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    department_id: Optional[UUID] = None
    position_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None
    status: Optional[str] = None


class EmployeeResponse(BaseResponse):
    """Response model for employee operations."""
    
    data: Employee


class EmployeeListResponse(PaginatedResponse[Employee]):
    """Response model for employee list operations."""
    pass
