"""
Standard response models for oneHRMS APIs.

This module provides consistent response structures across all microservices.
"""

from typing import Any, Optional, Dict, List
from pydantic import BaseModel


class SuccessResponse(BaseModel):
    """Standard success response model."""
    success: bool = True
    message: str
    data: Optional[Any] = None
    metadata: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Standard error response model."""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class ListResponse(BaseModel):
    """Standard list response model with pagination."""
    success: bool = True
    message: str
    data: List[Any]
    total: int
    page: int = 1
    page_size: int = 20
    total_pages: int
    metadata: Optional[Dict[str, Any]] = None


class ValidationErrorResponse(BaseModel):
    """Validation error response model."""
    success: bool = False
    message: str = "Validation failed"
    error_code: str = "VALIDATION_ERROR"
    errors: List[Dict[str, Any]]
