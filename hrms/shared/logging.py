"""
Logging configuration for oneHRMS microservices.

This module provides centralized logging configuration with structured
logging, different log levels, and integration with monitoring systems.
"""

import logging
import logging.config
import os
import sys
from datetime import datetime
from typing import Dict, Any

import structlog


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging(
    service_name: str = "hrms",
    log_level: str = None,
    log_format: str = None,
    enable_json: bool = None
) -> None:
    """
    Setup logging configuration for the service.
    
    Args:
        service_name: Name of the microservice
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format string
        enable_json: Whether to use JSON formatting
    """
    # Get configuration from environment variables
    log_level = log_level or os.getenv("LOG_LEVEL", "INFO").upper()
    enable_json = enable_json if enable_json is not None else os.getenv("LOG_JSON", "false").lower() == "true"
    environment = os.getenv("ENVIRONMENT", "development")
    
    # Default log format
    if log_format is None:
        if enable_json:
            log_format = "%(message)s"
        else:
            log_format = (
                "%(asctime)s - %(name)s - %(levelname)s - "
                "[%(filename)s:%(lineno)d] - %(message)s"
            )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if enable_json else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": log_format,
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "colored": {
                "()": ColoredFormatter,
                "format": log_format,
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": "json" if enable_json else ("colored" if environment == "development" else "standard"),
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": log_level,
                "formatter": "json" if enable_json else "standard",
                "filename": f"logs/{service_name}.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console"],
                "level": log_level,
                "propagate": False
            },
            service_name: {
                "handlers": ["console", "file"] if os.path.exists("logs") else ["console"],
                "level": log_level,
                "propagate": False
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn.access": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "sqlalchemy.engine": {
                "handlers": ["console"],
                "level": "WARNING",
                "propagate": False
            }
        }
    }
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Log startup message
    logger = get_logger(service_name)
    logger.info(
        "Logging configured",
        service=service_name,
        level=log_level,
        json_format=enable_json,
        environment=environment
    )


def get_logger(name: str = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func):
    """Decorator to log function calls with parameters and execution time."""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = datetime.utcnow()
        
        logger.debug(
            "Function called",
            function=func.__name__,
            args=str(args)[:200],  # Limit arg length
            kwargs=str(kwargs)[:200]
        )
        
        try:
            result = func(*args, **kwargs)
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.debug(
                "Function completed",
                function=func.__name__,
                execution_time=execution_time
            )
            
            return result
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.error(
                "Function failed",
                function=func.__name__,
                error=str(e),
                execution_time=execution_time
            )
            raise
    
    return wrapper


def log_api_request(request_id: str = None):
    """Decorator to log API requests and responses."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger("api")
            start_time = datetime.utcnow()
            
            logger.info(
                "API request started",
                endpoint=func.__name__,
                request_id=request_id
            )
            
            try:
                result = func(*args, **kwargs)
                execution_time = (datetime.utcnow() - start_time).total_seconds()
                
                logger.info(
                    "API request completed",
                    endpoint=func.__name__,
                    request_id=request_id,
                    execution_time=execution_time,
                    status="success"
                )
                
                return result
            except Exception as e:
                execution_time = (datetime.utcnow() - start_time).total_seconds()
                
                logger.error(
                    "API request failed",
                    endpoint=func.__name__,
                    request_id=request_id,
                    error=str(e),
                    execution_time=execution_time,
                    status="error"
                )
                raise
        
        return wrapper
    return decorator


# Utility functions for common logging patterns
def log_database_operation(operation: str, table: str, record_id: Any = None, **kwargs):
    """Log database operations."""
    logger = get_logger("database")
    logger.info(
        "Database operation",
        operation=operation,
        table=table,
        record_id=record_id,
        **kwargs
    )


def log_external_service_call(service: str, endpoint: str, method: str = "GET", **kwargs):
    """Log external service calls."""
    logger = get_logger("external")
    logger.info(
        "External service call",
        service=service,
        endpoint=endpoint,
        method=method,
        **kwargs
    )


def log_security_event(event_type: str, user_id: str = None, **kwargs):
    """Log security-related events."""
    logger = get_logger("security")
    logger.warning(
        "Security event",
        event_type=event_type,
        user_id=user_id,
        **kwargs
    )


# Initialize logging on module import
if not logging.getLogger().handlers:
    setup_logging()
