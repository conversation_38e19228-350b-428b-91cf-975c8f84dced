"""
Rate limiting utilities for oneHRMS microservices.

This module provides rate limiting functionality to prevent API abuse,
DoS attacks, and ensure fair usage across all microservices.
"""

import asyncio
import time
from typing import Dict, Optional
from functools import wraps
import os

import redis
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse

from .logging import get_logger

logger = get_logger(__name__)

# Redis configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Rate limit configurations by endpoint type
RATE_LIMITS = {
    "auth": {
        "requests": 5,
        "window": 60,  # 5 requests per minute
        "description": "Authentication endpoints"
    },
    "read": {
        "requests": 100,
        "window": 60,  # 100 requests per minute
        "description": "Read operations"
    },
    "write": {
        "requests": 30,
        "window": 60,  # 30 requests per minute
        "description": "Write operations"
    },
    "admin": {
        "requests": 200,
        "window": 60,  # 200 requests per minute
        "description": "Admin operations"
    },
    "bulk": {
        "requests": 5,
        "window": 300,  # 5 requests per 5 minutes
        "description": "Bulk operations"
    }
}


class RateLimiter:
    """Redis-based rate limiter with sliding window algorithm."""
    
    def __init__(self):
        try:
            self.redis_client = redis.from_url(REDIS_URL, decode_responses=True)
            # Test connection
            self.redis_client.ping()
            logger.info("Rate limiter connected to Redis")
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}. Rate limiting disabled.")
            self.redis_client = None
    
    def _get_client_identifier(self, request: Request) -> str:
        """Get unique identifier for the client."""
        # Try to get user ID from request state (set by auth middleware)
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    def _get_rate_limit_key(self, identifier: str, endpoint_type: str) -> str:
        """Generate Redis key for rate limiting."""
        return f"rate_limit:{endpoint_type}:{identifier}"
    
    async def is_allowed(
        self, 
        request: Request, 
        endpoint_type: str = "read"
    ) -> tuple[bool, Dict[str, int]]:
        """
        Check if request is allowed based on rate limits.
        
        Returns:
            tuple: (is_allowed, rate_limit_info)
        """
        if not self.redis_client:
            # If Redis is not available, allow all requests
            return True, {}
        
        if endpoint_type not in RATE_LIMITS:
            logger.warning(f"Unknown endpoint type: {endpoint_type}")
            endpoint_type = "read"
        
        config = RATE_LIMITS[endpoint_type]
        identifier = self._get_client_identifier(request)
        key = self._get_rate_limit_key(identifier, endpoint_type)
        
        try:
            current_time = int(time.time())
            window_start = current_time - config["window"]
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Remove old entries outside the window
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(key, config["window"])
            
            results = pipe.execute()
            current_requests = results[1]
            
            # Check if limit exceeded
            is_allowed = current_requests < config["requests"]
            
            rate_limit_info = {
                "limit": config["requests"],
                "remaining": max(0, config["requests"] - current_requests - 1),
                "reset": current_time + config["window"],
                "window": config["window"]
            }
            
            if not is_allowed:
                logger.warning(
                    f"Rate limit exceeded for {identifier} on {endpoint_type}: "
                    f"{current_requests}/{config['requests']} requests"
                )
            
            return is_allowed, rate_limit_info
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # On error, allow the request
            return True, {}
    
    async def reset_limits(self, identifier: str, endpoint_type: str = None):
        """Reset rate limits for a specific identifier."""
        if not self.redis_client:
            return
        
        try:
            if endpoint_type:
                key = self._get_rate_limit_key(identifier, endpoint_type)
                self.redis_client.delete(key)
            else:
                # Reset all endpoint types for this identifier
                pattern = f"rate_limit:*:{identifier}"
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
            
            logger.info(f"Rate limits reset for {identifier}")
            
        except Exception as e:
            logger.error(f"Failed to reset rate limits: {e}")


# Global rate limiter instance
rate_limiter = RateLimiter()


def rate_limit(endpoint_type: str = "read"):
    """
    Decorator for rate limiting endpoints.
    
    Usage:
        @router.get("/employees")
        @rate_limit("read")
        async def get_employees(request: Request):
            return {"employees": []}
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request from arguments
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # Try to find request in kwargs
                request = kwargs.get('request')
            
            if not request:
                logger.warning("Rate limiting skipped: Request object not found")
                return await func(*args, **kwargs)
            
            # Check rate limit
            is_allowed, rate_info = await rate_limiter.is_allowed(request, endpoint_type)
            
            if not is_allowed:
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "success": False,
                        "error_code": "RATE_LIMITED",
                        "message": f"Rate limit exceeded for {endpoint_type} operations",
                        "retry_after": rate_info.get("window", 60)
                    },
                    headers={
                        "X-RateLimit-Limit": str(rate_info.get("limit", 0)),
                        "X-RateLimit-Remaining": str(rate_info.get("remaining", 0)),
                        "X-RateLimit-Reset": str(rate_info.get("reset", 0)),
                        "Retry-After": str(rate_info.get("window", 60))
                    }
                )
            
            # Add rate limit headers to response
            response = await func(*args, **kwargs)
            
            # Add rate limit headers if response supports it
            if hasattr(response, 'headers'):
                response.headers["X-RateLimit-Limit"] = str(rate_info.get("limit", 0))
                response.headers["X-RateLimit-Remaining"] = str(rate_info.get("remaining", 0))
                response.headers["X-RateLimit-Reset"] = str(rate_info.get("reset", 0))
            
            return response
        
        return wrapper
    return decorator


async def rate_limit_middleware(request: Request, call_next):
    """
    Middleware for automatic rate limiting based on endpoint patterns.
    
    This middleware automatically applies rate limits based on HTTP methods
    and URL patterns without requiring decorators on each endpoint.
    """
    # Determine endpoint type based on method and path
    method = request.method.upper()
    path = request.url.path
    
    # Skip rate limiting for health checks and metrics
    if path in ["/health", "/metrics", "/docs", "/redoc", "/openapi.json"]:
        return await call_next(request)
    
    # Determine endpoint type
    endpoint_type = "read"  # default
    
    if "/auth/" in path or path.endswith("/login") or path.endswith("/logout"):
        endpoint_type = "auth"
    elif method in ["POST", "PUT", "PATCH", "DELETE"]:
        if "/bulk" in path or "/import" in path or "/export" in path:
            endpoint_type = "bulk"
        elif "/admin/" in path:
            endpoint_type = "admin"
        else:
            endpoint_type = "write"
    elif "/admin/" in path:
        endpoint_type = "admin"
    
    # Check rate limit
    is_allowed, rate_info = await rate_limiter.is_allowed(request, endpoint_type)
    
    if not is_allowed:
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "success": False,
                "error_code": "RATE_LIMITED",
                "message": f"Rate limit exceeded for {endpoint_type} operations",
                "retry_after": rate_info.get("window", 60)
            },
            headers={
                "X-RateLimit-Limit": str(rate_info.get("limit", 0)),
                "X-RateLimit-Remaining": str(rate_info.get("remaining", 0)),
                "X-RateLimit-Reset": str(rate_info.get("reset", 0)),
                "Retry-After": str(rate_info.get("window", 60))
            }
        )
    
    # Process request
    response = await call_next(request)
    
    # Add rate limit headers
    response.headers["X-RateLimit-Limit"] = str(rate_info.get("limit", 0))
    response.headers["X-RateLimit-Remaining"] = str(rate_info.get("remaining", 0))
    response.headers["X-RateLimit-Reset"] = str(rate_info.get("reset", 0))
    
    return response
