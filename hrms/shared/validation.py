"""
Input validation and sanitization utilities for oneHRMS microservices.

This module provides comprehensive input validation, sanitization, and
security measures to prevent injection attacks and ensure data integrity.
"""

import re
import html
import bleach
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, field_validator, Field
from fastapi import HTTPException, status

from .logging import get_logger

logger = get_logger(__name__)

# Security patterns for detecting malicious input
DANGEROUS_PATTERNS = [
    # SQL Injection patterns
    r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
    r"(--|#|/\*|\*/)",
    r"(\bUNION\b|\bOR\b\s+\d+\s*=\s*\d+)",
    r"(\bAND\b\s+\d+\s*=\s*\d+)",
    r"(\';|\"\;)",
    
    # XSS patterns
    r"(<script[^>]*>.*?</script>)",
    r"(javascript:)",
    r"(on\w+\s*=)",
    r"(<iframe[^>]*>.*?</iframe>)",
    r"(<object[^>]*>.*?</object>)",
    r"(<embed[^>]*>.*?</embed>)",
    
    # Command injection patterns
    r"(\||&|;|\$\(|\`)",
    r"(\.\.\/|\.\.\\)",
    r"(\beval\b|\bexec\b)",
    
    # LDAP injection patterns
    r"(\*|\(|\)|\||&)",
    
    # NoSQL injection patterns
    r"(\$where|\$ne|\$gt|\$lt)",
]

# Compile patterns for better performance
COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE) for pattern in DANGEROUS_PATTERNS]

# Allowed HTML tags and attributes for rich text fields
ALLOWED_TAGS = [
    'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'blockquote', 'a', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td'
]

ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    'img': ['src', 'alt', 'width', 'height'],
    'table': ['class'],
    'th': ['scope'],
    'td': ['colspan', 'rowspan']
}


class ValidationError(Exception):
    """Custom validation error."""
    pass


class InputSanitizer:
    """Input sanitization and validation service."""
    
    @staticmethod
    def sanitize_string(value: str, allow_html: bool = False) -> str:
        """
        Sanitize string input to prevent XSS and injection attacks.
        
        Args:
            value: Input string to sanitize
            allow_html: Whether to allow safe HTML tags
            
        Returns:
            Sanitized string
        """
        if not isinstance(value, str):
            return str(value)
        
        # Remove null bytes
        value = value.replace('\x00', '')
        
        if allow_html:
            # Allow safe HTML tags
            value = bleach.clean(
                value,
                tags=ALLOWED_TAGS,
                attributes=ALLOWED_ATTRIBUTES,
                strip=True
            )
        else:
            # Remove all HTML tags
            value = bleach.clean(value, tags=[], attributes={}, strip=True)
        
        # HTML escape remaining content
        value = html.escape(value)
        
        return value.strip()
    
    @staticmethod
    def validate_no_injection(value: str, field_name: str = "field") -> str:
        """
        Validate that input doesn't contain injection patterns.
        
        Args:
            value: Input string to validate
            field_name: Name of the field for error messages
            
        Returns:
            Original value if safe
            
        Raises:
            ValidationError: If dangerous patterns are detected
        """
        if not isinstance(value, str):
            return value
        
        # Check against dangerous patterns
        for pattern in COMPILED_PATTERNS:
            if pattern.search(value):
                logger.warning(f"Dangerous pattern detected in {field_name}: {value[:100]}")
                raise ValidationError(f"Invalid input detected in {field_name}")
        
        return value
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename to prevent directory traversal and other attacks.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        if not filename:
            return "unnamed_file"
        
        # Remove path separators and dangerous characters
        filename = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', filename)
        
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Prevent reserved names on Windows
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5',
            'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4',
            'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            filename = f"file_{filename}"
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:250] + ('.' + ext if ext else '')
        
        return filename or "unnamed_file"
    
    @staticmethod
    def validate_email(email: str) -> str:
        """
        Validate and sanitize email address.
        
        Args:
            email: Email address to validate
            
        Returns:
            Sanitized email address
            
        Raises:
            ValidationError: If email format is invalid
        """
        if not email:
            raise ValidationError("Email address is required")
        
        # Basic sanitization
        email = email.strip().lower()
        
        # Validate format
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Invalid email format")
        
        # Check for dangerous patterns
        InputSanitizer.validate_no_injection(email, "email")
        
        return email
    
    @staticmethod
    def validate_phone(phone: str) -> str:
        """
        Validate and sanitize phone number.
        
        Args:
            phone: Phone number to validate
            
        Returns:
            Sanitized phone number
            
        Raises:
            ValidationError: If phone format is invalid
        """
        if not phone:
            return ""
        
        # Remove all non-digit characters except + and spaces
        phone = re.sub(r'[^\d\+\s\-\(\)]', '', phone)
        
        # Basic validation (10-15 digits)
        digits_only = re.sub(r'[^\d]', '', phone)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValidationError("Phone number must be between 10 and 15 digits")
        
        return phone.strip()


class SecureBaseModel(BaseModel):
    """
    Base Pydantic model with automatic input sanitization and validation.
    
    All models should inherit from this class to ensure consistent
    security validation across the application.
    """
    
    class Config:
        # Enable validation on assignment
        validate_assignment = True
        # Use enum values
        use_enum_values = True
        # Allow population by field name
        populate_by_name = True
    
    @field_validator('*', mode='before')
    @classmethod
    def sanitize_strings(cls, v, info):
        """Automatically sanitize string inputs."""
        if isinstance(v, str):
            # Check field name to determine if HTML is allowed
            field_name = info.field_name if info.field_name else 'unknown'
            allow_html = field_name in getattr(cls, '_html_fields', [])

            try:
                # Sanitize the string
                sanitized = InputSanitizer.sanitize_string(v, allow_html=allow_html)

                # Validate for injection patterns
                InputSanitizer.validate_no_injection(sanitized, field_name)

                return sanitized
            except ValidationError as e:
                raise ValueError(str(e))

        return v


class SecureEmployeeModel(SecureBaseModel):
    """Secure employee model with enhanced validation."""
    
    # Define which fields can contain HTML
    _html_fields = ['description', 'notes']
    
    employee_id: str = Field(..., min_length=1, max_length=50)
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: str = Field(..., max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    
    @field_validator('email')
    @classmethod
    def validate_email_format(cls, v):
        """Validate email format."""
        return InputSanitizer.validate_email(v)

    @field_validator('phone')
    @classmethod
    def validate_phone_format(cls, v):
        """Validate phone format."""
        if v:
            return InputSanitizer.validate_phone(v)
        return v

    @field_validator('employee_id')
    @classmethod
    def validate_employee_id_format(cls, v):
        """Validate employee ID format."""
        if not re.match(r'^[A-Z0-9\-_]+$', v):
            raise ValueError("Employee ID can only contain uppercase letters, numbers, hyphens, and underscores")
        return v


def validate_request_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and sanitize request data.
    
    Args:
        data: Request data dictionary
        
    Returns:
        Sanitized data dictionary
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        sanitized_data = {}
        
        for key, value in data.items():
            # Validate key name
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                raise ValidationError(f"Invalid field name: {key}")
            
            # Sanitize value based on type
            if isinstance(value, str):
                sanitized_data[key] = InputSanitizer.sanitize_string(value)
                InputSanitizer.validate_no_injection(sanitized_data[key], key)
            elif isinstance(value, dict):
                sanitized_data[key] = validate_request_data(value)
            elif isinstance(value, list):
                sanitized_data[key] = [
                    InputSanitizer.sanitize_string(item) if isinstance(item, str) else item
                    for item in value
                ]
            else:
                sanitized_data[key] = value
        
        return sanitized_data
        
    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request data"
        )
