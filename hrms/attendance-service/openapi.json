{"openapi": "3.0.3", "info": {"title": "Attendance Service API", "description": "API for managing employee attendance, check-ins, check-outs, and time tracking in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/attendance", "description": "Production server"}, {"url": "http://localhost:8102/attendance", "description": "Development server"}], "paths": {"/attendance": {"get": {"summary": "Get attendance records", "description": "Retrieve attendance records with filtering and pagination", "operationId": "getAttendanceRecords", "tags": ["Attendance"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "employeeId", "in": "query", "description": "Filter by employee ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "description": "Filter by specific date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "startDate", "in": "query", "description": "Filter from start date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter to end date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "status", "in": "query", "description": "Filter by attendance status", "required": false, "schema": {"$ref": "#/components/schemas/AttendanceStatus"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create attendance record", "description": "Create a new attendance record (manual entry)", "operationId": "createAttendanceRecord", "tags": ["Attendance"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAttendanceRequest"}}}}, "responses": {"201": {"description": "Attendance record created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceRecord"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Attendance record already exists for this date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/attendance/{attendanceId}": {"get": {"summary": "Get attendance record by ID", "description": "Retrieve a specific attendance record by its ID", "operationId": "getAttendanceRecordById", "tags": ["Attendance"], "parameters": [{"name": "attendanceId", "in": "path", "description": "Attendance record ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceRecord"}}}}, "404": {"description": "Attendance record not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update attendance record", "description": "Update an existing attendance record", "operationId": "updateAttendanceRecord", "tags": ["Attendance"], "parameters": [{"name": "attendanceId", "in": "path", "description": "Attendance record ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAttendanceRequest"}}}}, "responses": {"200": {"description": "Attendance record updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceRecord"}}}}, "404": {"description": "Attendance record not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/check-in": {"post": {"summary": "Check in employee", "description": "Record employee check-in with location and timestamp", "operationId": "checkIn", "tags": ["Check-In/Out"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckInRequest"}}}}, "responses": {"201": {"description": "Check-in recorded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckInResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Employee already checked in", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/check-out": {"post": {"summary": "Check out employee", "description": "Record employee check-out with location and timestamp", "operationId": "checkOut", "tags": ["Check-In/Out"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckOutRequest"}}}}, "responses": {"200": {"description": "Check-out recorded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckOutResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Employee not checked in or already checked out", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/attendance/summary": {"get": {"summary": "Get attendance summary", "description": "Get attendance summary for employees within a date range", "operationId": "getAttendanceSummary", "tags": ["Reports"], "parameters": [{"name": "employeeId", "in": "query", "description": "Filter by employee ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "description": "Start date for summary (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date for summary (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceSummaryResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"AttendanceRecord": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique attendance record identifier"}, "employeeId": {"type": "string", "format": "uuid", "description": "Employee ID"}, "employee": {"$ref": "#/components/schemas/EmployeeInfo"}, "date": {"type": "string", "format": "date", "description": "Attendance date"}, "checkInTime": {"type": "string", "format": "time", "description": "Check-in time (HH:mm:ss)"}, "checkOutTime": {"type": "string", "format": "time", "description": "Check-out time (HH:mm:ss)"}, "checkInLocation": {"$ref": "#/components/schemas/Location"}, "checkOutLocation": {"$ref": "#/components/schemas/Location"}, "workingHours": {"type": "number", "format": "double", "description": "Total working hours for the day"}, "overtimeHours": {"type": "number", "format": "double", "description": "Overtime hours"}, "breakHours": {"type": "number", "format": "double", "description": "Break hours taken"}, "status": {"$ref": "#/components/schemas/AttendanceStatus"}, "remarks": {"type": "string", "description": "Additional remarks or notes"}, "isManualEntry": {"type": "boolean", "description": "Whether this is a manual entry"}, "approvedBy": {"type": "string", "format": "uuid", "description": "ID of the person who approved manual entry"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "date", "status"]}, "EmployeeInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "department": {"type": "string"}, "designation": {"type": "string"}}, "required": ["id", "employeeId", "firstName", "lastName"]}, "Location": {"type": "object", "properties": {"latitude": {"type": "number", "format": "double", "description": "Latitude coordinate"}, "longitude": {"type": "number", "format": "double", "description": "Longitude coordinate"}, "address": {"type": "string", "description": "Human-readable address"}, "accuracy": {"type": "number", "format": "double", "description": "Location accuracy in meters"}}, "required": ["latitude", "longitude"]}, "AttendanceStatus": {"type": "string", "enum": ["PRESENT", "ABSENT", "LATE", "HALF_DAY", "ON_LEAVE", "HOLIDAY", "WEEKEND"], "description": "Attendance status"}, "CreateAttendanceRequest": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "date": {"type": "string", "format": "date"}, "checkInTime": {"type": "string", "format": "time"}, "checkOutTime": {"type": "string", "format": "time"}, "checkInLocation": {"$ref": "#/components/schemas/Location"}, "checkOutLocation": {"$ref": "#/components/schemas/Location"}, "status": {"$ref": "#/components/schemas/AttendanceStatus"}, "remarks": {"type": "string"}, "isManualEntry": {"type": "boolean", "default": true}}, "required": ["employeeId", "date", "status"]}, "UpdateAttendanceRequest": {"type": "object", "properties": {"checkInTime": {"type": "string", "format": "time"}, "checkOutTime": {"type": "string", "format": "time"}, "checkInLocation": {"$ref": "#/components/schemas/Location"}, "checkOutLocation": {"$ref": "#/components/schemas/Location"}, "status": {"$ref": "#/components/schemas/AttendanceStatus"}, "remarks": {"type": "string"}}}, "CheckInRequest": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "format": "date-time", "description": "Check-in timestamp"}, "location": {"$ref": "#/components/schemas/Location"}, "deviceInfo": {"$ref": "#/components/schemas/DeviceInfo"}, "remarks": {"type": "string"}}, "required": ["employeeId", "timestamp"]}, "CheckOutRequest": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "format": "date-time", "description": "Check-out timestamp"}, "location": {"$ref": "#/components/schemas/Location"}, "deviceInfo": {"$ref": "#/components/schemas/DeviceInfo"}, "remarks": {"type": "string"}}, "required": ["employeeId", "timestamp"]}, "DeviceInfo": {"type": "object", "properties": {"deviceId": {"type": "string", "description": "Unique device identifier"}, "deviceType": {"type": "string", "enum": ["MOBILE", "WEB", "BIOMETRIC", "CARD"], "description": "Type of device used for check-in/out"}, "userAgent": {"type": "string", "description": "Browser user agent (for web)"}, "ipAddress": {"type": "string", "description": "IP address of the device"}}}, "CheckInResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "checkInTime": {"type": "string", "format": "date-time"}, "location": {"$ref": "#/components/schemas/Location"}, "status": {"type": "string", "enum": ["SUCCESS", "LATE", "EARLY"]}, "message": {"type": "string", "description": "Status message"}}, "required": ["id", "employeeId", "checkInTime", "status"]}, "CheckOutResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "checkOutTime": {"type": "string", "format": "date-time"}, "location": {"$ref": "#/components/schemas/Location"}, "workingHours": {"type": "number", "format": "double"}, "overtimeHours": {"type": "number", "format": "double"}, "status": {"type": "string", "enum": ["SUCCESS", "EARLY", "OVERTIME"]}, "message": {"type": "string", "description": "Status message"}}, "required": ["id", "employeeId", "checkOutTime", "workingHours", "status"]}, "AttendanceSummary": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "employee": {"$ref": "#/components/schemas/EmployeeInfo"}, "totalDays": {"type": "integer", "description": "Total working days in period"}, "presentDays": {"type": "integer", "description": "Days present"}, "absentDays": {"type": "integer", "description": "Days absent"}, "lateDays": {"type": "integer", "description": "Days late"}, "halfDays": {"type": "integer", "description": "Half days"}, "leaveDays": {"type": "integer", "description": "Days on leave"}, "totalWorkingHours": {"type": "number", "format": "double", "description": "Total working hours"}, "totalOvertimeHours": {"type": "number", "format": "double", "description": "Total overtime hours"}, "averageWorkingHours": {"type": "number", "format": "double", "description": "Average working hours per day"}, "attendancePercentage": {"type": "number", "format": "double", "description": "Attendance percentage"}}, "required": ["employeeId", "totalDays", "presentDays", "absentDays", "attendancePercentage"]}, "AttendanceListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceRecord"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "AttendanceSummaryResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceSummary"}}, "summary": {"type": "object", "properties": {"totalEmployees": {"type": "integer"}, "averageAttendance": {"type": "number", "format": "double"}, "totalWorkingHours": {"type": "number", "format": "double"}, "totalOvertimeHours": {"type": "number", "format": "double"}}}}, "required": ["data"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}