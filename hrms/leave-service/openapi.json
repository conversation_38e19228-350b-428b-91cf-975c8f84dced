{"openapi": "3.0.3", "info": {"title": "Leave Service API", "description": "API for managing employee leave requests, leave balances, and leave policies in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/leave", "description": "Production server"}, {"url": "http://localhost:8103/leave", "description": "Development server"}], "paths": {"/leave-requests": {"get": {"summary": "Get leave requests", "description": "Retrieve leave requests with filtering and pagination", "operationId": "getLeaveRequests", "tags": ["Leave Requests"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "employeeId", "in": "query", "description": "Filter by employee ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by leave request status", "required": false, "schema": {"$ref": "#/components/schemas/LeaveRequestStatus"}}, {"name": "leaveType", "in": "query", "description": "Filter by leave type", "required": false, "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "description": "Filter from start date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter to end date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequestListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create leave request", "description": "Submit a new leave request", "operationId": "createLeaveRequest", "tags": ["Leave Requests"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLeaveRequestRequest"}}}}, "responses": {"201": {"description": "Leave request created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Leave request conflicts with existing request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/leave-requests/{leaveRequestId}": {"get": {"summary": "Get leave request by ID", "description": "Retrieve a specific leave request by its ID", "operationId": "getLeaveRequestById", "tags": ["Leave Requests"], "parameters": [{"name": "leaveRequestId", "in": "path", "description": "Leave request ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "404": {"description": "Leave request not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update leave request", "description": "Update an existing leave request", "operationId": "updateLeaveRequest", "tags": ["Leave Requests"], "parameters": [{"name": "leaveRequestId", "in": "path", "description": "Leave request ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLeaveRequestRequest"}}}}, "responses": {"200": {"description": "Leave request updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "404": {"description": "Leave request not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Cancel leave request", "description": "Cancel a leave request", "operationId": "cancelLeaveRequest", "tags": ["Leave Requests"], "parameters": [{"name": "leaveRequestId", "in": "path", "description": "Leave request ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Leave request cancelled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "404": {"description": "Leave request not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/leave-requests/{leaveRequestId}/approve": {"post": {"summary": "Approve leave request", "description": "Approve a pending leave request", "operationId": "approveLeaveRequest", "tags": ["Leave Approval"], "parameters": [{"name": "leaveRequestId", "in": "path", "description": "Leave request ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalRequest"}}}}, "responses": {"200": {"description": "Leave request approved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "404": {"description": "Leave request not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/leave-requests/{leaveRequestId}/reject": {"post": {"summary": "Reject leave request", "description": "Reject a pending leave request", "operationId": "rejectLeaveRequest", "tags": ["Leave Approval"], "parameters": [{"name": "leaveRequestId", "in": "path", "description": "Leave request ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectionRequest"}}}}, "responses": {"200": {"description": "Leave request rejected successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveRequest"}}}}, "404": {"description": "Leave request not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/leave-balances": {"get": {"summary": "Get leave balances", "description": "Retrieve leave balances for employees", "operationId": "getLeaveBalances", "tags": ["Leave Balances"], "parameters": [{"name": "employeeId", "in": "query", "description": "Filter by employee ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "leaveType", "in": "query", "description": "Filter by leave type", "required": false, "schema": {"type": "string"}}, {"name": "year", "in": "query", "description": "Filter by year", "required": false, "schema": {"type": "integer", "minimum": 2020, "maximum": 2030}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveBalanceListResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/leave-types": {"get": {"summary": "Get leave types", "description": "Retrieve available leave types", "operationId": "getLeaveTypes", "tags": ["Leave Types"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaveTypeListResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"LeaveRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique leave request identifier"}, "employeeId": {"type": "string", "format": "uuid", "description": "Employee ID"}, "employee": {"$ref": "#/components/schemas/EmployeeInfo"}, "leaveType": {"$ref": "#/components/schemas/LeaveType"}, "startDate": {"type": "string", "format": "date", "description": "Leave start date"}, "endDate": {"type": "string", "format": "date", "description": "Leave end date"}, "totalDays": {"type": "number", "format": "double", "description": "Total number of leave days"}, "reason": {"type": "string", "description": "Reason for leave"}, "status": {"$ref": "#/components/schemas/LeaveRequestStatus"}, "appliedDate": {"type": "string", "format": "date-time", "description": "Date when leave was applied"}, "approvedBy": {"type": "string", "format": "uuid", "description": "ID of the person who approved/rejected"}, "approvedDate": {"type": "string", "format": "date-time", "description": "Date when leave was approved/rejected"}, "approverComments": {"type": "string", "description": "Comments from approver"}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/Attachment"}, "description": "Supporting documents"}, "isHalfDay": {"type": "boolean", "description": "Whether this is a half-day leave"}, "halfDayPeriod": {"type": "string", "enum": ["FIRST_HALF", "SECOND_HALF"], "description": "Which half of the day for half-day leave"}, "emergencyContact": {"$ref": "#/components/schemas/EmergencyContact"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "leaveType", "startDate", "endDate", "totalDays", "reason", "status", "appliedDate"]}, "EmployeeInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "department": {"type": "string"}, "designation": {"type": "string"}, "managerId": {"type": "string", "format": "uuid"}}, "required": ["id", "employeeId", "firstName", "lastName"]}, "LeaveType": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "description": "Leave type name (e.g., Annual Leave, Sick Leave)"}, "code": {"type": "string", "description": "Leave type code (e.g., AL, SL)"}, "description": {"type": "string", "description": "Leave type description"}, "maxDaysPerYear": {"type": "integer", "description": "Maximum days allowed per year"}, "maxConsecutiveDays": {"type": "integer", "description": "Maximum consecutive days allowed"}, "carryForward": {"type": "boolean", "description": "Whether unused days can be carried forward"}, "requiresApproval": {"type": "boolean", "description": "Whether this leave type requires approval"}, "requiresDocuments": {"type": "boolean", "description": "Whether supporting documents are required"}, "isActive": {"type": "boolean", "description": "Whether this leave type is active"}}, "required": ["id", "name", "code", "maxDaysPerYear", "requiresApproval", "isActive"]}, "LeaveRequestStatus": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "CANCELLED", "WITHDRAWN"], "description": "Leave request status"}, "LeaveBalance": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "employee": {"$ref": "#/components/schemas/EmployeeInfo"}, "leaveType": {"$ref": "#/components/schemas/LeaveType"}, "year": {"type": "integer", "description": "Year for which balance is calculated"}, "totalAllocated": {"type": "number", "format": "double", "description": "Total days allocated for the year"}, "totalUsed": {"type": "number", "format": "double", "description": "Total days used"}, "totalPending": {"type": "number", "format": "double", "description": "Total days in pending requests"}, "totalAvailable": {"type": "number", "format": "double", "description": "Total days available"}, "carriedForward": {"type": "number", "format": "double", "description": "Days carried forward from previous year"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "leaveType", "year", "totalAllocated", "totalUsed", "totalAvailable"]}, "Attachment": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fileName": {"type": "string"}, "fileSize": {"type": "integer", "description": "File size in bytes"}, "mimeType": {"type": "string"}, "uploadedDate": {"type": "string", "format": "date-time"}, "downloadUrl": {"type": "string", "format": "uri"}}, "required": ["id", "fileName", "fileSize", "mimeType", "downloadUrl"]}, "EmergencyContact": {"type": "object", "properties": {"name": {"type": "string"}, "relationship": {"type": "string"}, "phoneNumber": {"type": "string"}, "email": {"type": "string", "format": "email"}}, "required": ["name", "phoneNumber"]}, "CreateLeaveRequestRequest": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "leaveTypeId": {"type": "string", "format": "uuid"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "reason": {"type": "string"}, "isHalfDay": {"type": "boolean", "default": false}, "halfDayPeriod": {"type": "string", "enum": ["FIRST_HALF", "SECOND_HALF"]}, "emergencyContact": {"$ref": "#/components/schemas/EmergencyContact"}, "attachmentIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "IDs of uploaded attachments"}}, "required": ["employeeId", "leaveTypeId", "startDate", "endDate", "reason"]}, "UpdateLeaveRequestRequest": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "reason": {"type": "string"}, "isHalfDay": {"type": "boolean"}, "halfDayPeriod": {"type": "string", "enum": ["FIRST_HALF", "SECOND_HALF"]}, "emergencyContact": {"$ref": "#/components/schemas/EmergencyContact"}}}, "ApprovalRequest": {"type": "object", "properties": {"comments": {"type": "string", "description": "Approval comments"}}}, "RejectionRequest": {"type": "object", "properties": {"reason": {"type": "string", "description": "Reason for rejection"}}, "required": ["reason"]}, "LeaveRequestListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LeaveRequest"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "LeaveBalanceListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LeaveBalance"}}}, "required": ["data"]}, "LeaveTypeListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LeaveType"}}}, "required": ["data"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}