# Since version 2.23 (released in August 2019), git-blame has a feature
# to ignore or bypass certain commits.
#
# This file contains a list of commits that are not likely what you
# are looking for in a blame, such as mass reformatting or renaming.
# You can set this file as a default ignore file for blame by running
# the following command.
#
# $ git config blame.ignoreRevsFile .git-blame-ignore-revs

# sort and cleanup imports
4872c156974291f0c4c88f26033fef0b900ca995

# old black formatting commit (from erpnext)
76c895a6c659356151433715a1efe9337e348c11

# bulk formatting
b55d6e27af6bd274dfa47e66a3012ddec68ce798

# bulk formatting PWA frontend code
f37f15b2b5329e3b0b35891e1c4fd82f48562c6d

# bulk formatting PWA frontend code
920daa1a3ddccaefaf7b9348f850831d6e0a0e6b

# python ruff formatting
b68457552bb3540565267f23fbfcee35c9f86e1c

# js, scss prettier formatting
1ab1d6238171a5cee3263812402a8b82e7131cb1
