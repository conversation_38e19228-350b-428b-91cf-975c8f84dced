version: '3.8'

services:
  # Employee Service
  employee-service:
    build:
      context: ./employee-service
      dockerfile: Dockerfile.dev
    ports:
      - "8100:8100"
    environment:
      - DATABASE_URL=********************************************/onehrms_employee
      - JWT_SECRET=dev-jwt-secret-key
      - CORS_ORIGINS=http://localhost:3001,http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_OPENAPI=true
    volumes:
      - ./employee-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - onehrms-network

  # Payroll Service
  payroll-service:
    build:
      context: ./payroll-service
      dockerfile: Dockerfile.dev
    ports:
      - "8101:8101"
    environment:
      - DATABASE_URL=********************************************/onehrms_payroll
      - JWT_SECRET=dev-jwt-secret-key
      - CORS_ORIGINS=http://localhost:3001,http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_OPENAPI=true
      - EMPLOYEE_SERVICE_URL=http://employee-service:8100
    volumes:
      - ./payroll-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - employee-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - onehrms-network

  # Attendance Service
  attendance-service:
    build:
      context: ./attendance-service
      dockerfile: Dockerfile.dev
    ports:
      - "8102:8102"
    environment:
      - DATABASE_URL=********************************************/onehrms_attendance
      - JWT_SECRET=dev-jwt-secret-key
      - CORS_ORIGINS=http://localhost:3001,http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_OPENAPI=true
      - EMPLOYEE_SERVICE_URL=http://employee-service:8100
    volumes:
      - ./attendance-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - employee-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8102/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - onehrms-network

  # ESS (Employee Self Service) Service
  ess-service:
    build:
      context: ./ess-service
      dockerfile: Dockerfile.dev
    ports:
      - "8103:8103"
    environment:
      - DATABASE_URL=********************************************/onehrms_ess
      - JWT_SECRET=dev-jwt-secret-key
      - CORS_ORIGINS=http://localhost:3001,http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_OPENAPI=true
      - EMPLOYEE_SERVICE_URL=http://employee-service:8100
      - PAYROLL_SERVICE_URL=http://payroll-service:8101
      - ATTENDANCE_SERVICE_URL=http://attendance-service:8102
    volumes:
      - ./ess-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - employee-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8103/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - onehrms-network

  # Recruitment Service
  recruitment-service:
    build:
      context: ./recruitment-service
      dockerfile: Dockerfile.dev
    ports:
      - "8104:8104"
    environment:
      - DATABASE_URL=********************************************/onehrms_recruitment
      - JWT_SECRET=dev-jwt-secret-key
      - CORS_ORIGINS=http://localhost:3001,http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_OPENAPI=true
      - EMPLOYEE_SERVICE_URL=http://employee-service:8100
    volumes:
      - ./recruitment-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - employee-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8104/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - onehrms-network

  # Dashboard UI
  dashboard-ui:
    build:
      context: ./dashboard-ui
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    environment:
      - VITE_API_BASE_URL=http://localhost:8100
      - VITE_EMPLOYEE_SERVICE_URL=http://localhost:8100
      - VITE_PAYROLL_SERVICE_URL=http://localhost:8101
      - VITE_ATTENDANCE_SERVICE_URL=http://localhost:8102
      - VITE_ESS_SERVICE_URL=http://localhost:8103
      - VITE_RECRUITMENT_SERVICE_URL=http://localhost:8104
      - VITE_DEMO_MODE=false
      - VITE_ENABLE_DASHBOARD_API=true
      - VITE_ENABLE_LEGACY_API=true
      - VITE_USE_GENERATED_CLIENTS=true
      - VITE_DEBUG_API=true
    volumes:
      - ./dashboard-ui:/app
      - /app/node_modules
    depends_on:
      - employee-service
      - payroll-service
      - attendance-service
      - ess-service
      - recruitment-service
    networks:
      - onehrms-network

  # Kong API Gateway
  kong:
    image: kong:3.4
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8443:8443"
      - "8444:8444"
    environment:
      - KONG_DATABASE=off
      - KONG_DECLARATIVE_CONFIG=/kong/declarative/kong.yml
      - KONG_PROXY_ACCESS_LOG=/dev/stdout
      - KONG_ADMIN_ACCESS_LOG=/dev/stdout
      - KONG_PROXY_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_LISTEN=0.0.0.0:8001
    volumes:
      - ./kong/kong.yml:/kong/declarative/kong.yml
    networks:
      - onehrms-network

  # Keycloak Authentication
  keycloak:
    image: quay.io/keycloak/keycloak:22.0
    ports:
      - "8080:8080"
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
      - KC_DB=postgres
      - KC_DB_URL=****************************************
      - KC_DB_USERNAME=postgres
      - KC_DB_PASSWORD=password
      - KC_HOSTNAME_STRICT=false
      - KC_HTTP_ENABLED=true
    command: start-dev
    depends_on:
      - postgres
    networks:
      - onehrms-network

  # PostgreSQL Database
  postgres:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=onehrms
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - onehrms-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - onehrms-network

  # OpenAPI Generator Service (for development)
  openapi-generator:
    image: openapitools/openapi-generator-cli:latest
    volumes:
      - ./dashboard-ui:/workspace
    working_dir: /workspace
    command: tail -f /dev/null
    networks:
      - onehrms-network

volumes:
  postgres_data:
  redis_data:

networks:
  onehrms-network:
    driver: bridge
