"""
FastAPI router for Admin microservice endpoints.

This module provides REST API endpoints for dashboard management,
administrative functions, and system configuration.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from hrms.shared.auth import get_current_user, require_permissions
from hrms.shared.database import get_db_session
from hrms.shared.models import User
from hrms.shared.logging import get_logger
from hrms.shared.rate_limiting import rate_limit
from hrms.shared.audit import audit_log, AuditAction, AuditSeverity
# from hrms.shared.responses import SuccessResponse, ErrorResponse

from .models import (
    Dashboard, DashboardChart, NumberCard,
    DashboardCreate, DashboardResponse, DashboardData,
    ChartData, NumberCardData
)
from .dashboard_adapter import DashboardDataAdapter

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/admin", tags=["admin"])

# Legacy dashboard configurations path
LEGACY_DASHBOARD_PATH = Path(__file__).parent.parent / "legacy" / "hr"


@router.get("/dashboards", response_model=List[DashboardResponse])
@rate_limit("read")
@audit_log(AuditAction.READ, "dashboard", AuditSeverity.LOW)
async def list_dashboards(
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["admin:read", "dashboard:read"])),
    db: Session = Depends(get_db_session)
):
    """List all available dashboards."""
    try:
        # For now, return migrated legacy dashboards
        dashboards = await get_legacy_dashboards()
        return dashboards
    except Exception as e:
        logger.error(f"Error listing dashboards: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboards"
        )


@router.get("/dashboards/{dashboard_name}", response_model=DashboardResponse)
@rate_limit("read")
@audit_log(AuditAction.READ, "dashboard", AuditSeverity.LOW)
async def get_dashboard(
    dashboard_name: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["admin:read", "dashboard:read"])),
    db: Session = Depends(get_db_session)
):
    """Get a specific dashboard configuration."""
    try:
        dashboard = await get_legacy_dashboard_by_name(dashboard_name)
        if not dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Dashboard '{dashboard_name}' not found"
            )
        return dashboard
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving dashboard {dashboard_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard"
        )


@router.get("/dashboards/{dashboard_name}/data", response_model=DashboardData)
@rate_limit("read")
@audit_log(AuditAction.READ, "dashboard_data", AuditSeverity.LOW)
async def get_dashboard_data(
    dashboard_name: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["admin:read", "dashboard:read"])),
    db: Session = Depends(get_db_session)
):
    """Get dashboard data with charts and number cards."""
    try:
        # Get dashboard configuration
        dashboard = await get_legacy_dashboard_by_name(dashboard_name)
        if not dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Dashboard '{dashboard_name}' not found"
            )
        
        # Fetch data using adapter
        async with DashboardDataAdapter() as adapter:
            # Get number cards data
            cards_data = []
            for card_config in dashboard.cards:
                card_data = await get_number_card_data_by_name(card_config.card, adapter)
                if card_data:
                    cards_data.append(card_data)
            
            # Get charts data
            charts_data = []
            for chart_config in dashboard.charts:
                chart_data = await get_chart_data_by_name(chart_config.chart, adapter)
                if chart_data:
                    charts_data.append(chart_data)
            
            return DashboardData(
                dashboard=dashboard,
                cards_data=cards_data,
                charts_data=charts_data,
                last_updated=datetime.now()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving dashboard data for {dashboard_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard data"
        )


@router.get("/charts/{chart_name}/data", response_model=ChartData)
@rate_limit("read")
async def get_chart_data_endpoint(
    chart_name: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["admin:read", "dashboard:read"])),
    db: Session = Depends(get_db_session)
):
    """Get data for a specific chart."""
    try:
        async with DashboardDataAdapter() as adapter:
            chart_data = await get_chart_data_by_name(chart_name, adapter)
            if not chart_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Chart '{chart_name}' not found"
                )
            return chart_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving chart data for {chart_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve chart data"
        )


@router.get("/cards/{card_name}/data", response_model=NumberCardData)
@rate_limit("read")
async def get_card_data_endpoint(
    card_name: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["admin:read", "dashboard:read"])),
    db: Session = Depends(get_db_session)
):
    """Get data for a specific number card."""
    try:
        async with DashboardDataAdapter() as adapter:
            card_data = await get_number_card_data_by_name(card_name, adapter)
            if not card_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Card '{card_name}' not found"
                )
            return card_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving card data for {card_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve card data"
        )


# Helper functions

async def get_legacy_dashboards() -> List[DashboardResponse]:
    """Get all legacy dashboard configurations."""
    dashboards = []
    dashboard_path = LEGACY_DASHBOARD_PATH / "hr_dashboard"
    
    if dashboard_path.exists():
        for dashboard_dir in dashboard_path.iterdir():
            if dashboard_dir.is_dir():
                config_file = dashboard_dir / f"{dashboard_dir.name}.json"
                if config_file.exists():
                    try:
                        with open(config_file, 'r') as f:
                            config = json.load(f)
                        
                        dashboard = DashboardResponse(
                            id=UUID('550e8400-e29b-41d4-a716-************'),  # Mock UUID
                            name=config.get('name', dashboard_dir.name),
                            dashboard_name=config.get('dashboard_name', dashboard_dir.name.title()),
                            module=config.get('module', 'HR'),
                            is_default=config.get('is_default', False),
                            is_public=True,
                            cards=config.get('cards', []),
                            charts=config.get('charts', []),
                            created_at=datetime.now(),
                            updated_at=None
                        )
                        dashboards.append(dashboard)
                    except Exception as e:
                        logger.warning(f"Failed to load dashboard config {config_file}: {e}")
    
    return dashboards


async def get_legacy_dashboard_by_name(dashboard_name: str) -> Optional[DashboardResponse]:
    """Get a specific legacy dashboard by name."""
    dashboards = await get_legacy_dashboards()
    for dashboard in dashboards:
        if dashboard.name.lower() == dashboard_name.lower() or \
           dashboard.dashboard_name.lower() == dashboard_name.lower():
            return dashboard
    return None


async def get_number_card_data_by_name(card_name: str, adapter: DashboardDataAdapter) -> Optional[NumberCardData]:
    """Get number card data by card name."""
    try:
        # Load card configuration from legacy
        card_config = await get_legacy_card_config(card_name)
        if card_config:
            return await adapter.get_number_card_data(card_config)
        return None
    except Exception as e:
        logger.error(f"Error getting card data for {card_name}: {e}")
        return None


async def get_chart_data_by_name(chart_name: str, adapter: DashboardDataAdapter) -> Optional[ChartData]:
    """Get chart data by chart name."""
    try:
        # Load chart configuration from legacy
        chart_config = await get_legacy_chart_config(chart_name)
        if chart_config:
            return await adapter.get_chart_data(chart_config)
        return None
    except Exception as e:
        logger.error(f"Error getting chart data for {chart_name}: {e}")
        return None


async def get_legacy_card_config(card_name: str) -> Optional[Dict]:
    """Get legacy number card configuration."""
    card_path = LEGACY_DASHBOARD_PATH / "number_card"
    
    # Convert card name to directory name (replace spaces with underscores, lowercase)
    dir_name = card_name.lower().replace(' ', '_').replace('(', '').replace(')', '')
    card_dir = card_path / dir_name
    
    if card_dir.exists():
        config_file = card_dir / f"{dir_name}.json"
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                config['name'] = card_name  # Add the original name
                return config
            except Exception as e:
                logger.warning(f"Failed to load card config {config_file}: {e}")
    
    return None


async def get_legacy_chart_config(chart_name: str) -> Optional[Dict]:
    """Get legacy chart configuration."""
    chart_path = LEGACY_DASHBOARD_PATH / "dashboard_chart"
    
    # Convert chart name to directory name
    dir_name = chart_name.lower().replace(' ', '_').replace('(', '').replace(')', '')
    chart_dir = chart_path / dir_name
    
    if chart_dir.exists():
        config_file = chart_dir / f"{dir_name}.json"
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                config['chart_name'] = chart_name  # Add the original name
                return config
            except Exception as e:
                logger.warning(f"Failed to load chart config {config_file}: {e}")
    
    return None
