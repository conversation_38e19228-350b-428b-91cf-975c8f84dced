"""
Main application file for the Admin microservice.

This module creates and configures the FastAPI application for the
Admin microservice with dashboard management and administrative functions.
"""

import os
import uuid
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from hrms.shared.database import init_database, check_database_health
from hrms.shared.exceptions import HRMSException, hrms_exception_handler
from hrms.shared.logging import setup_logging, get_logger
from hrms.shared.rate_limiting import rate_limit_middleware
from hrms.shared.audit import audit_service
from hrms.shared.auth_service import auth_router

from hrms.admin.api import router as admin_router

# Setup logging
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Admin microservice...")
    
    try:
        # Initialize database
        init_database()
        logger.info("Database initialized successfully")

        # Check database health
        health_status = check_database_health()
        if health_status.get("database") == "healthy":
            logger.info("Database health check passed")
        else:
            logger.warning("Database health check failed")
        
        logger.info("Admin microservice startup completed")
        
    except Exception as e:
        logger.error(f"Failed to initialize Admin microservice: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Admin microservice...")


# Create FastAPI application with enhanced security
app = FastAPI(
    title="Admin Microservice",
    description="Administrative dashboard and management microservice for oneHRMS",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Security middleware - order matters!

# 1. Request ID middleware (for audit logging)
@app.middleware("http")
async def add_request_id_middleware(request: Request, call_next):
    """Add unique request ID for tracking and audit logging."""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response

# 2. Rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# 3. CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 4. Trusted host middleware
trusted_hosts = os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(TrustedHostMiddleware, allowed_hosts=trusted_hosts)

# Exception handlers
app.add_exception_handler(HRMSException, hrms_exception_handler)

# Include routers
app.include_router(auth_router)  # Authentication endpoints
app.include_router(admin_router)  # Admin endpoints

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        health_status = check_database_health()
        db_healthy = health_status.get("database") == "healthy"
        
        return {
            "service": "admin",
            "status": "healthy" if db_healthy else "degraded",
            "version": "1.0.0",
            "database": "healthy" if db_healthy else "unhealthy",
            "timestamp": "2025-07-26T20:00:00Z"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "oneHRMS Admin Microservice",
        "version": "1.0.0",
        "description": "Administrative dashboard and management service",
        "docs": "/docs",
        "health": "/health"
    }


# Dashboard-specific endpoints
@app.get("/api/v1/dashboard-info")
async def get_dashboard_info():
    """Get information about available dashboards."""
    return {
        "available_dashboards": [
            {
                "name": "human_resource",
                "title": "Human Resource Dashboard",
                "description": "Employee metrics, hiring analytics, and demographic insights",
                "cards": 3,
                "charts": 8
            },
            {
                "name": "attendance",
                "title": "Attendance Dashboard", 
                "description": "Attendance tracking, timesheet analytics, and shift management",
                "cards": 4,
                "charts": 4
            },
            {
                "name": "employee_lifecycle",
                "title": "Employee Lifecycle Dashboard",
                "description": "Onboarding, separations, promotions, and training analytics",
                "cards": 4,
                "charts": 6
            },
            {
                "name": "expense_claims",
                "title": "Expense Claims Dashboard",
                "description": "Expense tracking, approval analytics, and financial insights",
                "cards": 3,
                "charts": 3
            }
        ],
        "total_dashboards": 4,
        "legacy_migration_status": "in_progress"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8004))  # Different port for admin service
    reload = os.getenv("ENVIRONMENT", "development") == "development"
    
    logger.info(f"Starting Admin microservice on {host}:{port}")
    
    uvicorn.run(
        "hrms.admin.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )
