"""
Dashboard Data Adapter for oneHRMS Admin Service.

This module provides adapters to connect legacy dashboard configurations
to new microservice APIs, enabling seamless migration of existing dashboards.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import os

from .models import ChartData, NumberCardData, ChartDataPoint
from hrms.shared.logging import get_logger

logger = get_logger(__name__)

# Microservice endpoints
MICROSERVICE_URLS = {
    'employee': os.getenv('EMPLOYEE_SERVICE_URL', 'http://localhost:8001'),
    'attendance': os.getenv('ATTENDANCE_SERVICE_URL', 'http://localhost:8002'),
    'leave': os.getenv('LEAVE_SERVICE_URL', 'http://localhost:8003')
}


class DashboardDataAdapter:
    """Adapter to fetch dashboard data from microservices."""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_number_card_data(self, card_config: Dict[str, Any]) -> NumberCardData:
        """Get data for a number card based on legacy configuration."""
        try:
            card_name = card_config.get('name', '')
            document_type = card_config.get('document_type', '')
            function = card_config.get('function', 'Count')
            filters = json.loads(card_config.get('filters_json', '[]'))
            
            # Map legacy card names to microservice endpoints
            if 'Total Employees' in card_name or 'Active Employees' in card_config.get('label', ''):
                return await self._get_total_employees_data(card_config)
            elif 'New Hires' in card_name:
                return await self._get_new_hires_data(card_config)
            elif 'Employee Exits' in card_name:
                return await self._get_employee_exits_data(card_config)
            elif 'Total Present' in card_name:
                return await self._get_attendance_data(card_config, 'present')
            elif 'Total Absent' in card_name:
                return await self._get_attendance_data(card_config, 'absent')
            elif 'Late Entry' in card_name:
                return await self._get_attendance_data(card_config, 'late')
            elif 'Early Exit' in card_name:
                return await self._get_attendance_data(card_config, 'early_exit')
            else:
                # Generic handler for unknown cards
                return await self._get_generic_card_data(card_config)
                
        except Exception as e:
            logger.error(f"Error fetching number card data for {card_name}: {e}")
            return NumberCardData(
                name=card_name,
                label=card_config.get('label', card_name),
                value=0,
                metadata={'error': str(e)}
            )
    
    async def _get_total_employees_data(self, card_config: Dict[str, Any]) -> NumberCardData:
        """Get total employees count."""
        try:
            # Call employee microservice
            url = f"{MICROSERVICE_URLS['employee']}/api/v1/employees/stats/total"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    current_count = data.get('total', 0)
                    
                    # Calculate percentage change (mock for now)
                    percentage_change = 2.5  # This would come from historical data
                    
                    return NumberCardData(
                        name="Total Employees",
                        label="Active Employees",
                        value=current_count,
                        percentage_change=percentage_change,
                        trend="up" if percentage_change > 0 else "down",
                        metadata={'last_updated': datetime.now().isoformat()}
                    )
                else:
                    # Fallback to mock data if service unavailable
                    return self._get_mock_employee_count()
                    
        except Exception as e:
            logger.warning(f"Employee service unavailable, using mock data: {e}")
            return self._get_mock_employee_count()
    
    def _get_mock_employee_count(self) -> NumberCardData:
        """Mock employee count data for development."""
        return NumberCardData(
            name="Total Employees",
            label="Active Employees",
            value=156,
            percentage_change=2.5,
            trend="up",
            metadata={'source': 'mock_data', 'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_new_hires_data(self, card_config: Dict[str, Any]) -> NumberCardData:
        """Get new hires count."""
        try:
            # This would call employee service with date filters
            # For now, return mock data
            return NumberCardData(
                name="New Hires (This Year)",
                label="New Hires This Year",
                value=23,
                percentage_change=15.2,
                trend="up",
                metadata={'period': 'year', 'last_updated': datetime.now().isoformat()}
            )
        except Exception as e:
            logger.error(f"Error fetching new hires data: {e}")
            return NumberCardData(name="New Hires (This Year)", label="New Hires This Year", value=0)
    
    async def _get_employee_exits_data(self, card_config: Dict[str, Any]) -> NumberCardData:
        """Get employee exits count."""
        return NumberCardData(
            name="Employee Exits (This Year)",
            label="Employee Exits This Year",
            value=8,
            percentage_change=-12.5,
            trend="down",
            metadata={'period': 'year', 'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_attendance_data(self, card_config: Dict[str, Any], attendance_type: str) -> NumberCardData:
        """Get attendance-related data."""
        try:
            # This would call attendance microservice
            # For now, return mock data based on type
            data_map = {
                'present': {'value': 142, 'change': 3.2, 'trend': 'up'},
                'absent': {'value': 14, 'change': -8.1, 'trend': 'down'},
                'late': {'value': 23, 'change': -15.3, 'trend': 'down'},
                'early_exit': {'value': 7, 'change': -22.1, 'trend': 'down'}
            }
            
            data = data_map.get(attendance_type, {'value': 0, 'change': 0, 'trend': 'stable'})
            
            return NumberCardData(
                name=card_config.get('name', f'{attendance_type.title()} Count'),
                label=card_config.get('label', f'{attendance_type.title()} This Month'),
                value=data['value'],
                percentage_change=data['change'],
                trend=data['trend'],
                metadata={'period': 'month', 'type': attendance_type, 'last_updated': datetime.now().isoformat()}
            )
        except Exception as e:
            logger.error(f"Error fetching attendance data for {attendance_type}: {e}")
            return NumberCardData(
                name=card_config.get('name', f'{attendance_type.title()} Count'),
                label=card_config.get('label', f'{attendance_type.title()} This Month'),
                value=0
            )
    
    async def _get_generic_card_data(self, card_config: Dict[str, Any]) -> NumberCardData:
        """Generic handler for unknown card types."""
        return NumberCardData(
            name=card_config.get('name', 'Unknown Card'),
            label=card_config.get('label', 'Unknown Metric'),
            value=0,
            metadata={'status': 'not_implemented', 'config': card_config}
        )
    
    async def get_chart_data(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get data for a chart based on legacy configuration."""
        try:
            chart_name = chart_config.get('chart_name', '')
            chart_type = chart_config.get('chart_type', 'Group By')
            display_type = chart_config.get('type', 'Bar')
            document_type = chart_config.get('document_type', '')
            
            # Map legacy chart names to data fetchers
            if 'Department Wise Employee Count' in chart_name:
                return await self._get_department_wise_employee_chart(chart_config)
            elif 'Gender Diversity' in chart_name:
                return await self._get_gender_diversity_chart(chart_config)
            elif 'Attendance Count' in chart_name:
                return await self._get_attendance_count_chart(chart_config)
            elif 'Hiring vs Attrition' in chart_name:
                return await self._get_hiring_attrition_chart(chart_config)
            elif 'Employees by Age' in chart_name:
                return await self._get_employees_by_age_chart(chart_config)
            else:
                return await self._get_generic_chart_data(chart_config)
                
        except Exception as e:
            logger.error(f"Error fetching chart data for {chart_name}: {e}")
            return ChartData(
                chart_name=chart_name,
                chart_type=chart_type,
                type=display_type,
                data=[],
                metadata={'error': str(e)}
            )
    
    async def _get_department_wise_employee_chart(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get department-wise employee count chart data."""
        try:
            # This would call employee microservice for department stats
            # For now, return mock data
            mock_data = [
                ChartDataPoint(label="Engineering", value=45, color="#3498db"),
                ChartDataPoint(label="Sales", value=32, color="#e74c3c"),
                ChartDataPoint(label="Marketing", value=18, color="#f39c12"),
                ChartDataPoint(label="HR", value=12, color="#2ecc71"),
                ChartDataPoint(label="Finance", value=15, color="#9b59b6"),
                ChartDataPoint(label="Operations", value=34, color="#1abc9c")
            ]
            
            return ChartData(
                chart_name="Department Wise Employee Count",
                chart_type="Group By",
                type="Pie",
                data=mock_data,
                total=sum(point.value for point in mock_data),
                metadata={'last_updated': datetime.now().isoformat()}
            )
        except Exception as e:
            logger.error(f"Error fetching department chart data: {e}")
            return ChartData(
                chart_name="Department Wise Employee Count",
                chart_type="Group By",
                type="Pie",
                data=[]
            )
    
    async def _get_gender_diversity_chart(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get gender diversity chart data."""
        mock_data = [
            ChartDataPoint(label="Male", value=89, color="#3498db"),
            ChartDataPoint(label="Female", value=67, color="#e74c3c")
        ]
        
        return ChartData(
            chart_name="Gender Diversity Ratio",
            chart_type="Group By",
            type="Donut",
            data=mock_data,
            total=sum(point.value for point in mock_data),
            metadata={'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_attendance_count_chart(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get attendance count chart data."""
        # Generate mock time series data for last 30 days
        mock_data = []
        base_date = datetime.now() - timedelta(days=29)
        
        for i in range(30):
            date = base_date + timedelta(days=i)
            # Mock attendance data with some variation
            present_count = 140 + (i % 10) - 5
            mock_data.append(ChartDataPoint(
                label=date.strftime("%Y-%m-%d"),
                value=present_count
            ))
        
        return ChartData(
            chart_name="Attendance Count",
            chart_type="Time Series",
            type="Line",
            data=mock_data,
            metadata={'period': 'last_30_days', 'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_hiring_attrition_chart(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get hiring vs attrition chart data."""
        # Generate mock monthly data for the year
        months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        mock_data = []
        
        for month in months:
            # Mock hiring data
            hiring = 8 + (hash(month) % 10)
            attrition = 3 + (hash(month) % 5)
            
            mock_data.extend([
                ChartDataPoint(label=f"{month} Hiring", value=hiring, color="#2ecc71"),
                ChartDataPoint(label=f"{month} Attrition", value=attrition, color="#e74c3c")
            ])
        
        return ChartData(
            chart_name="Hiring vs Attrition Count",
            chart_type="Group By",
            type="Bar",
            data=mock_data,
            metadata={'period': 'yearly', 'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_employees_by_age_chart(self, chart_config: Dict[str, Any]) -> ChartData:
        """Get employees by age chart data."""
        mock_data = [
            ChartDataPoint(label="20-25", value=23, color="#3498db"),
            ChartDataPoint(label="26-30", value=45, color="#2ecc71"),
            ChartDataPoint(label="31-35", value=38, color="#f39c12"),
            ChartDataPoint(label="36-40", value=28, color="#e74c3c"),
            ChartDataPoint(label="41-45", value=15, color="#9b59b6"),
            ChartDataPoint(label="46+", value=7, color="#34495e")
        ]
        
        return ChartData(
            chart_name="Employees by Age",
            chart_type="Group By",
            type="Bar",
            data=mock_data,
            total=sum(point.value for point in mock_data),
            metadata={'last_updated': datetime.now().isoformat()}
        )
    
    async def _get_generic_chart_data(self, chart_config: Dict[str, Any]) -> ChartData:
        """Generic handler for unknown chart types."""
        return ChartData(
            chart_name=chart_config.get('chart_name', 'Unknown Chart'),
            chart_type=chart_config.get('chart_type', 'Group By'),
            type=chart_config.get('type', 'Bar'),
            data=[],
            metadata={'status': 'not_implemented', 'config': chart_config}
        )
