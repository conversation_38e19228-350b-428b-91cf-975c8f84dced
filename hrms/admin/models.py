"""
Database models for the Admin microservice.

This module defines models for dashboard configurations, charts, number cards,
and other administrative data structures.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from sqlalchemy import Column, String, DateTime, Text, JSON, <PERSON>olean, Integer
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.sql import func
from pydantic import BaseModel, Field

from hrms.shared.database import Base
from hrms.shared.validation import SecureBaseModel


class Dashboard(Base):
    """Dashboard configuration model."""
    __tablename__ = "dashboards"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False, unique=True)
    dashboard_name = Column(String(255), nullable=False)
    module = Column(String(100), nullable=False)
    is_default = Column(Boolean, default=False)
    is_standard = Column(Boolean, default=False)
    is_public = Column(Boolean, default=True)
    
    # Dashboard configuration
    cards = Column(JSON, nullable=True)  # List of number cards
    charts = Column(JSON, nullable=True)  # List of charts
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(255), nullable=True)
    modified_by = Column(String(255), nullable=True)


class DashboardChart(Base):
    """Dashboard chart configuration model."""
    __tablename__ = "dashboard_charts"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False, unique=True)
    chart_name = Column(String(255), nullable=False)
    chart_type = Column(String(100), nullable=False)  # Group By, Time Series, etc.
    type = Column(String(100), nullable=False)  # Pie, Bar, Line, etc.
    
    # Data source configuration
    document_type = Column(String(255), nullable=False)  # Employee, Attendance, etc.
    filters_json = Column(JSON, nullable=True)
    dynamic_filters_json = Column(JSON, nullable=True)
    
    # Chart-specific configuration
    group_by_based_on = Column(String(255), nullable=True)
    group_by_type = Column(String(100), nullable=True)  # Count, Sum, Average
    time_interval = Column(String(100), nullable=True)  # Daily, Monthly, Yearly
    timespan = Column(String(100), nullable=True)  # Last Month, Last Year
    timeseries = Column(Boolean, default=False)
    number_of_groups = Column(Integer, default=0)
    
    # Display configuration
    custom_options = Column(Text, nullable=True)
    y_axis = Column(JSON, nullable=True)
    
    # Permissions and metadata
    module = Column(String(100), nullable=False)
    is_public = Column(Boolean, default=True)
    is_standard = Column(Boolean, default=False)
    roles = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(255), nullable=True)
    modified_by = Column(String(255), nullable=True)
    last_synced_on = Column(DateTime(timezone=True), nullable=True)


class NumberCard(Base):
    """Number card configuration model."""
    __tablename__ = "number_cards"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False, unique=True)
    label = Column(String(255), nullable=False)
    
    # Data source configuration
    document_type = Column(String(255), nullable=False)
    function = Column(String(100), nullable=False)  # Count, Sum, Average
    type = Column(String(100), nullable=False)  # Document Type, Report, etc.
    
    # Filters
    filters_json = Column(JSON, nullable=True)
    dynamic_filters_json = Column(JSON, nullable=True)
    
    # Display configuration
    show_percentage_stats = Column(Boolean, default=False)
    stats_time_interval = Column(String(100), nullable=True)  # Monthly, Yearly
    
    # Permissions and metadata
    module = Column(String(100), nullable=False)
    is_public = Column(Boolean, default=True)
    is_standard = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(255), nullable=True)
    modified_by = Column(String(255), nullable=True)


# Pydantic models for API

class DashboardCardConfig(BaseModel):
    """Dashboard card configuration."""
    card: str


class DashboardChartConfig(BaseModel):
    """Dashboard chart configuration."""
    chart: str
    width: str = "Full"  # Full, Half


class DashboardCreate(SecureBaseModel):
    """Dashboard creation model."""
    name: str = Field(..., min_length=1, max_length=255)
    dashboard_name: str = Field(..., min_length=1, max_length=255)
    module: str = Field(..., min_length=1, max_length=100)
    is_default: bool = False
    is_public: bool = True
    cards: List[DashboardCardConfig] = []
    charts: List[DashboardChartConfig] = []


class DashboardResponse(BaseModel):
    """Dashboard response model."""
    id: UUID
    name: str
    dashboard_name: str
    module: str
    is_default: bool
    is_public: bool
    cards: List[DashboardCardConfig]
    charts: List[DashboardChartConfig]
    created_at: datetime
    updated_at: Optional[datetime]


class ChartDataPoint(BaseModel):
    """Chart data point model."""
    label: str
    value: float
    color: Optional[str] = None


class ChartData(BaseModel):
    """Chart data response model."""
    chart_name: str
    chart_type: str
    type: str
    data: List[ChartDataPoint]
    total: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


class NumberCardData(BaseModel):
    """Number card data response model."""
    name: str
    label: str
    value: float
    percentage_change: Optional[float] = None
    trend: Optional[str] = None  # up, down, stable
    metadata: Optional[Dict[str, Any]] = None


class DashboardData(BaseModel):
    """Complete dashboard data response."""
    dashboard: DashboardResponse
    cards_data: List[NumberCardData]
    charts_data: List[ChartData]
    last_updated: datetime
