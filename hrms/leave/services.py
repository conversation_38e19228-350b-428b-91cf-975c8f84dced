"""
Business logic services for the Leave Management microservice.

This module contains the service layer that handles business logic
for leave applications, leave types, and leave balance management.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract

from hrms.shared.exceptions import NotFoundError, ConflictError, ValidationError
from hrms.shared.logging import get_logger
from hrms.shared.utils import paginate_query

from hrms.leave.models import LeaveApplication, LeaveType, LeaveBalance, LeavePolicy
from hrms.leave.schemas import (
    LeaveApplicationCreate, LeaveApplicationUpdate, LeaveApplicationSearchParams,
    LeaveTypeCreate, LeaveTypeUpdate, LeaveTypeSearchParams,
    LeaveBalanceCreate, LeaveBalanceUpdate, LeaveBalanceSearchParams,
    LeaveApprovalRequest, LeaveStatistics, EmployeeLeaveBalance
)

logger = get_logger(__name__)


class LeaveService:
    """Service class for leave application operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_leave_application(self, leave_data: LeaveApplicationCreate) -> LeaveApplication:
        """Create a new leave application."""
        logger.info("Creating new leave application", employee_id=str(leave_data.employee_id))
        
        # Validate leave type exists
        leave_type = self.db.query(LeaveType).filter(
            LeaveType.id == leave_data.leave_type_id
        ).first()
        if not leave_type:
            raise ValidationError(f"Leave type with ID '{leave_data.leave_type_id}' not found")
        
        if not leave_type.is_active:
            raise ValidationError("Leave type is not active")
        
        # Calculate total days
        total_days = self._calculate_leave_days(
            leave_data.start_date, 
            leave_data.end_date, 
            leave_data.is_half_day
        )
        
        # Validate leave application rules
        self._validate_leave_application(leave_data, leave_type, total_days)
        
        # Check leave balance
        self._check_leave_balance(leave_data.employee_id, leave_data.leave_type_id, total_days)
        
        # Create leave application
        leave_application = LeaveApplication(
            **leave_data.dict(),
            total_days=total_days,
            status="pending"
        )
        
        self.db.add(leave_application)
        self.db.commit()
        self.db.refresh(leave_application)
        
        # Update pending balance
        self._update_pending_balance(leave_data.employee_id, leave_data.leave_type_id, total_days, "add")
        
        logger.info("Leave application created successfully", 
                   application_id=str(leave_application.id), 
                   employee_id=str(leave_data.employee_id))
        return leave_application
    
    def get_leave_application(self, application_id: UUID) -> LeaveApplication:
        """Get leave application by ID."""
        application = self.db.query(LeaveApplication).filter(
            LeaveApplication.id == application_id
        ).first()
        if not application:
            raise NotFoundError("Leave application", application_id)
        return application
    
    def update_leave_application(
        self, 
        application_id: UUID, 
        leave_data: LeaveApplicationUpdate
    ) -> LeaveApplication:
        """Update a leave application."""
        logger.info("Updating leave application", application_id=str(application_id))
        
        application = self.get_leave_application(application_id)
        
        # Check if application can be updated
        if application.status not in ["pending"]:
            raise ValidationError("Cannot update leave application that is not pending")
        
        # Store original values for balance adjustment
        original_total_days = application.total_days
        
        # Update fields
        update_data = leave_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(application, field, value)
        
        # Recalculate total days if dates changed
        if 'start_date' in update_data or 'end_date' in update_data or 'is_half_day' in update_data:
            application.total_days = self._calculate_leave_days(
                application.start_date,
                application.end_date,
                application.is_half_day
            )
            
            # Adjust pending balance
            balance_diff = application.total_days - original_total_days
            if balance_diff != 0:
                self._update_pending_balance(
                    application.employee_id, 
                    application.leave_type_id, 
                    abs(balance_diff), 
                    "add" if balance_diff > 0 else "subtract"
                )
        
        application.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(application)
        
        logger.info("Leave application updated successfully", application_id=str(application_id))
        return application
    
    def approve_reject_leave(
        self, 
        application_id: UUID, 
        approval_data: LeaveApprovalRequest,
        approver_id: UUID
    ) -> LeaveApplication:
        """Approve or reject a leave application."""
        logger.info("Processing leave approval", 
                   application_id=str(application_id), 
                   action=approval_data.action)
        
        application = self.get_leave_application(application_id)
        
        if application.status != "pending":
            raise ValidationError("Leave application is not pending approval")
        
        if approval_data.action == "approve":
            application.status = "approved"
            application.approved_by = approver_id
            application.approved_date = datetime.utcnow()
            
            # Move from pending to used balance
            self._update_pending_balance(
                application.employee_id, 
                application.leave_type_id, 
                application.total_days, 
                "subtract"
            )
            self._update_used_balance(
                application.employee_id, 
                application.leave_type_id, 
                application.total_days, 
                "add"
            )
            
        elif approval_data.action == "reject":
            application.status = "rejected"
            application.rejection_reason = approval_data.comments
            
            # Remove from pending balance
            self._update_pending_balance(
                application.employee_id, 
                application.leave_type_id, 
                application.total_days, 
                "subtract"
            )
        
        application.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(application)
        
        logger.info("Leave application processed successfully", 
                   application_id=str(application_id), 
                   status=application.status)
        return application
    
    def cancel_leave_application(self, application_id: UUID, employee_id: UUID) -> LeaveApplication:
        """Cancel a leave application."""
        logger.info("Cancelling leave application", application_id=str(application_id))
        
        application = self.get_leave_application(application_id)
        
        # Check if employee owns the application
        if application.employee_id != employee_id:
            raise ValidationError("Cannot cancel another employee's leave application")
        
        if application.status not in ["pending", "approved"]:
            raise ValidationError("Cannot cancel leave application with current status")
        
        # Adjust balances based on current status
        if application.status == "pending":
            self._update_pending_balance(
                application.employee_id, 
                application.leave_type_id, 
                application.total_days, 
                "subtract"
            )
        elif application.status == "approved":
            self._update_used_balance(
                application.employee_id, 
                application.leave_type_id, 
                application.total_days, 
                "subtract"
            )
        
        application.status = "cancelled"
        application.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(application)
        
        logger.info("Leave application cancelled successfully", application_id=str(application_id))
        return application
    
    def search_leave_applications(
        self,
        search_params: LeaveApplicationSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search leave applications with filters and pagination."""
        query = self.db.query(LeaveApplication)
        
        # Apply filters
        if search_params.employee_id:
            query = query.filter(LeaveApplication.employee_id == search_params.employee_id)
        
        if search_params.leave_type_id:
            query = query.filter(LeaveApplication.leave_type_id == search_params.leave_type_id)
        
        if search_params.status:
            query = query.filter(LeaveApplication.status == search_params.status)
        
        if search_params.start_date_from:
            query = query.filter(LeaveApplication.start_date >= search_params.start_date_from)
        
        if search_params.start_date_to:
            query = query.filter(LeaveApplication.start_date <= search_params.start_date_to)
        
        if search_params.applied_date_from:
            query = query.filter(LeaveApplication.applied_date >= search_params.applied_date_from)
        
        if search_params.applied_date_to:
            query = query.filter(LeaveApplication.applied_date <= search_params.applied_date_to)
        
        # Order by applied date descending
        query = query.order_by(LeaveApplication.applied_date.desc())
        
        return paginate_query(query, page, page_size)
    
    def get_leave_statistics(
        self, 
        start_date: date = None, 
        end_date: date = None
    ) -> LeaveStatistics:
        """Get leave statistics for a date range."""
        query = self.db.query(LeaveApplication)
        
        if start_date:
            query = query.filter(LeaveApplication.applied_date >= start_date)
        if end_date:
            query = query.filter(LeaveApplication.applied_date <= end_date)
        
        applications = query.all()
        
        total_applications = len(applications)
        pending_applications = len([a for a in applications if a.status == "pending"])
        approved_applications = len([a for a in applications if a.status == "approved"])
        rejected_applications = len([a for a in applications if a.status == "rejected"])
        
        total_days_applied = sum(a.total_days for a in applications)
        total_days_approved = sum(a.total_days for a in applications if a.status == "approved")
        
        # Find most used leave type
        leave_type_usage = {}
        for app in applications:
            if app.status == "approved":
                leave_type_usage[app.leave_type_id] = leave_type_usage.get(app.leave_type_id, 0) + app.total_days
        
        most_used_leave_type = None
        if leave_type_usage:
            most_used_id = max(leave_type_usage, key=leave_type_usage.get)
            leave_type = self.db.query(LeaveType).filter(LeaveType.id == most_used_id).first()
            most_used_leave_type = leave_type.name if leave_type else None
        
        return LeaveStatistics(
            total_applications=total_applications,
            pending_applications=pending_applications,
            approved_applications=approved_applications,
            rejected_applications=rejected_applications,
            total_days_applied=total_days_applied,
            total_days_approved=total_days_approved,
            most_used_leave_type=most_used_leave_type
        )
    
    def _calculate_leave_days(self, start_date: date, end_date: date, is_half_day: bool) -> float:
        """Calculate number of leave days."""
        if start_date > end_date:
            raise ValidationError("Start date cannot be after end date")
        
        if is_half_day:
            if start_date != end_date:
                raise ValidationError("Half day leave must be for a single day")
            return 0.5
        
        # Calculate business days (excluding weekends)
        total_days = 0
        current_date = start_date
        
        while current_date <= end_date:
            # Skip weekends (Saturday = 5, Sunday = 6)
            if current_date.weekday() < 5:
                total_days += 1
            current_date += timedelta(days=1)
        
        return float(total_days)
    
    def _validate_leave_application(
        self, 
        leave_data: LeaveApplicationCreate, 
        leave_type: LeaveType, 
        total_days: float
    ):
        """Validate leave application against leave type rules."""
        
        # Check maximum consecutive days
        if leave_type.max_consecutive_days and total_days > leave_type.max_consecutive_days:
            raise ValidationError(
                f"Cannot apply for more than {leave_type.max_consecutive_days} consecutive days"
            )
        
        # Check minimum notice period
        days_notice = (leave_data.start_date - date.today()).days
        if days_notice < leave_type.min_days_notice:
            raise ValidationError(
                f"Minimum {leave_type.min_days_notice} days notice required"
            )
        
        # Check maximum advance booking
        if days_notice > leave_type.max_days_advance:
            raise ValidationError(
                f"Cannot apply more than {leave_type.max_days_advance} days in advance"
            )
    
    def _check_leave_balance(self, employee_id: UUID, leave_type_id: UUID, days_requested: float):
        """Check if employee has sufficient leave balance."""
        current_year = date.today().year
        
        balance = self.db.query(LeaveBalance).filter(
            and_(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.leave_type_id == leave_type_id,
                LeaveBalance.year == current_year
            )
        ).first()
        
        if not balance:
            raise ValidationError("No leave balance found for this leave type")
        
        balance.calculate_available_days()
        
        if balance.available_days < days_requested:
            raise ValidationError(
                f"Insufficient leave balance. Available: {balance.available_days}, Requested: {days_requested}"
            )
    
    def _update_pending_balance(
        self, 
        employee_id: UUID, 
        leave_type_id: UUID, 
        days: float, 
        operation: str
    ):
        """Update pending balance for an employee."""
        current_year = date.today().year
        
        balance = self.db.query(LeaveBalance).filter(
            and_(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.leave_type_id == leave_type_id,
                LeaveBalance.year == current_year
            )
        ).first()
        
        if balance:
            if operation == "add":
                balance.pending_days += days
            elif operation == "subtract":
                balance.pending_days = max(0, balance.pending_days - days)
            
            balance.calculate_available_days()
            balance.updated_at = datetime.utcnow()
            self.db.commit()
    
    def _update_used_balance(
        self, 
        employee_id: UUID, 
        leave_type_id: UUID, 
        days: float, 
        operation: str
    ):
        """Update used balance for an employee."""
        current_year = date.today().year
        
        balance = self.db.query(LeaveBalance).filter(
            and_(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.leave_type_id == leave_type_id,
                LeaveBalance.year == current_year
            )
        ).first()
        
        if balance:
            if operation == "add":
                balance.used_days += days
            elif operation == "subtract":
                balance.used_days = max(0, balance.used_days - days)
            
            balance.calculate_available_days()
            balance.updated_at = datetime.utcnow()
            self.db.commit()


class LeaveTypeService:
    """Service class for leave type operations."""

    def __init__(self, db: Session):
        self.db = db

    def create_leave_type(self, leave_type_data: LeaveTypeCreate) -> LeaveType:
        """Create a new leave type."""
        logger.info("Creating new leave type", name=leave_type_data.name)

        # Check if code already exists
        existing = self.db.query(LeaveType).filter(
            LeaveType.code == leave_type_data.code
        ).first()
        if existing:
            raise ConflictError(f"Leave type with code '{leave_type_data.code}' already exists")

        leave_type = LeaveType(**leave_type_data.dict())
        self.db.add(leave_type)
        self.db.commit()
        self.db.refresh(leave_type)

        logger.info("Leave type created successfully", leave_type_id=str(leave_type.id))
        return leave_type

    def get_leave_type(self, leave_type_id: UUID) -> LeaveType:
        """Get leave type by ID."""
        leave_type = self.db.query(LeaveType).filter(
            LeaveType.id == leave_type_id
        ).first()
        if not leave_type:
            raise NotFoundError("Leave type", leave_type_id)
        return leave_type

    def search_leave_types(
        self,
        search_params: LeaveTypeSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search leave types with filters and pagination."""
        query = self.db.query(LeaveType)

        # Apply filters
        if search_params.search:
            search_term = f"%{search_params.search}%"
            query = query.filter(
                or_(
                    LeaveType.name.ilike(search_term),
                    LeaveType.code.ilike(search_term)
                )
            )

        if search_params.is_active is not None:
            query = query.filter(LeaveType.is_active == search_params.is_active)

        # Order by name
        query = query.order_by(LeaveType.name)

        return paginate_query(query, page, page_size)


class LeaveBalanceService:
    """Service class for leave balance operations."""

    def __init__(self, db: Session):
        self.db = db

    def get_employee_leave_balances(
        self,
        employee_id: UUID,
        year: int = None
    ) -> List[LeaveBalance]:
        """Get all leave balances for an employee."""
        if year is None:
            year = date.today().year

        balances = self.db.query(LeaveBalance).filter(
            and_(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.year == year
            )
        ).all()

        # Recalculate available days
        for balance in balances:
            balance.calculate_available_days()

        return balances

    def initialize_employee_balances(self, employee_id: UUID, year: int = None) -> List[LeaveBalance]:
        """Initialize leave balances for an employee for all active leave types."""
        if year is None:
            year = date.today().year

        logger.info("Initializing leave balances", employee_id=str(employee_id), year=year)

        # Get all active leave types
        leave_types = self.db.query(LeaveType).filter(
            LeaveType.is_active == True
        ).all()

        created_balances = []

        for leave_type in leave_types:
            # Check if balance already exists
            existing = self.db.query(LeaveBalance).filter(
                and_(
                    LeaveBalance.employee_id == employee_id,
                    LeaveBalance.leave_type_id == leave_type.id,
                    LeaveBalance.year == year
                )
            ).first()

            if not existing:
                balance = LeaveBalance(
                    employee_id=employee_id,
                    leave_type_id=leave_type.id,
                    year=year,
                    allocated_days=leave_type.max_days_per_year
                )
                balance.calculate_available_days()

                self.db.add(balance)
                created_balances.append(balance)

        if created_balances:
            self.db.commit()
            for balance in created_balances:
                self.db.refresh(balance)

        logger.info("Leave balances initialized",
                   employee_id=str(employee_id),
                   count=len(created_balances))
        return created_balances
