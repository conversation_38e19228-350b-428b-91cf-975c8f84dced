"""
SQLAlchemy models for the Leave Management microservice.

This module defines the database models for leave applications,
leave types, leave balances, and leave policies.
"""

from datetime import datetime, date
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text, Integer, Float, Date
from sqlalchemy.types import TypeDecorator, CHAR
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from hrms.shared.database import Base

# UUID type that works with both SQLite and PostgreSQL
class UUID(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(value)
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value


class LeaveType(Base):
    """Leave type model for defining different types of leaves."""
    
    __tablename__ = "leave_types"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Basic information
    name = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Leave configuration
    max_days_per_year = Column(Float, nullable=False, default=0.0)
    max_consecutive_days = Column(Float, nullable=True)
    min_days_notice = Column(Integer, default=1)  # Minimum days notice required
    max_days_advance = Column(Integer, default=365)  # Maximum days in advance
    
    # Carry forward rules
    is_carry_forward = Column(Boolean, default=False)
    max_carry_forward_days = Column(Float, default=0.0)
    
    # Encashment rules
    is_encashable = Column(Boolean, default=False)
    max_encashment_days = Column(Float, default=0.0)
    
    # Gender and eligibility
    gender_restriction = Column(String(10), nullable=True)  # male, female, null for all
    min_service_months = Column(Integer, default=0)  # Minimum service required
    
    # Status and workflow
    requires_approval = Column(Boolean, default=True)
    approval_levels = Column(Integer, default=1)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    leave_applications = relationship("LeaveApplication", back_populates="leave_type")
    leave_balances = relationship("LeaveBalance", back_populates="leave_type")
    
    def __repr__(self):
        return f"<LeaveType(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def to_dict(self) -> dict:
        """Convert leave type to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "max_days_per_year": self.max_days_per_year,
            "max_consecutive_days": self.max_consecutive_days,
            "min_days_notice": self.min_days_notice,
            "max_days_advance": self.max_days_advance,
            "is_carry_forward": self.is_carry_forward,
            "max_carry_forward_days": self.max_carry_forward_days,
            "is_encashable": self.is_encashable,
            "max_encashment_days": self.max_encashment_days,
            "gender_restriction": self.gender_restriction,
            "min_service_months": self.min_service_months,
            "requires_approval": self.requires_approval,
            "approval_levels": self.approval_levels,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class LeaveBalance(Base):
    """Leave balance model for tracking employee leave balances."""
    
    __tablename__ = "leave_balances"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Employee and leave type
    employee_id = Column(UUID(), nullable=False, index=True)
    leave_type_id = Column(UUID(), ForeignKey("leave_types.id"), nullable=False)
    
    # Balance information
    year = Column(Integer, nullable=False, index=True)
    allocated_days = Column(Float, nullable=False, default=0.0)
    used_days = Column(Float, nullable=False, default=0.0)
    pending_days = Column(Float, nullable=False, default=0.0)  # Days in pending applications
    carry_forward_days = Column(Float, nullable=False, default=0.0)
    encashed_days = Column(Float, nullable=False, default=0.0)
    
    # Calculated fields
    available_days = Column(Float, nullable=False, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    leave_type = relationship("LeaveType", back_populates="leave_balances")
    
    # Unique constraint
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )
    
    def __repr__(self):
        return f"<LeaveBalance(id={self.id}, employee_id={self.employee_id}, year={self.year})>"
    
    def calculate_available_days(self):
        """Calculate available days."""
        self.available_days = (
            self.allocated_days + 
            self.carry_forward_days - 
            self.used_days - 
            self.pending_days - 
            self.encashed_days
        )
    
    def to_dict(self) -> dict:
        """Convert leave balance to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "leave_type_id": str(self.leave_type_id),
            "year": self.year,
            "allocated_days": self.allocated_days,
            "used_days": self.used_days,
            "pending_days": self.pending_days,
            "carry_forward_days": self.carry_forward_days,
            "encashed_days": self.encashed_days,
            "available_days": self.available_days,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class LeaveApplication(Base):
    """Leave application model for employee leave requests."""
    
    __tablename__ = "leave_applications"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Employee and leave type
    employee_id = Column(UUID(), nullable=False, index=True)
    leave_type_id = Column(UUID(), ForeignKey("leave_types.id"), nullable=False)
    
    # Application details
    start_date = Column(Date, nullable=False, index=True)
    end_date = Column(Date, nullable=False, index=True)
    total_days = Column(Float, nullable=False)
    reason = Column(Text, nullable=False)
    
    # Contact information during leave
    contact_number = Column(String(20), nullable=True)
    contact_address = Column(Text, nullable=True)
    
    # Workflow status
    status = Column(String(20), nullable=False, default="pending")  # pending, approved, rejected, cancelled
    applied_date = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Approval workflow
    current_approval_level = Column(Integer, default=1)
    approved_by = Column(UUID(), nullable=True)
    approved_date = Column(DateTime(timezone=True), nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Additional information
    is_half_day = Column(Boolean, default=False)
    half_day_session = Column(String(10), nullable=True)  # morning, afternoon
    attachment_path = Column(String(500), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    leave_type = relationship("LeaveType", back_populates="leave_applications")
    
    def __repr__(self):
        return f"<LeaveApplication(id={self.id}, employee_id={self.employee_id}, status='{self.status}')>"
    
    def to_dict(self) -> dict:
        """Convert leave application to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "leave_type_id": str(self.leave_type_id),
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "total_days": self.total_days,
            "reason": self.reason,
            "contact_number": self.contact_number,
            "contact_address": self.contact_address,
            "status": self.status,
            "applied_date": self.applied_date.isoformat() if self.applied_date else None,
            "current_approval_level": self.current_approval_level,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approved_date": self.approved_date.isoformat() if self.approved_date else None,
            "rejection_reason": self.rejection_reason,
            "is_half_day": self.is_half_day,
            "half_day_session": self.half_day_session,
            "attachment_path": self.attachment_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class LeavePolicy(Base):
    """Leave policy model for company-wide leave policies."""
    
    __tablename__ = "leave_policies"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Policy information
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    effective_from = Column(Date, nullable=False)
    effective_to = Column(Date, nullable=True)
    
    # Policy rules
    weekend_included = Column(Boolean, default=False)
    holiday_included = Column(Boolean, default=False)
    sandwich_leave_policy = Column(Boolean, default=False)  # Leave between holidays
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    def __repr__(self):
        return f"<LeavePolicy(id={self.id}, name='{self.name}')>"
    
    def to_dict(self) -> dict:
        """Convert leave policy to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "effective_from": self.effective_from.isoformat() if self.effective_from else None,
            "effective_to": self.effective_to.isoformat() if self.effective_to else None,
            "weekend_included": self.weekend_included,
            "holiday_included": self.holiday_included,
            "sandwich_leave_policy": self.sandwich_leave_policy,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
