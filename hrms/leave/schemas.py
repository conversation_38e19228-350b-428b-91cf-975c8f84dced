"""
Pydantic schemas for the Leave Management microservice.

This module defines request/response schemas for leave-related operations
using Pydantic for data validation and serialization.
"""

from datetime import datetime, date
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from hrms.shared.models import BaseResponse, PaginatedResponse


# Leave Type Schemas
class LeaveTypeBase(BaseModel):
    """Base leave type schema."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Leave type name")
    code: str = Field(..., min_length=1, max_length=20, description="Leave type code")
    description: Optional[str] = Field(None, description="Leave type description")
    max_days_per_year: float = Field(..., ge=0, description="Maximum days per year")
    max_consecutive_days: Optional[float] = Field(None, ge=0, description="Maximum consecutive days")
    min_days_notice: int = Field(default=1, ge=0, description="Minimum days notice required")
    max_days_advance: int = Field(default=365, ge=1, description="Maximum days in advance")
    is_carry_forward: bool = Field(default=False, description="Allow carry forward")
    max_carry_forward_days: float = Field(default=0.0, ge=0, description="Maximum carry forward days")
    is_encashable: bool = Field(default=False, description="Allow encashment")
    max_encashment_days: float = Field(default=0.0, ge=0, description="Maximum encashment days")
    gender_restriction: Optional[str] = Field(None, description="Gender restriction")
    min_service_months: int = Field(default=0, ge=0, description="Minimum service months")
    requires_approval: bool = Field(default=True, description="Requires approval")
    approval_levels: int = Field(default=1, ge=1, description="Number of approval levels")
    
    @validator('gender_restriction')
    def validate_gender_restriction(cls, v):
        if v is not None and v not in ['male', 'female']:
            raise ValueError('Gender restriction must be "male" or "female"')
        return v


class LeaveTypeCreate(LeaveTypeBase):
    """Schema for creating a leave type."""
    pass


class LeaveTypeUpdate(BaseModel):
    """Schema for updating a leave type."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    max_days_per_year: Optional[float] = Field(None, ge=0)
    max_consecutive_days: Optional[float] = Field(None, ge=0)
    min_days_notice: Optional[int] = Field(None, ge=0)
    max_days_advance: Optional[int] = Field(None, ge=1)
    is_carry_forward: Optional[bool] = None
    max_carry_forward_days: Optional[float] = Field(None, ge=0)
    is_encashable: Optional[bool] = None
    max_encashment_days: Optional[float] = Field(None, ge=0)
    gender_restriction: Optional[str] = None
    min_service_months: Optional[int] = Field(None, ge=0)
    requires_approval: Optional[bool] = None
    approval_levels: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    
    @validator('gender_restriction')
    def validate_gender_restriction(cls, v):
        if v is not None and v not in ['male', 'female']:
            raise ValueError('Gender restriction must be "male" or "female"')
        return v


class LeaveType(LeaveTypeBase):
    """Schema for leave type response."""
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Leave Application Schemas
class LeaveApplicationBase(BaseModel):
    """Base leave application schema."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    leave_type_id: UUID = Field(..., description="Leave type ID")
    start_date: date = Field(..., description="Leave start date")
    end_date: date = Field(..., description="Leave end date")
    reason: str = Field(..., min_length=1, description="Reason for leave")
    contact_number: Optional[str] = Field(None, max_length=20, description="Contact number during leave")
    contact_address: Optional[str] = Field(None, description="Contact address during leave")
    is_half_day: bool = Field(default=False, description="Is half day leave")
    half_day_session: Optional[str] = Field(None, description="Half day session")
    
    @validator('end_date')
    def validate_end_date(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v
    
    @validator('half_day_session')
    def validate_half_day_session(cls, v, values):
        if values.get('is_half_day') and v not in ['morning', 'afternoon']:
            raise ValueError('Half day session must be "morning" or "afternoon"')
        return v


class LeaveApplicationCreate(LeaveApplicationBase):
    """Schema for creating a leave application."""
    pass


class LeaveApplicationUpdate(BaseModel):
    """Schema for updating a leave application."""
    
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    reason: Optional[str] = Field(None, min_length=1)
    contact_number: Optional[str] = Field(None, max_length=20)
    contact_address: Optional[str] = None
    is_half_day: Optional[bool] = None
    half_day_session: Optional[str] = None
    status: Optional[str] = None
    rejection_reason: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['pending', 'approved', 'rejected', 'cancelled']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v
    
    @validator('half_day_session')
    def validate_half_day_session(cls, v, values):
        if values.get('is_half_day') and v not in ['morning', 'afternoon']:
            raise ValueError('Half day session must be "morning" or "afternoon"')
        return v


class LeaveApplication(LeaveApplicationBase):
    """Schema for leave application response."""
    
    id: UUID
    total_days: float
    status: str
    applied_date: datetime
    current_approval_level: int
    approved_by: Optional[UUID]
    approved_date: Optional[datetime]
    rejection_reason: Optional[str]
    attachment_path: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Leave Balance Schemas
class LeaveBalanceBase(BaseModel):
    """Base leave balance schema."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    leave_type_id: UUID = Field(..., description="Leave type ID")
    year: int = Field(..., description="Year")
    allocated_days: float = Field(..., ge=0, description="Allocated days")
    carry_forward_days: float = Field(default=0.0, ge=0, description="Carry forward days")


class LeaveBalanceCreate(LeaveBalanceBase):
    """Schema for creating a leave balance."""
    pass


class LeaveBalanceUpdate(BaseModel):
    """Schema for updating a leave balance."""
    
    allocated_days: Optional[float] = Field(None, ge=0)
    used_days: Optional[float] = Field(None, ge=0)
    pending_days: Optional[float] = Field(None, ge=0)
    carry_forward_days: Optional[float] = Field(None, ge=0)
    encashed_days: Optional[float] = Field(None, ge=0)


class LeaveBalance(LeaveBalanceBase):
    """Schema for leave balance response."""
    
    id: UUID
    used_days: float
    pending_days: float
    encashed_days: float
    available_days: float
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Response Schemas
class LeaveTypeResponse(BaseResponse):
    """Response schema for single leave type operations."""
    
    data: LeaveType


class LeaveTypeListResponse(PaginatedResponse[LeaveType]):
    """Response schema for leave type list operations."""
    pass


class LeaveApplicationResponse(BaseResponse):
    """Response schema for single leave application operations."""
    
    data: LeaveApplication


class LeaveApplicationListResponse(PaginatedResponse[LeaveApplication]):
    """Response schema for leave application list operations."""
    pass


class LeaveBalanceResponse(BaseResponse):
    """Response schema for single leave balance operations."""
    
    data: LeaveBalance


class LeaveBalanceListResponse(PaginatedResponse[LeaveBalance]):
    """Response schema for leave balance list operations."""
    pass


# Search and Filter Schemas
class LeaveApplicationSearchParams(BaseModel):
    """Schema for leave application search parameters."""
    
    employee_id: Optional[UUID] = Field(None, description="Filter by employee")
    leave_type_id: Optional[UUID] = Field(None, description="Filter by leave type")
    status: Optional[str] = Field(None, description="Filter by status")
    start_date_from: Optional[date] = Field(None, description="Filter by start date from")
    start_date_to: Optional[date] = Field(None, description="Filter by start date to")
    applied_date_from: Optional[datetime] = Field(None, description="Filter by applied date from")
    applied_date_to: Optional[datetime] = Field(None, description="Filter by applied date to")


class LeaveTypeSearchParams(BaseModel):
    """Schema for leave type search parameters."""
    
    search: Optional[str] = Field(None, description="Search term for name or code")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    requires_approval: Optional[bool] = Field(None, description="Filter by approval requirement")
    is_encashable: Optional[bool] = Field(None, description="Filter by encashment eligibility")


class LeaveBalanceSearchParams(BaseModel):
    """Schema for leave balance search parameters."""
    
    employee_id: Optional[UUID] = Field(None, description="Filter by employee")
    leave_type_id: Optional[UUID] = Field(None, description="Filter by leave type")
    year: Optional[int] = Field(None, description="Filter by year")


# Statistics and Reports
class LeaveStatistics(BaseModel):
    """Schema for leave statistics."""
    
    total_applications: int
    pending_applications: int
    approved_applications: int
    rejected_applications: int
    total_days_applied: float
    total_days_approved: float
    most_used_leave_type: Optional[str]


class EmployeeLeaveBalance(BaseModel):
    """Schema for employee leave balance summary."""
    
    employee_id: UUID
    leave_balances: List[LeaveBalance]
    total_allocated: float
    total_used: float
    total_available: float


# Approval Schemas
class LeaveApprovalRequest(BaseModel):
    """Schema for leave approval/rejection."""
    
    action: str = Field(..., description="Action to take")
    comments: Optional[str] = Field(None, description="Approval/rejection comments")
    
    @validator('action')
    def validate_action(cls, v):
        allowed_actions = ['approve', 'reject']
        if v not in allowed_actions:
            raise ValueError(f'Action must be one of: {", ".join(allowed_actions)}')
        return v
