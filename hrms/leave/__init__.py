"""
Leave Management microservice for oneHRMS.

This microservice handles all leave-related operations including:
- Leave applications and approvals
- Leave types and policies management
- Leave balance tracking
- Leave calendar and scheduling
- Leave reports and analytics
"""

__version__ = "1.0.0"
__author__ = "oneHRMS Team"

from .api import router as leave_router
from .models import LeaveApplication, LeaveType, LeaveBalance, LeavePolicy
from .services import LeaveService, LeaveTypeService, LeaveBalanceService

__all__ = [
    "leave_router",
    "LeaveApplication",
    "LeaveType", 
    "LeaveBalance",
    "LeavePolicy",
    "LeaveService",
    "LeaveTypeService",
    "LeaveBalanceService",
]
