"""
FastAPI router for the Leave Management microservice.

This module defines the REST API endpoints for leave applications,
leave types, and leave balance management.
"""

from datetime import date
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from hrms.shared.auth import get_current_user, require_permission
from hrms.shared.database import get_db
from hrms.shared.exceptions import NotFoundError, ConflictError, ValidationError
from hrms.shared.models import User

from hrms.leave.schemas import (
    # Leave application schemas
    LeaveApplication, LeaveApplicationCreate, LeaveApplicationUpdate,
    LeaveApplicationResponse, LeaveApplicationListResponse, LeaveApplicationSearchParams,
    
    # Leave type schemas
    LeaveType, LeaveTypeCreate, LeaveTypeUpdate, LeaveTypeSearchParams,
    LeaveTypeResponse, LeaveTypeListResponse,
    
    # Leave balance schemas
    LeaveBalance, LeaveBalanceResponse, LeaveBalanceListResponse, LeaveBalanceSearchParams,
    
    # Statistics and approval schemas
    LeaveStatistics, EmployeeLeaveBalance, LeaveApprovalRequest,
)
from hrms.leave.services import LeaveService, LeaveTypeService, LeaveBalanceService

# Create router
router = APIRouter(prefix="/api/v1/leave", tags=["Leave Management"])


# Leave Application Endpoints
@router.post("/applications/", response_model=LeaveApplicationResponse, status_code=status.HTTP_201_CREATED)
async def create_leave_application(
    leave_data: LeaveApplicationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new leave application."""
    try:
        service = LeaveService(db)
        leave_application = service.create_leave_application(leave_data)
        
        return LeaveApplicationResponse(
            success=True,
            message="Leave application created successfully",
            data=leave_application
        )
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/applications/{application_id}", response_model=LeaveApplicationResponse)
async def get_leave_application(
    application_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a leave application by ID."""
    try:
        service = LeaveService(db)
        leave_application = service.get_leave_application(application_id)
        
        return LeaveApplicationResponse(
            success=True,
            message="Leave application retrieved successfully",
            data=leave_application
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put("/applications/{application_id}", response_model=LeaveApplicationResponse)
async def update_leave_application(
    application_id: UUID,
    leave_data: LeaveApplicationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a leave application."""
    try:
        service = LeaveService(db)
        leave_application = service.update_leave_application(application_id, leave_data)
        
        return LeaveApplicationResponse(
            success=True,
            message="Leave application updated successfully",
            data=leave_application
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/applications/{application_id}/approve", response_model=LeaveApplicationResponse)
async def approve_reject_leave_application(
    application_id: UUID,
    approval_data: LeaveApprovalRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Approve or reject a leave application."""
    try:
        service = LeaveService(db)
        leave_application = service.approve_reject_leave(
            application_id, 
            approval_data, 
            UUID(current_user.id)
        )
        
        action_message = "approved" if approval_data.action == "approve" else "rejected"
        return LeaveApplicationResponse(
            success=True,
            message=f"Leave application {action_message} successfully",
            data=leave_application
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/applications/{application_id}/cancel", response_model=LeaveApplicationResponse)
async def cancel_leave_application(
    application_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a leave application."""
    try:
        service = LeaveService(db)
        leave_application = service.cancel_leave_application(application_id, UUID(current_user.id))
        
        return LeaveApplicationResponse(
            success=True,
            message="Leave application cancelled successfully",
            data=leave_application
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/applications/", response_model=LeaveApplicationListResponse)
async def search_leave_applications(
    employee_id: Optional[UUID] = Query(None, description="Filter by employee"),
    leave_type_id: Optional[UUID] = Query(None, description="Filter by leave type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    start_date_from: Optional[date] = Query(None, description="Filter by start date from"),
    start_date_to: Optional[date] = Query(None, description="Filter by start date to"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search leave applications with filters and pagination."""
    try:
        search_params = LeaveApplicationSearchParams(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            status=status,
            start_date_from=start_date_from,
            start_date_to=start_date_to
        )
        
        service = LeaveService(db)
        result = service.search_leave_applications(search_params, page, page_size)
        
        return LeaveApplicationListResponse(
            success=True,
            message="Leave applications retrieved successfully",
            data=result["items"],
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Leave Type Endpoints
@router.post("/types/", response_model=LeaveTypeResponse, status_code=status.HTTP_201_CREATED)
async def create_leave_type(
    leave_type_data: LeaveTypeCreate,
    current_user: User = Depends(require_permission("leave_types:create")),
    db: Session = Depends(get_db)
):
    """Create a new leave type."""
    try:
        service = LeaveTypeService(db)
        leave_type = service.create_leave_type(leave_type_data)
        
        return LeaveTypeResponse(
            success=True,
            message="Leave type created successfully",
            data=leave_type
        )
    except ConflictError as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/types/{leave_type_id}", response_model=LeaveTypeResponse)
async def get_leave_type(
    leave_type_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a leave type by ID."""
    try:
        service = LeaveTypeService(db)
        leave_type = service.get_leave_type(leave_type_id)
        
        return LeaveTypeResponse(
            success=True,
            message="Leave type retrieved successfully",
            data=leave_type
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.get("/types/", response_model=LeaveTypeListResponse)
async def search_leave_types(
    search: Optional[str] = Query(None, description="Search term for name or code"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    requires_approval: Optional[bool] = Query(None, description="Filter by approval requirement"),
    is_encashable: Optional[bool] = Query(None, description="Filter by encashment eligibility"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search leave types with filters and pagination."""
    try:
        search_params = LeaveTypeSearchParams(
            search=search,
            is_active=is_active,
            requires_approval=requires_approval,
            is_encashable=is_encashable
        )
        
        service = LeaveTypeService(db)
        result = service.search_leave_types(search_params, page, page_size)
        
        return LeaveTypeListResponse(
            success=True,
            message="Leave types retrieved successfully",
            data=result["items"],
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Leave Balance Endpoints
@router.get("/balances/employee/{employee_id}", response_model=LeaveBalanceListResponse)
async def get_employee_leave_balances(
    employee_id: UUID,
    year: Optional[int] = Query(None, description="Year for leave balances"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get leave balances for an employee."""
    try:
        service = LeaveBalanceService(db)
        balances = service.get_employee_leave_balances(employee_id, year)
        
        return LeaveBalanceListResponse(
            success=True,
            message="Leave balances retrieved successfully",
            data=balances,
            total=len(balances),
            page=1,
            page_size=len(balances),
            total_pages=1
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/balances/employee/{employee_id}/initialize", response_model=LeaveBalanceListResponse)
async def initialize_employee_leave_balances(
    employee_id: UUID,
    year: Optional[int] = Query(None, description="Year for leave balances"),
    current_user: User = Depends(require_permission("leave_balances:create")),
    db: Session = Depends(get_db)
):
    """Initialize leave balances for an employee."""
    try:
        service = LeaveBalanceService(db)
        balances = service.initialize_employee_balances(employee_id, year)
        
        return LeaveBalanceListResponse(
            success=True,
            message="Leave balances initialized successfully",
            data=balances,
            total=len(balances),
            page=1,
            page_size=len(balances),
            total_pages=1
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Statistics Endpoints
@router.get("/statistics", response_model=LeaveStatistics)
async def get_leave_statistics(
    start_date: Optional[date] = Query(None, description="Start date for statistics"),
    end_date: Optional[date] = Query(None, description="End date for statistics"),
    current_user: User = Depends(require_permission("leave:view_statistics")),
    db: Session = Depends(get_db)
):
    """Get leave statistics for a date range."""
    try:
        service = LeaveService(db)
        statistics = service.get_leave_statistics(start_date, end_date)
        
        return statistics
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
