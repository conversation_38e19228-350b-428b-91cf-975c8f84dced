{"openapi": "3.0.3", "info": {"title": "Employee Self Service (ESS) API", "description": "API for employee self-service features including profile management, document requests, expense claims, and personal information updates in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/ess", "description": "Production server"}, {"url": "http://localhost:8104/ess", "description": "Development server"}], "paths": {"/profile": {"get": {"summary": "Get employee profile", "description": "Retrieve current employee's profile information", "operationId": "getEmployeeProfile", "tags": ["Profile"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeProfile"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update employee profile", "description": "Update current employee's profile information", "operationId": "updateEmployeeProfile", "tags": ["Profile"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileRequest"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeProfile"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/documents": {"get": {"summary": "Get employee documents", "description": "Retrieve current employee's documents", "operationId": "getEmployeeDocuments", "tags": ["Documents"], "parameters": [{"name": "type", "in": "query", "description": "Filter by document type", "required": false, "schema": {"$ref": "#/components/schemas/DocumentType"}}, {"name": "status", "in": "query", "description": "Filter by document status", "required": false, "schema": {"$ref": "#/components/schemas/DocumentStatus"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentListResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Upload employee document", "description": "Upload a new document for the employee", "operationId": "uploadEmployeeDocument", "tags": ["Documents"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/UploadDocumentRequest"}}}}, "responses": {"201": {"description": "Document uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Document"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/documents/{documentId}": {"get": {"summary": "Download document", "description": "Download a specific document", "operationId": "downloadDocument", "tags": ["Documents"], "parameters": [{"name": "documentId", "in": "path", "description": "Document ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Document file", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Delete document", "description": "Delete a document", "operationId": "deleteDocument", "tags": ["Documents"], "parameters": [{"name": "documentId", "in": "path", "description": "Document ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Document deleted successfully"}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/expense-claims": {"get": {"summary": "Get expense claims", "description": "Retrieve current employee's expense claims", "operationId": "getExpenseClaims", "tags": ["Expense <PERSON>"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "description": "Filter by expense claim status", "required": false, "schema": {"$ref": "#/components/schemas/ExpenseClaimStatus"}}, {"name": "startDate", "in": "query", "description": "Filter from start date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter to end date (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExpenseClaimListResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create expense claim", "description": "Submit a new expense claim", "operationId": "createExpenseClaim", "tags": ["Expense <PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateExpenseClaimRequest"}}}}, "responses": {"201": {"description": "Expense claim created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExpenseClaim"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/expense-claims/{expenseClaimId}": {"get": {"summary": "Get expense claim by ID", "description": "Retrieve a specific expense claim by its ID", "operationId": "getExpenseClaimById", "tags": ["Expense <PERSON>"], "parameters": [{"name": "expenseClaimId", "in": "path", "description": "Expense claim ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExpenseClaim"}}}}, "404": {"description": "Expense claim not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update expense claim", "description": "Update an existing expense claim", "operationId": "updateExpenseClaim", "tags": ["Expense <PERSON>"], "parameters": [{"name": "expenseClaimId", "in": "path", "description": "Expense claim ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateExpenseClaimRequest"}}}}, "responses": {"200": {"description": "Expense claim updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExpenseClaim"}}}}, "404": {"description": "Expense claim not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/payslips": {"get": {"summary": "Get employee payslips", "description": "Retrieve current employee's payslips", "operationId": "getEmployeePayslips", "tags": ["Payslips"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "year", "in": "query", "description": "Filter by year", "required": false, "schema": {"type": "integer", "minimum": 2020, "maximum": 2030}}, {"name": "month", "in": "query", "description": "Filter by month (1-12)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 12}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayslipListResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/payslips/{payslipId}/download": {"get": {"summary": "Download payslip PDF", "description": "Download payslip as PDF file", "operationId": "downloadPayslipPdf", "tags": ["Payslips"], "parameters": [{"name": "payslipId", "in": "path", "description": "Payslip ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Payslip not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"EmployeeProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Employee ID"}, "employeeId": {"type": "string", "description": "Employee number"}, "personalInfo": {"$ref": "#/components/schemas/PersonalInfo"}, "contactInfo": {"$ref": "#/components/schemas/ContactInfo"}, "employmentInfo": {"$ref": "#/components/schemas/EmploymentInfo"}, "bankInfo": {"$ref": "#/components/schemas/BankInfo"}, "emergencyContacts": {"type": "array", "items": {"$ref": "#/components/schemas/EmergencyContact"}}, "profilePicture": {"type": "string", "format": "uri", "description": "Profile picture URL"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "personalInfo", "contactInfo", "employmentInfo"]}, "PersonalInfo": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER", "PREFER_NOT_TO_SAY"]}, "maritalStatus": {"type": "string", "enum": ["SINGLE", "MARRIED", "DIVORCED", "WIDOWED", "SEPARATED"]}, "nationality": {"type": "string"}, "bloodGroup": {"type": "string"}, "identificationNumber": {"type": "string", "description": "National ID, SSN, etc."}}, "required": ["firstName", "lastName", "dateOfBirth"]}, "ContactInfo": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "personalEmail": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "alternatePhone": {"type": "string"}, "address": {"$ref": "#/components/schemas/Address"}, "permanentAddress": {"$ref": "#/components/schemas/Address"}}, "required": ["email", "phone", "address"]}, "Address": {"type": "object", "properties": {"street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}}, "required": ["street", "city", "state", "country", "postalCode"]}, "EmploymentInfo": {"type": "object", "properties": {"department": {"type": "string"}, "designation": {"type": "string"}, "dateOfJoining": {"type": "string", "format": "date"}, "employeeType": {"type": "string", "enum": ["FULL_TIME", "PART_TIME", "CONTRACT", "INTERN"]}, "workLocation": {"type": "string"}, "reportingManager": {"$ref": "#/components/schemas/ManagerInfo"}, "probationEndDate": {"type": "string", "format": "date"}, "confirmationDate": {"type": "string", "format": "date"}}, "required": ["department", "designation", "dateOfJoining", "employeeType"]}, "ManagerInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "designation": {"type": "string"}}, "required": ["id", "employeeId", "firstName", "lastName"]}, "BankInfo": {"type": "object", "properties": {"bankName": {"type": "string"}, "accountNumber": {"type": "string"}, "routingNumber": {"type": "string"}, "accountType": {"type": "string", "enum": ["SAVINGS", "CHECKING", "CURRENT"]}, "accountHolderName": {"type": "string"}, "branchName": {"type": "string"}, "ifscCode": {"type": "string"}}, "required": ["bankName", "accountNumber", "accountHolderName"]}, "EmergencyContact": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "relationship": {"type": "string"}, "phoneNumber": {"type": "string"}, "email": {"type": "string", "format": "email"}, "address": {"$ref": "#/components/schemas/Address"}, "isPrimary": {"type": "boolean", "default": false}}, "required": ["name", "relationship", "phoneNumber"]}, "Document": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "type": {"$ref": "#/components/schemas/DocumentType"}, "name": {"type": "string"}, "fileName": {"type": "string"}, "fileSize": {"type": "integer", "description": "File size in bytes"}, "mimeType": {"type": "string"}, "status": {"$ref": "#/components/schemas/DocumentStatus"}, "uploadedDate": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date"}, "downloadUrl": {"type": "string", "format": "uri"}, "isVerified": {"type": "boolean", "default": false}, "verifiedBy": {"type": "string", "format": "uuid"}, "verifiedDate": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "type", "name", "fileName", "fileSize", "mimeType", "status", "uploadedDate"]}, "DocumentType": {"type": "string", "enum": ["RESUME", "ID_PROOF", "ADDRESS_PROOF", "EDUCATION_CERTIFICATE", "EXPERIENCE_CERTIFICATE", "PASSPORT", "VISA", "DRIVING_LICENSE", "MEDICAL_CERTIFICATE", "OTHER"], "description": "Document type"}, "DocumentStatus": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "EXPIRED"], "description": "Document status"}, "ExpenseClaim": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "claimNumber": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "totalAmount": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "status": {"$ref": "#/components/schemas/ExpenseClaimStatus"}, "submittedDate": {"type": "string", "format": "date-time"}, "approvedDate": {"type": "string", "format": "date-time"}, "approvedBy": {"type": "string", "format": "uuid"}, "approverComments": {"type": "string"}, "expenses": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseItem"}}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/Attachment"}}}, "required": ["id", "employeeId", "claimNumber", "title", "totalAmount", "currency", "status", "submittedDate", "expenses"]}, "ExpenseItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "category": {"type": "string"}, "description": {"type": "string"}, "amount": {"type": "number", "format": "double"}, "date": {"type": "string", "format": "date"}, "receipt": {"$ref": "#/components/schemas/Attachment"}}, "required": ["id", "category", "description", "amount", "date"]}, "ExpenseClaimStatus": {"type": "string", "enum": ["DRAFT", "SUBMITTED", "UNDER_REVIEW", "APPROVED", "REJECTED", "PAID"], "description": "Expense claim status"}, "Attachment": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fileName": {"type": "string"}, "fileSize": {"type": "integer"}, "mimeType": {"type": "string"}, "uploadedDate": {"type": "string", "format": "date-time"}, "downloadUrl": {"type": "string", "format": "uri"}}, "required": ["id", "fileName", "fileSize", "mimeType", "downloadUrl"]}, "Payslip": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string", "format": "uuid"}, "payPeriod": {"type": "string", "description": "Pay period (YYYY-MM)"}, "payDate": {"type": "string", "format": "date"}, "grossSalary": {"type": "number", "format": "double"}, "netSalary": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "status": {"type": "string", "enum": ["DRAFT", "GENERATED", "APPROVED", "PAID"]}, "downloadUrl": {"type": "string", "format": "uri"}}, "required": ["id", "employeeId", "payPeriod", "payDate", "grossSalary", "netSalary", "currency", "status"]}, "UpdateProfileRequest": {"type": "object", "properties": {"personalInfo": {"$ref": "#/components/schemas/PersonalInfo"}, "contactInfo": {"$ref": "#/components/schemas/ContactInfo"}, "bankInfo": {"$ref": "#/components/schemas/BankInfo"}, "emergencyContacts": {"type": "array", "items": {"$ref": "#/components/schemas/EmergencyContact"}}}}, "UploadDocumentRequest": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "type": {"$ref": "#/components/schemas/DocumentType"}, "name": {"type": "string"}, "expiryDate": {"type": "string", "format": "date"}}, "required": ["file", "type", "name"]}, "CreateExpenseClaimRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "expenses": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseItem"}}}, "required": ["title", "expenses"]}, "UpdateExpenseClaimRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "expenses": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseItem"}}}}, "DocumentListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}, "required": ["data"]}, "ExpenseClaimListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseClaim"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PayslipListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Payslip"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}