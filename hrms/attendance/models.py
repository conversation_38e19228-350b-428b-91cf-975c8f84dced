"""
SQLAlchemy models for the Attendance microservice.

This module defines the database models for attendance tracking,
check-in/check-out records, and shift management.
"""

from datetime import datetime, time
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text, Integer, Time, Float
from sqlalchemy.types import TypeDecorator, CHAR
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
import uuid

# UUID type that works with both SQLite and PostgreSQL
class UUID(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(value)
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from hrms.shared.database import Base


class ShiftType(Base):
    """Shift type model for defining work shifts."""
    
    __tablename__ = "shift_types"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Basic information
    name = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Shift timing
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    break_duration = Column(Integer, default=0)  # Break duration in minutes
    
    # Working hours
    working_hours = Column(Float, nullable=False)  # Expected working hours per day
    
    # Grace periods
    late_grace_period = Column(Integer, default=15)  # Grace period for late arrival in minutes
    early_exit_grace_period = Column(Integer, default=15)  # Grace period for early exit in minutes
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    attendance_records = relationship("AttendanceRecord", back_populates="shift_type")
    
    def __repr__(self):
        return f"<ShiftType(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def to_dict(self) -> dict:
        """Convert shift type to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
            "end_time": self.end_time.strftime("%H:%M:%S") if self.end_time else None,
            "break_duration": self.break_duration,
            "working_hours": self.working_hours,
            "late_grace_period": self.late_grace_period,
            "early_exit_grace_period": self.early_exit_grace_period,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class AttendanceRecord(Base):
    """Attendance record model for tracking daily attendance."""
    
    __tablename__ = "attendance_records"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Employee and date
    employee_id = Column(UUID(), nullable=False, index=True)
    attendance_date = Column(DateTime(timezone=True), nullable=False, index=True)

    # Shift information
    shift_type_id = Column(UUID(), ForeignKey("shift_types.id"), nullable=True)
    
    # Check-in/Check-out times
    check_in_time = Column(DateTime(timezone=True), nullable=True)
    check_out_time = Column(DateTime(timezone=True), nullable=True)
    
    # Calculated fields
    working_hours = Column(Float, default=0.0)  # Actual working hours
    overtime_hours = Column(Float, default=0.0)  # Overtime hours
    break_hours = Column(Float, default=0.0)  # Break hours
    
    # Status fields
    status = Column(String(20), nullable=False, default="present")  # present, absent, late, half_day
    is_late = Column(Boolean, default=False)
    is_early_exit = Column(Boolean, default=False)
    late_minutes = Column(Integer, default=0)
    early_exit_minutes = Column(Integer, default=0)
    
    # Additional information
    location = Column(String(255), nullable=True)  # Check-in location
    device_info = Column(Text, nullable=True)  # Device information
    ip_address = Column(String(45), nullable=True)  # IP address
    notes = Column(Text, nullable=True)
    
    # Approval workflow
    is_approved = Column(Boolean, default=False)
    approved_by = Column(UUID(), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    shift_type = relationship("ShiftType", back_populates="attendance_records")
    
    def __repr__(self):
        return f"<AttendanceRecord(id={self.id}, employee_id={self.employee_id}, date={self.attendance_date})>"
    
    def to_dict(self) -> dict:
        """Convert attendance record to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "attendance_date": self.attendance_date.isoformat() if self.attendance_date else None,
            "shift_type_id": str(self.shift_type_id) if self.shift_type_id else None,
            "check_in_time": self.check_in_time.isoformat() if self.check_in_time else None,
            "check_out_time": self.check_out_time.isoformat() if self.check_out_time else None,
            "working_hours": self.working_hours,
            "overtime_hours": self.overtime_hours,
            "break_hours": self.break_hours,
            "status": self.status,
            "is_late": self.is_late,
            "is_early_exit": self.is_early_exit,
            "late_minutes": self.late_minutes,
            "early_exit_minutes": self.early_exit_minutes,
            "location": self.location,
            "device_info": self.device_info,
            "ip_address": self.ip_address,
            "notes": self.notes,
            "is_approved": self.is_approved,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approved_at": self.approved_at.isoformat() if self.approved_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Attendance(Base):
    """Legacy attendance model for compatibility."""
    
    __tablename__ = "attendance"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Employee and date
    employee_id = Column(UUID(), nullable=False, index=True)
    attendance_date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Status
    status = Column(String(20), nullable=False, default="present")
    
    # Times
    in_time = Column(DateTime(timezone=True), nullable=True)
    out_time = Column(DateTime(timezone=True), nullable=True)
    
    # Working hours
    working_hours = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    def __repr__(self):
        return f"<Attendance(id={self.id}, employee_id={self.employee_id}, date={self.attendance_date})>"
    
    def to_dict(self) -> dict:
        """Convert attendance to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "attendance_date": self.attendance_date.isoformat() if self.attendance_date else None,
            "status": self.status,
            "in_time": self.in_time.isoformat() if self.in_time else None,
            "out_time": self.out_time.isoformat() if self.out_time else None,
            "working_hours": self.working_hours,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class AttendanceRequest(Base):
    """Attendance request model for manual attendance corrections."""
    
    __tablename__ = "attendance_requests"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Employee and date
    employee_id = Column(UUID(), nullable=False, index=True)
    attendance_date = Column(DateTime(timezone=True), nullable=False, index=True)

    # Request details
    request_type = Column(String(50), nullable=False)  # check_in, check_out, correction
    requested_time = Column(DateTime(timezone=True), nullable=False)
    reason = Column(Text, nullable=False)

    # Approval workflow
    status = Column(String(20), nullable=False, default="pending")  # pending, approved, rejected
    approved_by = Column(UUID(), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    def __repr__(self):
        return f"<AttendanceRequest(id={self.id}, employee_id={self.employee_id}, type='{self.request_type}')>"
    
    def to_dict(self) -> dict:
        """Convert attendance request to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "attendance_date": self.attendance_date.isoformat() if self.attendance_date else None,
            "request_type": self.request_type,
            "requested_time": self.requested_time.isoformat() if self.requested_time else None,
            "reason": self.reason,
            "status": self.status,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approved_at": self.approved_at.isoformat() if self.approved_at else None,
            "rejection_reason": self.rejection_reason,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
