"""
Attendance microservice for oneHRMS.

This microservice handles all attendance-related operations including:
- Employee check-in/check-out
- Attendance tracking and reporting
- Shift management
- Leave management integration
- Overtime calculations
"""

__version__ = "1.0.0"
__author__ = "oneHRMS Team"

from .api import router as attendance_router
from .models import Attendance, AttendanceRecord, ShiftType
from .services import AttendanceService, ShiftService

__all__ = [
    "attendance_router",
    "Attendance",
    "AttendanceRecord",
    "ShiftType",
    "AttendanceService",
    "ShiftService",
]
