"""
Business logic services for the Attendance microservice.

This module contains the service layer that handles business logic
for attendance tracking, check-in/check-out, and shift management.
"""

from datetime import datetime, timedelta, date
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from hrms.shared.exceptions import NotFoundError, ConflictError, ValidationError
from hrms.shared.logging import get_logger
from hrms.shared.utils import paginate_query

from hrms.attendance.models import AttendanceRecord, ShiftType, Attendance, AttendanceRequest
from hrms.attendance.schemas import (
    CheckInRequest, CheckOutRequest,
    AttendanceRecordCreate, AttendanceRecordUpdate, AttendanceSearchParams,
    ShiftTypeCreate, ShiftTypeUpdate, ShiftTypeSearchParams,
    AttendanceSummary, AttendanceStats
)

logger = get_logger(__name__)


class AttendanceService:
    """Service class for attendance operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_in(self, check_in_data: CheckInRequest, ip_address: str = None) -> AttendanceRecord:
        """Process employee check-in."""
        logger.info("Processing check-in", employee_id=str(check_in_data.employee_id))
        
        # Use current time if not provided
        check_in_time = check_in_data.check_in_time or datetime.utcnow()
        attendance_date = check_in_time.date()
        
        # Check if employee already has attendance record for today
        existing_record = self.db.query(AttendanceRecord).filter(
            and_(
                AttendanceRecord.employee_id == check_in_data.employee_id,
                func.date(AttendanceRecord.attendance_date) == attendance_date
            )
        ).first()
        
        if existing_record and existing_record.check_in_time:
            raise ConflictError("Employee has already checked in today")
        
        # Get employee's shift type (this would typically come from employee service)
        # For now, we'll use a default shift or the one specified
        shift_type = None
        if existing_record and existing_record.shift_type_id:
            shift_type = self.db.query(ShiftType).filter(
                ShiftType.id == existing_record.shift_type_id
            ).first()
        
        if existing_record:
            # Update existing record
            existing_record.check_in_time = check_in_time
            existing_record.location = check_in_data.location
            existing_record.device_info = check_in_data.device_info
            existing_record.ip_address = ip_address
            existing_record.notes = check_in_data.notes
            existing_record.status = "present"
            
            # Calculate if late
            if shift_type:
                self._calculate_late_status(existing_record, shift_type)
            
            existing_record.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(existing_record)
            
            logger.info("Check-in updated", employee_id=str(check_in_data.employee_id))
            return existing_record
        else:
            # Create new attendance record
            attendance_record = AttendanceRecord(
                employee_id=check_in_data.employee_id,
                attendance_date=check_in_time,
                check_in_time=check_in_time,
                location=check_in_data.location,
                device_info=check_in_data.device_info,
                ip_address=ip_address,
                notes=check_in_data.notes,
                status="present"
            )
            
            # Calculate if late
            if shift_type:
                self._calculate_late_status(attendance_record, shift_type)
                attendance_record.shift_type_id = shift_type.id
            
            self.db.add(attendance_record)
            self.db.commit()
            self.db.refresh(attendance_record)
            
            logger.info("Check-in recorded", employee_id=str(check_in_data.employee_id))
            return attendance_record
    
    def check_out(self, check_out_data: CheckOutRequest, ip_address: str = None) -> AttendanceRecord:
        """Process employee check-out."""
        logger.info("Processing check-out", employee_id=str(check_out_data.employee_id))
        
        # Use current time if not provided
        check_out_time = check_out_data.check_out_time or datetime.utcnow()
        attendance_date = check_out_time.date()
        
        # Find today's attendance record
        attendance_record = self.db.query(AttendanceRecord).filter(
            and_(
                AttendanceRecord.employee_id == check_out_data.employee_id,
                func.date(AttendanceRecord.attendance_date) == attendance_date
            )
        ).first()
        
        if not attendance_record:
            raise NotFoundError("Attendance record", f"for employee {check_out_data.employee_id} on {attendance_date}")
        
        if not attendance_record.check_in_time:
            raise ValidationError("Cannot check out without checking in first")
        
        if attendance_record.check_out_time:
            raise ConflictError("Employee has already checked out today")
        
        # Update check-out information
        attendance_record.check_out_time = check_out_time
        if check_out_data.location:
            attendance_record.location = check_out_data.location
        if check_out_data.device_info:
            attendance_record.device_info = check_out_data.device_info
        if check_out_data.notes:
            attendance_record.notes = check_out_data.notes
        
        # Calculate working hours and other metrics
        self._calculate_working_hours(attendance_record)
        
        # Check for early exit
        if attendance_record.shift_type_id:
            shift_type = self.db.query(ShiftType).filter(
                ShiftType.id == attendance_record.shift_type_id
            ).first()
            if shift_type:
                self._calculate_early_exit_status(attendance_record, shift_type)
        
        attendance_record.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(attendance_record)
        
        logger.info("Check-out recorded", employee_id=str(check_out_data.employee_id))
        return attendance_record
    
    def get_attendance_record(self, record_id: UUID) -> AttendanceRecord:
        """Get attendance record by ID."""
        record = self.db.query(AttendanceRecord).filter(AttendanceRecord.id == record_id).first()
        if not record:
            raise NotFoundError("Attendance record", record_id)
        return record
    
    def get_employee_attendance_today(self, employee_id: UUID) -> Optional[AttendanceRecord]:
        """Get employee's attendance record for today."""
        today = date.today()
        return self.db.query(AttendanceRecord).filter(
            and_(
                AttendanceRecord.employee_id == employee_id,
                func.date(AttendanceRecord.attendance_date) == today
            )
        ).first()
    
    def search_attendance(
        self,
        search_params: AttendanceSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search attendance records with filters and pagination."""
        query = self.db.query(AttendanceRecord)
        
        # Apply filters
        if search_params.employee_id:
            query = query.filter(AttendanceRecord.employee_id == search_params.employee_id)
        
        if search_params.date_from:
            query = query.filter(AttendanceRecord.attendance_date >= search_params.date_from)
        
        if search_params.date_to:
            query = query.filter(AttendanceRecord.attendance_date <= search_params.date_to)
        
        if search_params.status:
            query = query.filter(AttendanceRecord.status == search_params.status)
        
        if search_params.shift_type_id:
            query = query.filter(AttendanceRecord.shift_type_id == search_params.shift_type_id)
        
        if search_params.is_approved is not None:
            query = query.filter(AttendanceRecord.is_approved == search_params.is_approved)
        
        if search_params.is_late is not None:
            query = query.filter(AttendanceRecord.is_late == search_params.is_late)
        
        # Order by attendance date descending
        query = query.order_by(AttendanceRecord.attendance_date.desc())
        
        return paginate_query(query, page, page_size)
    
    def get_attendance_summary(
        self,
        employee_id: UUID,
        start_date: date,
        end_date: date
    ) -> AttendanceSummary:
        """Get attendance summary for an employee in a date range."""
        records = self.db.query(AttendanceRecord).filter(
            and_(
                AttendanceRecord.employee_id == employee_id,
                func.date(AttendanceRecord.attendance_date) >= start_date,
                func.date(AttendanceRecord.attendance_date) <= end_date
            )
        ).all()
        
        total_days = len(records)
        present_days = len([r for r in records if r.status == "present"])
        absent_days = len([r for r in records if r.status == "absent"])
        late_days = len([r for r in records if r.is_late])
        half_days = len([r for r in records if r.status == "half_day"])
        
        total_working_hours = sum(r.working_hours for r in records)
        total_overtime_hours = sum(r.overtime_hours for r in records)
        average_working_hours = total_working_hours / total_days if total_days > 0 else 0
        
        return AttendanceSummary(
            employee_id=employee_id,
            total_days=total_days,
            present_days=present_days,
            absent_days=absent_days,
            late_days=late_days,
            half_days=half_days,
            total_working_hours=total_working_hours,
            total_overtime_hours=total_overtime_hours,
            average_working_hours=average_working_hours
        )
    
    def _calculate_late_status(self, record: AttendanceRecord, shift_type: ShiftType):
        """Calculate if employee is late based on shift type."""
        if not record.check_in_time or not shift_type.start_time:
            return
        
        # Convert shift start time to datetime for comparison
        shift_start = datetime.combine(record.check_in_time.date(), shift_type.start_time)
        grace_period = timedelta(minutes=shift_type.late_grace_period)
        
        if record.check_in_time > shift_start + grace_period:
            record.is_late = True
            record.late_minutes = int((record.check_in_time - shift_start).total_seconds() / 60)
            record.status = "late"
    
    def _calculate_early_exit_status(self, record: AttendanceRecord, shift_type: ShiftType):
        """Calculate if employee left early based on shift type."""
        if not record.check_out_time or not shift_type.end_time:
            return
        
        # Convert shift end time to datetime for comparison
        shift_end = datetime.combine(record.check_out_time.date(), shift_type.end_time)
        grace_period = timedelta(minutes=shift_type.early_exit_grace_period)
        
        if record.check_out_time < shift_end - grace_period:
            record.is_early_exit = True
            record.early_exit_minutes = int((shift_end - record.check_out_time).total_seconds() / 60)
    
    def _calculate_working_hours(self, record: AttendanceRecord):
        """Calculate working hours for an attendance record."""
        if not record.check_in_time or not record.check_out_time:
            return
        
        # Calculate total time
        total_time = record.check_out_time - record.check_in_time
        total_hours = total_time.total_seconds() / 3600
        
        # Subtract break time if applicable
        break_hours = record.break_hours or 0
        working_hours = max(0, total_hours - break_hours)
        
        record.working_hours = working_hours
        
        # Calculate overtime if shift type is available
        if record.shift_type_id:
            shift_type = self.db.query(ShiftType).filter(
                ShiftType.id == record.shift_type_id
            ).first()
            if shift_type and working_hours > shift_type.working_hours:
                record.overtime_hours = working_hours - shift_type.working_hours


class ShiftService:
    """Service class for shift type operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_shift_type(self, shift_data: ShiftTypeCreate) -> ShiftType:
        """Create a new shift type."""
        logger.info("Creating new shift type", code=shift_data.code)
        
        # Check if code already exists
        existing = self.db.query(ShiftType).filter(
            ShiftType.code == shift_data.code
        ).first()
        if existing:
            raise ConflictError(f"Shift type with code '{shift_data.code}' already exists")
        
        shift_type = ShiftType(**shift_data.dict())
        self.db.add(shift_type)
        self.db.commit()
        self.db.refresh(shift_type)
        
        logger.info("Shift type created successfully", code=shift_type.code, id=str(shift_type.id))
        return shift_type
    
    def get_shift_type(self, shift_type_id: UUID) -> ShiftType:
        """Get shift type by ID."""
        shift_type = self.db.query(ShiftType).filter(ShiftType.id == shift_type_id).first()
        if not shift_type:
            raise NotFoundError("Shift type", shift_type_id)
        return shift_type
    
    def update_shift_type(self, shift_type_id: UUID, shift_data: ShiftTypeUpdate) -> ShiftType:
        """Update a shift type."""
        logger.info("Updating shift type", shift_type_id=str(shift_type_id))
        
        shift_type = self.get_shift_type(shift_type_id)
        
        # Check code uniqueness if being updated
        if shift_data.code and shift_data.code != shift_type.code:
            existing = self.db.query(ShiftType).filter(
                and_(ShiftType.code == shift_data.code, ShiftType.id != shift_type_id)
            ).first()
            if existing:
                raise ConflictError(f"Shift type with code '{shift_data.code}' already exists")
        
        # Update fields
        update_data = shift_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(shift_type, field, value)
        
        shift_type.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(shift_type)
        
        logger.info("Shift type updated successfully", shift_type_id=str(shift_type_id))
        return shift_type
    
    def delete_shift_type(self, shift_type_id: UUID) -> bool:
        """Delete a shift type (soft delete)."""
        logger.info("Deleting shift type", shift_type_id=str(shift_type_id))
        
        shift_type = self.get_shift_type(shift_type_id)
        
        # Check if shift type is being used
        usage_count = self.db.query(AttendanceRecord).filter(
            AttendanceRecord.shift_type_id == shift_type_id
        ).count()
        if usage_count > 0:
            raise ConflictError(f"Cannot delete shift type with {usage_count} attendance records")
        
        shift_type.is_active = False
        shift_type.updated_at = datetime.utcnow()
        self.db.commit()
        
        logger.info("Shift type deleted successfully", shift_type_id=str(shift_type_id))
        return True
    
    def search_shift_types(
        self,
        search_params: ShiftTypeSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search shift types with filters and pagination."""
        query = self.db.query(ShiftType)
        
        # Apply filters
        if search_params.search:
            search_term = f"%{search_params.search}%"
            query = query.filter(
                or_(
                    ShiftType.name.ilike(search_term),
                    ShiftType.code.ilike(search_term)
                )
            )
        
        if search_params.is_active is not None:
            query = query.filter(ShiftType.is_active == search_params.is_active)
        
        # Order by name
        query = query.order_by(ShiftType.name)
        
        return paginate_query(query, page, page_size)
