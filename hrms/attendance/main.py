"""
Main application file for the Attendance microservice.

This module creates and configures the FastAPI application for the
Attendance microservice with all necessary middleware and dependencies.
"""

import os
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from hrms.shared.database import init_database, check_database_health
from hrms.shared.exceptions import HRMSException, hrms_exception_handler
from hrms.shared.logging import setup_logging, get_logger

from hrms.attendance.api import router as attendance_router

# Setup logging
setup_logging("attendance-service")
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Attendance microservice...")
    
    try:
        # Initialize database
        init_database()
        logger.info("Database initialized successfully")
        
        # Check database health
        health = check_database_health()
        if health.get("database") != "healthy":
            logger.error("Database health check failed", health=health)
            raise Exception("Database is not healthy")
        
        logger.info("Attendance microservice started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start Attendance microservice: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Attendance microservice...")


# Create FastAPI application
app = FastAPI(
    title="Attendance Microservice",
    description="Attendance tracking microservice for oneHRMS",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware
trusted_hosts = os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(TrustedHostMiddleware, allowed_hosts=trusted_hosts)


# Exception handlers
@app.exception_handler(HRMSException)
async def hrms_exception_handler_middleware(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    return hrms_exception_handler(request, exc)


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": "HTTP_ERROR"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )


# Include routers
app.include_router(attendance_router)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "attendance",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        db_health = check_database_health()
        
        return {
            "service": "attendance",
            "status": "healthy",
            "version": "1.0.0",
            "database": db_health,
            "environment": os.getenv("ENVIRONMENT", "development")
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "service": "attendance",
                "status": "unhealthy",
                "error": str(e)
            }
        )


# Metrics endpoint (for monitoring)
@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    return {
        "service": "attendance",
        "version": "1.0.0",
        "uptime": "N/A",  # TODO: Implement uptime tracking
        "requests_total": "N/A",  # TODO: Implement request counting
        "database_connections": "N/A"  # TODO: Implement connection monitoring
    }


if __name__ == "__main__":
    import uvicorn
    
    # Configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8002"))
    reload = os.getenv("ENVIRONMENT", "development") == "development"
    
    logger.info(f"Starting Attendance microservice on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_config=None  # Use our custom logging
    )
