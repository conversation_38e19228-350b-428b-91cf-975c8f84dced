"""
FastAPI router for Attendance microservice endpoints.

This module defines all REST API endpoints for attendance tracking,
check-in/check-out, and shift management operations.
"""

from datetime import date, datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.orm import Session

from hrms.shared.auth import get_current_user, require_permissions
from hrms.shared.database import get_db_session
from hrms.shared.exceptions import HRMSException, hrms_exception_handler
from hrms.shared.models import User
from hrms.shared.logging import get_logger, log_api_request

from hrms.attendance.schemas import (
    # Check-in/Check-out schemas
    CheckInRequest, CheckOutRequest, CheckInResponse, CheckOutResponse,

    # Attendance record schemas
    AttendanceRecord, AttendanceRecordCreate, AttendanceRecordUpdate,
    AttendanceRecordResponse, AttendanceRecordListResponse, AttendanceSearchParams,

    # Shift type schemas
    ShiftType, ShiftTypeCreate, ShiftTypeUpdate, ShiftTypeSearchParams,
    ShiftTypeResponse, ShiftTypeListResponse,

    # Statistics schemas
    AttendanceSummary, AttendanceStats,
)
from hrms.attendance.services import AttendanceService, ShiftService

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/attendance", tags=["attendance"])


# Check-in/Check-out Endpoints
@router.post("/check-in", response_model=CheckInResponse, status_code=status.HTTP_201_CREATED)
@log_api_request()
async def check_in(
    check_in_data: CheckInRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:write"])),
    db: Session = Depends(get_db_session)
):
    """Process employee check-in."""
    try:
        service = AttendanceService(db)
        
        # Get client IP address
        ip_address = request.client.host if request.client else None
        
        attendance_record = service.check_in(check_in_data, ip_address)
        
        return CheckInResponse(
            message="Check-in recorded successfully",
            data=AttendanceRecord.from_orm(attendance_record)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.post("/check-out", response_model=CheckOutResponse)
@log_api_request()
async def check_out(
    check_out_data: CheckOutRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:write"])),
    db: Session = Depends(get_db_session)
):
    """Process employee check-out."""
    try:
        service = AttendanceService(db)
        
        # Get client IP address
        ip_address = request.client.host if request.client else None
        
        attendance_record = service.check_out(check_out_data, ip_address)
        
        return CheckOutResponse(
            message="Check-out recorded successfully",
            data=AttendanceRecord.from_orm(attendance_record)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/employee/{employee_id}/today", response_model=AttendanceRecordResponse)
@log_api_request()
async def get_employee_attendance_today(
    employee_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:read"])),
    db: Session = Depends(get_db_session)
):
    """Get employee's attendance record for today."""
    try:
        service = AttendanceService(db)
        attendance_record = service.get_employee_attendance_today(employee_id)
        
        if not attendance_record:
            return AttendanceRecordResponse(
                message="No attendance record found for today",
                data=None
            )
        
        return AttendanceRecordResponse(
            message="Attendance record retrieved successfully",
            data=AttendanceRecord.from_orm(attendance_record)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Attendance Record Endpoints
@router.get("/{record_id}", response_model=AttendanceRecordResponse)
@log_api_request()
async def get_attendance_record(
    record_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:read"])),
    db: Session = Depends(get_db_session)
):
    """Get attendance record by ID."""
    try:
        service = AttendanceService(db)
        attendance_record = service.get_attendance_record(record_id)
        
        return AttendanceRecordResponse(
            message="Attendance record retrieved successfully",
            data=AttendanceRecord.from_orm(attendance_record)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/", response_model=AttendanceRecordListResponse)
@log_api_request()
async def search_attendance(
    employee_id: Optional[UUID] = Query(None, description="Filter by employee"),
    date_from: Optional[datetime] = Query(None, description="Filter by date from"),
    date_to: Optional[datetime] = Query(None, description="Filter by date to"),
    status: Optional[str] = Query(None, description="Filter by status"),
    shift_type_id: Optional[UUID] = Query(None, description="Filter by shift type"),
    is_approved: Optional[bool] = Query(None, description="Filter by approval status"),
    is_late: Optional[bool] = Query(None, description="Filter by late status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:read"])),
    db: Session = Depends(get_db_session)
):
    """Search attendance records with filters and pagination."""
    try:
        search_params = AttendanceSearchParams(
            employee_id=employee_id,
            date_from=date_from,
            date_to=date_to,
            status=status,
            shift_type_id=shift_type_id,
            is_approved=is_approved,
            is_late=is_late
        )
        
        service = AttendanceService(db)
        result = service.search_attendance(search_params, page, page_size)
        
        attendance_records = [AttendanceRecord.from_orm(record) for record in result["data"]]
        
        return AttendanceRecordListResponse(
            message="Attendance records retrieved successfully",
            data=attendance_records,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/employee/{employee_id}/summary")
@log_api_request()
async def get_attendance_summary(
    employee_id: UUID,
    start_date: date = Query(..., description="Start date for summary"),
    end_date: date = Query(..., description="End date for summary"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["attendance:read"])),
    db: Session = Depends(get_db_session)
):
    """Get attendance summary for an employee in a date range."""
    try:
        service = AttendanceService(db)
        summary = service.get_attendance_summary(employee_id, start_date, end_date)
        
        return {
            "success": True,
            "message": "Attendance summary retrieved successfully",
            "data": summary
        }
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Shift Type Endpoints
@router.post("/shift-types/", response_model=ShiftTypeResponse, status_code=status.HTTP_201_CREATED)
@log_api_request()
async def create_shift_type(
    shift_data: ShiftTypeCreate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["shift:write"])),
    db: Session = Depends(get_db_session)
):
    """Create a new shift type."""
    try:
        service = ShiftService(db)
        shift_type = service.create_shift_type(shift_data)
        
        return ShiftTypeResponse(
            message="Shift type created successfully",
            data=ShiftType.from_orm(shift_type)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/shift-types/{shift_type_id}", response_model=ShiftTypeResponse)
@log_api_request()
async def get_shift_type(
    shift_type_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["shift:read"])),
    db: Session = Depends(get_db_session)
):
    """Get shift type by ID."""
    try:
        service = ShiftService(db)
        shift_type = service.get_shift_type(shift_type_id)
        
        return ShiftTypeResponse(
            message="Shift type retrieved successfully",
            data=ShiftType.from_orm(shift_type)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.put("/shift-types/{shift_type_id}", response_model=ShiftTypeResponse)
@log_api_request()
async def update_shift_type(
    shift_type_id: UUID,
    shift_data: ShiftTypeUpdate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["shift:write"])),
    db: Session = Depends(get_db_session)
):
    """Update a shift type."""
    try:
        service = ShiftService(db)
        shift_type = service.update_shift_type(shift_type_id, shift_data)
        
        return ShiftTypeResponse(
            message="Shift type updated successfully",
            data=ShiftType.from_orm(shift_type)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.delete("/shift-types/{shift_type_id}", status_code=status.HTTP_204_NO_CONTENT)
@log_api_request()
async def delete_shift_type(
    shift_type_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["shift:delete"])),
    db: Session = Depends(get_db_session)
):
    """Delete a shift type (soft delete)."""
    try:
        service = ShiftService(db)
        service.delete_shift_type(shift_type_id)
        return None
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/shift-types/", response_model=ShiftTypeListResponse)
@log_api_request()
async def search_shift_types(
    search: Optional[str] = Query(None, description="Search term"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["shift:read"])),
    db: Session = Depends(get_db_session)
):
    """Search shift types with filters and pagination."""
    try:
        search_params = ShiftTypeSearchParams(
            search=search,
            is_active=is_active
        )
        
        service = ShiftService(db)
        result = service.search_shift_types(search_params, page, page_size)
        
        shift_types = [ShiftType.from_orm(shift) for shift in result["data"]]
        
        return ShiftTypeListResponse(
            message="Shift types retrieved successfully",
            data=shift_types,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint for the Attendance microservice."""
    return {
        "service": "attendance",
        "status": "healthy",
        "version": "1.0.0"
    }
