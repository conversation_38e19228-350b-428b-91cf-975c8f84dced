"""
Pydantic schemas for the Attendance microservice.

This module defines request/response schemas for attendance-related operations
using Pydantic for data validation and serialization.
"""

from datetime import datetime, time
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from hrms.shared.models import BaseResponse, PaginatedResponse


# Shift Type Schemas
class ShiftTypeBase(BaseModel):
    """Base shift type schema."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Shift name")
    code: str = Field(..., min_length=1, max_length=20, description="Shift code")
    description: Optional[str] = Field(None, description="Shift description")
    start_time: time = Field(..., description="Shift start time")
    end_time: time = Field(..., description="Shift end time")
    break_duration: int = Field(default=0, ge=0, description="Break duration in minutes")
    working_hours: float = Field(..., gt=0, le=24, description="Expected working hours")
    late_grace_period: int = Field(default=15, ge=0, description="Late grace period in minutes")
    early_exit_grace_period: int = Field(default=15, ge=0, description="Early exit grace period in minutes")


class ShiftTypeCreate(ShiftTypeBase):
    """Schema for creating a shift type."""
    pass


class ShiftTypeUpdate(BaseModel):
    """Schema for updating a shift type."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    break_duration: Optional[int] = Field(None, ge=0)
    working_hours: Optional[float] = Field(None, gt=0, le=24)
    late_grace_period: Optional[int] = Field(None, ge=0)
    early_exit_grace_period: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None


class ShiftType(ShiftTypeBase):
    """Schema for shift type response."""
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Attendance Record Schemas
class AttendanceRecordBase(BaseModel):
    """Base attendance record schema."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    attendance_date: datetime = Field(..., description="Attendance date")
    shift_type_id: Optional[UUID] = Field(None, description="Shift type ID")
    location: Optional[str] = Field(None, max_length=255, description="Check-in location")
    notes: Optional[str] = Field(None, description="Additional notes")


class CheckInRequest(BaseModel):
    """Schema for check-in request."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    check_in_time: Optional[datetime] = Field(None, description="Check-in time (defaults to now)")
    location: Optional[str] = Field(None, max_length=255, description="Check-in location")
    device_info: Optional[str] = Field(None, description="Device information")
    notes: Optional[str] = Field(None, description="Additional notes")


class CheckOutRequest(BaseModel):
    """Schema for check-out request."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    check_out_time: Optional[datetime] = Field(None, description="Check-out time (defaults to now)")
    location: Optional[str] = Field(None, max_length=255, description="Check-out location")
    device_info: Optional[str] = Field(None, description="Device information")
    notes: Optional[str] = Field(None, description="Additional notes")


class AttendanceRecordCreate(AttendanceRecordBase):
    """Schema for creating an attendance record."""
    
    check_in_time: Optional[datetime] = None
    check_out_time: Optional[datetime] = None
    status: str = Field(default="present", description="Attendance status")


class AttendanceRecordUpdate(BaseModel):
    """Schema for updating an attendance record."""
    
    shift_type_id: Optional[UUID] = None
    check_in_time: Optional[datetime] = None
    check_out_time: Optional[datetime] = None
    status: Optional[str] = None
    location: Optional[str] = Field(None, max_length=255)
    notes: Optional[str] = None
    is_approved: Optional[bool] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['present', 'absent', 'late', 'half_day']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v


class AttendanceRecord(AttendanceRecordBase):
    """Schema for attendance record response."""
    
    id: UUID
    check_in_time: Optional[datetime]
    check_out_time: Optional[datetime]
    working_hours: float
    overtime_hours: float
    break_hours: float
    status: str
    is_late: bool
    is_early_exit: bool
    late_minutes: int
    early_exit_minutes: int
    device_info: Optional[str]
    ip_address: Optional[str]
    is_approved: bool
    approved_by: Optional[UUID]
    approved_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Attendance Request Schemas
class AttendanceRequestBase(BaseModel):
    """Base attendance request schema."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    attendance_date: datetime = Field(..., description="Attendance date")
    request_type: str = Field(..., description="Request type")
    requested_time: datetime = Field(..., description="Requested time")
    reason: str = Field(..., min_length=1, description="Reason for request")
    
    @validator('request_type')
    def validate_request_type(cls, v):
        allowed_types = ['check_in', 'check_out', 'correction']
        if v not in allowed_types:
            raise ValueError(f'Request type must be one of: {", ".join(allowed_types)}')
        return v


class AttendanceRequestCreate(AttendanceRequestBase):
    """Schema for creating an attendance request."""
    pass


class AttendanceRequestUpdate(BaseModel):
    """Schema for updating an attendance request."""
    
    status: str = Field(..., description="Request status")
    rejection_reason: Optional[str] = Field(None, description="Rejection reason")
    
    @validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['pending', 'approved', 'rejected']
        if v not in allowed_statuses:
            raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v


class AttendanceRequest(AttendanceRequestBase):
    """Schema for attendance request response."""
    
    id: UUID
    status: str
    approved_by: Optional[UUID]
    approved_at: Optional[datetime]
    rejection_reason: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Response Schemas
class ShiftTypeResponse(BaseResponse):
    """Response schema for single shift type operations."""
    
    data: ShiftType


class ShiftTypeListResponse(PaginatedResponse[ShiftType]):
    """Response schema for shift type list operations."""
    pass


class AttendanceRecordResponse(BaseResponse):
    """Response schema for single attendance record operations."""
    
    data: AttendanceRecord


class AttendanceRecordListResponse(PaginatedResponse[AttendanceRecord]):
    """Response schema for attendance record list operations."""
    pass


class AttendanceRequestResponse(BaseResponse):
    """Response schema for single attendance request operations."""
    
    data: AttendanceRequest


class AttendanceRequestListResponse(PaginatedResponse[AttendanceRequest]):
    """Response schema for attendance request list operations."""
    pass


# Search and Filter Schemas
class AttendanceSearchParams(BaseModel):
    """Schema for attendance search parameters."""
    
    employee_id: Optional[UUID] = Field(None, description="Filter by employee")
    date_from: Optional[datetime] = Field(None, description="Filter by date from")
    date_to: Optional[datetime] = Field(None, description="Filter by date to")
    status: Optional[str] = Field(None, description="Filter by status")
    shift_type_id: Optional[UUID] = Field(None, description="Filter by shift type")
    is_approved: Optional[bool] = Field(None, description="Filter by approval status")
    is_late: Optional[bool] = Field(None, description="Filter by late status")


class ShiftTypeSearchParams(BaseModel):
    """Schema for shift type search parameters."""
    
    search: Optional[str] = Field(None, description="Search term for name or code")
    is_active: Optional[bool] = Field(None, description="Filter by active status")


class AttendanceRequestSearchParams(BaseModel):
    """Schema for attendance request search parameters."""
    
    employee_id: Optional[UUID] = Field(None, description="Filter by employee")
    request_type: Optional[str] = Field(None, description="Filter by request type")
    status: Optional[str] = Field(None, description="Filter by status")
    date_from: Optional[datetime] = Field(None, description="Filter by date from")
    date_to: Optional[datetime] = Field(None, description="Filter by date to")


# Statistics and Reports
class AttendanceSummary(BaseModel):
    """Schema for attendance summary."""
    
    employee_id: UUID
    total_days: int
    present_days: int
    absent_days: int
    late_days: int
    half_days: int
    total_working_hours: float
    total_overtime_hours: float
    average_working_hours: float


class AttendanceStats(BaseModel):
    """Schema for attendance statistics."""
    
    period_start: datetime
    period_end: datetime
    total_employees: int
    total_present: int
    total_absent: int
    total_late: int
    attendance_percentage: float
    average_working_hours: float


class CheckInResponse(BaseResponse):
    """Response schema for check-in operations."""
    
    data: AttendanceRecord


class CheckOutResponse(BaseResponse):
    """Response schema for check-out operations."""
    
    data: AttendanceRecord
