[tool:pytest]
# pytest configuration for oneHRMS

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    payroll: Payroll module tests
    recruitment: Recruitment module tests
    attendance: Attendance module tests
    ess: Employee Self Service tests
    employee: Employee management tests
    api: API endpoint tests
    auth: Authentication tests
    tenant: Multi-tenancy tests

# Minimum version
minversion = 6.0
