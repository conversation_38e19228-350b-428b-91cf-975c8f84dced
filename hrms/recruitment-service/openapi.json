{"openapi": "3.0.3", "info": {"title": "Recruitment Service API", "description": "API for managing job postings, applications, candidate management, and recruitment workflow in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/recruitment", "description": "Production server"}, {"url": "http://localhost:8105/recruitment", "description": "Development server"}], "paths": {"/job-postings": {"get": {"summary": "Get job postings", "description": "Retrieve job postings with filtering and pagination", "operationId": "getJobPostings", "tags": ["Job Postings"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "description": "Filter by job posting status", "required": false, "schema": {"$ref": "#/components/schemas/JobPostingStatus"}}, {"name": "department", "in": "query", "description": "Filter by department", "required": false, "schema": {"type": "string"}}, {"name": "location", "in": "query", "description": "Filter by job location", "required": false, "schema": {"type": "string"}}, {"name": "jobType", "in": "query", "description": "Filter by job type", "required": false, "schema": {"$ref": "#/components/schemas/JobType"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPostingListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create job posting", "description": "Create a new job posting", "operationId": "createJobPosting", "tags": ["Job Postings"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobPostingRequest"}}}}, "responses": {"201": {"description": "Job posting created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPosting"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-postings/{jobPostingId}": {"get": {"summary": "Get job posting by ID", "description": "Retrieve a specific job posting by its ID", "operationId": "getJobPostingById", "tags": ["Job Postings"], "parameters": [{"name": "jobPostingId", "in": "path", "description": "Job posting ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPosting"}}}}, "404": {"description": "Job posting not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update job posting", "description": "Update an existing job posting", "operationId": "updateJobPosting", "tags": ["Job Postings"], "parameters": [{"name": "jobPostingId", "in": "path", "description": "Job posting ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJobPostingRequest"}}}}, "responses": {"200": {"description": "Job posting updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPosting"}}}}, "404": {"description": "Job posting not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-requisitions": {"get": {"summary": "Get job requisitions", "description": "Retrieve job requisitions with filtering and pagination", "operationId": "getJobRequisitions", "tags": ["Job Requisitions"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "status", "in": "query", "description": "Filter by requisition status", "required": false, "schema": {"$ref": "#/components/schemas/RequisitionStatus"}}, {"name": "priority", "in": "query", "description": "Filter by requisition priority", "required": false, "schema": {"$ref": "#/components/schemas/RequisitionPriority"}}, {"name": "department", "in": "query", "description": "Filter by department", "required": false, "schema": {"type": "string"}}, {"name": "requestedBy", "in": "query", "description": "Filter by requesting user", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisitionListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create job requisition", "description": "Create a new job requisition", "operationId": "createJobRequisition", "tags": ["Job Requisitions"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobRequisitionRequest"}}}}, "responses": {"201": {"description": "Job requisition created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-requisitions/{requisitionId}": {"get": {"summary": "Get job requisition by ID", "description": "Retrieve a specific job requisition by its ID", "operationId": "getJobRequisitionById", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update job requisition", "description": "Update an existing job requisition", "operationId": "updateJobRequisition", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJobRequisitionRequest"}}}}, "responses": {"200": {"description": "Job requisition updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Delete job requisition", "description": "Delete a job requisition", "operationId": "deleteJobRequisition", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Job requisition deleted successfully"}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-requisitions/{requisitionId}/approve": {"post": {"summary": "Approve job requisition", "description": "Approve a job requisition by HR or Finance approver", "operationId": "approveJobRequisition", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveRequisitionRequest"}}}}, "responses": {"200": {"description": "Job requisition approved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-requisitions/{requisitionId}/reject": {"post": {"summary": "Reject job requisition", "description": "Reject a job requisition with reason", "operationId": "rejectJobRequisition", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectRequisitionRequest"}}}}, "responses": {"200": {"description": "Job requisition rejected successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/job-requisitions/{requisitionId}/submit": {"post": {"summary": "Submit job requisition", "description": "Submit a job requisition for approval", "operationId": "submitJobRequisition", "tags": ["Job Requisitions"], "parameters": [{"name": "requisitionId", "in": "path", "description": "Job requisition ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Job requisition submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequisition"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Job requisition not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/applications": {"get": {"summary": "Get job applications", "description": "Retrieve job applications with filtering and pagination", "operationId": "getJobApplications", "tags": ["Applications"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "jobPostingId", "in": "query", "description": "Filter by job posting ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by application status", "required": false, "schema": {"$ref": "#/components/schemas/ApplicationStatus"}}, {"name": "candidateId", "in": "query", "description": "Filter by candidate ID", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplicationListResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create job application", "description": "Submit a new job application", "operationId": "createJobApplication", "tags": ["Applications"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobApplicationRequest"}}}}, "responses": {"201": {"description": "Job application created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplication"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/applications/{applicationId}": {"get": {"summary": "Get job application by ID", "description": "Retrieve a specific job application by its ID", "operationId": "getJobApplicationById", "tags": ["Applications"], "parameters": [{"name": "applicationId", "in": "path", "description": "Job application ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplication"}}}}, "404": {"description": "Job application not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update job application status", "description": "Update the status of a job application", "operationId": "updateJobApplicationStatus", "tags": ["Applications"], "parameters": [{"name": "applicationId", "in": "path", "description": "Job application ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateApplicationStatusRequest"}}}}, "responses": {"200": {"description": "Job application status updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplication"}}}}, "404": {"description": "Job application not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/candidates": {"get": {"summary": "Get candidates", "description": "Retrieve candidates with filtering and pagination", "operationId": "getCandidates", "tags": ["Candidates"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "search", "in": "query", "description": "Search term for candidate name or email", "required": false, "schema": {"type": "string"}}, {"name": "skills", "in": "query", "description": "Filter by skills (comma-separated)", "required": false, "schema": {"type": "string"}}, {"name": "experience", "in": "query", "description": "Filter by minimum years of experience", "required": false, "schema": {"type": "integer", "minimum": 0}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CandidateListResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create candidate", "description": "Create a new candidate profile", "operationId": "createCandidate", "tags": ["Candidates"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCandidateRequest"}}}}, "responses": {"201": {"description": "Candidate created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Candidate"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/candidates/{candidateId}": {"get": {"summary": "Get candidate by ID", "description": "Retrieve a specific candidate by their ID", "operationId": "getCandidateById", "tags": ["Candidates"], "parameters": [{"name": "candidateId", "in": "path", "description": "Candidate ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Candidate"}}}}, "404": {"description": "Candidate not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"JobPosting": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique job posting identifier"}, "title": {"type": "string", "description": "Job title"}, "description": {"type": "string", "description": "Job description"}, "department": {"type": "string", "description": "Department"}, "location": {"type": "string", "description": "Job location"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}, "description": "Job requirements"}, "responsibilities": {"type": "array", "items": {"type": "string"}, "description": "Job responsibilities"}, "skills": {"type": "array", "items": {"type": "string"}, "description": "Required skills"}, "benefits": {"type": "array", "items": {"type": "string"}, "description": "Job benefits"}, "status": {"$ref": "#/components/schemas/JobPostingStatus"}, "postedDate": {"type": "string", "format": "date-time", "description": "Date when job was posted"}, "closingDate": {"type": "string", "format": "date", "description": "Application closing date"}, "postedBy": {"type": "string", "format": "uuid", "description": "ID of the person who posted the job"}, "applicationCount": {"type": "integer", "description": "Number of applications received"}, "isRemote": {"type": "boolean", "description": "Whether the job is remote"}, "isUrgent": {"type": "boolean", "description": "Whether the job is urgent"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "title", "description", "department", "location", "jobType", "experienceLevel", "status", "postedDate"]}, "JobType": {"type": "string", "enum": ["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP", "FREELANCE"], "description": "Job type"}, "ExperienceLevel": {"type": "string", "enum": ["ENTRY_LEVEL", "MID_LEVEL", "SENIOR_LEVEL", "EXECUTIVE"], "description": "Experience level required"}, "JobPostingStatus": {"type": "string", "enum": ["DRAFT", "ACTIVE", "PAUSED", "CLOSED", "CANCELLED"], "description": "Job posting status"}, "SalaryRange": {"type": "object", "properties": {"min": {"type": "number", "format": "double", "description": "Minimum salary"}, "max": {"type": "number", "format": "double", "description": "Maximum salary"}, "currency": {"type": "string", "description": "Currency code"}, "period": {"type": "string", "enum": ["HOURLY", "MONTHLY", "YEARLY"], "description": "Salary period"}}, "required": ["min", "max", "currency", "period"]}, "Candidate": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique candidate identifier"}, "firstName": {"type": "string", "description": "First name"}, "lastName": {"type": "string", "description": "Last name"}, "email": {"type": "string", "format": "email", "description": "Email address"}, "phone": {"type": "string", "description": "Phone number"}, "address": {"$ref": "#/components/schemas/Address"}, "dateOfBirth": {"type": "string", "format": "date", "description": "Date of birth"}, "summary": {"type": "string", "description": "Professional summary"}, "experience": {"type": "array", "items": {"$ref": "#/components/schemas/WorkExperience"}, "description": "Work experience"}, "education": {"type": "array", "items": {"$ref": "#/components/schemas/Education"}, "description": "Educational background"}, "skills": {"type": "array", "items": {"type": "string"}, "description": "Skills"}, "certifications": {"type": "array", "items": {"$ref": "#/components/schemas/Certification"}, "description": "Certifications"}, "languages": {"type": "array", "items": {"$ref": "#/components/schemas/Language"}, "description": "Languages"}, "resumeUrl": {"type": "string", "format": "uri", "description": "Resume file URL"}, "portfolioUrl": {"type": "string", "format": "uri", "description": "Portfolio URL"}, "linkedinUrl": {"type": "string", "format": "uri", "description": "LinkedIn profile URL"}, "totalExperience": {"type": "number", "format": "double", "description": "Total years of experience"}, "currentSalary": {"type": "number", "format": "double", "description": "Current salary"}, "expectedSalary": {"type": "number", "format": "double", "description": "Expected salary"}, "noticePeriod": {"type": "integer", "description": "Notice period in days"}, "isAvailable": {"type": "boolean", "description": "Whether candidate is available"}, "source": {"type": "string", "description": "Source of candidate (e.g., LinkedIn, referral)"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "firstName", "lastName", "email", "phone"]}, "Address": {"type": "object", "properties": {"street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}}, "required": ["city", "state", "country"]}, "WorkExperience": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "company": {"type": "string"}, "position": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "isCurrent": {"type": "boolean"}, "description": {"type": "string"}, "achievements": {"type": "array", "items": {"type": "string"}}, "technologies": {"type": "array", "items": {"type": "string"}}}, "required": ["company", "position", "startDate"]}, "Education": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "institution": {"type": "string"}, "degree": {"type": "string"}, "fieldOfStudy": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "grade": {"type": "string"}, "description": {"type": "string"}}, "required": ["institution", "degree", "fieldOfStudy"]}, "Certification": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "issuer": {"type": "string"}, "issueDate": {"type": "string", "format": "date"}, "expiryDate": {"type": "string", "format": "date"}, "credentialId": {"type": "string"}, "credentialUrl": {"type": "string", "format": "uri"}}, "required": ["name", "issuer", "issueDate"]}, "Language": {"type": "object", "properties": {"language": {"type": "string"}, "proficiency": {"type": "string", "enum": ["BASIC", "INTERMEDIATE", "ADVANCED", "NATIVE"]}}, "required": ["language", "proficiency"]}, "JobApplication": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique application identifier"}, "jobPostingId": {"type": "string", "format": "uuid", "description": "Job posting ID"}, "jobPosting": {"$ref": "#/components/schemas/JobPosting"}, "candidateId": {"type": "string", "format": "uuid", "description": "Candidate ID"}, "candidate": {"$ref": "#/components/schemas/Candidate"}, "status": {"$ref": "#/components/schemas/ApplicationStatus"}, "appliedDate": {"type": "string", "format": "date-time", "description": "Date when application was submitted"}, "coverLetter": {"type": "string", "description": "Cover letter"}, "resumeUrl": {"type": "string", "format": "uri", "description": "Resume file URL"}, "additionalDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}, "description": "Additional documents"}, "interviews": {"type": "array", "items": {"$ref": "#/components/schemas/Interview"}, "description": "Interview records"}, "notes": {"type": "array", "items": {"$ref": "#/components/schemas/ApplicationNote"}, "description": "Application notes"}, "rating": {"type": "number", "format": "double", "minimum": 1, "maximum": 5, "description": "Application rating (1-5)"}, "rejectionReason": {"type": "string", "description": "Reason for rejection"}, "offerDetails": {"$ref": "#/components/schemas/OfferDetails"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "jobPostingId", "candidateId", "status", "appliedDate"]}, "ApplicationStatus": {"type": "string", "enum": ["APPLIED", "SCREENING", "INTERVIEW_SCHEDULED", "INTERVIEWED", "UNDER_REVIEW", "SHORTLISTED", "OFFER_EXTENDED", "OFFER_ACCEPTED", "OFFER_DECLINED", "REJECTED", "WITHDRAWN"], "description": "Application status"}, "Document": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "type": {"type": "string"}, "uploadedDate": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "url", "type"]}, "Interview": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"type": "string", "enum": ["PHONE", "VIDEO", "IN_PERSON", "TECHNICAL", "HR"]}, "scheduledDate": {"type": "string", "format": "date-time"}, "duration": {"type": "integer", "description": "Duration in minutes"}, "interviewers": {"type": "array", "items": {"$ref": "#/components/schemas/Interviewer"}}, "location": {"type": "string"}, "meetingLink": {"type": "string", "format": "uri"}, "status": {"type": "string", "enum": ["SCHEDULED", "COMPLETED", "CANCELLED", "RESCHEDULED"]}, "feedback": {"type": "string"}, "rating": {"type": "number", "format": "double", "minimum": 1, "maximum": 5}, "recommendation": {"type": "string", "enum": ["HIRE", "NO_HIRE", "MAYBE"]}}, "required": ["id", "type", "scheduledDate", "status"]}, "Interviewer": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "designation": {"type": "string"}}, "required": ["id", "name", "email"]}, "ApplicationNote": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "note": {"type": "string"}, "createdBy": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "isPrivate": {"type": "boolean"}}, "required": ["id", "note", "created<PERSON>y", "createdAt"]}, "OfferDetails": {"type": "object", "properties": {"salary": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "benefits": {"type": "array", "items": {"type": "string"}}, "offerLetterUrl": {"type": "string", "format": "uri"}, "expiryDate": {"type": "string", "format": "date"}}}, "CreateJobPostingRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "department": {"type": "string"}, "location": {"type": "string"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}}, "responsibilities": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"type": "string"}}, "benefits": {"type": "array", "items": {"type": "string"}}, "closingDate": {"type": "string", "format": "date"}, "isRemote": {"type": "boolean"}, "isUrgent": {"type": "boolean"}}, "required": ["title", "description", "department", "location", "jobType", "experienceLevel"]}, "UpdateJobPostingRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "department": {"type": "string"}, "location": {"type": "string"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}}, "responsibilities": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"type": "string"}}, "benefits": {"type": "array", "items": {"type": "string"}}, "status": {"$ref": "#/components/schemas/JobPostingStatus"}, "closingDate": {"type": "string", "format": "date"}, "isRemote": {"type": "boolean"}, "isUrgent": {"type": "boolean"}}}, "CreateJobApplicationRequest": {"type": "object", "properties": {"jobPostingId": {"type": "string", "format": "uuid"}, "candidateId": {"type": "string", "format": "uuid"}, "coverLetter": {"type": "string"}, "resumeUrl": {"type": "string", "format": "uri"}}, "required": ["jobPostingId", "candidateId"]}, "UpdateApplicationStatusRequest": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApplicationStatus"}, "notes": {"type": "string"}, "rating": {"type": "number", "format": "double", "minimum": 1, "maximum": 5}, "rejectionReason": {"type": "string"}}, "required": ["status"]}, "CreateCandidateRequest": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "address": {"$ref": "#/components/schemas/Address"}, "summary": {"type": "string"}, "skills": {"type": "array", "items": {"type": "string"}}, "totalExperience": {"type": "number", "format": "double"}, "currentSalary": {"type": "number", "format": "double"}, "expectedSalary": {"type": "number", "format": "double"}, "resumeUrl": {"type": "string", "format": "uri"}, "source": {"type": "string"}}, "required": ["firstName", "lastName", "email", "phone"]}, "JobPostingListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/JobPosting"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "JobApplicationListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/JobApplication"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "CandidateListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Candidate"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}, "JobRequisition": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique requisition identifier"}, "title": {"type": "string", "description": "Job title"}, "description": {"type": "string", "description": "Job description"}, "department": {"type": "string", "description": "Department"}, "location": {"type": "string", "description": "Job location"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}, "description": "Job requirements"}, "responsibilities": {"type": "array", "items": {"type": "string"}, "description": "Job responsibilities"}, "skills": {"type": "array", "items": {"type": "string"}, "description": "Required skills"}, "benefits": {"type": "array", "items": {"type": "string"}, "description": "Job benefits"}, "status": {"$ref": "#/components/schemas/RequisitionStatus"}, "priority": {"$ref": "#/components/schemas/RequisitionPriority"}, "createdBy": {"type": "string", "format": "uuid", "description": "ID of the person who created the requisition"}, "requestedBy": {"type": "string", "format": "uuid", "description": "ID of the person who requested the requisition"}, "approvedBy": {"type": "string", "format": "uuid", "description": "ID of the person who approved the requisition"}, "rejectedBy": {"type": "string", "format": "uuid", "description": "ID of the person who rejected the requisition"}, "submittedDate": {"type": "string", "format": "date-time", "description": "Date when requisition was submitted"}, "approvedDate": {"type": "string", "format": "date-time", "description": "Date when requisition was approved"}, "rejectedDate": {"type": "string", "format": "date-time", "description": "Date when requisition was rejected"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "title", "description", "department", "location", "jobType", "experienceLevel", "status", "priority", "created<PERSON>y", "requestedBy", "submittedDate"]}, "RequisitionStatus": {"type": "string", "enum": ["DRAFT", "SUBMITTED", "APPROVED", "REJECTED", "CLOSED"], "description": "Job requisition status"}, "RequisitionPriority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "description": "Job requisition priority"}, "CreateJobRequisitionRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "department": {"type": "string"}, "location": {"type": "string"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}}, "responsibilities": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"type": "string"}}, "benefits": {"type": "array", "items": {"type": "string"}}, "priority": {"$ref": "#/components/schemas/RequisitionPriority"}, "closingDate": {"type": "string", "format": "date"}, "isRemote": {"type": "boolean"}, "isUrgent": {"type": "boolean"}}, "required": ["title", "description", "department", "location", "jobType", "experienceLevel", "priority"]}, "UpdateJobRequisitionRequest": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "department": {"type": "string"}, "location": {"type": "string"}, "jobType": {"$ref": "#/components/schemas/JobType"}, "experienceLevel": {"$ref": "#/components/schemas/ExperienceLevel"}, "salaryRange": {"$ref": "#/components/schemas/SalaryRange"}, "requirements": {"type": "array", "items": {"type": "string"}}, "responsibilities": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"type": "string"}}, "benefits": {"type": "array", "items": {"type": "string"}}, "status": {"$ref": "#/components/schemas/RequisitionStatus"}, "priority": {"$ref": "#/components/schemas/RequisitionPriority"}, "closingDate": {"type": "string", "format": "date"}, "isRemote": {"type": "boolean"}, "isUrgent": {"type": "boolean"}}}, "ApproveRequisitionRequest": {"type": "object", "properties": {"notes": {"type": "string"}}, "required": ["notes"]}, "RejectRequisitionRequest": {"type": "object", "properties": {"reason": {"type": "string"}}, "required": ["reason"]}, "JobRequisitionListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/JobRequisition"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}