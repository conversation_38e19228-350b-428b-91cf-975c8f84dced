"""
Employee microservice for oneHRMS.

This microservice handles all employee-related operations including:
- Employee CRUD operations
- Department management
- Position management
- Employee documents
- Employee search and filtering
"""

__version__ = "1.0.0"
__author__ = "oneHRMS Team"

from .api import router as employee_router
from .models import Employee, Department, Position
from .services import EmployeeService, DepartmentService, PositionService

__all__ = [
    "employee_router",
    "Employee",
    "Department", 
    "Position",
    "EmployeeService",
    "DepartmentService",
    "PositionService",
]
