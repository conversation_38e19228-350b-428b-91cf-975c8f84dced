"""
SQLAlchemy models for the Employee microservice.

This module defines the database models for employees, departments,
and positions using SQLAlchemy ORM.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text, Integer
from sqlalchemy.types import TypeDecorator, CHAR
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
import uuid

# UUID type that works with both SQLite and PostgreSQL
class UUID(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(value)
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from hrms.shared.database import Base


class Employee(Base):
    """Employee model for storing employee information."""
    
    __tablename__ = "employees"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)
    
    # Basic information
    employee_id = Column(String(50), unique=True, nullable=False, index=True)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    
    # Employment information
    department_id = Column(UUID(), ForeignKey("departments.id"), nullable=True)
    position_id = Column(UUID(), ForeignKey("positions.id"), nullable=True)
    manager_id = Column(UUID(), ForeignKey("employees.id"), nullable=True)
    hire_date = Column(DateTime(timezone=True), nullable=True)
    termination_date = Column(DateTime(timezone=True), nullable=True)
    
    # Status and metadata
    status = Column(String(20), nullable=False, default="active")
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    department = relationship("Department", back_populates="employees")
    position = relationship("Position", back_populates="employees")
    manager = relationship("Employee", remote_side=[id], back_populates="subordinates")
    subordinates = relationship("Employee", back_populates="manager")
    
    def __repr__(self):
        return f"<Employee(id={self.id}, employee_id='{self.employee_id}', name='{self.first_name} {self.last_name}')>"
    
    @property
    def full_name(self) -> str:
        """Get employee's full name."""
        return f"{self.first_name} {self.last_name}"
    
    def to_dict(self) -> dict:
        """Convert employee to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": self.employee_id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": self.full_name,
            "email": self.email,
            "phone": self.phone,
            "department_id": str(self.department_id) if self.department_id else None,
            "position_id": str(self.position_id) if self.position_id else None,
            "manager_id": str(self.manager_id) if self.manager_id else None,
            "hire_date": self.hire_date.isoformat() if self.hire_date else None,
            "termination_date": self.termination_date.isoformat() if self.termination_date else None,
            "status": self.status,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Department(Base):
    """Department model for organizing employees."""
    
    __tablename__ = "departments"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Basic information
    name = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Hierarchy
    parent_department_id = Column(UUID(), ForeignKey("departments.id"), nullable=True)
    manager_id = Column(UUID(), ForeignKey("employees.id"), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    employees = relationship("Employee", back_populates="department")
    positions = relationship("Position", back_populates="department")
    parent_department = relationship("Department", remote_side=[id], back_populates="sub_departments")
    sub_departments = relationship("Department", back_populates="parent_department")
    manager = relationship("Employee", foreign_keys=[manager_id])
    
    def __repr__(self):
        return f"<Department(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def to_dict(self) -> dict:
        """Convert department to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "parent_department_id": str(self.parent_department_id) if self.parent_department_id else None,
            "manager_id": str(self.manager_id) if self.manager_id else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Position(Base):
    """Position/Job Title model."""
    
    __tablename__ = "positions"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Basic information
    title = Column(String(100), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Organization
    department_id = Column(UUID(), ForeignKey("departments.id"), nullable=True)
    level = Column(Integer, nullable=True)  # Job level/grade
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    employees = relationship("Employee", back_populates="position")
    department = relationship("Department", back_populates="positions")
    
    def __repr__(self):
        return f"<Position(id={self.id}, title='{self.title}', code='{self.code}')>"
    
    def to_dict(self) -> dict:
        """Convert position to dictionary."""
        return {
            "id": str(self.id),
            "title": self.title,
            "code": self.code,
            "description": self.description,
            "department_id": str(self.department_id) if self.department_id else None,
            "level": self.level,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class EmployeeDocument(Base):
    """Employee document model for storing employee-related documents."""
    
    __tablename__ = "employee_documents"
    
    # Primary key
    id = Column(UUID(), primary_key=True, default=uuid4)

    # Document information
    employee_id = Column(UUID(), ForeignKey("employees.id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # resume, id_card, contract, etc.
    document_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)

    # Metadata
    uploaded_by = Column(UUID(), nullable=True)  # User who uploaded
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Relationships
    employee = relationship("Employee")
    
    def __repr__(self):
        return f"<EmployeeDocument(id={self.id}, employee_id={self.employee_id}, type='{self.document_type}')>"
    
    def to_dict(self) -> dict:
        """Convert document to dictionary."""
        return {
            "id": str(self.id),
            "employee_id": str(self.employee_id),
            "document_type": self.document_type,
            "document_name": self.document_name,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "mime_type": self.mime_type,
            "uploaded_by": str(self.uploaded_by) if self.uploaded_by else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
