"""
FastAPI router for Employee microservice endpoints.

This module defines all REST API endpoints for employee, department,
and position management operations.

SECURITY: Enhanced with comprehensive security measures:
- JWT authentication required for all endpoints
- Role-based access control
- Input validation and sanitization
- Comprehensive audit logging
- Rate limiting protection
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.orm import Session

from hrms.shared.auth import get_current_user, require_permissions
from hrms.shared.database import get_db_session
from hrms.shared.exceptions import HRMSException, hrms_exception_handler
from hrms.shared.models import User
from hrms.shared.logging import get_logger, log_api_request
from hrms.shared.rate_limiting import rate_limit
from hrms.shared.audit import audit_log, AuditAction, AuditSeverity
from hrms.shared.validation import validate_request_data

from hrms.employee.schemas import (
    # Employee schemas
    Employee, Employee<PERSON>reate, EmployeeUpdate, EmployeeSearchParams,
    EmployeeResponse, EmployeeListResponse,

    # Department schemas
    Department, DepartmentCreate, DepartmentUpdate, DepartmentSearchParams,
    DepartmentResponse, DepartmentListResponse,

    # Position schemas
    Position, PositionCreate, PositionUpdate, PositionSearchParams,
    PositionResponse, PositionListResponse,
)
from hrms.employee.services import EmployeeService, DepartmentService, PositionService

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/employees", tags=["employees"])


# Employee Endpoints
@router.post("/", response_model=EmployeeResponse, status_code=status.HTTP_201_CREATED)
@rate_limit("write")
@audit_log(AuditAction.CREATE, "employee", AuditSeverity.MEDIUM, "Create new employee")
@log_api_request()
async def create_employee(
    request: Request,
    employee_data: EmployeeCreate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:write"])),
    db: Session = Depends(get_db_session)
):
    """Create a new employee with enhanced security validation."""
    try:
        service = EmployeeService(db)
        employee = service.create_employee(employee_data)
        
        return EmployeeResponse(
            message="Employee created successfully",
            data=Employee.from_orm(employee)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/{employee_id}", response_model=EmployeeResponse)
@log_api_request()
async def get_employee(
    employee_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:read"])),
    db: Session = Depends(get_db_session)
):
    """Get employee by ID."""
    try:
        service = EmployeeService(db)
        employee = service.get_employee(employee_id)
        
        return EmployeeResponse(
            message="Employee retrieved successfully",
            data=Employee.from_orm(employee)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/by-employee-id/{employee_id}", response_model=EmployeeResponse)
@log_api_request()
async def get_employee_by_employee_id(
    employee_id: str,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:read"])),
    db: Session = Depends(get_db_session)
):
    """Get employee by employee ID."""
    try:
        service = EmployeeService(db)
        employee = service.get_employee_by_employee_id(employee_id)
        
        return EmployeeResponse(
            message="Employee retrieved successfully",
            data=Employee.from_orm(employee)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.put("/{employee_id}", response_model=EmployeeResponse)
@log_api_request()
async def update_employee(
    employee_id: UUID,
    employee_data: EmployeeUpdate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:write"])),
    db: Session = Depends(get_db_session)
):
    """Update an employee."""
    try:
        service = EmployeeService(db)
        employee = service.update_employee(employee_id, employee_data)
        
        return EmployeeResponse(
            message="Employee updated successfully",
            data=Employee.from_orm(employee)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.delete("/{employee_id}", status_code=status.HTTP_204_NO_CONTENT)
@log_api_request()
async def delete_employee(
    employee_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:delete"])),
    db: Session = Depends(get_db_session)
):
    """Delete an employee (soft delete)."""
    try:
        service = EmployeeService(db)
        service.delete_employee(employee_id)
        return None
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/", response_model=EmployeeListResponse)
@log_api_request()
async def search_employees(
    search: Optional[str] = Query(None, description="Search term"),
    department_id: Optional[UUID] = Query(None, description="Filter by department"),
    position_id: Optional[UUID] = Query(None, description="Filter by position"),
    manager_id: Optional[UUID] = Query(None, description="Filter by manager"),
    status: Optional[str] = Query(None, description="Filter by status"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:read"])),
    db: Session = Depends(get_db_session)
):
    """Search employees with filters and pagination."""
    try:
        search_params = EmployeeSearchParams(
            search=search,
            department_id=department_id,
            position_id=position_id,
            manager_id=manager_id,
            status=status,
            is_active=is_active
        )
        
        service = EmployeeService(db)
        result = service.search_employees(search_params, page, page_size)
        
        employees = [Employee.from_orm(emp) for emp in result["data"]]
        
        return EmployeeListResponse(
            message="Employees retrieved successfully",
            data=employees,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/{manager_id}/subordinates", response_model=EmployeeListResponse)
@log_api_request()
async def get_employee_subordinates(
    manager_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:read"])),
    db: Session = Depends(get_db_session)
):
    """Get all subordinates of a manager."""
    try:
        service = EmployeeService(db)
        subordinates = service.get_employee_subordinates(manager_id)
        
        employees = [Employee.from_orm(emp) for emp in subordinates]
        
        return EmployeeListResponse(
            message="Subordinates retrieved successfully",
            data=employees,
            total=len(employees),
            page=1,
            page_size=len(employees),
            total_pages=1
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.post("/generate-employee-id")
@log_api_request()
async def generate_employee_id(
    prefix: str = Query("EMP", description="Prefix for employee ID"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["employee:write"])),
    db: Session = Depends(get_db_session)
):
    """Generate a unique employee ID."""
    try:
        service = EmployeeService(db)
        employee_id = service.generate_employee_id(prefix)
        
        return {
            "success": True,
            "message": "Employee ID generated successfully",
            "employee_id": employee_id
        }
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Department Endpoints
@router.post("/departments/", response_model=DepartmentResponse, status_code=status.HTTP_201_CREATED)
@log_api_request()
async def create_department(
    department_data: DepartmentCreate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["department:write"])),
    db: Session = Depends(get_db_session)
):
    """Create a new department."""
    try:
        service = DepartmentService(db)
        department = service.create_department(department_data)
        
        return DepartmentResponse(
            message="Department created successfully",
            data=Department.from_orm(department)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/departments/{department_id}", response_model=DepartmentResponse)
@log_api_request()
async def get_department(
    department_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["department:read"])),
    db: Session = Depends(get_db_session)
):
    """Get department by ID."""
    try:
        service = DepartmentService(db)
        department = service.get_department(department_id)
        
        return DepartmentResponse(
            message="Department retrieved successfully",
            data=Department.from_orm(department)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.put("/departments/{department_id}", response_model=DepartmentResponse)
@log_api_request()
async def update_department(
    department_id: UUID,
    department_data: DepartmentUpdate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["department:write"])),
    db: Session = Depends(get_db_session)
):
    """Update a department."""
    try:
        service = DepartmentService(db)
        department = service.update_department(department_id, department_data)
        
        return DepartmentResponse(
            message="Department updated successfully",
            data=Department.from_orm(department)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.delete("/departments/{department_id}", status_code=status.HTTP_204_NO_CONTENT)
@log_api_request()
async def delete_department(
    department_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["department:delete"])),
    db: Session = Depends(get_db_session)
):
    """Delete a department (soft delete)."""
    try:
        service = DepartmentService(db)
        service.delete_department(department_id)
        return None
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/departments/", response_model=DepartmentListResponse)
@log_api_request()
async def search_departments(
    search: Optional[str] = Query(None, description="Search term"),
    parent_department_id: Optional[UUID] = Query(None, description="Filter by parent department"),
    manager_id: Optional[UUID] = Query(None, description="Filter by manager"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["department:read"])),
    db: Session = Depends(get_db_session)
):
    """Search departments with filters and pagination."""
    try:
        search_params = DepartmentSearchParams(
            search=search,
            parent_department_id=parent_department_id,
            manager_id=manager_id,
            is_active=is_active
        )
        
        service = DepartmentService(db)
        result = service.search_departments(search_params, page, page_size)
        
        departments = [Department.from_orm(dept) for dept in result["data"]]
        
        return DepartmentListResponse(
            message="Departments retrieved successfully",
            data=departments,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Position Endpoints
@router.post("/positions/", response_model=PositionResponse, status_code=status.HTTP_201_CREATED)
@log_api_request()
async def create_position(
    position_data: PositionCreate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["position:write"])),
    db: Session = Depends(get_db_session)
):
    """Create a new position."""
    try:
        service = PositionService(db)
        position = service.create_position(position_data)

        return PositionResponse(
            message="Position created successfully",
            data=Position.from_orm(position)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/positions/{position_id}", response_model=PositionResponse)
@log_api_request()
async def get_position(
    position_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["position:read"])),
    db: Session = Depends(get_db_session)
):
    """Get position by ID."""
    try:
        service = PositionService(db)
        position = service.get_position(position_id)

        return PositionResponse(
            message="Position retrieved successfully",
            data=Position.from_orm(position)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.put("/positions/{position_id}", response_model=PositionResponse)
@log_api_request()
async def update_position(
    position_id: UUID,
    position_data: PositionUpdate,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["position:write"])),
    db: Session = Depends(get_db_session)
):
    """Update a position."""
    try:
        service = PositionService(db)
        position = service.update_position(position_id, position_data)

        return PositionResponse(
            message="Position updated successfully",
            data=Position.from_orm(position)
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.delete("/positions/{position_id}", status_code=status.HTTP_204_NO_CONTENT)
@log_api_request()
async def delete_position(
    position_id: UUID,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["position:delete"])),
    db: Session = Depends(get_db_session)
):
    """Delete a position (soft delete)."""
    try:
        service = PositionService(db)
        service.delete_position(position_id)
        return None
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


@router.get("/positions/", response_model=PositionListResponse)
@log_api_request()
async def search_positions(
    search: Optional[str] = Query(None, description="Search term"),
    department_id: Optional[UUID] = Query(None, description="Filter by department"),
    level: Optional[int] = Query(None, description="Filter by level"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions(["position:read"])),
    db: Session = Depends(get_db_session)
):
    """Search positions with filters and pagination."""
    try:
        search_params = PositionSearchParams(
            search=search,
            department_id=department_id,
            level=level,
            is_active=is_active
        )

        service = PositionService(db)
        result = service.search_positions(search_params, page, page_size)

        positions = [Position.from_orm(pos) for pos in result["data"]]

        return PositionListResponse(
            message="Positions retrieved successfully",
            data=positions,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
    except HRMSException as e:
        raise hrms_exception_handler(None, e)


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint for the Employee microservice."""
    return {
        "service": "employee",
        "status": "healthy",
        "version": "1.0.0"
    }
