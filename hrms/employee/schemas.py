"""
Pydantic schemas for the Employee microservice.

This module defines request/response schemas for employee-related operations
using Pydantic for data validation and serialization.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from hrms.shared.models import BaseResponse, PaginatedResponse


# Employee Schemas
class EmployeeBase(BaseModel):
    """Base employee schema with common fields."""
    
    employee_id: str = Field(..., description="Unique employee identifier")
    first_name: str = Field(..., min_length=1, max_length=100, description="Employee first name")
    last_name: str = Field(..., min_length=1, max_length=100, description="Employee last name")
    email: str = Field(..., description="Employee email address")
    phone: Optional[str] = Field(None, max_length=20, description="Employee phone number")
    department_id: Optional[UUID] = Field(None, description="Department ID")
    position_id: Optional[UUID] = Field(None, description="Position ID")
    manager_id: Optional[UUID] = Field(None, description="Manager employee ID")
    hire_date: Optional[datetime] = Field(None, description="Date of hire")
    status: str = Field(default="active", description="Employee status")
    
    @validator('email')
    def validate_email(cls, v):
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, v):
            raise ValueError('Invalid email format')
        return v.lower()
    
    @validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['active', 'inactive', 'terminated', 'on_leave']
        if v not in allowed_statuses:
            raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v


class EmployeeCreate(EmployeeBase):
    """Schema for creating a new employee."""
    pass


class EmployeeUpdate(BaseModel):
    """Schema for updating an employee."""
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[str] = None
    phone: Optional[str] = Field(None, max_length=20)
    department_id: Optional[UUID] = None
    position_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None
    status: Optional[str] = None
    
    @validator('email')
    def validate_email(cls, v):
        if v is not None:
            import re
            pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(pattern, v):
                raise ValueError('Invalid email format')
            return v.lower()
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'terminated', 'on_leave']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v


class Employee(EmployeeBase):
    """Schema for employee response."""
    
    id: UUID
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    termination_date: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Department Schemas
class DepartmentBase(BaseModel):
    """Base department schema."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Department name")
    code: str = Field(..., min_length=1, max_length=20, description="Department code")
    description: Optional[str] = Field(None, description="Department description")
    parent_department_id: Optional[UUID] = Field(None, description="Parent department ID")
    manager_id: Optional[UUID] = Field(None, description="Department manager employee ID")


class DepartmentCreate(DepartmentBase):
    """Schema for creating a new department."""
    pass


class DepartmentUpdate(BaseModel):
    """Schema for updating a department."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    parent_department_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None
    is_active: Optional[bool] = None


class Department(DepartmentBase):
    """Schema for department response."""
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Position Schemas
class PositionBase(BaseModel):
    """Base position schema."""
    
    title: str = Field(..., min_length=1, max_length=100, description="Position title")
    code: str = Field(..., min_length=1, max_length=20, description="Position code")
    description: Optional[str] = Field(None, description="Position description")
    department_id: Optional[UUID] = Field(None, description="Department ID")
    level: Optional[int] = Field(None, ge=1, le=20, description="Position level/grade")


class PositionCreate(PositionBase):
    """Schema for creating a new position."""
    pass


class PositionUpdate(BaseModel):
    """Schema for updating a position."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    department_id: Optional[UUID] = None
    level: Optional[int] = Field(None, ge=1, le=20)
    is_active: Optional[bool] = None


class Position(PositionBase):
    """Schema for position response."""
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Document Schemas
class EmployeeDocumentBase(BaseModel):
    """Base employee document schema."""
    
    document_type: str = Field(..., description="Type of document")
    document_name: str = Field(..., description="Document name")


class EmployeeDocumentCreate(EmployeeDocumentBase):
    """Schema for creating an employee document."""
    
    employee_id: UUID = Field(..., description="Employee ID")
    file_path: str = Field(..., description="File path")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type")


class EmployeeDocument(EmployeeDocumentBase):
    """Schema for employee document response."""
    
    id: UUID
    employee_id: UUID
    file_path: str
    file_size: Optional[int]
    mime_type: Optional[str]
    uploaded_by: Optional[UUID]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Response Schemas
class EmployeeResponse(BaseResponse):
    """Response schema for single employee operations."""
    
    data: Employee


class EmployeeListResponse(PaginatedResponse[Employee]):
    """Response schema for employee list operations."""
    pass


class DepartmentResponse(BaseResponse):
    """Response schema for single department operations."""
    
    data: Department


class DepartmentListResponse(PaginatedResponse[Department]):
    """Response schema for department list operations."""
    pass


class PositionResponse(BaseResponse):
    """Response schema for single position operations."""
    
    data: Position


class PositionListResponse(PaginatedResponse[Position]):
    """Response schema for position list operations."""
    pass


class EmployeeDocumentResponse(BaseResponse):
    """Response schema for single document operations."""
    
    data: EmployeeDocument


class EmployeeDocumentListResponse(PaginatedResponse[EmployeeDocument]):
    """Response schema for document list operations."""
    pass


# Search and Filter Schemas
class EmployeeSearchParams(BaseModel):
    """Schema for employee search parameters."""
    
    search: Optional[str] = Field(None, description="Search term for name, email, or employee ID")
    department_id: Optional[UUID] = Field(None, description="Filter by department")
    position_id: Optional[UUID] = Field(None, description="Filter by position")
    manager_id: Optional[UUID] = Field(None, description="Filter by manager")
    status: Optional[str] = Field(None, description="Filter by status")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    hire_date_from: Optional[datetime] = Field(None, description="Filter by hire date from")
    hire_date_to: Optional[datetime] = Field(None, description="Filter by hire date to")


class DepartmentSearchParams(BaseModel):
    """Schema for department search parameters."""
    
    search: Optional[str] = Field(None, description="Search term for name or code")
    parent_department_id: Optional[UUID] = Field(None, description="Filter by parent department")
    manager_id: Optional[UUID] = Field(None, description="Filter by manager")
    is_active: Optional[bool] = Field(None, description="Filter by active status")


class PositionSearchParams(BaseModel):
    """Schema for position search parameters."""
    
    search: Optional[str] = Field(None, description="Search term for title or code")
    department_id: Optional[UUID] = Field(None, description="Filter by department")
    level: Optional[int] = Field(None, description="Filter by level")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
