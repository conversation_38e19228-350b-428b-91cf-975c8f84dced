"""
Basic tests for the Employee microservice.

This module contains unit tests for the Employee microservice
to verify core functionality and API endpoints.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from hrms.shared.database import Base, get_db_session
from hrms.shared.auth import get_current_user
from hrms.shared.models import User

from .main import app
from .models import Employee, Department, Position
from .schemas import Employee<PERSON><PERSON>, DepartmentCreate, PositionCreate

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_employee.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


def override_get_current_user():
    """Override authentication for testing."""
    return User(
        id=uuid4(),
        username="test_user",
        email="<EMAIL>",
        full_name="Test User",
        is_active=True,
        is_superuser=True,
        roles=["admin"],
        permissions=["*"]
    )


# Override dependencies
app.dependency_overrides[get_db_session] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user

client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Setup test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session():
    """Get database session for testing."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def sample_department(db_session):
    """Create a sample department for testing."""
    department = Department(
        name="Engineering",
        code="ENG",
        description="Engineering Department"
    )
    db_session.add(department)
    db_session.commit()
    db_session.refresh(department)
    return department


@pytest.fixture
def sample_position(db_session, sample_department):
    """Create a sample position for testing."""
    position = Position(
        title="Software Engineer",
        code="SE",
        description="Software Engineer Position",
        department_id=sample_department.id,
        level=3
    )
    db_session.add(position)
    db_session.commit()
    db_session.refresh(position)
    return position


class TestEmployeeAPI:
    """Test cases for Employee API endpoints."""
    
    def test_health_check(self, setup_database):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["service"] == "employee"
        assert data["status"] == "healthy"
    
    def test_create_department(self, setup_database):
        """Test creating a department."""
        department_data = {
            "name": "Human Resources",
            "code": "HR",
            "description": "Human Resources Department"
        }
        
        response = client.post("/api/v1/employees/departments/", json=department_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "Human Resources"
        assert data["data"]["code"] == "HR"
    
    def test_create_position(self, setup_database, sample_department):
        """Test creating a position."""
        position_data = {
            "title": "HR Manager",
            "code": "HRM",
            "description": "HR Manager Position",
            "department_id": str(sample_department.id),
            "level": 5
        }
        
        response = client.post("/api/v1/employees/positions/", json=position_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["title"] == "HR Manager"
        assert data["data"]["code"] == "HRM"
    
    def test_create_employee(self, setup_database, sample_department, sample_position):
        """Test creating an employee."""
        employee_data = {
            "employee_id": "EMP001",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "department_id": str(sample_department.id),
            "position_id": str(sample_position.id),
            "hire_date": "2023-01-01T00:00:00",
            "status": "active"
        }
        
        response = client.post("/api/v1/employees/", json=employee_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["employee_id"] == "EMP001"
        assert data["data"]["first_name"] == "John"
        assert data["data"]["last_name"] == "Doe"
        assert data["data"]["email"] == "<EMAIL>"
    
    def test_get_employee_by_id(self, setup_database, sample_department, sample_position):
        """Test getting an employee by ID."""
        # First create an employee
        employee_data = {
            "employee_id": "EMP002",
            "first_name": "Jane",
            "last_name": "Smith",
            "email": "<EMAIL>",
            "department_id": str(sample_department.id),
            "position_id": str(sample_position.id),
            "status": "active"
        }
        
        create_response = client.post("/api/v1/employees/", json=employee_data)
        assert create_response.status_code == 201
        
        created_employee = create_response.json()["data"]
        employee_id = created_employee["id"]
        
        # Get the employee
        response = client.get(f"/api/v1/employees/{employee_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == employee_id
        assert data["data"]["employee_id"] == "EMP002"
    
    def test_update_employee(self, setup_database, sample_department, sample_position):
        """Test updating an employee."""
        # First create an employee
        employee_data = {
            "employee_id": "EMP003",
            "first_name": "Bob",
            "last_name": "Johnson",
            "email": "<EMAIL>",
            "department_id": str(sample_department.id),
            "position_id": str(sample_position.id),
            "status": "active"
        }
        
        create_response = client.post("/api/v1/employees/", json=employee_data)
        assert create_response.status_code == 201
        
        created_employee = create_response.json()["data"]
        employee_id = created_employee["id"]
        
        # Update the employee
        update_data = {
            "first_name": "Robert",
            "phone": "+9876543210"
        }
        
        response = client.put(f"/api/v1/employees/{employee_id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["first_name"] == "Robert"
        assert data["data"]["phone"] == "+9876543210"
    
    def test_search_employees(self, setup_database):
        """Test searching employees."""
        response = client.get("/api/v1/employees/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "total" in data
        assert "page" in data
    
    def test_generate_employee_id(self, setup_database):
        """Test generating employee ID."""
        response = client.post("/api/v1/employees/generate-employee-id?prefix=TEST")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "employee_id" in data
        assert data["employee_id"].startswith("TEST")
    
    def test_duplicate_employee_id_error(self, setup_database, sample_department, sample_position):
        """Test error when creating employee with duplicate employee ID."""
        employee_data = {
            "employee_id": "EMP999",
            "first_name": "Test",
            "last_name": "User",
            "email": "<EMAIL>",
            "department_id": str(sample_department.id),
            "position_id": str(sample_position.id),
            "status": "active"
        }
        
        # Create first employee
        response1 = client.post("/api/v1/employees/", json=employee_data)
        assert response1.status_code == 201
        
        # Try to create second employee with same employee_id
        employee_data["email"] = "<EMAIL>"
        response2 = client.post("/api/v1/employees/", json=employee_data)
        assert response2.status_code == 409  # Conflict
    
    def test_invalid_email_error(self, setup_database, sample_department, sample_position):
        """Test error when creating employee with invalid email."""
        employee_data = {
            "employee_id": "EMP998",
            "first_name": "Test",
            "last_name": "User",
            "email": "invalid-email",
            "department_id": str(sample_department.id),
            "position_id": str(sample_position.id),
            "status": "active"
        }
        
        response = client.post("/api/v1/employees/", json=employee_data)
        assert response.status_code == 422  # Validation error


if __name__ == "__main__":
    pytest.main([__file__])
