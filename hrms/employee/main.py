"""
Main application file for the Employee microservice.

This module creates and configures the FastAPI application for the
Employee microservice with all necessary middleware and dependencies.

SECURITY: Enhanced with comprehensive security measures:
- JWT authentication required for all protected endpoints
- Rate limiting to prevent abuse
- Input validation and sanitization
- Comprehensive audit logging
"""

import os
import uuid
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from hrms.shared.database import init_database, check_database_health
from hrms.shared.exceptions import HRMSException, hrms_exception_handler
from hrms.shared.logging import setup_logging, get_logger
from hrms.shared.rate_limiting import rate_limit_middleware
from hrms.shared.audit import audit_service
from hrms.shared.auth_service import auth_router

from hrms.employee.api import router as employee_router

# Setup logging
setup_logging("employee-service")
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Employee microservice...")
    
    try:
        # Initialize database
        init_database()
        logger.info("Database initialized successfully")
        
        # Check database health
        health = check_database_health()
        if health.get("database") != "healthy":
            logger.error("Database health check failed", health=health)
            raise Exception("Database is not healthy")
        
        logger.info("Employee microservice started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start Employee microservice: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Employee microservice...")


# Create FastAPI application with enhanced security
app = FastAPI(
    title="Employee Microservice",
    description="Employee management microservice for oneHRMS with enhanced security",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Security middleware - order matters!

# 1. Request ID middleware (for audit logging)
@app.middleware("http")
async def add_request_id_middleware(request: Request, call_next):
    """Add unique request ID for tracking and audit logging."""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id

    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response

# 2. Rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# 3. CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 4. Trusted host middleware
trusted_hosts = os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(TrustedHostMiddleware, allowed_hosts=trusted_hosts)


# Exception handlers
@app.exception_handler(HRMSException)
async def hrms_exception_handler_middleware(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    return hrms_exception_handler(request, exc)


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": "HTTP_ERROR"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )


# Include routers
app.include_router(auth_router)  # Authentication endpoints
app.include_router(employee_router)  # Employee endpoints


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "employee",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        db_health = check_database_health()
        
        return {
            "service": "employee",
            "status": "healthy",
            "version": "1.0.0",
            "database": db_health,
            "environment": os.getenv("ENVIRONMENT", "development")
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "service": "employee",
                "status": "unhealthy",
                "error": str(e)
            }
        )


# Metrics endpoint (for monitoring)
@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    return {
        "service": "employee",
        "version": "1.0.0",
        "uptime": "N/A",  # TODO: Implement uptime tracking
        "requests_total": "N/A",  # TODO: Implement request counting
        "database_connections": "N/A"  # TODO: Implement connection monitoring
    }


if __name__ == "__main__":
    import uvicorn
    
    # Configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8001"))
    reload = os.getenv("ENVIRONMENT", "development") == "development"
    
    logger.info(f"Starting Employee microservice on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_config=None  # Use our custom logging
    )
