"""
Business logic services for the Employee microservice.

This module contains the service layer that handles business logic
for employee, department, and position operations.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from hrms.shared.exceptions import NotFoundError, ConflictError, ValidationError
from hrms.shared.logging import get_logger
from hrms.shared.utils import paginate_query, generate_employee_id

from hrms.employee.models import Employee, Department, Position, EmployeeDocument
from hrms.employee.schemas import (
    EmployeeCreate, EmployeeUpdate, EmployeeSearchParams,
    DepartmentCreate, DepartmentUpdate, DepartmentSearchParams,
    PositionCreate, PositionUpdate, PositionSearchParams,
    EmployeeDocumentCreate
)

logger = get_logger(__name__)


class EmployeeService:
    """Service class for employee operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_employee(self, employee_data: EmployeeCreate) -> Employee:
        """Create a new employee."""
        logger.info("Creating new employee", employee_id=employee_data.employee_id)
        
        # Check if employee ID already exists
        existing = self.db.query(Employee).filter(
            Employee.employee_id == employee_data.employee_id
        ).first()
        if existing:
            raise ConflictError(f"Employee with ID '{employee_data.employee_id}' already exists")
        
        # Check if email already exists
        existing_email = self.db.query(Employee).filter(
            Employee.email == employee_data.email
        ).first()
        if existing_email:
            raise ConflictError(f"Employee with email '{employee_data.email}' already exists")
        
        # Validate department exists if provided
        if employee_data.department_id:
            department = self.db.query(Department).filter(
                Department.id == employee_data.department_id
            ).first()
            if not department:
                raise ValidationError(f"Department with ID '{employee_data.department_id}' not found")
        
        # Validate position exists if provided
        if employee_data.position_id:
            position = self.db.query(Position).filter(
                Position.id == employee_data.position_id
            ).first()
            if not position:
                raise ValidationError(f"Position with ID '{employee_data.position_id}' not found")
        
        # Validate manager exists if provided
        if employee_data.manager_id:
            manager = self.db.query(Employee).filter(
                Employee.id == employee_data.manager_id
            ).first()
            if not manager:
                raise ValidationError(f"Manager with ID '{employee_data.manager_id}' not found")
        
        # Create employee
        employee = Employee(**employee_data.dict())
        self.db.add(employee)
        self.db.commit()
        self.db.refresh(employee)
        
        logger.info("Employee created successfully", employee_id=employee.employee_id, id=str(employee.id))
        return employee
    
    def get_employee(self, employee_id: UUID) -> Employee:
        """Get employee by ID."""
        employee = self.db.query(Employee).filter(Employee.id == employee_id).first()
        if not employee:
            raise NotFoundError("Employee", employee_id)
        return employee
    
    def get_employee_by_employee_id(self, employee_id: str) -> Employee:
        """Get employee by employee ID."""
        employee = self.db.query(Employee).filter(Employee.employee_id == employee_id).first()
        if not employee:
            raise NotFoundError("Employee", employee_id)
        return employee
    
    def update_employee(self, employee_id: UUID, employee_data: EmployeeUpdate) -> Employee:
        """Update an employee."""
        logger.info("Updating employee", employee_id=str(employee_id))
        
        employee = self.get_employee(employee_id)
        
        # Check email uniqueness if being updated
        if employee_data.email and employee_data.email != employee.email:
            existing_email = self.db.query(Employee).filter(
                and_(Employee.email == employee_data.email, Employee.id != employee_id)
            ).first()
            if existing_email:
                raise ConflictError(f"Employee with email '{employee_data.email}' already exists")
        
        # Validate department if being updated
        if employee_data.department_id:
            department = self.db.query(Department).filter(
                Department.id == employee_data.department_id
            ).first()
            if not department:
                raise ValidationError(f"Department with ID '{employee_data.department_id}' not found")
        
        # Validate position if being updated
        if employee_data.position_id:
            position = self.db.query(Position).filter(
                Position.id == employee_data.position_id
            ).first()
            if not position:
                raise ValidationError(f"Position with ID '{employee_data.position_id}' not found")
        
        # Validate manager if being updated
        if employee_data.manager_id:
            if employee_data.manager_id == employee_id:
                raise ValidationError("Employee cannot be their own manager")
            
            manager = self.db.query(Employee).filter(
                Employee.id == employee_data.manager_id
            ).first()
            if not manager:
                raise ValidationError(f"Manager with ID '{employee_data.manager_id}' not found")
        
        # Update fields
        update_data = employee_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(employee, field, value)
        
        employee.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(employee)
        
        logger.info("Employee updated successfully", employee_id=str(employee_id))
        return employee
    
    def delete_employee(self, employee_id: UUID) -> bool:
        """Delete an employee (soft delete by setting inactive)."""
        logger.info("Deleting employee", employee_id=str(employee_id))
        
        employee = self.get_employee(employee_id)
        employee.is_active = False
        employee.status = "terminated"
        employee.termination_date = datetime.utcnow()
        employee.updated_at = datetime.utcnow()
        
        self.db.commit()
        
        logger.info("Employee deleted successfully", employee_id=str(employee_id))
        return True
    
    def search_employees(
        self,
        search_params: EmployeeSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search employees with filters and pagination."""
        query = self.db.query(Employee)
        
        # Apply filters
        if search_params.search:
            search_term = f"%{search_params.search}%"
            query = query.filter(
                or_(
                    Employee.first_name.ilike(search_term),
                    Employee.last_name.ilike(search_term),
                    Employee.email.ilike(search_term),
                    Employee.employee_id.ilike(search_term)
                )
            )
        
        if search_params.department_id:
            query = query.filter(Employee.department_id == search_params.department_id)
        
        if search_params.position_id:
            query = query.filter(Employee.position_id == search_params.position_id)
        
        if search_params.manager_id:
            query = query.filter(Employee.manager_id == search_params.manager_id)
        
        if search_params.status:
            query = query.filter(Employee.status == search_params.status)
        
        if search_params.is_active is not None:
            query = query.filter(Employee.is_active == search_params.is_active)
        
        if search_params.hire_date_from:
            query = query.filter(Employee.hire_date >= search_params.hire_date_from)
        
        if search_params.hire_date_to:
            query = query.filter(Employee.hire_date <= search_params.hire_date_to)
        
        # Order by created date
        query = query.order_by(Employee.created_at.desc())
        
        return paginate_query(query, page, page_size)
    
    def get_employee_subordinates(self, manager_id: UUID) -> List[Employee]:
        """Get all subordinates of a manager."""
        return self.db.query(Employee).filter(Employee.manager_id == manager_id).all()
    
    def generate_employee_id(self, prefix: str = "EMP") -> str:
        """Generate a unique employee ID."""
        while True:
            employee_id = generate_employee_id(prefix)
            existing = self.db.query(Employee).filter(
                Employee.employee_id == employee_id
            ).first()
            if not existing:
                return employee_id


class DepartmentService:
    """Service class for department operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_department(self, department_data: DepartmentCreate) -> Department:
        """Create a new department."""
        logger.info("Creating new department", code=department_data.code)
        
        # Check if code already exists
        existing = self.db.query(Department).filter(
            Department.code == department_data.code
        ).first()
        if existing:
            raise ConflictError(f"Department with code '{department_data.code}' already exists")
        
        # Validate parent department if provided
        if department_data.parent_department_id:
            parent = self.db.query(Department).filter(
                Department.id == department_data.parent_department_id
            ).first()
            if not parent:
                raise ValidationError(f"Parent department with ID '{department_data.parent_department_id}' not found")
        
        # Validate manager if provided
        if department_data.manager_id:
            manager = self.db.query(Employee).filter(
                Employee.id == department_data.manager_id
            ).first()
            if not manager:
                raise ValidationError(f"Manager with ID '{department_data.manager_id}' not found")
        
        department = Department(**department_data.dict())
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)
        
        logger.info("Department created successfully", code=department.code, id=str(department.id))
        return department
    
    def get_department(self, department_id: UUID) -> Department:
        """Get department by ID."""
        department = self.db.query(Department).filter(Department.id == department_id).first()
        if not department:
            raise NotFoundError("Department", department_id)
        return department
    
    def update_department(self, department_id: UUID, department_data: DepartmentUpdate) -> Department:
        """Update a department."""
        logger.info("Updating department", department_id=str(department_id))
        
        department = self.get_department(department_id)
        
        # Check code uniqueness if being updated
        if department_data.code and department_data.code != department.code:
            existing = self.db.query(Department).filter(
                and_(Department.code == department_data.code, Department.id != department_id)
            ).first()
            if existing:
                raise ConflictError(f"Department with code '{department_data.code}' already exists")
        
        # Update fields
        update_data = department_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(department, field, value)
        
        department.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(department)
        
        logger.info("Department updated successfully", department_id=str(department_id))
        return department
    
    def delete_department(self, department_id: UUID) -> bool:
        """Delete a department (soft delete)."""
        logger.info("Deleting department", department_id=str(department_id))
        
        department = self.get_department(department_id)
        
        # Check if department has employees
        employee_count = self.db.query(Employee).filter(
            Employee.department_id == department_id
        ).count()
        if employee_count > 0:
            raise ConflictError(f"Cannot delete department with {employee_count} employees")
        
        department.is_active = False
        department.updated_at = datetime.utcnow()
        self.db.commit()
        
        logger.info("Department deleted successfully", department_id=str(department_id))
        return True
    
    def search_departments(
        self,
        search_params: DepartmentSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search departments with filters and pagination."""
        query = self.db.query(Department)
        
        # Apply filters
        if search_params.search:
            search_term = f"%{search_params.search}%"
            query = query.filter(
                or_(
                    Department.name.ilike(search_term),
                    Department.code.ilike(search_term)
                )
            )
        
        if search_params.parent_department_id:
            query = query.filter(Department.parent_department_id == search_params.parent_department_id)
        
        if search_params.manager_id:
            query = query.filter(Department.manager_id == search_params.manager_id)
        
        if search_params.is_active is not None:
            query = query.filter(Department.is_active == search_params.is_active)
        
        # Order by name
        query = query.order_by(Department.name)
        
        return paginate_query(query, page, page_size)


class PositionService:
    """Service class for position operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_position(self, position_data: PositionCreate) -> Position:
        """Create a new position."""
        logger.info("Creating new position", code=position_data.code)
        
        # Check if code already exists
        existing = self.db.query(Position).filter(
            Position.code == position_data.code
        ).first()
        if existing:
            raise ConflictError(f"Position with code '{position_data.code}' already exists")
        
        # Validate department if provided
        if position_data.department_id:
            department = self.db.query(Department).filter(
                Department.id == position_data.department_id
            ).first()
            if not department:
                raise ValidationError(f"Department with ID '{position_data.department_id}' not found")
        
        position = Position(**position_data.dict())
        self.db.add(position)
        self.db.commit()
        self.db.refresh(position)
        
        logger.info("Position created successfully", code=position.code, id=str(position.id))
        return position
    
    def get_position(self, position_id: UUID) -> Position:
        """Get position by ID."""
        position = self.db.query(Position).filter(Position.id == position_id).first()
        if not position:
            raise NotFoundError("Position", position_id)
        return position
    
    def update_position(self, position_id: UUID, position_data: PositionUpdate) -> Position:
        """Update a position."""
        logger.info("Updating position", position_id=str(position_id))
        
        position = self.get_position(position_id)
        
        # Check code uniqueness if being updated
        if position_data.code and position_data.code != position.code:
            existing = self.db.query(Position).filter(
                and_(Position.code == position_data.code, Position.id != position_id)
            ).first()
            if existing:
                raise ConflictError(f"Position with code '{position_data.code}' already exists")
        
        # Update fields
        update_data = position_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(position, field, value)
        
        position.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(position)
        
        logger.info("Position updated successfully", position_id=str(position_id))
        return position
    
    def delete_position(self, position_id: UUID) -> bool:
        """Delete a position (soft delete)."""
        logger.info("Deleting position", position_id=str(position_id))
        
        position = self.get_position(position_id)
        
        # Check if position has employees
        employee_count = self.db.query(Employee).filter(
            Employee.position_id == position_id
        ).count()
        if employee_count > 0:
            raise ConflictError(f"Cannot delete position with {employee_count} employees")
        
        position.is_active = False
        position.updated_at = datetime.utcnow()
        self.db.commit()
        
        logger.info("Position deleted successfully", position_id=str(position_id))
        return True
    
    def search_positions(
        self,
        search_params: PositionSearchParams,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """Search positions with filters and pagination."""
        query = self.db.query(Position)
        
        # Apply filters
        if search_params.search:
            search_term = f"%{search_params.search}%"
            query = query.filter(
                or_(
                    Position.title.ilike(search_term),
                    Position.code.ilike(search_term)
                )
            )
        
        if search_params.department_id:
            query = query.filter(Position.department_id == search_params.department_id)
        
        if search_params.level:
            query = query.filter(Position.level == search_params.level)
        
        if search_params.is_active is not None:
            query = query.filter(Position.is_active == search_params.is_active)
        
        # Order by title
        query = query.order_by(Position.title)
        
        return paginate_query(query, page, page_size)
