[project]
name = "hrms"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>" },
]
description = "Open Source HR & Payroll Software"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.frappe.testing.function_type_validation]
max_module_depth = 0

[tool.ruff]
line-length = 110
target-version = "py310"

[tool.ruff.lint]
select = ["F", "E", "W", "I", "UP", "B", "RUF"]
ignore = [
    "B017",   # assertRaises(Exception) - should be more specific
    "B018",   # useless expression, not assigned to anything
    "B023",   # function doesn't bind loop variable - will have last iteration's value
    "B904",   # raise inside except without from
    "E101",   # indentation contains mixed spaces and tabs
    "E402",   # module level import not at top of file
    "E501",   # line too long
    "E741",   # ambiguous variable name
    "F401",   # "unused" imports
    "F403",   # can't detect undefined names from * import
    "F405",   # can't detect undefined names from * import
    "F722",   # syntax error in forward type annotation
    "W191",   # indentation contains tabs
    "RUF001", # string contains ambiguous unicode character
]
typing-modules = ["frappe.types.DF"]

[tool.ruff.format]
quote-style = "double"
indent-style = "tab"
docstring-code-format = true

[tool.ruff.lint.isort.sections]
"frappe" = ["frappe"]
"erpnext" = ["erpnext"]
"hrms" = ["hrms"]

[tool.ruff.lint.isort]
section-order = [
    "future",
    "standard-library",
    "third-party",
    "frappe",
    "erpnext",
    "hrms",
    "first-party",
    "local-folder",
]


[project.urls]
Homepage = "https://frappe.io/hr"
Repository = "https://github.com/frappe/hrms.git"
"Bug Reports" = "https://github.com/frappe/hrms/issues"


[tool.bench.frappe-dependencies]
frappe = ">=16.0.0-dev,<17.0.0"
erpnext = ">=16.0.0-dev,<17.0.0"

[tool.black]
line-length = 110
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | node_modules
)/
'''

[tool.isort]
profile = "black"
line_length = 110
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
