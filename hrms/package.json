{"private": true, "name": "hrms", "description": "Open Source HR & Payroll System powered by the Frappe Framework", "repository": {"type": "git", "url": "git+https://github.com/frappe/hrms.git"}, "homepage": "https://frappe.io/hr", "author": "Frappe Technologies Pvt. Ltd.", "license": "GPL-3.0", "bugs": {"url": "https://github.com/frappe/hrms/issues"}, "aworkspaces": ["../frontend/dashboard-ui"], "scripts": {"postinstall": "yarn install-dashboard-deps", "install-dashboard-deps": "cd ../frontend/dashboard-ui && yarn install --check-files", "dev-dashboard": "cd ../frontend/dashboard-ui && yarn dev", "build": "yarn build-dashboard", "build-dashboard": "cd ../frontend/dashboard-ui && yarn build"}, "dependencies": {"html2canvas": "^1.4.1"}}