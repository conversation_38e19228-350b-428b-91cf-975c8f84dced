exclude: "node_modules|.git"
default_stages: [pre-commit]
fail_fast: false

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: trailing-whitespace
        files: "hrms.*"
        exclude: ".*json$|.*txt$|.*csv|.*md"
      - id: check-yaml
      - id: no-commit-to-branch
        args: ["--branch", "develop"]
      - id: check-merge-conflict
      - id: check-ast

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [javascript, ts, vue, css, scss]
        # Ignore frontend folder and any files that might contain jinja / bundles
        exclude: |
          (?x)^(
              frontend/.*|
              hrms/public/dist/.*|
              .*node_modules.*|
              .*boilerplate.*|
              hrms/templates/includes/.*|
              hrms/hr/doctype/employee_promotion/employee_promotion.js|
              hrms/hr/doctype/employee_transfer/employee_transfer.js|
          )$

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.3.7
    hooks:
      - id: ruff
        name: "Run ruff linter and apply fixes"
        args: ["--fix"]

      - id: ruff-format
        name: "Format Python code"

ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
