pull_request_rules:
  - name: Auto-close PRs on stable branch
    conditions:
      - and:
          - and:
              - author!=ruchamahabal
              - author!=saurabh6790
              - author!=frappe-pr-bot
              - author!=mergify[bot]
          - or:
              - base=version-16
              - base=version-15
              - base=version-14
    actions:
      close:
      comment:
        message: |
          @{{author}}, thanks for the contribution, but we do not accept pull requests on a stable branch. Please raise PR on an appropriate hotfix branch or the develop branch.

  - name: Automatic merge on CI success and review
    conditions:
      - status-success=linters
      - status-success=Sider
      - status-success=Semantic Pull Request
      - status-success=Python Unit Tests (1)
      - status-success=Python Unit Tests (2)
      - label!=dont-merge
      - label!=squash
      - "#approved-reviews-by>=1"
    actions:
      merge:
        method: merge

  - name: Automatic squash on CI success and review
    conditions:
      - status-success=linters
      - status-success=Sider
      - status-success=Python Unit Tests (1)
      - status-success=Python Unit Tests (2)
      - label!=dont-merge
      - label=squash
      - "#approved-reviews-by>=1"
    actions:
      merge:
        method: squash
        commit_message_template: |
          {{ title }} (#{{ number }})

          {{ body }}

  - name: backport to develop
    conditions:
      - label="backport develop"
    actions:
      backport:
        branches:
          - develop
        assignees:
          - "{{ author }}"

  - name: backport to version-14-hotfix
    conditions:
      - label="backport version-14-hotfix"
    actions:
      backport:
        branches:
          - version-14-hotfix
        assignees:
          - "{{ author }}"

  - name: backport to version-15-hotfix
    conditions:
      - label="backport version-15-hotfix"
    actions:
      backport:
        branches:
          - version-15-hotfix
        assignees:
          - "{{ author }}"
