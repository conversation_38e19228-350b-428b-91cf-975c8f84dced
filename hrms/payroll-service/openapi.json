{"openapi": "3.0.3", "info": {"title": "Payroll Service API", "description": "API for managing payroll, salary slips, and compensation in oneHRMS system", "version": "1.0.0", "contact": {"name": "oneHRMS Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.onehrms.com/payroll", "description": "Production server"}, {"url": "http://localhost:8101/payroll", "description": "Development server"}], "paths": {"/salary-slips": {"get": {"summary": "Get salary slips", "description": "Retrieve salary slips with filtering and pagination", "operationId": "getSalarySlips", "tags": ["<PERSON><PERSON> Slips"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "employeeId", "in": "query", "description": "Filter by employee ID", "required": false, "schema": {"type": "string"}}, {"name": "payPeriod", "in": "query", "description": "Filter by pay period (YYYY-MM)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}$"}}, {"name": "status", "in": "query", "description": "Filter by salary slip status", "required": false, "schema": {"$ref": "#/components/schemas/SalarySlipStatus"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalarySlipListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Generate salary slip", "description": "Generate a new salary slip for an employee", "operationId": "generateSalarySlip", "tags": ["<PERSON><PERSON> Slips"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateSalarySlipRequest"}}}}, "responses": {"201": {"description": "Salary slip generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalarySlip"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Salary slip already exists for this period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/salary-slips/{salarySlipId}": {"get": {"summary": "Get salary slip by ID", "description": "Retrieve a specific salary slip by its ID", "operationId": "getSalarySlipById", "tags": ["<PERSON><PERSON> Slips"], "parameters": [{"name": "salarySlipId", "in": "path", "description": "Salary slip ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalarySlip"}}}}, "404": {"description": "Salary slip not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Update salary slip", "description": "Update an existing salary slip", "operationId": "updateSalarySlip", "tags": ["<PERSON><PERSON> Slips"], "parameters": [{"name": "salarySlipId", "in": "path", "description": "Salary slip ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSalarySlipRequest"}}}}, "responses": {"200": {"description": "Salary slip updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalarySlip"}}}}, "404": {"description": "Salary slip not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/salary-slips/{salarySlipId}/pdf": {"get": {"summary": "Download salary slip PDF", "description": "Download salary slip as PDF file", "operationId": "downloadSalarySlipPdf", "tags": ["<PERSON><PERSON> Slips"], "parameters": [{"name": "salarySlipId", "in": "path", "description": "Salary slip ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Salary slip not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/payroll-runs": {"get": {"summary": "Get payroll runs", "description": "Retrieve payroll runs with filtering and pagination", "operationId": "getPayrollRuns", "tags": ["Payroll Runs"], "parameters": [{"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "payPeriod", "in": "query", "description": "Filter by pay period (YYYY-MM)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}$"}}, {"name": "status", "in": "query", "description": "Filter by payroll run status", "required": false, "schema": {"$ref": "#/components/schemas/PayrollRunStatus"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayrollRunListResponse"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Create payroll run", "description": "Create a new payroll run for a specific period", "operationId": "createPayrollRun", "tags": ["Payroll Runs"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePayrollRunRequest"}}}}, "responses": {"201": {"description": "Payroll run created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayrollRun"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"SalarySlip": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique salary slip identifier"}, "employeeId": {"type": "string", "format": "uuid", "description": "Employee ID"}, "employee": {"$ref": "#/components/schemas/EmployeeInfo"}, "payPeriod": {"type": "string", "description": "Pay period (YYYY-MM)"}, "payDate": {"type": "string", "format": "date", "description": "Pay date"}, "earnings": {"$ref": "#/components/schemas/Earnings"}, "deductions": {"$ref": "#/components/schemas/Deductions"}, "grossSalary": {"type": "number", "format": "double", "description": "Gross salary amount"}, "netSalary": {"type": "number", "format": "double", "description": "Net salary amount"}, "currency": {"type": "string", "description": "Currency code"}, "status": {"$ref": "#/components/schemas/SalarySlipStatus"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "employeeId", "payPeriod", "payDate", "earnings", "deductions", "grossSalary", "netSalary", "currency", "status"]}, "EmployeeInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "employeeId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "department": {"type": "string"}, "designation": {"type": "string"}}, "required": ["id", "employeeId", "firstName", "lastName"]}, "Earnings": {"type": "object", "properties": {"basicSalary": {"type": "number", "format": "double"}, "hra": {"type": "number", "format": "double", "description": "House Rent Allowance"}, "da": {"type": "number", "format": "double", "description": "Dearness Allowance"}, "conveyanceAllowance": {"type": "number", "format": "double"}, "medicalAllowance": {"type": "number", "format": "double"}, "specialAllowance": {"type": "number", "format": "double"}, "overtime": {"type": "number", "format": "double"}, "bonus": {"type": "number", "format": "double"}, "incentives": {"type": "number", "format": "double"}, "total": {"type": "number", "format": "double"}}, "required": ["basicSalary", "total"]}, "Deductions": {"type": "object", "properties": {"pf": {"type": "number", "format": "double", "description": "Provident Fund"}, "esi": {"type": "number", "format": "double", "description": "Employee State Insurance"}, "tds": {"type": "number", "format": "double", "description": "Tax Deducted at Source"}, "professionalTax": {"type": "number", "format": "double"}, "loanDeduction": {"type": "number", "format": "double"}, "advanceDeduction": {"type": "number", "format": "double"}, "otherDeductions": {"type": "number", "format": "double"}, "total": {"type": "number", "format": "double"}}, "required": ["total"]}, "SalarySlipStatus": {"type": "string", "enum": ["DRAFT", "GENERATED", "APPROVED", "PAID", "CANCELLED"], "description": "Salary slip status"}, "PayrollRun": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "payPeriod": {"type": "string", "description": "Pay period (YYYY-MM)"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "payDate": {"type": "string", "format": "date"}, "status": {"$ref": "#/components/schemas/PayrollRunStatus"}, "totalEmployees": {"type": "integer"}, "totalGrossAmount": {"type": "number", "format": "double"}, "totalNetAmount": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "createdBy": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "payPeriod", "startDate", "endDate", "payDate", "status", "totalEmployees", "totalGrossAmount", "totalNetAmount", "currency"]}, "PayrollRunStatus": {"type": "string", "enum": ["DRAFT", "IN_PROGRESS", "COMPLETED", "APPROVED", "PAID", "CANCELLED"], "description": "Payroll run status"}, "GenerateSalarySlipRequest": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "payPeriod": {"type": "string", "description": "Pay period (YYYY-MM)"}, "payDate": {"type": "string", "format": "date"}, "earnings": {"$ref": "#/components/schemas/Earnings"}, "deductions": {"$ref": "#/components/schemas/Deductions"}}, "required": ["employeeId", "payPeriod", "payDate", "earnings", "deductions"]}, "UpdateSalarySlipRequest": {"type": "object", "properties": {"earnings": {"$ref": "#/components/schemas/Earnings"}, "deductions": {"$ref": "#/components/schemas/Deductions"}, "status": {"$ref": "#/components/schemas/SalarySlipStatus"}}}, "CreatePayrollRunRequest": {"type": "object", "properties": {"payPeriod": {"type": "string", "description": "Pay period (YYYY-MM)"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "payDate": {"type": "string", "format": "date"}, "employeeIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of employee IDs to include in payroll run"}}, "required": ["payPeriod", "startDate", "endDate", "payDate"]}, "SalarySlipListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/SalarySlip"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PayrollRunListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PayrollRun"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}, "required": ["data", "pagination"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrevious": {"type": "boolean"}}, "required": ["page", "limit", "total", "totalPages", "hasNext", "has<PERSON>revious"]}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "code": {"type": "string", "description": "Error code"}, "details": {"type": "object", "description": "Additional error details"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error", "code", "timestamp"]}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}}, "security": [{"bearerAuth": []}]}