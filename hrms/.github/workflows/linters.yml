name: Linters

on:
  pull_request: {}

jobs:
  commit-lint:
    name: "Semantic Commits"
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 200
      - uses: actions/setup-node@v2
        with:
          node-version: 18
          check-latest: true

      - name: Check commit titles
        run: |
          npm install @commitlint/cli @commitlint/config-conventional
          npx commitlint --verbose --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }}

  linter:
    name: "<PERSON>ap<PERSON> <PERSON>"
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v2

      - name: Set up Python 3.10
        uses: actions/setup-python@v2
        with:
          python-version: "3.10"

      - name: Install and Run Pre-commit
        uses: pre-commit/action@v3.0.0

      - name: Download Semgrep rules
        run: git clone --depth 1 https://github.com/frappe/semgrep-rules.git frappe-semgrep-rules

      - name: Download semgrep
        run: pip install semgrep

      - name: Run Semgrep rules
        run: semgrep ci --config ./frappe-semgrep-rules/rules --config r/python.lang.correctness
