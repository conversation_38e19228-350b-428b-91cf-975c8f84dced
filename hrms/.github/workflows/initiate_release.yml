# This workflow is agnostic to branches. Only maintain on develop branch.
# To add/remove versions just modify the matrix.

name: Create weekly release pull requests
on:
  schedule:
    # 9:45 UTC => 3:15 PM IST Tuesday
    - cron: "45 9 * * 2"
  workflow_dispatch:

jobs:
  stable-release:
    name: Release
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        version: ["14", "15"]

    steps:
      - uses: octokit/request-action@v2.x
        with:
          route: POST /repos/{owner}/{repo}/pulls
          owner: frappe
          repo: hrms
          title: |-
            "chore: release v${{ matrix.version }}"
          body: "Automated Release."
          base: version-${{ matrix.version }}
          head: version-${{ matrix.version }}-hotfix
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}
