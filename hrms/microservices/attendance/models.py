"""
Pydantic models for Attendance Management Service.

Defines request/response schemas, validation rules, and data transfer objects.
"""

from datetime import date, datetime, time
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel as PydanticBaseModel
from pydantic import EmailStr, Field, field_validator
from sqlalchemy import Boolean, Column, Date, DateTime, ForeignKey, Integer, Numeric, String, Text, Time
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from ..shared.models import Base, PydanticBaseModel, TenantMixin, TimestampMixin


# Enums
class AttendanceStatus(str, Enum):
    """Attendance status enumeration."""

    PRESENT = "PRESENT"
    ABSENT = "ABSENT"
    HALF_DAY = "HALF_DAY"
    ON_LEAVE = "ON_LEAVE"
    WORK_FROM_HOME = "WORK_FROM_HOME"


class CheckInType(str, Enum):
    """Check-in type enumeration."""

    CHECK_IN = "CHECK_IN"
    CHECK_OUT = "CHECK_OUT"
    BREAK_START = "BREAK_START"
    BREAK_END = "BREAK_END"


class ShiftStatus(str, Enum):
    """Shift status enumeration."""

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    DRAFT = "DRAFT"


class HalfDayStatus(str, Enum):
    """Half day status enumeration."""

    FIRST_HALF = "FIRST_HALF"
    SECOND_HALF = "SECOND_HALF"


# Database Models
class AttendanceRecord(Base, TimestampMixin, TenantMixin):
    """Attendance record database model."""

    __tablename__ = "attendance_records"

    id = Column(PGUUID(as_uuid=True), primary_key=True, index=True)
    employee_id = Column(String(50), nullable=False, index=True)
    attendance_date = Column(Date, nullable=False, index=True)
    status = Column(String(20), nullable=False)
    shift_id = Column(PGUUID(as_uuid=True), ForeignKey("shift_types.id"), nullable=True)

    # Time tracking
    in_time = Column(DateTime, nullable=True)
    out_time = Column(DateTime, nullable=True)
    working_hours = Column(Numeric(5, 2), nullable=True)
    break_duration_minutes = Column(Integer, default=0)

    # Status flags
    late_entry = Column(Boolean, default=False)
    early_exit = Column(Boolean, default=False)
    half_day_status = Column(String(20), nullable=True)

    # Additional information
    notes = Column(Text, nullable=True)
    leave_type = Column(String(50), nullable=True)
    leave_application_id = Column(String(50), nullable=True)

    # Relationships
    shift = relationship("ShiftType", back_populates="attendance_records")
    checkins = relationship("CheckInRecord", back_populates="attendance")


class CheckInRecord(Base, TimestampMixin, TenantMixin):
    """Check-in/check-out record database model."""

    __tablename__ = "checkin_records"

    id = Column(PGUUID(as_uuid=True), primary_key=True, index=True)
    employee_id = Column(String(50), nullable=False, index=True)
    check_time = Column(DateTime, nullable=False, index=True)
    check_type = Column(String(20), nullable=False)

    # Location tracking
    latitude = Column(Numeric(10, 8), nullable=True)
    longitude = Column(Numeric(11, 8), nullable=True)
    location_name = Column(String(200), nullable=True)

    # Shift information
    shift_id = Column(PGUUID(as_uuid=True), ForeignKey("shift_types.id"), nullable=True)
    shift_start = Column(DateTime, nullable=True)
    shift_end = Column(DateTime, nullable=True)

    # Device information
    device_id = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)

    # Processing status
    attendance_id = Column(PGUUID(as_uuid=True), ForeignKey("attendance_records.id"), nullable=True)
    is_processed = Column(Boolean, default=False)
    skip_auto_attendance = Column(Boolean, default=False)

    # Relationships
    shift = relationship("ShiftType", back_populates="checkins")
    attendance = relationship("AttendanceRecord", back_populates="checkins")


class ShiftType(Base, TimestampMixin, TenantMixin):
    """Shift type database model."""

    __tablename__ = "shift_types"

    id = Column(PGUUID(as_uuid=True), primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Shift timing
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    break_duration_minutes = Column(Integer, default=0)

    # Validation rules
    grace_period_minutes = Column(Integer, default=15)
    working_hours_threshold = Column(Numeric(5, 2), nullable=False)
    late_entry_threshold_minutes = Column(Integer, default=15)
    early_exit_threshold_minutes = Column(Integer, default=15)

    # Auto attendance settings
    enable_auto_attendance = Column(Boolean, default=True)
    enable_late_entry_marking = Column(Boolean, default=True)
    enable_early_exit_marking = Column(Boolean, default=True)

    # Location validation
    enable_location_validation = Column(Boolean, default=False)
    allowed_locations = Column(Text, nullable=True)  # JSON string
    checkin_radius_meters = Column(Integer, default=100)

    # Status
    status = Column(String(20), default=ShiftStatus.ACTIVE)
    is_active = Column(Boolean, default=True)

    # Relationships
    attendance_records = relationship("AttendanceRecord", back_populates="shift")
    checkins = relationship("CheckInRecord", back_populates="shift")


# Pydantic Models for API


# Base schemas
class AttendanceBase(PydanticBaseModel):
    """Base attendance schema."""

    employee_id: str = Field(..., min_length=1, max_length=50)
    attendance_date: date
    status: AttendanceStatus
    shift_id: Optional[str] = None
    working_hours: Optional[Decimal] = Field(None, ge=0, le=24)
    late_entry: bool = False
    early_exit: bool = False
    half_day_status: Optional[HalfDayStatus] = None
    notes: Optional[str] = Field(None, max_length=1000)


class CheckInBase(PydanticBaseModel):
    """Base check-in schema."""

    employee_id: str = Field(..., min_length=1, max_length=50)
    check_time: datetime
    check_type: CheckInType
    shift_id: Optional[str] = None
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    location_name: Optional[str] = Field(None, max_length=200)
    device_id: Optional[str] = Field(None, max_length=100)


class ShiftTypeBase(PydanticBaseModel):
    """Base shift type schema."""

    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    start_time: time
    end_time: time
    break_duration_minutes: int = Field(0, ge=0, le=480)
    grace_period_minutes: int = Field(15, ge=0, le=120)
    working_hours_threshold: Decimal = Field(..., ge=0, le=24)
    late_entry_threshold_minutes: int = Field(15, ge=0, le=120)
    early_exit_threshold_minutes: int = Field(15, ge=0, le=120)
    enable_auto_attendance: bool = True
    enable_late_entry_marking: bool = True
    enable_early_exit_marking: bool = True
    enable_location_validation: bool = False
    checkin_radius_meters: int = Field(100, ge=0, le=10000)
    is_active: bool = True


# Create schemas
class AttendanceCreate(AttendanceBase):
    """Schema for creating attendance records."""

    @field_validator("attendance_date")
    @classmethod
    def validate_attendance_date(cls, v):
        """Validate attendance date is not in the future."""
        if v > date.today():
            raise ValueError("Attendance date cannot be in the future")
        return v

    @field_validator("working_hours")
    @classmethod
    def validate_working_hours(cls, v):
        """Validate working hours based on status."""
        if v is not None and v < 0:
            raise ValueError("Working hours cannot be negative")
        return v


class CheckInCreate(CheckInBase):
    """Schema for creating check-in records."""

    @field_validator("check_time")
    @classmethod
    def validate_check_time(cls, v):
        """Validate check time is not too far in the future."""
        if v > datetime.now().replace(microsecond=0) + timedelta(minutes=5):
            raise ValueError("Check time cannot be more than 5 minutes in the future")
        return v


class ShiftTypeCreate(ShiftTypeBase):
    """Schema for creating shift types."""

    @field_validator("end_time")
    @classmethod
    def validate_shift_times(cls, v, info):
        """Validate shift timing logic."""
        if hasattr(info, "data") and "start_time" in info.data and v == info.data["start_time"]:
            raise ValueError("Start time and end time cannot be the same")
        return v


# Update schemas
class AttendanceUpdate(PydanticBaseModel):
    """Schema for updating attendance records."""

    status: Optional[AttendanceStatus] = None
    working_hours: Optional[Decimal] = Field(None, ge=0, le=24)
    late_entry: Optional[bool] = None
    early_exit: Optional[bool] = None
    half_day_status: Optional[HalfDayStatus] = None
    notes: Optional[str] = Field(None, max_length=1000)
    in_time: Optional[datetime] = None
    out_time: Optional[datetime] = None


class ShiftTypeUpdate(PydanticBaseModel):
    """Schema for updating shift types."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    break_duration_minutes: Optional[int] = Field(None, ge=0, le=480)
    grace_period_minutes: Optional[int] = Field(None, ge=0, le=120)
    working_hours_threshold: Optional[Decimal] = Field(None, ge=0, le=24)
    enable_auto_attendance: Optional[bool] = None
    enable_late_entry_marking: Optional[bool] = None
    enable_early_exit_marking: Optional[bool] = None
    enable_location_validation: Optional[bool] = None
    checkin_radius_meters: Optional[int] = Field(None, ge=0, le=10000)
    is_active: Optional[bool] = None


# Response schemas
class AttendanceResponse(AttendanceBase):
    """Schema for attendance record responses."""

    id: str
    in_time: Optional[datetime] = None
    out_time: Optional[datetime] = None
    break_duration_minutes: int = 0
    leave_type: Optional[str] = None
    leave_application_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CheckInResponse(CheckInBase):
    """Schema for check-in record responses."""

    id: str
    shift_start: Optional[datetime] = None
    shift_end: Optional[datetime] = None
    ip_address: Optional[str] = None
    is_processed: bool = False
    attendance_id: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ShiftTypeResponse(ShiftTypeBase):
    """Schema for shift type responses."""

    id: str
    status: ShiftStatus = ShiftStatus.ACTIVE
    allowed_locations: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Reporting schemas
class AttendanceSummary(PydanticBaseModel):
    """Schema for attendance summary reports."""

    employee_id: str
    total_days: int
    present_days: int
    absent_days: int
    half_days: int
    work_from_home_days: int
    on_leave_days: int
    total_working_hours: Decimal
    average_working_hours: Decimal
    attendance_percentage: Decimal
    late_entries: int
    early_exits: int


class TeamAttendanceReport(PydanticBaseModel):
    """Schema for team attendance reports."""

    department_id: Optional[str] = None
    team_name: Optional[str] = None
    total_employees: int
    present_today: int
    absent_today: int
    on_leave_today: int
    average_attendance_percentage: Decimal
    employees: List[AttendanceSummary]


# Bulk operation schemas
class BulkAttendanceCreate(PydanticBaseModel):
    """Schema for bulk attendance creation."""

    attendance_records: List[AttendanceCreate]

    @field_validator("attendance_records")
    @classmethod
    def validate_attendance_records(cls, v):
        """Validate bulk attendance records."""
        if len(v) == 0:
            raise ValueError("At least one attendance record is required")
        if len(v) > 1000:
            raise ValueError("Cannot process more than 1000 records at once")
        return v


class BulkAttendanceResponse(PydanticBaseModel):
    """Schema for bulk attendance operation responses."""

    total_records: int
    successful_records: int
    failed_records: int
    errors: List[Dict[str, Any]]
    created_records: List[AttendanceResponse]
