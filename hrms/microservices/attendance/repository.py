"""
Repository layer for Attendance Management Service.

Handles data access operations with tenant-aware queries.
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, asc, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..shared.database import TenantAwareRepository
from ..shared.exceptions import DatabaseError
from ..shared.logging import get_logger
from .models import AttendanceRecord, AttendanceStatus, CheckInRecord, ShiftType

logger = get_logger(__name__)


class AttendanceRepository(TenantAwareRepository):
    """Repository for attendance record operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(session, AttendanceRecord)

    async def check_duplicate(
        self, tenant_id: str, employee_id: str, attendance_date: date, shift_id: Optional[str] = None
    ) -> bool:
        """Check if attendance record already exists."""
        try:
            query = self._get_tenant_query(tenant_id).filter(
                and_(self.model.employee_id == employee_id, self.model.attendance_date == attendance_date)
            )

            if shift_id:
                query = query.filter(self.model.shift_id == shift_id)

            result = await self.session.execute(query)
            return result.first() is not None

        except Exception as e:
            logger.error(f"Error checking duplicate attendance: {str(e)}")
            raise DatabaseError(f"Failed to check duplicate attendance: {str(e)}")

    async def get_by_employee_date(
        self, tenant_id: str, employee_id: str, attendance_date: date
    ) -> Optional[AttendanceRecord]:
        """Get attendance record by employee and date."""
        try:
            query = (
                self._get_tenant_query(tenant_id)
                .filter(
                    and_(self.model.employee_id == employee_id, self.model.attendance_date == attendance_date)
                )
                .options(selectinload(self.model.shift))
            )

            result = await self.session.execute(query)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting attendance by employee date: {str(e)}")
            raise DatabaseError(f"Failed to get attendance: {str(e)}")

    async def list_with_filters(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[AttendanceRecord]:
        """List attendance records with filtering."""
        try:
            query = self._get_tenant_query(tenant_id).options(selectinload(self.model.shift))

            # Apply filters
            if filters:
                if "employee_id" in filters:
                    query = query.filter(self.model.employee_id == filters["employee_id"])

                if "start_date" in filters:
                    query = query.filter(self.model.attendance_date >= filters["start_date"])

                if "end_date" in filters:
                    query = query.filter(self.model.attendance_date <= filters["end_date"])

                if "status" in filters:
                    if isinstance(filters["status"], list):
                        query = query.filter(self.model.status.in_(filters["status"]))
                    else:
                        query = query.filter(self.model.status == filters["status"])

                if "shift_id" in filters:
                    query = query.filter(self.model.shift_id == filters["shift_id"])

                if "late_entry" in filters:
                    query = query.filter(self.model.late_entry == filters["late_entry"])

                if "early_exit" in filters:
                    query = query.filter(self.model.early_exit == filters["early_exit"])

            # Apply ordering
            query = query.order_by(desc(self.model.attendance_date), desc(self.model.created_at))

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error listing attendance records: {str(e)}")
            raise DatabaseError(f"Failed to list attendance records: {str(e)}")

    async def get_summary(
        self, tenant_id: str, employee_id: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """Get attendance summary for an employee in date range."""
        try:
            # Base query for the date range
            base_query = self._get_tenant_query(tenant_id).filter(
                and_(
                    self.model.employee_id == employee_id,
                    self.model.attendance_date >= start_date,
                    self.model.attendance_date <= end_date,
                )
            )

            # Count total days
            total_result = await self.session.execute(base_query.with_only_columns(func.count(self.model.id)))
            total_days = total_result.scalar() or 0

            # Count by status
            status_query = base_query.with_only_columns(
                self.model.status, func.count(self.model.id).label("count")
            ).group_by(self.model.status)

            status_result = await self.session.execute(status_query)
            status_counts = {row.status: row.count for row in status_result}

            # Calculate working hours statistics
            hours_query = base_query.with_only_columns(
                func.sum(self.model.working_hours).label("total_hours"),
                func.avg(self.model.working_hours).label("avg_hours"),
            ).filter(self.model.working_hours.isnot(None))

            hours_result = await self.session.execute(hours_query)
            hours_data = hours_result.first()

            # Count late entries and early exits
            late_result = await self.session.execute(
                base_query.filter(self.model.late_entry == True).with_only_columns(func.count(self.model.id))
            )
            late_entries = late_result.scalar() or 0

            early_result = await self.session.execute(
                base_query.filter(self.model.early_exit == True).with_only_columns(func.count(self.model.id))
            )
            early_exits = early_result.scalar() or 0

            return {
                "total_days": total_days,
                "present_days": status_counts.get(AttendanceStatus.PRESENT, 0),
                "absent_days": status_counts.get(AttendanceStatus.ABSENT, 0),
                "half_days": status_counts.get(AttendanceStatus.HALF_DAY, 0),
                "work_from_home_days": status_counts.get(AttendanceStatus.WORK_FROM_HOME, 0),
                "on_leave_days": status_counts.get(AttendanceStatus.ON_LEAVE, 0),
                "total_working_hours": hours_data.total_hours or Decimal(0),
                "average_working_hours": hours_data.avg_hours or Decimal(0),
                "late_entries": late_entries,
                "early_exits": early_exits,
            }

        except Exception as e:
            logger.error(f"Error getting attendance summary: {str(e)}")
            raise DatabaseError(f"Failed to get attendance summary: {str(e)}")

    async def get_team_summary(
        self, tenant_id: str, employee_ids: List[str], start_date: date, end_date: date
    ) -> List[Dict[str, Any]]:
        """Get attendance summary for multiple employees."""
        try:
            summaries = []
            for employee_id in employee_ids:
                summary = await self.get_summary(tenant_id, employee_id, start_date, end_date)
                summary["employee_id"] = employee_id
                summaries.append(summary)

            return summaries

        except Exception as e:
            logger.error(f"Error getting team attendance summary: {str(e)}")
            raise DatabaseError(f"Failed to get team attendance summary: {str(e)}")


class CheckInRepository(TenantAwareRepository):
    """Repository for check-in record operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(session, CheckInRecord)

    async def get_by_employee_date(
        self, tenant_id: str, employee_id: str, check_date: date
    ) -> List[CheckInRecord]:
        """Get check-in records for employee on specific date."""
        try:
            start_datetime = datetime.combine(check_date, datetime.min.time())
            end_datetime = datetime.combine(check_date, datetime.max.time())

            query = (
                self._get_tenant_query(tenant_id)
                .filter(
                    and_(
                        self.model.employee_id == employee_id,
                        self.model.check_time >= start_datetime,
                        self.model.check_time <= end_datetime,
                    )
                )
                .options(selectinload(self.model.shift))
                .order_by(self.model.check_time)
            )

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting check-ins by employee date: {str(e)}")
            raise DatabaseError(f"Failed to get check-ins: {str(e)}")

    async def get_by_attendance_id(self, tenant_id: str, attendance_id: str) -> List[CheckInRecord]:
        """Get check-in records linked to attendance."""
        try:
            query = (
                self._get_tenant_query(tenant_id)
                .filter(self.model.attendance_id == attendance_id)
                .order_by(self.model.check_time)
            )

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting check-ins by attendance: {str(e)}")
            raise DatabaseError(f"Failed to get check-ins: {str(e)}")

    async def get_unprocessed_checkins(self, tenant_id: str, limit: int = 1000) -> List[CheckInRecord]:
        """Get unprocessed check-in records for auto-attendance."""
        try:
            query = (
                self._get_tenant_query(tenant_id)
                .filter(and_(self.model.is_processed == False, self.model.skip_auto_attendance == False))
                .options(selectinload(self.model.shift))
                .order_by(self.model.check_time)
                .limit(limit)
            )

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting unprocessed check-ins: {str(e)}")
            raise DatabaseError(f"Failed to get unprocessed check-ins: {str(e)}")

    async def list_with_filters(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[CheckInRecord]:
        """List check-in records with filtering."""
        try:
            query = self._get_tenant_query(tenant_id).options(selectinload(self.model.shift))

            # Apply filters
            if filters:
                if "employee_id" in filters:
                    query = query.filter(self.model.employee_id == filters["employee_id"])

                if "start_date" in filters:
                    start_datetime = datetime.combine(filters["start_date"], datetime.min.time())
                    query = query.filter(self.model.check_time >= start_datetime)

                if "end_date" in filters:
                    end_datetime = datetime.combine(filters["end_date"], datetime.max.time())
                    query = query.filter(self.model.check_time <= end_datetime)

                if "check_type" in filters:
                    query = query.filter(self.model.check_type == filters["check_type"])

                if "shift_id" in filters:
                    query = query.filter(self.model.shift_id == filters["shift_id"])

                if "is_processed" in filters:
                    query = query.filter(self.model.is_processed == filters["is_processed"])

            # Apply ordering
            query = query.order_by(desc(self.model.check_time))

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error listing check-in records: {str(e)}")
            raise DatabaseError(f"Failed to list check-in records: {str(e)}")


class ShiftTypeRepository(TenantAwareRepository):
    """Repository for shift type operations."""

    def __init__(self, session: AsyncSession):
        super().__init__(session, ShiftType)

    async def check_name_exists(self, tenant_id: str, name: str, exclude_id: Optional[str] = None) -> bool:
        """Check if shift type name already exists."""
        try:
            query = self._get_tenant_query(tenant_id).filter(func.lower(self.model.name) == func.lower(name))

            if exclude_id:
                query = query.filter(self.model.id != exclude_id)

            result = await self.session.execute(query)
            return result.first() is not None

        except Exception as e:
            logger.error(f"Error checking shift name exists: {str(e)}")
            raise DatabaseError(f"Failed to check shift name: {str(e)}")

    async def get_active_shifts(self, tenant_id: str) -> List[ShiftType]:
        """Get all active shift types."""
        try:
            query = (
                self._get_tenant_query(tenant_id)
                .filter(self.model.is_active == True)
                .order_by(self.model.name)
            )

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting active shifts: {str(e)}")
            raise DatabaseError(f"Failed to get active shifts: {str(e)}")

    async def list_with_filters(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[ShiftType]:
        """List shift types with filtering."""
        try:
            query = self._get_tenant_query(tenant_id)

            # Apply filters
            if filters:
                if "is_active" in filters:
                    query = query.filter(self.model.is_active == filters["is_active"])

                if "status" in filters:
                    query = query.filter(self.model.status == filters["status"])

                if "name" in filters:
                    query = query.filter(self.model.name.ilike(f"%{filters['name']}%"))

            # Apply ordering
            query = query.order_by(self.model.name)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.session.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error listing shift types: {str(e)}")
            raise DatabaseError(f"Failed to list shift types: {str(e)}")
