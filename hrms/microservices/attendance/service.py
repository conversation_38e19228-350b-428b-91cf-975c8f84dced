"""
Business logic service for Attendance Management.

Handles attendance operations, validation, and business rules.
"""

import asyncio
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

from ..shared.exceptions import BusinessLogicError, ConflictError, NotFoundError, ValidationError
from ..shared.logging import get_logger
from .models import (
    AttendanceCreate,
    AttendanceResponse,
    AttendanceStatus,
    AttendanceSummary,
    AttendanceUpdate,
    CheckInCreate,
    CheckInResponse,
    CheckInType,
    HalfDayStatus,
    ShiftTypeCreate,
    ShiftTypeResponse,
    ShiftTypeUpdate,
    TeamAttendanceReport,
)

logger = get_logger(__name__)


class AttendanceService:
    """Service class for attendance management operations."""

    def __init__(
        self,
        attendance_repo,  # AttendanceRepository
        checkin_repo,  # CheckInRepository
        shift_repo,  # ShiftTypeRepository
        employee_service=None,
    ):
        self.attendance_repo = attendance_repo
        self.checkin_repo = checkin_repo
        self.shift_repo = shift_repo
        self.employee_service = employee_service

    # Attendance Operations
    async def create_attendance(
        self, tenant_id: str, attendance_data: AttendanceCreate, created_by: str
    ) -> AttendanceResponse:
        """Create new attendance record."""
        logger.info(f"Creating attendance for employee {attendance_data.employee_id}")

        # Validate employee exists and is active
        await self._validate_employee(tenant_id, attendance_data.employee_id)

        # Validate shift if provided
        if attendance_data.shift_id:
            await self._validate_shift(tenant_id, attendance_data.shift_id)

        # Check for duplicate attendance
        if await self.attendance_repo.check_duplicate(
            tenant_id, attendance_data.employee_id, attendance_data.attendance_date, attendance_data.shift_id
        ):
            raise ConflictError(
                f"Attendance already exists for employee {attendance_data.employee_id} "
                f"on {attendance_data.attendance_date}"
            )

        # Validate business rules
        await self._validate_attendance_business_rules(attendance_data)

        # Create attendance record
        attendance_dict = attendance_data.dict()
        attendance_dict["created_by"] = created_by

        # Auto-calculate status if working hours provided
        if attendance_data.working_hours is not None and attendance_data.shift_id:
            shift = await self.shift_repo.get_by_id(tenant_id, attendance_data.shift_id)
            if shift:
                calculated_status = self._calculate_attendance_status(
                    attendance_data.working_hours, shift.working_hours_threshold
                )
                if attendance_data.status != calculated_status:
                    attendance_dict["status"] = calculated_status

        attendance = await self.attendance_repo.create(tenant_id, attendance_dict)

        logger.info(f"Attendance created successfully: {attendance.id}")
        return AttendanceResponse.from_orm(attendance)

    async def get_attendance(self, tenant_id: str, attendance_id: str) -> AttendanceResponse:
        """Get attendance record by ID."""
        attendance = await self.attendance_repo.get_by_id(tenant_id, attendance_id)
        if not attendance:
            raise NotFoundError(f"Attendance not found: {attendance_id}")

        return AttendanceResponse.from_orm(attendance)

    async def update_attendance(
        self, tenant_id: str, attendance_id: str, update_data: AttendanceUpdate, updated_by: str
    ) -> AttendanceResponse:
        """Update attendance record."""
        logger.info(f"Updating attendance {attendance_id}")

        attendance = await self.attendance_repo.get_by_id(tenant_id, attendance_id)
        if not attendance:
            raise NotFoundError(f"Attendance not found: {attendance_id}")

        # Validate update data
        await self._validate_attendance_update(attendance, update_data)

        # Prepare update dictionary
        update_dict = {k: v for k, v in update_data.dict(exclude_unset=True).items() if v is not None}
        update_dict["updated_by"] = updated_by

        updated_attendance = await self.attendance_repo.update(tenant_id, attendance_id, update_dict)

        logger.info(f"Attendance updated successfully: {attendance_id}")
        return AttendanceResponse.from_orm(updated_attendance)

    async def delete_attendance(self, tenant_id: str, attendance_id: str) -> bool:
        """Delete attendance record (soft delete)."""
        logger.info(f"Deleting attendance {attendance_id}")

        attendance = await self.attendance_repo.get_by_id(tenant_id, attendance_id)
        if not attendance:
            raise NotFoundError(f"Attendance not found: {attendance_id}")

        # Check if attendance has linked check-ins
        checkins = await self.checkin_repo.get_by_attendance_id(tenant_id, attendance_id)
        if checkins:
            # Unlink check-ins instead of preventing deletion
            for checkin in checkins:
                await self.checkin_repo.update(
                    tenant_id, checkin.id, {"attendance_id": None, "is_processed": False}
                )

        result = await self.attendance_repo.delete(tenant_id, attendance_id)

        logger.info(f"Attendance deleted successfully: {attendance_id}")
        return result

    async def list_attendance(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[AttendanceResponse]:
        """List attendance records with filtering."""
        attendance_records = await self.attendance_repo.list_with_filters(tenant_id, skip, limit, filters)
        return [AttendanceResponse.from_orm(record) for record in attendance_records]

    # Check-in Operations
    async def create_checkin(self, tenant_id: str, checkin_data: CheckInCreate) -> CheckInResponse:
        """Create new check-in record."""
        logger.info(f"Creating check-in for employee {checkin_data.employee_id}")

        # Validate employee
        await self._validate_employee(tenant_id, checkin_data.employee_id)

        # Validate shift if provided
        if checkin_data.shift_id:
            shift = await self._validate_shift(tenant_id, checkin_data.shift_id)

            # Validate location if shift requires it
            if shift.enable_location_validation:
                await self._validate_checkin_location(checkin_data, shift)

        # Auto-detect shift if not provided
        if not checkin_data.shift_id:
            shift = await self._detect_employee_shift(
                tenant_id, checkin_data.employee_id, checkin_data.check_time
            )
            if shift:
                checkin_data.shift_id = str(shift.id)

        # Create check-in record
        checkin_dict = checkin_data.dict()
        checkin = await self.checkin_repo.create(tenant_id, checkin_dict)

        # Trigger auto-attendance processing if enabled
        if shift and shift.enable_auto_attendance:
            asyncio.create_task(
                self._process_auto_attendance_async(
                    tenant_id, checkin_data.employee_id, checkin_data.check_time.date()
                )
            )

        logger.info(f"Check-in created successfully: {checkin.id}")
        return CheckInResponse.from_orm(checkin)

    async def process_auto_attendance(
        self, tenant_id: str, employee_id: str, attendance_date: date
    ) -> Optional[AttendanceResponse]:
        """Process automatic attendance from check-ins."""
        logger.info(f"Processing auto attendance for {employee_id} on {attendance_date}")

        # Get check-ins for the day
        checkins = await self.checkin_repo.get_by_employee_date(tenant_id, employee_id, attendance_date)

        if not checkins:
            logger.info("No check-ins found for auto attendance processing")
            return None

        # Check if attendance already exists
        existing_attendance = await self.attendance_repo.get_by_employee_date(
            tenant_id, employee_id, attendance_date
        )

        if existing_attendance:
            logger.info("Attendance already exists, skipping auto processing")
            return AttendanceResponse.from_orm(existing_attendance)

        # Calculate attendance from check-ins
        attendance_data = await self._calculate_attendance_from_checkins(tenant_id, checkins, attendance_date)

        if not attendance_data:
            logger.info("Could not calculate attendance from check-ins")
            return None

        # Create attendance record
        attendance = await self.attendance_repo.create(tenant_id, attendance_data)

        # Link check-ins to attendance
        for checkin in checkins:
            await self.checkin_repo.update(
                tenant_id, checkin.id, {"attendance_id": str(attendance.id), "is_processed": True}
            )

        logger.info(f"Auto attendance processed successfully: {attendance.id}")
        return AttendanceResponse.from_orm(attendance)

    # Shift Management
    async def create_shift_type(
        self, tenant_id: str, shift_data: ShiftTypeCreate, created_by: str
    ) -> ShiftTypeResponse:
        """Create new shift type."""
        logger.info(f"Creating shift type: {shift_data.name}")

        # Check for duplicate name
        if await self.shift_repo.check_name_exists(tenant_id, shift_data.name):
            raise ConflictError(f"Shift type name already exists: {shift_data.name}")

        # Validate shift timing
        self._validate_shift_timing(shift_data.start_time, shift_data.end_time)

        # Create shift type
        shift_dict = shift_data.dict()
        shift_dict["created_by"] = created_by

        shift = await self.shift_repo.create(tenant_id, shift_dict)

        logger.info(f"Shift type created successfully: {shift.id}")
        return ShiftTypeResponse.from_orm(shift)

    async def get_shift_type(self, tenant_id: str, shift_id: str) -> ShiftTypeResponse:
        """Get shift type by ID."""
        shift = await self.shift_repo.get_by_id(tenant_id, shift_id)
        if not shift:
            raise NotFoundError(f"Shift type not found: {shift_id}")

        return ShiftTypeResponse.from_orm(shift)

    # Reporting
    async def get_attendance_summary(
        self, tenant_id: str, employee_id: str, start_date: date, end_date: date
    ) -> AttendanceSummary:
        """Get attendance summary for an employee."""
        summary_data = await self.attendance_repo.get_summary(tenant_id, employee_id, start_date, end_date)

        # Calculate attendance percentage
        total_days = summary_data.get("total_days", 0)
        present_days = summary_data.get("present_days", 0)
        attendance_percentage = Decimal(present_days / total_days * 100) if total_days > 0 else Decimal(0)

        return AttendanceSummary(
            employee_id=employee_id, attendance_percentage=round(attendance_percentage, 2), **summary_data
        )

    # Private helper methods
    async def _validate_employee(self, tenant_id: str, employee_id: str):
        """Validate employee exists and is active."""
        if self.employee_service:
            try:
                employee = await self.employee_service.get_employee(tenant_id, employee_id)
                if employee.get("status") != "ACTIVE":
                    raise ValidationError(f"Employee {employee_id} is not active")
            except NotFoundError:
                raise ValidationError(f"Employee not found: {employee_id}")

    async def _validate_shift(self, tenant_id: str, shift_id: str):
        """Validate shift exists and is active."""
        shift = await self.shift_repo.get_by_id(tenant_id, shift_id)
        if not shift:
            raise ValidationError(f"Shift not found: {shift_id}")
        if not shift.is_active:
            raise ValidationError(f"Shift {shift_id} is not active")
        return shift

    def _calculate_attendance_status(self, working_hours: Decimal, threshold: Decimal) -> AttendanceStatus:
        """Calculate attendance status based on working hours."""
        if working_hours >= threshold:
            return AttendanceStatus.PRESENT
        elif working_hours >= threshold * Decimal("0.5"):
            return AttendanceStatus.HALF_DAY
        else:
            return AttendanceStatus.ABSENT

    def _is_late_entry(self, shift_start: time, actual_time: time, grace_period_minutes: int) -> bool:
        """Check if entry is late."""
        grace_time = (
            datetime.combine(date.today(), shift_start) + timedelta(minutes=grace_period_minutes)
        ).time()
        return actual_time > grace_time

    def _is_early_exit(self, shift_end: time, actual_time: time, grace_period_minutes: int) -> bool:
        """Check if exit is early."""
        grace_time = (
            datetime.combine(date.today(), shift_end) - timedelta(minutes=grace_period_minutes)
        ).time()
        return actual_time < grace_time

    async def _process_auto_attendance_async(self, tenant_id: str, employee_id: str, attendance_date: date):
        """Async wrapper for auto attendance processing."""
        try:
            await self.process_auto_attendance(tenant_id, employee_id, attendance_date)
        except Exception as e:
            logger.error(f"Auto attendance processing failed: {str(e)}")

    def _validate_shift_timing(self, start_time: time, end_time: time):
        """Validate shift timing logic."""
        if start_time == end_time:
            raise ValidationError("Start time and end time cannot be the same")

    async def _validate_attendance_business_rules(self, attendance_data: AttendanceCreate):
        """Validate attendance business rules."""
        # Add custom business rule validations here
        pass

    async def _validate_attendance_update(self, attendance, update_data: AttendanceUpdate):
        """Validate attendance update rules."""
        # Add update validation rules here
        pass
