"""
Attendance Management Microservice

Comprehensive attendance tracking service for time management, shift scheduling,
and attendance reporting.

Features:
- Employee attendance tracking and management
- Check-in/check-out functionality with location validation
- Shift type management and assignment
- Automatic attendance calculation from check-ins
- Late entry and early exit detection
- Attendance reporting and analytics
- Multi-tenant data isolation
- Integration with Employee service

API Endpoints:
- GET /api/v1/attendance/records - List attendance records
- POST /api/v1/attendance/records - Create attendance record
- GET /api/v1/attendance/records/{id} - Get attendance details
- PUT /api/v1/attendance/records/{id} - Update attendance
- DELETE /api/v1/attendance/records/{id} - Delete attendance
- POST /api/v1/attendance/checkins - Create check-in record
- GET /api/v1/attendance/checkins/employee/{id} - Get employee check-ins
- POST /api/v1/attendance/process-auto - Process auto attendance
- GET /api/v1/attendance/shifts - List shift types
- POST /api/v1/attendance/shifts - Create shift type
- GET /api/v1/attendance/shifts/{id} - Get shift details
- GET /api/v1/attendance/reports/summary - Get attendance summary
- GET /api/v1/attendance/reports/team - Get team attendance report
"""

__version__ = "1.0.0"
__service_name__ = "attendance-service"
__service_port__ = 8102

# Service metadata
SERVICE_INFO = {
    "name": "Attendance Management Service",
    "version": __version__,
    "description": "Time tracking, shift management, and attendance reporting",
    "port": __service_port__,
    "api_prefix": "/api/v1/attendance",
    "health_endpoint": "/health",
    "docs_endpoint": "/docs",
    "openapi_endpoint": "/openapi.json",
}

# Feature flags
FEATURES = {
    "auto_attendance": True,
    "location_validation": True,
    "shift_management": True,
    "attendance_reporting": True,
    "bulk_operations": True,
    "real_time_notifications": False,  # Future feature
    "biometric_integration": False,  # Future feature
    "mobile_app_support": True,
}

# Business rules configuration
BUSINESS_RULES = {
    "max_working_hours_per_day": 24,
    "min_working_hours_for_full_day": 7.5,
    "default_grace_period_minutes": 15,
    "max_late_entry_threshold_minutes": 120,
    "max_early_exit_threshold_minutes": 120,
    "auto_attendance_processing_enabled": True,
    "location_validation_radius_meters": 100,
    "max_checkins_per_day": 20,
    "attendance_date_future_limit_days": 0,  # Cannot mark future attendance
    "attendance_date_past_limit_days": 30,  # Can mark up to 30 days in past
}

# Integration settings
INTEGRATIONS = {
    "employee_service": {"enabled": True, "base_url": "http://employee-service:8100", "timeout_seconds": 30},
    "payroll_service": {"enabled": True, "base_url": "http://payroll-service:8101", "timeout_seconds": 30},
    "notification_service": {
        "enabled": False,  # Future integration
        "base_url": "http://notification-service:8105",
        "timeout_seconds": 10,
    },
}

# Export main components
from .api import app
from .models import (
    AttendanceCreate,
    AttendanceResponse,
    AttendanceStatus,
    AttendanceSummary,
    AttendanceUpdate,
    CheckInCreate,
    CheckInResponse,
    CheckInType,
    HalfDayStatus,
    ShiftTypeCreate,
    ShiftTypeResponse,
    ShiftTypeUpdate,
    TeamAttendanceReport,
)
from .repository import AttendanceRepository, CheckInRepository, ShiftTypeRepository
from .service import AttendanceService

__all__ = [
    # Main application
    "app",
    # Service layer
    "AttendanceService",
    # Repository layer
    "AttendanceRepository",
    "CheckInRepository",
    "ShiftTypeRepository",
    # Models and schemas
    "AttendanceCreate",
    "AttendanceUpdate",
    "AttendanceResponse",
    "CheckInCreate",
    "CheckInResponse",
    "ShiftTypeCreate",
    "ShiftTypeUpdate",
    "ShiftTypeResponse",
    "AttendanceSummary",
    "TeamAttendanceReport",
    # Enums
    "AttendanceStatus",
    "CheckInType",
    "HalfDayStatus",
    # Metadata
    "SERVICE_INFO",
    "FEATURES",
    "BUSINESS_RULES",
    "INTEGRATIONS",
]
