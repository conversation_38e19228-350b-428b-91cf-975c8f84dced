"""
FastAPI application for Attendance Management Service.

Provides REST API endpoints for attendance, check-in, and shift management.
"""

from datetime import date, datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Path, Query, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from ..shared.auth import Tenant, User, get_current_tenant, get_current_user
from ..shared.database import get_db_session
from ..shared.exceptions import HRMSException, create_http_exception
from ..shared.logging import get_logger, get_request_logger, setup_logging
from ..shared.models import ErrorResponse, HealthStatus, PaginatedResponse, SuccessResponse
from ..shared.service_client import register_default_services
from ..shared.utils import paginate_query_params
from .models import (
    AttendanceCreate,
    AttendanceResponse,
    AttendanceSummary,
    AttendanceUpdate,
    BulkAttendanceCreate,
    BulkAttendanceResponse,
    CheckInCreate,
    CheckInResponse,
    ShiftTypeCreate,
    ShiftTypeResponse,
    Shift<PERSON><PERSON>Update,
    TeamAttendanceReport,
)
from .repository import AttendanceRepository, CheckInRepository, ShiftTypeRepository
from .service import AttendanceService

# Setup logging
setup_logging("attendance-service")
logger = get_logger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Attendance Management Service",
    description="""
    Microservice for attendance tracking, shift management, and time reporting.

    This service provides comprehensive attendance management capabilities including:
    - Employee attendance tracking and management
    - Check-in/check-out functionality with location validation
    - Shift type management and assignment
    - Automatic attendance calculation from check-ins
    - Attendance reporting and analytics
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)


# Startup event to initialize service clients
@app.on_event("startup")
async def startup_event():
    """Initialize service clients on startup."""
    await register_default_services()
    logger.info("Service clients initialized")


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)


# Request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all requests."""
    request_logger = get_request_logger("attendance-service")
    start_time = datetime.utcnow()

    response = await call_next(request)

    process_time = (datetime.utcnow() - start_time).total_seconds()
    request_logger.log_response(
        status_code=response.status_code,
        response_time_ms=process_time * 1000,  # Convert to milliseconds
        extra={"method": request.method, "path": str(request.url.path), "request_id": str(uuid4())},
    )

    return response


# Exception handlers
@app.exception_handler(HRMSException)
async def hrms_exception_handler(request, exc: HRMSException):
    """Handle HRMS custom exceptions."""
    return create_http_exception(exc)


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error", message="An unexpected error occurred"
        ).model_dump(),
    )


# Dependency injection
async def get_attendance_service(session=Depends(get_db_session)) -> AttendanceService:
    """Get attendance service instance."""
    attendance_repo = AttendanceRepository(session)
    checkin_repo = CheckInRepository(session)
    shift_repo = ShiftTypeRepository(session)

    # Initialize employee service client
    from hrms.microservices.shared.service_client import get_service_client
    from hrms.microservices.shared.clients.employee_client import create_employee_client

    employee_client = get_service_client('employee')
    employee_service = create_employee_client(employee_client) if employee_client else None

    return AttendanceService(
        attendance_repo=attendance_repo,
        checkin_repo=checkin_repo,
        shift_repo=shift_repo,
        employee_service=employee_service,
    )


# Health check endpoints
@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint."""
    from ..shared.health_check import create_health_check_endpoint, get_health_checker

    # Configure health checker with dependencies
    checker = get_health_checker("attendance-service", "1.0.0")
    checker.add_required_dependency("employee")
    checker.add_optional_dependency("payroll")

    health = await create_health_check_endpoint("attendance-service", "1.0.0")

    # Return simple format for basic health checks
    if health.status == "healthy":
        return {
            "status": "healthy",
            "service": "attendance-service",
            "version": "1.0.0",
            "timestamp": health.timestamp
        }
    else:
        from fastapi import HTTPException
        raise HTTPException(
            status_code=503,
            detail={
                "status": health.status,
                "service": "attendance-service",
                "version": "1.0.0",
                "timestamp": health.timestamp,
                "checks": [check.dict() for check in health.checks],
                "dependencies": {k: v.dict() for k, v in health.dependencies.items()}
            }
        )


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check endpoint."""
    from ..shared.health_check import create_health_check_endpoint, get_health_checker

    # Configure health checker with dependencies
    checker = get_health_checker("attendance-service", "1.0.0")
    checker.add_required_dependency("employee")
    checker.add_optional_dependency("payroll")

    return await create_health_check_endpoint("attendance-service", "1.0.0")


@app.get("/info")
async def service_info():
    """Service information endpoint."""
    return {
        "service_name": "attendance-service",
        "version": "1.0.0",
        "description": "Attendance Management Service",
        "api_version": "v1",
        "endpoints": {
            "attendance": "/api/v1/attendance/records",
            "checkins": "/api/v1/attendance/checkins",
            "shifts": "/api/v1/attendance/shifts",
            "reports": "/api/v1/attendance/reports",
        },
    }


# Attendance Management Endpoints
@app.post(
    "/api/v1/attendance/records",
    response_model=SuccessResponse[AttendanceResponse],
    status_code=status.HTTP_201_CREATED,
)
async def create_attendance(
    attendance_data: AttendanceCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Create new attendance record."""
    try:
        result = await service.create_attendance(current_tenant.id, attendance_data, current_user.id)
        return SuccessResponse(data=result, message="Attendance created successfully")
    except Exception as e:
        logger.error(f"Error creating attendance: {str(e)}")
        raise


@app.get("/api/v1/attendance/records/{attendance_id}", response_model=SuccessResponse[AttendanceResponse])
async def get_attendance(
    attendance_id: str = Path(..., description="Attendance record ID"),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Get attendance record by ID."""
    try:
        result = await service.get_attendance(current_tenant.id, attendance_id)
        return SuccessResponse(data=result)
    except Exception as e:
        logger.error(f"Error getting attendance: {str(e)}")
        raise


@app.get("/api/v1/attendance/records", response_model=PaginatedResponse[AttendanceResponse])
async def list_attendance_records(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    employee_id: Optional[str] = Query(None, description="Filter by employee ID"),
    start_date: Optional[date] = Query(None, description="Filter from date"),
    end_date: Optional[date] = Query(None, description="Filter to date"),
    status: Optional[str] = Query(None, description="Filter by attendance status"),
    shift_id: Optional[str] = Query(None, description="Filter by shift ID"),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """List attendance records with filtering and pagination."""
    try:
        filters = {}
        if employee_id:
            filters["employee_id"] = employee_id
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if status:
            filters["status"] = status
        if shift_id:
            filters["shift_id"] = shift_id

        records = await service.list_attendance(current_tenant.id, skip, limit, filters)

        return PaginatedResponse(data=records, pagination=paginate_query_params(skip, limit, len(records)))
    except Exception as e:
        logger.error(f"Error listing attendance records: {str(e)}")
        raise


@app.put("/api/v1/attendance/records/{attendance_id}", response_model=SuccessResponse[AttendanceResponse])
async def update_attendance(
    attendance_id: str,
    update_data: AttendanceUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Update attendance record."""
    try:
        result = await service.update_attendance(
            current_tenant.id, attendance_id, update_data, current_user.id
        )
        return SuccessResponse(data=result, message="Attendance updated successfully")
    except Exception as e:
        logger.error(f"Error updating attendance: {str(e)}")
        raise


@app.delete("/api/v1/attendance/records/{attendance_id}", response_model=SuccessResponse[bool])
async def delete_attendance(
    attendance_id: str,
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Delete attendance record."""
    try:
        result = await service.delete_attendance(current_tenant.id, attendance_id)
        return SuccessResponse(data=result, message="Attendance deleted successfully")
    except Exception as e:
        logger.error(f"Error deleting attendance: {str(e)}")
        raise


# Check-in Management Endpoints
@app.post(
    "/api/v1/attendance/checkins",
    response_model=SuccessResponse[CheckInResponse],
    status_code=status.HTTP_201_CREATED,
)
async def create_checkin(
    checkin_data: CheckInCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Create new check-in record."""
    try:
        result = await service.create_checkin(current_tenant.id, checkin_data)
        return SuccessResponse(data=result, message="Check-in created successfully")
    except Exception as e:
        logger.error(f"Error creating check-in: {str(e)}")
        raise


@app.get(
    "/api/v1/attendance/checkins/employee/{employee_id}",
    response_model=SuccessResponse[List[CheckInResponse]],
)
async def get_employee_checkins(
    employee_id: str,
    start_date: Optional[date] = Query(None, description="Filter from date"),
    end_date: Optional[date] = Query(None, description="Filter to date"),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Get check-in records for an employee."""
    try:
        # Implementation would call checkin repository directly
        # This is a simplified version
        return SuccessResponse(data=[], message="Check-ins retrieved successfully")
    except Exception as e:
        logger.error(f"Error getting employee check-ins: {str(e)}")
        raise


@app.post("/api/v1/attendance/process-auto", response_model=SuccessResponse[Optional[AttendanceResponse]])
async def process_auto_attendance(
    request_data: Dict[str, Any],
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Process automatic attendance from check-ins."""
    try:
        employee_id = request_data.get("employee_id")
        attendance_date = date.fromisoformat(request_data.get("attendance_date"))

        result = await service.process_auto_attendance(current_tenant.id, employee_id, attendance_date)

        message = "Auto attendance processed successfully" if result else "No attendance to process"
        return SuccessResponse(data=result, message=message)
    except Exception as e:
        logger.error(f"Error processing auto attendance: {str(e)}")
        raise


# Shift Management Endpoints
@app.post(
    "/api/v1/attendance/shifts",
    response_model=SuccessResponse[ShiftTypeResponse],
    status_code=status.HTTP_201_CREATED,
)
async def create_shift_type(
    shift_data: ShiftTypeCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Create new shift type."""
    try:
        result = await service.create_shift_type(current_tenant.id, shift_data, current_user.id)
        return SuccessResponse(data=result, message="Shift type created successfully")
    except Exception as e:
        logger.error(f"Error creating shift type: {str(e)}")
        raise


@app.get("/api/v1/attendance/shifts/{shift_id}", response_model=SuccessResponse[ShiftTypeResponse])
async def get_shift_type(
    shift_id: str,
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Get shift type by ID."""
    try:
        result = await service.get_shift_type(current_tenant.id, shift_id)
        return SuccessResponse(data=result)
    except Exception as e:
        logger.error(f"Error getting shift type: {str(e)}")
        raise


@app.get("/api/v1/attendance/shifts", response_model=SuccessResponse[List[ShiftTypeResponse]])
async def list_shift_types(
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """List all shift types."""
    try:
        # Implementation would call shift repository
        return SuccessResponse(data=[], message="Shift types retrieved successfully")
    except Exception as e:
        logger.error(f"Error listing shift types: {str(e)}")
        raise


# Reporting Endpoints
@app.get("/api/v1/attendance/reports/summary", response_model=SuccessResponse[AttendanceSummary])
async def get_attendance_summary(
    employee_id: str = Query(..., description="Employee ID"),
    start_date: date = Query(..., description="Report start date"),
    end_date: date = Query(..., description="Report end date"),
    current_tenant: Tenant = Depends(get_current_tenant),
    service: AttendanceService = Depends(get_attendance_service),
):
    """Get attendance summary for an employee."""
    try:
        result = await service.get_attendance_summary(current_tenant.id, employee_id, start_date, end_date)
        return SuccessResponse(data=result, message="Attendance summary generated successfully")
    except Exception as e:
        logger.error(f"Error generating attendance summary: {str(e)}")
        raise


# Development server
if __name__ == "__main__":
    uvicorn.run(
        "hrms.microservices.attendance.api:app", host="0.0.0.0", port=8102, reload=True, log_level="debug"
    )
