openapi: 3.0.3
info:
  title: Attendance Management Service
  description: |
    Microservice for attendance tracking, shift management, and time reporting.

    This service provides comprehensive attendance management capabilities including:
    - Employee attendance tracking and management
    - Check-in/check-out functionality with location validation
    - Shift type management and assignment
    - Automatic attendance calculation from check-ins
    - Attendance reporting and analytics
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.

    ## Rate Limiting
    API requests are rate limited to prevent abuse. Standard limits apply:
    - 1000 requests per hour for authenticated users
    - 100 requests per hour for unauthenticated requests

    ## Error Handling
    The API uses standard HTTP status codes and returns detailed error messages
    in a consistent format for all error responses.
  version: 1.0.0
  contact:
    name: oneHRMS Development Team
    email: <EMAIL>
    url: https://onehrms.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8102
    description: Development server
  - url: https://api.onehrms.com/attendance
    description: Production server

security:
  - BearerAuth: []
  - TenantHeader: []

paths:
  /health:
    get:
      summary: Health check
      description: Check service health and status
      tags:
        - Health
      security: []
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"

  /info:
    get:
      summary: Service information
      description: Get service metadata and endpoint information
      tags:
        - Health
      security: []
      responses:
        "200":
          description: Service information
          content:
            application/json:
              schema:
                type: object
                properties:
                  service_name:
                    type: string
                  version:
                    type: string
                  description:
                    type: string

  /api/v1/attendance/records:
    post:
      summary: Create attendance record
      description: Create a new attendance record for an employee
      tags:
        - Attendance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AttendanceCreate"
      responses:
        "201":
          description: Attendance record created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AttendanceResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"

    get:
      summary: List attendance records
      description: Get a paginated list of attendance records with optional filtering
      tags:
        - Attendance
      parameters:
        - name: skip
          in: query
          description: Number of records to skip for pagination
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: limit
          in: query
          description: Maximum number of records to return
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
        - name: employee_id
          in: query
          description: Filter by employee ID
          schema:
            type: string
        - name: start_date
          in: query
          description: Filter records from this date (inclusive)
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter records to this date (inclusive)
          schema:
            type: string
            format: date
        - name: status
          in: query
          description: Filter by attendance status
          schema:
            $ref: "#/components/schemas/AttendanceStatus"
        - name: shift_id
          in: query
          description: Filter by shift ID
          schema:
            type: string
      responses:
        "200":
          description: List of attendance records
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/PaginatedResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/AttendanceResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/records/{attendance_id}:
    get:
      summary: Get attendance record
      description: Retrieve a specific attendance record by ID
      tags:
        - Attendance
      parameters:
        - name: attendance_id
          in: path
          required: true
          description: Attendance record ID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Attendance record details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AttendanceResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

    put:
      summary: Update attendance record
      description: Update an existing attendance record
      tags:
        - Attendance
      parameters:
        - name: attendance_id
          in: path
          required: true
          description: Attendance record ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AttendanceUpdate"
      responses:
        "200":
          description: Attendance record updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AttendanceResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

    delete:
      summary: Delete attendance record
      description: Delete an attendance record (soft delete)
      tags:
        - Attendance
      parameters:
        - name: attendance_id
          in: path
          required: true
          description: Attendance record ID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Attendance record deleted successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        type: boolean
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/checkins:
    post:
      summary: Create check-in record
      description: Create a new check-in or check-out record
      tags:
        - Check-ins
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckInCreate"
      responses:
        "201":
          description: Check-in record created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CheckInResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/checkins/employee/{employee_id}:
    get:
      summary: Get employee check-ins
      description: Get check-in records for a specific employee
      tags:
        - Check-ins
      parameters:
        - name: employee_id
          in: path
          required: true
          description: Employee ID
          schema:
            type: string
        - name: start_date
          in: query
          description: Filter from this date
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter to this date
          schema:
            type: string
            format: date
      responses:
        "200":
          description: List of check-in records
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/CheckInResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/process-auto:
    post:
      summary: Process automatic attendance
      description: Process automatic attendance calculation from check-ins
      tags:
        - Attendance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - employee_id
                - attendance_date
              properties:
                employee_id:
                  type: string
                  description: Employee ID
                attendance_date:
                  type: string
                  format: date
                  description: Date to process attendance for
      responses:
        "200":
          description: Auto attendance processed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        oneOf:
                          - $ref: "#/components/schemas/AttendanceResponse"
                          - type: "null"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/shifts:
    post:
      summary: Create shift type
      description: Create a new shift type
      tags:
        - Shifts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShiftTypeCreate"
      responses:
        "201":
          description: Shift type created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ShiftTypeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "409":
          $ref: "#/components/responses/Conflict"

    get:
      summary: List shift types
      description: Get a list of all shift types
      tags:
        - Shifts
      responses:
        "200":
          description: List of shift types
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/ShiftTypeResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/shifts/{shift_id}:
    get:
      summary: Get shift type
      description: Retrieve a specific shift type by ID
      tags:
        - Shifts
      parameters:
        - name: shift_id
          in: path
          required: true
          description: Shift type ID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Shift type details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ShiftTypeResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/attendance/reports/summary:
    get:
      summary: Get attendance summary
      description: Generate attendance summary report for an employee
      tags:
        - Reports
      parameters:
        - name: employee_id
          in: query
          required: true
          description: Employee ID
          schema:
            type: string
        - name: start_date
          in: query
          required: true
          description: Report start date
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          required: true
          description: Report end date
          schema:
            type: string
            format: date
      responses:
        "200":
          description: Attendance summary report
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AttendanceSummary"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication
    TenantHeader:
      type: apiKey
      in: header
      name: X-Tenant-ID
      description: Tenant identifier for multi-tenancy

  schemas:
    AttendanceStatus:
      type: string
      enum:
        - PRESENT
        - ABSENT
        - HALF_DAY
        - ON_LEAVE
        - WORK_FROM_HOME
      description: Attendance status enumeration

    CheckInType:
      type: string
      enum:
        - CHECK_IN
        - CHECK_OUT
        - BREAK_START
        - BREAK_END
      description: Check-in type enumeration

    HalfDayStatus:
      type: string
      enum:
        - FIRST_HALF
        - SECOND_HALF
      description: Half day status enumeration

    AttendanceCreate:
      type: object
      required:
        - employee_id
        - attendance_date
        - status
      properties:
        employee_id:
          type: string
          minLength: 1
          maxLength: 50
          description: Employee identifier
        attendance_date:
          type: string
          format: date
          description: Date of attendance
        status:
          $ref: "#/components/schemas/AttendanceStatus"
        shift_id:
          type: string
          format: uuid
          description: Shift type identifier
        working_hours:
          type: number
          format: decimal
          minimum: 0
          maximum: 24
          description: Total working hours
        late_entry:
          type: boolean
          default: false
          description: Whether employee was late
        early_exit:
          type: boolean
          default: false
          description: Whether employee left early
        half_day_status:
          $ref: "#/components/schemas/HalfDayStatus"
        notes:
          type: string
          maxLength: 1000
          description: Additional notes

    AttendanceUpdate:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/AttendanceStatus"
        working_hours:
          type: number
          format: decimal
          minimum: 0
          maximum: 24
        late_entry:
          type: boolean
        early_exit:
          type: boolean
        half_day_status:
          $ref: "#/components/schemas/HalfDayStatus"
        notes:
          type: string
          maxLength: 1000
        in_time:
          type: string
          format: date-time
        out_time:
          type: string
          format: date-time

    AttendanceResponse:
      allOf:
        - $ref: "#/components/schemas/AttendanceCreate"
        - type: object
          required:
            - id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Unique attendance record identifier
            in_time:
              type: string
              format: date-time
              description: Check-in time
            out_time:
              type: string
              format: date-time
              description: Check-out time
            break_duration_minutes:
              type: integer
              default: 0
              description: Break duration in minutes
            leave_type:
              type: string
              description: Type of leave if on leave
            leave_application_id:
              type: string
              description: Related leave application ID
            created_at:
              type: string
              format: date-time
              description: Record creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Record last update timestamp

    CheckInCreate:
      type: object
      required:
        - employee_id
        - check_time
        - check_type
      properties:
        employee_id:
          type: string
          minLength: 1
          maxLength: 50
          description: Employee identifier
        check_time:
          type: string
          format: date-time
          description: Check-in/out timestamp
        check_type:
          $ref: "#/components/schemas/CheckInType"
        shift_id:
          type: string
          format: uuid
          description: Shift type identifier
        latitude:
          type: number
          format: decimal
          minimum: -90
          maximum: 90
          description: GPS latitude
        longitude:
          type: number
          format: decimal
          minimum: -180
          maximum: 180
          description: GPS longitude
        location_name:
          type: string
          maxLength: 200
          description: Location name or address
        device_id:
          type: string
          maxLength: 100
          description: Device identifier

    CheckInResponse:
      allOf:
        - $ref: "#/components/schemas/CheckInCreate"
        - type: object
          required:
            - id
            - created_at
          properties:
            id:
              type: string
              format: uuid
              description: Unique check-in record identifier
            shift_start:
              type: string
              format: date-time
              description: Shift start time
            shift_end:
              type: string
              format: date-time
              description: Shift end time
            ip_address:
              type: string
              description: IP address of the request
            is_processed:
              type: boolean
              default: false
              description: Whether processed for attendance
            attendance_id:
              type: string
              format: uuid
              description: Linked attendance record ID
            created_at:
              type: string
              format: date-time
              description: Record creation timestamp

    ShiftTypeCreate:
      type: object
      required:
        - name
        - start_time
        - end_time
        - working_hours_threshold
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          description: Shift type name
        description:
          type: string
          maxLength: 500
          description: Shift description
        start_time:
          type: string
          format: time
          description: Shift start time
        end_time:
          type: string
          format: time
          description: Shift end time
        break_duration_minutes:
          type: integer
          minimum: 0
          maximum: 480
          default: 0
          description: Break duration in minutes
        grace_period_minutes:
          type: integer
          minimum: 0
          maximum: 120
          default: 15
          description: Grace period for late entry
        working_hours_threshold:
          type: number
          format: decimal
          minimum: 0
          maximum: 24
          description: Minimum hours for full attendance
        late_entry_threshold_minutes:
          type: integer
          minimum: 0
          maximum: 120
          default: 15
          description: Late entry threshold in minutes
        early_exit_threshold_minutes:
          type: integer
          minimum: 0
          maximum: 120
          default: 15
          description: Early exit threshold in minutes
        enable_auto_attendance:
          type: boolean
          default: true
          description: Enable automatic attendance processing
        enable_late_entry_marking:
          type: boolean
          default: true
          description: Enable late entry marking
        enable_early_exit_marking:
          type: boolean
          default: true
          description: Enable early exit marking
        enable_location_validation:
          type: boolean
          default: false
          description: Enable location-based validation
        checkin_radius_meters:
          type: integer
          minimum: 0
          maximum: 10000
          default: 100
          description: Allowed check-in radius in meters
        is_active:
          type: boolean
          default: true
          description: Whether shift type is active

    ShiftTypeResponse:
      allOf:
        - $ref: "#/components/schemas/ShiftTypeCreate"
        - type: object
          required:
            - id
            - status
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Unique shift type identifier
            status:
              type: string
              enum: [ACTIVE, INACTIVE, DRAFT]
              description: Shift type status
            allowed_locations:
              type: string
              description: JSON string of allowed locations
            created_at:
              type: string
              format: date-time
              description: Record creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Record last update timestamp

    AttendanceSummary:
      type: object
      required:
        - employee_id
        - total_days
        - present_days
        - absent_days
        - half_days
        - work_from_home_days
        - on_leave_days
        - total_working_hours
        - average_working_hours
        - attendance_percentage
        - late_entries
        - early_exits
      properties:
        employee_id:
          type: string
          description: Employee identifier
        total_days:
          type: integer
          description: Total days in period
        present_days:
          type: integer
          description: Number of present days
        absent_days:
          type: integer
          description: Number of absent days
        half_days:
          type: integer
          description: Number of half days
        work_from_home_days:
          type: integer
          description: Number of work from home days
        on_leave_days:
          type: integer
          description: Number of leave days
        total_working_hours:
          type: number
          format: decimal
          description: Total working hours
        average_working_hours:
          type: number
          format: decimal
          description: Average working hours per day
        attendance_percentage:
          type: number
          format: decimal
          description: Attendance percentage
        late_entries:
          type: integer
          description: Number of late entries
        early_exits:
          type: integer
          description: Number of early exits

    HealthStatus:
      type: object
      required:
        - status
        - service
        - version
        - timestamp
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
          description: Service health status
        service:
          type: string
          description: Service name
        version:
          type: string
          description: Service version
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp

    SuccessResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
          description: Indicates successful operation
        data:
          description: Response data (type varies by endpoint)
        message:
          type: string
          description: Optional success message

    ErrorResponse:
      type: object
      required:
        - success
        - error
      properties:
        success:
          type: boolean
          example: false
          description: Indicates failed operation
        error:
          type: string
          description: Error type or code
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional error details

    PaginatedResponse:
      type: object
      required:
        - success
        - data
        - pagination
      properties:
        success:
          type: boolean
          example: true
          description: Indicates successful operation
        data:
          type: array
          description: Array of response data
        pagination:
          type: object
          required:
            - skip
            - limit
            - total
          properties:
            skip:
              type: integer
              description: Number of records skipped
            limit:
              type: integer
              description: Maximum records returned
            total:
              type: integer
              description: Total records available

  responses:
    BadRequest:
      description: Bad request - invalid input data
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            error: "VALIDATION_ERROR"
            message: "Invalid input data provided"

    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            error: "UNAUTHORIZED"
            message: "Authentication required"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            error: "FORBIDDEN"
            message: "Insufficient permissions"

    NotFound:
      description: Not found - resource does not exist
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            error: "NOT_FOUND"
            message: "Resource not found"

    Conflict:
      description: Conflict - resource already exists
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            error: "CONFLICT"
            message: "Resource already exists"

tags:
  - name: Health
    description: Health check and service information endpoints
  - name: Attendance
    description: Attendance record management operations
  - name: Check-ins
    description: Check-in and check-out operations
  - name: Shifts
    description: Shift type management operations
  - name: Reports
    description: Attendance reporting and analytics
