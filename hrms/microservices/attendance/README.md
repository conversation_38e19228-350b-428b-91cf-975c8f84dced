# Attendance Management Service

The Attendance Management Service is a comprehensive microservice for tracking employee attendance, managing shifts, and generating attendance reports within the oneHRMS platform.

## Features

### Core Functionality
- **Attendance Tracking**: Create, update, and manage employee attendance records
- **Check-in/Check-out**: Real-time employee check-in and check-out functionality
- **Shift Management**: Define and manage shift types with flexible scheduling
- **Auto Attendance**: Automatic attendance calculation from check-in records
- **Location Validation**: GPS-based location validation for check-ins
- **Reporting**: Comprehensive attendance reports and analytics

### Business Rules
- **Working Hours Calculation**: Automatic calculation based on check-in/out times
- **Late Entry Detection**: Configurable grace periods and late entry marking
- **Early Exit Detection**: Early departure tracking and reporting
- **Half Day Logic**: Intelligent half-day attendance based on working hours
- **Multi-shift Support**: Support for multiple shifts per day

### Technical Features
- **Multi-tenant Architecture**: Complete tenant isolation
- **OpenAPI Compliance**: Full OpenAPI 3.0 specification
- **Hot Reload Development**: Fast development with automatic reloading
- **Comprehensive Testing**: 90%+ test coverage with TDD approach
- **Docker Support**: Multi-stage Docker builds for all environments

## Architecture

### Service Structure
```
hrms/microservices/attendance/
├── __init__.py          # Service package initialization
├── api.py               # FastAPI application and endpoints
├── service.py           # Business logic layer
├── repository.py        # Data access layer
├── models.py            # Pydantic models and database schemas
├── openapi.yaml         # OpenAPI specification
├── Dockerfile           # Multi-stage Docker configuration
└── README.md            # This documentation
```

### Database Models
- **AttendanceRecord**: Core attendance tracking
- **CheckInRecord**: Check-in/check-out events
- **ShiftType**: Shift definitions and rules

### API Endpoints

#### Attendance Management
- `POST /api/v1/attendance/records` - Create attendance record
- `GET /api/v1/attendance/records` - List attendance records
- `GET /api/v1/attendance/records/{id}` - Get attendance details
- `PUT /api/v1/attendance/records/{id}` - Update attendance
- `DELETE /api/v1/attendance/records/{id}` - Delete attendance

#### Check-in Management
- `POST /api/v1/attendance/checkins` - Create check-in record
- `GET /api/v1/attendance/checkins/employee/{id}` - Get employee check-ins
- `POST /api/v1/attendance/process-auto` - Process auto attendance

#### Shift Management
- `POST /api/v1/attendance/shifts` - Create shift type
- `GET /api/v1/attendance/shifts` - List shift types
- `GET /api/v1/attendance/shifts/{id}` - Get shift details

#### Reporting
- `GET /api/v1/attendance/reports/summary` - Attendance summary
- `GET /api/v1/attendance/reports/team` - Team attendance report

## Development Setup

### Prerequisites
- Python 3.9+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose
- uv (Astral package manager) - recommended for faster installs

### Local Development

1. **Install uv (Recommended)**
   ```bash
   # Install uv for faster package management
   curl -LsSf https://astral.sh/uv/install.sh | sh
   source ~/.bashrc  # or restart terminal
   ```

2. **Clone and Setup**
   ```bash
   cd oneHRMS
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Dependencies**
   ```bash
   # Using uv (recommended - much faster)
   uv pip install -e ".[dev]"

   # Or using traditional pip
   pip install -r requirements-dev.txt
   ```

4. **Database Setup**
   ```bash
   # Start PostgreSQL and Redis
   docker-compose -f docker-compose.microservices.yml up postgres redis -d

   # Run migrations (when available)
   alembic upgrade head
   ```

5. **Run Service**
   ```bash
   # Using the startup script (recommended)
   python scripts/start_attendance_service.py --dev

   # Or manually with uvicorn
   uvicorn hrms.microservices.attendance.api:app --host 0.0.0.0 --port 8102 --reload
   ```

6. **Access Documentation**
   - API Docs: http://localhost:8102/docs
   - ReDoc: http://localhost:8102/redoc
   - Health Check: http://localhost:8102/health

### Quick Start with uv

```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies and start service
python scripts/start_attendance_service.py --dev
```

### Docker Development

```bash
# Build and run all services
docker-compose -f docker-compose.microservices.yml up attendance-service

# Run with dependencies
docker-compose -f docker-compose.microservices.yml up postgres redis employee-service attendance-service
```

## Testing

### Test Structure
```
tests/microservices/attendance/
├── test_attendance_service.py  # Service layer tests
├── test_attendance_api.py      # API endpoint tests
├── test_attendance_models.py   # Model validation tests
└── conftest.py                 # Test fixtures
```

### Running Tests

```bash
# Run all attendance tests
pytest tests/microservices/attendance/ -v

# Run with coverage
pytest tests/microservices/attendance/ --cov=hrms.microservices.attendance --cov-report=html

# Run specific test categories
pytest tests/microservices/attendance/ -m "unit"
pytest tests/microservices/attendance/ -m "integration"
```

### Test Coverage Target
- **Minimum**: 90% code coverage
- **Service Layer**: 95% coverage
- **API Layer**: 90% coverage
- **Repository Layer**: 95% coverage

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://...` |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379/2` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `TESTING` | Enable test mode | `false` |
| `KEYCLOAK_URL` | Keycloak server URL | `http://localhost:8080` |
| `EMPLOYEE_SERVICE_URL` | Employee service URL | `http://localhost:8100` |

### Business Rules Configuration

```python
BUSINESS_RULES = {
    "max_working_hours_per_day": 24,
    "min_working_hours_for_full_day": 7.5,
    "default_grace_period_minutes": 15,
    "auto_attendance_processing_enabled": True,
    "location_validation_radius_meters": 100
}
```

## Integration

### Employee Service Integration
- Employee validation and status checking
- Department and position information
- Manager hierarchy for approvals

### Payroll Service Integration
- Working hours data for salary calculation
- Overtime and attendance-based deductions
- Leave integration for payroll processing

### Authentication
- Keycloak OIDC/JWT authentication
- Multi-tenant access control
- Role-based permissions

## Monitoring and Observability

### Health Checks
- `/health` - Service health status
- `/info` - Service metadata
- Database connectivity checks
- Redis connectivity checks

### Logging
- Structured JSON logging
- Request/response logging
- Error tracking and alerting
- Performance metrics

### Metrics
- Request latency and throughput
- Database query performance
- Cache hit rates
- Business metrics (attendance rates, etc.)

## Deployment

### Production Deployment

```bash
# Build production image
docker build -f hrms/microservices/attendance/Dockerfile --target production -t attendance-service:latest .

# Deploy with Docker Compose
docker-compose -f docker-compose.microservices.yml up -d attendance-service
```

### Kubernetes Deployment
- Helm charts available in `/k8s` directory
- Horizontal Pod Autoscaling configured
- Health checks and readiness probes
- ConfigMaps for environment-specific settings

## API Documentation

### OpenAPI Specification
The service provides a complete OpenAPI 3.0 specification at `/openapi.json` and interactive documentation at `/docs`.

### Authentication
All endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt-token>
X-Tenant-ID: <tenant-id>
```

### Error Handling
Standardized error responses with appropriate HTTP status codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate resource)
- `500` - Internal Server Error

## Contributing

### Development Guidelines
1. Follow TDD principles - write tests first
2. Maintain 90%+ test coverage
3. Use type hints throughout
4. Follow PEP 8 style guidelines
5. Update documentation for new features

### Code Review Process
1. Create feature branch from `main`
2. Implement feature with tests
3. Ensure all tests pass
4. Submit pull request
5. Code review and approval
6. Merge to `main`

## Support

### Documentation
- [oneHRMS Documentation](https://docs.onehrms.com)
- [API Reference](https://api.onehrms.com/attendance/docs)
- [Architecture Guide](https://docs.onehrms.com/architecture)

### Contact
- Development Team: <EMAIL>
- Issues: [GitHub Issues](https://github.com/onehrms/onehrms/issues)
- Slack: #attendance-service

## License

This project is licensed under the MIT License - see the [LICENSE](../../../LICENSE) file for details.
