-- Initial schema for Attendance Management Service
-- This migration creates the core tables for attendance tracking

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create attendance_records table
CREATE TABLE IF NOT EXISTS attendance_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id VARCHAR(50) NOT NULL,
    employee_id VARCHAR(50) NOT NULL,
    attendance_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('PRESENT', 'ABSENT', 'HALF_DAY', 'ON_LEAVE', 'WORK_FROM_HOME')),
    shift_id UUID,

    -- Time tracking
    in_time TIMESTAMP,
    out_time TIMESTAMP,
    working_hours NUMERIC(5,2),
    break_duration_minutes INTEGER DEFAULT 0,

    -- Status flags
    late_entry BOOLEAN DEFAULT FALSE,
    early_exit BOOLEAN DEFAULT FALSE,
    half_day_status VARCHAR(20) CHECK (half_day_status IN ('FIRST_HALF', 'SECOND_HALF')),

    -- Additional information
    notes TEXT,
    leave_type VARCHAR(50),
    leave_application_id VARCHAR(50),

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),

    -- Constraints
    CONSTRAINT unique_employee_date_shift UNIQUE (tenant_id, employee_id, attendance_date, shift_id)
);

-- Create checkin_records table
CREATE TABLE IF NOT EXISTS checkin_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id VARCHAR(50) NOT NULL,
    employee_id VARCHAR(50) NOT NULL,
    check_time TIMESTAMP NOT NULL,
    check_type VARCHAR(20) NOT NULL CHECK (check_type IN ('CHECK_IN', 'CHECK_OUT', 'BREAK_START', 'BREAK_END')),

    -- Location tracking
    latitude NUMERIC(10,8),
    longitude NUMERIC(11,8),
    location_name VARCHAR(200),

    -- Shift information
    shift_id UUID,
    shift_start TIMESTAMP,
    shift_end TIMESTAMP,

    -- Device information
    device_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,

    -- Processing status
    attendance_id UUID,
    is_processed BOOLEAN DEFAULT FALSE,
    skip_auto_attendance BOOLEAN DEFAULT FALSE,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- Create shift_types table
CREATE TABLE IF NOT EXISTS shift_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,

    -- Shift timing
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_duration_minutes INTEGER DEFAULT 0,

    -- Validation rules
    grace_period_minutes INTEGER DEFAULT 15,
    working_hours_threshold NUMERIC(5,2) NOT NULL,
    late_entry_threshold_minutes INTEGER DEFAULT 15,
    early_exit_threshold_minutes INTEGER DEFAULT 15,

    -- Auto attendance settings
    enable_auto_attendance BOOLEAN DEFAULT TRUE,
    enable_late_entry_marking BOOLEAN DEFAULT TRUE,
    enable_early_exit_marking BOOLEAN DEFAULT TRUE,

    -- Location validation
    enable_location_validation BOOLEAN DEFAULT FALSE,
    allowed_locations TEXT, -- JSON string
    checkin_radius_meters INTEGER DEFAULT 100,

    -- Status
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'DRAFT')),
    is_active BOOLEAN DEFAULT TRUE,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),

    -- Constraints
    CONSTRAINT unique_shift_name_per_tenant UNIQUE (tenant_id, name)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_employee ON attendance_records(tenant_id, employee_id);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_date ON attendance_records(tenant_id, attendance_date);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_status ON attendance_records(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_attendance_shift_id ON attendance_records(shift_id);

CREATE INDEX IF NOT EXISTS idx_checkin_tenant_employee ON checkin_records(tenant_id, employee_id);
CREATE INDEX IF NOT EXISTS idx_checkin_tenant_time ON checkin_records(tenant_id, check_time);
CREATE INDEX IF NOT EXISTS idx_checkin_tenant_type ON checkin_records(tenant_id, check_type);
CREATE INDEX IF NOT EXISTS idx_checkin_shift_id ON checkin_records(shift_id);
CREATE INDEX IF NOT EXISTS idx_checkin_attendance_id ON checkin_records(attendance_id);
CREATE INDEX IF NOT EXISTS idx_checkin_is_processed ON checkin_records(tenant_id, is_processed);

CREATE INDEX IF NOT EXISTS idx_shift_tenant_name ON shift_types(tenant_id, name);
CREATE INDEX IF NOT EXISTS idx_shift_tenant_active ON shift_types(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_shift_tenant_status ON shift_types(tenant_id, status);

-- Add foreign key constraints
ALTER TABLE attendance_records
ADD CONSTRAINT fk_attendance_shift
FOREIGN KEY (shift_id) REFERENCES shift_types(id) ON DELETE SET NULL;

ALTER TABLE checkin_records
ADD CONSTRAINT fk_checkin_shift
FOREIGN KEY (shift_id) REFERENCES shift_types(id) ON DELETE SET NULL;

ALTER TABLE checkin_records
ADD CONSTRAINT fk_checkin_attendance
FOREIGN KEY (attendance_id) REFERENCES attendance_records(id) ON DELETE SET NULL;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_attendance_records_updated_at
    BEFORE UPDATE ON attendance_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_checkin_records_updated_at
    BEFORE UPDATE ON checkin_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shift_types_updated_at
    BEFORE UPDATE ON shift_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default shift types for testing
INSERT INTO shift_types (
    tenant_id, name, description, start_time, end_time,
    working_hours_threshold, created_by
) VALUES
(
    'default',
    'Standard Day Shift',
    'Standard 9-to-5 working hours',
    '09:00:00',
    '17:00:00',
    7.5,
    'system'
),
(
    'default',
    'Morning Shift',
    'Early morning shift',
    '06:00:00',
    '14:00:00',
    7.5,
    'system'
),
(
    'default',
    'Evening Shift',
    'Evening shift hours',
    '14:00:00',
    '22:00:00',
    7.5,
    'system'
),
(
    'default',
    'Night Shift',
    'Night shift hours',
    '22:00:00',
    '06:00:00',
    7.5,
    'system'
)
ON CONFLICT (tenant_id, name) DO NOTHING;

-- Create views for reporting
CREATE OR REPLACE VIEW attendance_summary_view AS
SELECT
    tenant_id,
    employee_id,
    DATE_TRUNC('month', attendance_date) as month,
    COUNT(*) as total_days,
    COUNT(CASE WHEN status = 'PRESENT' THEN 1 END) as present_days,
    COUNT(CASE WHEN status = 'ABSENT' THEN 1 END) as absent_days,
    COUNT(CASE WHEN status = 'HALF_DAY' THEN 1 END) as half_days,
    COUNT(CASE WHEN status = 'ON_LEAVE' THEN 1 END) as leave_days,
    COUNT(CASE WHEN status = 'WORK_FROM_HOME' THEN 1 END) as wfh_days,
    SUM(working_hours) as total_working_hours,
    AVG(working_hours) as avg_working_hours,
    COUNT(CASE WHEN late_entry = TRUE THEN 1 END) as late_entries,
    COUNT(CASE WHEN early_exit = TRUE THEN 1 END) as early_exits
FROM attendance_records
GROUP BY tenant_id, employee_id, DATE_TRUNC('month', attendance_date);

-- Create view for daily attendance status
CREATE OR REPLACE VIEW daily_attendance_view AS
SELECT
    ar.tenant_id,
    ar.employee_id,
    ar.attendance_date,
    ar.status,
    ar.working_hours,
    ar.late_entry,
    ar.early_exit,
    st.name as shift_name,
    st.start_time as shift_start,
    st.end_time as shift_end,
    ar.in_time,
    ar.out_time,
    CASE
        WHEN ar.working_hours >= st.working_hours_threshold THEN 'FULL_DAY'
        WHEN ar.working_hours >= (st.working_hours_threshold * 0.5) THEN 'HALF_DAY'
        ELSE 'INSUFFICIENT_HOURS'
    END as calculated_status
FROM attendance_records ar
LEFT JOIN shift_types st ON ar.shift_id = st.id;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO attendance_service;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO attendance_service;
