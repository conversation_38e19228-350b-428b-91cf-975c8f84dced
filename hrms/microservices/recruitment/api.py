"""
FastAPI application for Recruitment microservice.

This module defines the REST API endpoints for recruitment operations including
job management, candidate tracking, application processing, interview scheduling,
and offer management.
"""

from typing import List, Optional

from fastapi import Depends, FastAPI, File, HTTPException, Query, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from hrms.microservices.shared.auth import (
    Tenant,
    User,
    get_current_tenant,
    get_current_user,
    require_admin,
    require_manager,
    require_manager_or_admin,
)
from hrms.microservices.shared.database import DatabaseManager, get_db_manager
from hrms.microservices.shared.exceptions import HRMSException, create_http_exception
from hrms.microservices.shared.logging import get_logger, setup_logging
from hrms.microservices.shared.models import HealthStatus
from hrms.microservices.shared.utils import paginate_query_params

from .models import (
    ApplicationCreate,
    ApplicationResponse,
    ApplicationStatus,
    CandidateCreate,
    CandidateResponse,
    InterviewCreate,
    InterviewResponse,
    InterviewStatus,
    Job<PERSON>reate,
    JobRequisitionCreate,
    JobRequisitionResponse,
    JobRequisitionUpdate,
    JobResponse,
    JobStatus,
    OfferCreate,
    OfferResponse,
    RequisitionPriority,
    RequisitionStatus,
    StaffingPlanCreate,
    StaffingPlanItemCreate,
    StaffingPlanItemResponse,
    StaffingPlanItemUpdate,
    StaffingPlanResponse,
    StaffingPlanStatus,
    StaffingPlanUpdate,
)
from .repository import (
    ApplicationRepository,
    CandidateRepository,
    InterviewRepository,
    JobRepository,
    JobRequisitionRepository,
    OfferRepository,
    StaffingPlanItemRepository,
    StaffingPlanRepository,
)
from .service import RecruitmentService

# Setup logging
setup_logging("recruitment-service")
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Recruitment Management",
    description="""
    Microservice for recruitment and candidate management.

    This service provides comprehensive recruitment capabilities including:
    - Job posting and management
    - Candidate application tracking
    - Interview scheduling and management
    - Hiring workflow automation
    - Candidate evaluation and scoring
    - Offer management and onboarding
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Initialize database and repositories
db_manager = get_db_manager()
job_repo = JobRepository(db_manager)
candidate_repo = CandidateRepository(db_manager)
application_repo = ApplicationRepository(db_manager)
interview_repo = InterviewRepository(db_manager)
offer_repo = OfferRepository(db_manager)
job_requisition_repo = JobRequisitionRepository(db_manager)
staffing_plan_repo = StaffingPlanRepository(db_manager)
staffing_plan_item_repo = StaffingPlanItemRepository(db_manager)

# Initialize service
recruitment_service = RecruitmentService(
    job_repo,
    candidate_repo,
    application_repo,
    interview_repo,
    offer_repo,
    job_requisition_repo,
    staffing_plan_repo,
    staffing_plan_item_repo,
)


# Exception handler
@app.exception_handler(HRMSException)
async def hrms_exception_handler(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    http_exc = create_http_exception(exc)
    return JSONResponse(status_code=http_exc.status_code, content=http_exc.detail)


# Health check endpoint
@app.get("/health", response_model=HealthStatus, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    db_health = await db_manager.health_check()
    return HealthStatus(service="recruitment-service", dependencies={"database": db_health})


# Job endpoints
@app.post("/api/v1/recruitment/jobs", response_model=JobResponse, tags=["Jobs"])
async def create_job(
    job_data: JobCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new job posting."""
    try:
        return await recruitment_service.create_job(tenant.id, job_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/jobs", response_model=List[JobResponse], tags=["Jobs"])
async def get_jobs(
    status: Optional[JobStatus] = Query(None, description="Filter by job status"),
    department: Optional[str] = Query(None, description="Filter by department"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get job postings with optional filtering."""
    try:
        pagination = paginate_query_params(page, size)
        return await recruitment_service.get_jobs(
            tenant.id, status, department, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/jobs/{job_id}", response_model=JobResponse, tags=["Jobs"])
async def get_job(
    job_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific job posting."""
    try:
        return await recruitment_service.get_job(tenant.id, job_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put("/api/v1/recruitment/jobs/{job_id}", response_model=JobResponse, tags=["Jobs"])
async def update_job(
    job_id: str,
    job_data: dict,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update job posting."""
    try:
        return await recruitment_service.update_job(tenant.id, job_id, job_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch("/api/v1/recruitment/jobs/{job_id}/publish", response_model=JobResponse, tags=["Jobs"])
async def publish_job(
    job_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Publish a job posting."""
    try:
        return await recruitment_service.publish_job(tenant.id, job_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch("/api/v1/recruitment/jobs/{job_id}/close", response_model=JobResponse, tags=["Jobs"])
async def close_job(
    job_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Close a job posting."""
    try:
        return await recruitment_service.close_job(tenant.id, job_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Candidate endpoints
@app.post("/api/v1/recruitment/candidates", response_model=CandidateResponse, tags=["Candidates"])
async def create_candidate(
    candidate_data: CandidateCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new candidate."""
    try:
        return await recruitment_service.create_candidate(tenant.id, candidate_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/candidates", response_model=List[CandidateResponse], tags=["Candidates"])
async def get_candidates(
    search: Optional[str] = Query(None, description="Search candidates by name, email, or company"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get candidates with optional search."""
    try:
        pagination = paginate_query_params(page, size)
        return await recruitment_service.get_candidates(
            tenant.id, search, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/candidates/{candidate_id}", response_model=CandidateResponse, tags=["Candidates"]
)
async def get_candidate(
    candidate_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific candidate."""
    try:
        return await recruitment_service.get_candidate(tenant.id, candidate_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put(
    "/api/v1/recruitment/candidates/{candidate_id}", response_model=CandidateResponse, tags=["Candidates"]
)
async def update_candidate(
    candidate_id: str,
    candidate_data: dict,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update candidate information."""
    try:
        return await recruitment_service.update_candidate(tenant.id, candidate_id, candidate_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.post(
    "/api/v1/recruitment/candidates/{candidate_id}/resume",
    response_model=CandidateResponse,
    tags=["Candidates"],
)
async def upload_resume(
    candidate_id: str,
    file: UploadFile = File(...),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Upload resume for candidate."""
    try:
        return await recruitment_service.upload_resume(tenant.id, candidate_id, file)
    except HRMSException as e:
        raise create_http_exception(e)


# Application endpoints
@app.post("/api/v1/recruitment/applications", response_model=ApplicationResponse, tags=["Applications"])
async def create_application(
    application_data: ApplicationCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Submit a job application."""
    try:
        return await recruitment_service.create_application(tenant.id, application_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/applications", response_model=List[ApplicationResponse], tags=["Applications"])
async def get_applications_by_job(
    job_id: str = Query(..., description="Job ID"),
    status: Optional[ApplicationStatus] = Query(None, description="Filter by application status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get applications for a job."""
    try:
        pagination = paginate_query_params(page, size)
        return await recruitment_service.get_applications_by_job(
            tenant.id, job_id, status, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/applications/{application_id}",
    response_model=ApplicationResponse,
    tags=["Applications"],
)
async def get_application(
    application_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific application."""
    try:
        return await recruitment_service.get_application(tenant.id, application_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/applications/{application_id}/status",
    response_model=ApplicationResponse,
    tags=["Applications"],
)
async def update_application_status(
    application_id: str,
    status: ApplicationStatus,
    review_notes: Optional[str] = None,
    rejection_reason: Optional[str] = None,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update application status."""
    try:
        return await recruitment_service.update_application_status(
            tenant.id, application_id, status, user.id, review_notes, rejection_reason
        )
    except HRMSException as e:
        raise create_http_exception(e)


# Interview endpoints
@app.post("/api/v1/recruitment/interviews", response_model=InterviewResponse, tags=["Interviews"])
async def schedule_interview(
    interview_data: InterviewCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Schedule an interview."""
    try:
        return await recruitment_service.schedule_interview(tenant.id, interview_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/interviews", response_model=List[InterviewResponse], tags=["Interviews"])
async def get_interviews_by_interviewer(
    interviewer_id: str = Query(..., description="Interviewer ID"),
    status: Optional[InterviewStatus] = Query(None, description="Filter by interview status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get interviews by interviewer."""
    try:
        pagination = paginate_query_params(page, size)
        return await recruitment_service.get_interviews_by_interviewer(
            tenant.id, interviewer_id, status, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/interviews/{interview_id}", response_model=InterviewResponse, tags=["Interviews"]
)
async def get_interview(
    interview_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific interview."""
    try:
        return await recruitment_service.get_interview(tenant.id, interview_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/interviews/{interview_id}/complete",
    response_model=InterviewResponse,
    tags=["Interviews"],
)
async def complete_interview(
    interview_id: str,
    feedback: str,
    rating: float,
    recommendation: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Complete interview with feedback."""
    try:
        return await recruitment_service.complete_interview(
            tenant.id, interview_id, feedback, rating, recommendation
        )
    except HRMSException as e:
        raise create_http_exception(e)


# Offer endpoints
@app.post("/api/v1/recruitment/offers", response_model=OfferResponse, tags=["Offers"])
async def create_offer(
    offer_data: OfferCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a job offer."""
    try:
        return await recruitment_service.create_offer(tenant.id, offer_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/recruitment/offers/{offer_id}", response_model=OfferResponse, tags=["Offers"])
async def get_offer(
    offer_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific offer."""
    try:
        return await recruitment_service.get_offer(tenant.id, offer_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch("/api/v1/recruitment/offers/{offer_id}/send", response_model=OfferResponse, tags=["Offers"])
async def send_offer(
    offer_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Send offer to candidate."""
    try:
        return await recruitment_service.send_offer(tenant.id, offer_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch("/api/v1/recruitment/offers/{offer_id}/accept", response_model=OfferResponse, tags=["Offers"])
async def accept_offer(
    offer_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Accept job offer."""
    try:
        return await recruitment_service.accept_offer(tenant.id, offer_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch("/api/v1/recruitment/offers/{offer_id}/reject", response_model=OfferResponse, tags=["Offers"])
async def reject_offer(
    offer_id: str,
    rejection_reason: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Reject job offer."""
    try:
        return await recruitment_service.reject_offer(offer_id, rejection_reason, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Job Requisition Endpoints
@app.post(
    "/api/v1/recruitment/requisitions", response_model=JobRequisitionResponse, tags=["Job Requisitions"]
)
async def create_job_requisition(
    requisition_data: JobRequisitionCreate,
    user: User = Depends(require_manager_or_admin),  # ✅ Role validation
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new job requisition."""
    try:
        return await recruitment_service.create_job_requisition(requisition_data, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/requisitions", response_model=List[JobRequisitionResponse], tags=["Job Requisitions"]
)
async def get_job_requisitions(
    status: Optional[RequisitionStatus] = Query(None, description="Filter by requisition status"),
    department_id: Optional[str] = Query(None, description="Filter by department"),
    priority: Optional[RequisitionPriority] = Query(None, description="Filter by priority"),
    requested_by: Optional[str] = Query(None, description="Filter by requester"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get job requisitions with filtering and pagination."""
    try:
        # Convert page-based pagination to offset-based
        offset = (page - 1) * size
        return await recruitment_service.get_job_requisitions(
            tenant_id=tenant.id,
            status=status,
            department_id=department_id,
            priority=priority,
            requested_by=requested_by,
            limit=size,
            offset=offset,
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/requisitions/{requisition_id}",
    response_model=JobRequisitionResponse,
    tags=["Job Requisitions"],
)
async def get_job_requisition(
    requisition_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get a specific job requisition by ID."""
    try:
        return await recruitment_service.get_job_requisition(requisition_id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put(
    "/api/v1/recruitment/requisitions/{requisition_id}",
    response_model=JobRequisitionResponse,
    tags=["Job Requisitions"],
)
async def update_job_requisition(
    requisition_id: str,
    requisition_data: JobRequisitionUpdate,
    user: User = Depends(require_manager_or_admin),  # ✅ Role validation
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update a job requisition."""
    try:
        return await recruitment_service.update_job_requisition(
            requisition_id, requisition_data, user.id, tenant.id
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/requisitions/{requisition_id}/submit",
    response_model=JobRequisitionResponse,
    tags=["Job Requisitions"],
)
async def submit_job_requisition(
    requisition_id: str,
    user: User = Depends(require_manager),  # ✅ Only managers can submit
    tenant: Tenant = Depends(get_current_tenant),
):
    """Submit a job requisition for approval."""
    try:
        return await recruitment_service.submit_job_requisition(requisition_id, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/requisitions/{requisition_id}/approve",
    response_model=JobRequisitionResponse,
    tags=["Job Requisitions"],
)
async def approve_job_requisition(
    requisition_id: str,
    user: User = Depends(require_admin),  # ✅ Only admins can approve
    tenant: Tenant = Depends(get_current_tenant),
):
    """Approve a job requisition."""
    try:
        return await recruitment_service.approve_job_requisition(requisition_id, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/requisitions/{requisition_id}/reject",
    response_model=JobRequisitionResponse,
    tags=["Job Requisitions"],
)
async def reject_job_requisition(
    requisition_id: str,
    rejection_reason: str,
    user: User = Depends(require_admin),  # ✅ Only admins can reject
    tenant: Tenant = Depends(get_current_tenant),
):
    """Reject a job requisition."""
    try:
        return await recruitment_service.reject_job_requisition(
            requisition_id, rejection_reason, user.id, tenant.id
        )
    except HRMSException as e:
        raise create_http_exception(e)


# Staffing Plan Endpoints
@app.post("/api/v1/recruitment/staffing-plans", response_model=StaffingPlanResponse, tags=["Staffing Plans"])
async def create_staffing_plan(
    plan_data: StaffingPlanCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new staffing plan."""
    try:
        return await recruitment_service.create_staffing_plan(plan_data, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/staffing-plans", response_model=List[StaffingPlanResponse], tags=["Staffing Plans"]
)
async def get_staffing_plans(
    department_id: Optional[str] = Query(None, description="Filter by department"),
    fiscal_year: Optional[int] = Query(None, description="Filter by fiscal year"),
    status: Optional[StaffingPlanStatus] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get staffing plans with filtering and pagination."""
    try:
        return await recruitment_service.get_staffing_plans(
            department_id=department_id,
            fiscal_year=fiscal_year,
            status=status,
            page=page,
            size=size,
            tenant_id=tenant.id,
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/staffing-plans/{plan_id}",
    response_model=StaffingPlanResponse,
    tags=["Staffing Plans"],
)
async def get_staffing_plan(
    plan_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get a specific staffing plan by ID."""
    try:
        return await recruitment_service.get_staffing_plan(plan_id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put(
    "/api/v1/recruitment/staffing-plans/{plan_id}",
    response_model=StaffingPlanResponse,
    tags=["Staffing Plans"],
)
async def update_staffing_plan(
    plan_id: str,
    plan_data: StaffingPlanUpdate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update a staffing plan."""
    try:
        return await recruitment_service.update_staffing_plan(plan_id, plan_data, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/recruitment/staffing-plans/{plan_id}/activate",
    response_model=StaffingPlanResponse,
    tags=["Staffing Plans"],
)
async def activate_staffing_plan(
    plan_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Activate a staffing plan."""
    try:
        return await recruitment_service.activate_staffing_plan(plan_id, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Staffing Plan Items Endpoints
@app.post(
    "/api/v1/recruitment/staffing-plans/{plan_id}/items",
    response_model=StaffingPlanItemResponse,
    tags=["Staffing Plan Items"],
)
async def create_staffing_plan_item(
    plan_id: str,
    item_data: StaffingPlanItemCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new staffing plan item."""
    try:
        return await recruitment_service.create_staffing_plan_item(plan_id, item_data, user.id, tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/recruitment/staffing-plans/{plan_id}/items",
    response_model=List[StaffingPlanItemResponse],
    tags=["Staffing Plan Items"],
)
async def get_staffing_plan_items(
    plan_id: str,
    status: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get staffing plan items."""
    try:
        return await recruitment_service.get_staffing_plan_items(
            plan_id, status=status, page=page, size=size, tenant_id=tenant.id
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.put(
    "/api/v1/recruitment/staffing-plans/{plan_id}/items/{item_id}",
    response_model=StaffingPlanItemResponse,
    tags=["Staffing Plan Items"],
)
async def update_staffing_plan_item(
    plan_id: str,
    item_id: str,
    item_data: StaffingPlanItemUpdate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update a staffing plan item."""
    try:
        return await recruitment_service.update_staffing_plan_item(
            plan_id, item_id, item_data, user.id, tenant.id
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.delete("/api/v1/recruitment/staffing-plans/{plan_id}/items/{item_id}", tags=["Staffing Plan Items"])
async def delete_staffing_plan_item(
    plan_id: str,
    item_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Delete a staffing plan item."""
    try:
        await recruitment_service.delete_staffing_plan_item(plan_id, item_id, tenant.id)
        return {"message": "Staffing plan item deleted successfully"}
    except HRMSException as e:
        raise create_http_exception(e)
