-- Migration: Add Job Requisitions and Staffing Plans
-- Date: 2024-01-15
-- Description: Add new tables for job requisitions and staffing plans

-- Create enum types
CREATE TYPE requisition_status AS ENUM ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'cancelled');
CREATE TYPE requisition_priority AS ENUM ('low', 'medium', 'high', 'urgent');
CREATE TYPE staffing_plan_status AS ENUM ('draft', 'active', 'completed', 'cancelled');

-- Create job requisitions table
CREATE TABLE recruitment_job_requisitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    department_id VARCHAR(36) NOT NULL,
    position_id VARCHAR(36) NOT NULL,
    location VARCHAR(200),
    employment_type VARCHAR(50),
    experience_level VARCHAR(50),
    headcount_needed INTEGER DEFAULT 1,
    estimated_salary_min NUMERIC(12, 2),
    estimated_salary_max NUMERIC(12, 2),
    currency VARCHAR(3) DEFAULT 'USD',
    budget_allocation NUMERIC(12, 2),
    requirements TEXT,
    responsibilities TEXT,
    business_justification TEXT NOT NULL,
    priority requisition_priority DEFAULT 'medium',
    status requisition_status DEFAULT 'draft',
    requested_by VARCHAR(50) NOT NULL,
    hiring_manager_id VARCHAR(50),
    department_head_id VARCHAR(50),
    hr_approver_id VARCHAR(50),
    finance_approver_id VARCHAR(50),
    submitted_date TIMESTAMP,
    approved_date TIMESTAMP,
    rejection_reason TEXT,
    expected_start_date DATE,
    is_urgent BOOLEAN DEFAULT FALSE,
    replacement_for VARCHAR(50),
    additional_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create staffing plans table
CREATE TABLE recruitment_staffing_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    department_id VARCHAR(36) NOT NULL,
    fiscal_year INTEGER NOT NULL,
    quarter INTEGER CHECK (quarter >= 1 AND quarter <= 4),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_budget NUMERIC(12, 2) NOT NULL,
    allocated_budget NUMERIC(12, 2) DEFAULT 0,
    current_headcount INTEGER DEFAULT 0,
    target_headcount INTEGER DEFAULT 0,
    planned_hires INTEGER DEFAULT 0,
    completed_hires INTEGER DEFAULT 0,
    status staffing_plan_status DEFAULT 'draft',
    created_by VARCHAR(50) NOT NULL,
    approved_by VARCHAR(50),
    approved_date TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create staffing plan items table
CREATE TABLE recruitment_staffing_plan_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    staffing_plan_id UUID NOT NULL REFERENCES recruitment_staffing_plans(id) ON DELETE CASCADE,
    position_id VARCHAR(36) NOT NULL,
    requisition_id UUID REFERENCES recruitment_job_requisitions(id),
    job_posting_id UUID REFERENCES recruitment_jobs(id),
    headcount_needed INTEGER DEFAULT 1,
    estimated_salary_min NUMERIC(12, 2),
    estimated_salary_max NUMERIC(12, 2),
    budget_allocation NUMERIC(12, 2),
    priority requisition_priority DEFAULT 'medium',
    status VARCHAR(50) DEFAULT 'planned',
    expected_start_date DATE,
    actual_start_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add requisition_id to existing jobs table
ALTER TABLE recruitment_jobs ADD COLUMN requisition_id UUID REFERENCES recruitment_job_requisitions(id);

-- Create indexes
CREATE INDEX idx_recruitment_job_requisitions_tenant_id ON recruitment_job_requisitions(tenant_id);
CREATE INDEX idx_recruitment_job_requisitions_status ON recruitment_job_requisitions(status);
CREATE INDEX idx_recruitment_job_requisitions_department_id ON recruitment_job_requisitions(department_id);
CREATE INDEX idx_recruitment_job_requisitions_requested_by ON recruitment_job_requisitions(requested_by);
CREATE INDEX idx_recruitment_job_requisitions_priority ON recruitment_job_requisitions(priority);

CREATE INDEX idx_recruitment_staffing_plans_tenant_id ON recruitment_staffing_plans(tenant_id);
CREATE INDEX idx_recruitment_staffing_plans_department_id ON recruitment_staffing_plans(department_id);
CREATE INDEX idx_recruitment_staffing_plans_fiscal_year ON recruitment_staffing_plans(fiscal_year);
CREATE INDEX idx_recruitment_staffing_plans_status ON recruitment_staffing_plans(status);

CREATE INDEX idx_recruitment_staffing_plan_items_tenant_id ON recruitment_staffing_plan_items(tenant_id);
CREATE INDEX idx_recruitment_staffing_plan_items_plan_id ON recruitment_staffing_plan_items(staffing_plan_id);
CREATE INDEX idx_recruitment_staffing_plan_items_position_id ON recruitment_staffing_plan_items(position_id);

CREATE INDEX idx_recruitment_jobs_requisition_id ON recruitment_jobs(requisition_id);

-- Add constraints
ALTER TABLE recruitment_job_requisitions ADD CONSTRAINT chk_requisition_salary_range
    CHECK (estimated_salary_max IS NULL OR estimated_salary_min IS NULL OR estimated_salary_max > estimated_salary_min);

ALTER TABLE recruitment_staffing_plans ADD CONSTRAINT chk_staffing_plan_period
    CHECK (period_end > period_start);

ALTER TABLE recruitment_staffing_plans ADD CONSTRAINT chk_staffing_plan_fiscal_year
    CHECK (fiscal_year >= 2020 AND fiscal_year <= 2030);

-- Add comments
COMMENT ON TABLE recruitment_job_requisitions IS 'Job requisitions for hiring needs';
COMMENT ON TABLE recruitment_staffing_plans IS 'Staffing plans for departments';
COMMENT ON TABLE recruitment_staffing_plan_items IS 'Individual items within staffing plans';
