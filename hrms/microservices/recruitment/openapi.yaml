openapi: 3.0.3
info:
  title: Recruitment Management API
  description: |
    Microservice for recruitment and candidate management.

    This service provides comprehensive recruitment capabilities including:
    - Job posting and management
    - Candidate application tracking
    - Interview scheduling and management
    - Hiring workflow automation
    - Candidate evaluation and scoring
    - Offer management and onboarding
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.

    ## Workflows
    - **Job Creation** → **Application** → **Screening** → **Interview** → **Evaluation** → **Offer** → **Onboarding**

  version: 1.0.0
  contact:
    name: oneHRMS Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8104
    description: Development server
  - url: https://api.onehrms.com/recruitment
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check service health and dependencies
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"

  /api/v1/recruitment/jobs:
    post:
      tags:
        - Jobs
      summary: Create job posting
      description: Create a new job posting
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/JobCreate"
      responses:
        "201":
          description: Job created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

    get:
      tags:
        - Jobs
      summary: Get job postings
      description: Get job postings with optional filtering
      parameters:
        - name: status
          in: query
          description: Filter by job status
          schema:
            $ref: "#/components/schemas/JobStatus"
        - name: department
          in: query
          description: Filter by department
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: Jobs retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/JobResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/recruitment/jobs/{job_id}:
    get:
      tags:
        - Jobs
      summary: Get job posting
      description: Get specific job posting details
      parameters:
        - name: job_id
          in: path
          required: true
          description: Job ID
          schema:
            type: string
      responses:
        "200":
          description: Job retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

    put:
      tags:
        - Jobs
      summary: Update job posting
      description: Update job posting details
      parameters:
        - name: job_id
          in: path
          required: true
          description: Job ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        "200":
          description: Job updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/recruitment/jobs/{job_id}/publish:
    patch:
      tags:
        - Jobs
      summary: Publish job posting
      description: Publish a draft job posting
      parameters:
        - name: job_id
          in: path
          required: true
          description: Job ID
          schema:
            type: string
      responses:
        "200":
          description: Job published successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/recruitment/jobs/{job_id}/close:
    patch:
      tags:
        - Jobs
      summary: Close job posting
      description: Close a published job posting
      parameters:
        - name: job_id
          in: path
          required: true
          description: Job ID
          schema:
            type: string
      responses:
        "200":
          description: Job closed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/recruitment/candidates:
    post:
      tags:
        - Candidates
      summary: Create candidate
      description: Create a new candidate profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CandidateCreate"
      responses:
        "201":
          description: Candidate created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CandidateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "409":
          $ref: "#/components/responses/Conflict"

    get:
      tags:
        - Candidates
      summary: Get candidates
      description: Get candidates with optional search
      parameters:
        - name: search
          in: query
          description: Search candidates by name, email, or company
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: Candidates retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CandidateResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/recruitment/candidates/{candidate_id}:
    get:
      tags:
        - Candidates
      summary: Get candidate
      description: Get specific candidate details
      parameters:
        - name: candidate_id
          in: path
          required: true
          description: Candidate ID
          schema:
            type: string
      responses:
        "200":
          description: Candidate retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CandidateResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

    put:
      tags:
        - Candidates
      summary: Update candidate
      description: Update candidate information
      parameters:
        - name: candidate_id
          in: path
          required: true
          description: Candidate ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        "200":
          description: Candidate updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CandidateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/recruitment/candidates/{candidate_id}/resume:
    post:
      tags:
        - Candidates
      summary: Upload resume
      description: Upload resume for candidate
      parameters:
        - name: candidate_id
          in: path
          required: true
          description: Candidate ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Resume file
              required:
                - file
      responses:
        "200":
          description: Resume uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CandidateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    HealthStatus:
      type: object
      properties:
        status:
          type: string
          example: "healthy"
        service:
          type: string
          example: "recruitment-service"
        dependencies:
          type: object
          additionalProperties:
            type: string

    JobStatus:
      type: string
      enum:
        - draft
        - published
        - closed
        - cancelled

    ApplicationStatus:
      type: string
      enum:
        - submitted
        - under_review
        - shortlisted
        - interviewed
        - offered
        - hired
        - rejected
        - withdrawn

    InterviewStatus:
      type: string
      enum:
        - scheduled
        - confirmed
        - completed
        - cancelled
        - no_show

    OfferStatus:
      type: string
      enum:
        - draft
        - sent
        - accepted
        - rejected
        - expired
        - withdrawn

    JobCreate:
      type: object
      required:
        - title
        - description
        - department
      properties:
        title:
          type: string
          maxLength: 200
          description: Job title
          example: "Senior Software Engineer"
        description:
          type: string
          description: Job description
        department:
          type: string
          maxLength: 100
          description: Department
          example: "Engineering"
        location:
          type: string
          maxLength: 200
          description: Job location
          example: "San Francisco, CA"
        employment_type:
          type: string
          maxLength: 50
          description: Employment type
          example: "Full-time"
        experience_level:
          type: string
          maxLength: 50
          description: Experience level
          example: "Senior"
        salary_min:
          type: number
          format: decimal
          description: Minimum salary
        salary_max:
          type: number
          format: decimal
          description: Maximum salary
        currency:
          type: string
          maxLength: 3
          default: "USD"
          description: Currency code
        requirements:
          type: string
          description: Job requirements
        responsibilities:
          type: string
          description: Job responsibilities
        benefits:
          type: string
          description: Job benefits
        closing_date:
          type: string
          format: date
          description: Application closing date
        hiring_manager_id:
          type: string
          description: Hiring manager ID
        is_remote:
          type: boolean
          default: false
          description: Is remote position

    JobResponse:
      type: object
      properties:
        id:
          type: string
          description: Job ID
        title:
          type: string
          description: Job title
        description:
          type: string
          description: Job description
        department:
          type: string
          description: Department
        location:
          type: string
          description: Job location
        employment_type:
          type: string
          description: Employment type
        experience_level:
          type: string
          description: Experience level
        salary_min:
          type: number
          format: decimal
          description: Minimum salary
        salary_max:
          type: number
          format: decimal
          description: Maximum salary
        currency:
          type: string
          description: Currency code
        requirements:
          type: string
          description: Job requirements
        responsibilities:
          type: string
          description: Job responsibilities
        benefits:
          type: string
          description: Job benefits
        status:
          $ref: "#/components/schemas/JobStatus"
        posted_date:
          type: string
          format: date-time
          description: Posted date
        closing_date:
          type: string
          format: date
          description: Closing date
        hiring_manager_id:
          type: string
          description: Hiring manager ID
        recruiter_id:
          type: string
          description: Recruiter ID
        is_remote:
          type: boolean
          description: Is remote position
        application_count:
          type: integer
          description: Number of applications
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CandidateCreate:
      type: object
      required:
        - first_name
        - last_name
        - email
      properties:
        first_name:
          type: string
          maxLength: 100
          description: First name
        last_name:
          type: string
          maxLength: 100
          description: Last name
        email:
          type: string
          format: email
          maxLength: 255
          description: Email address
        phone:
          type: string
          maxLength: 20
          description: Phone number
        linkedin_url:
          type: string
          maxLength: 500
          description: LinkedIn profile URL
        portfolio_url:
          type: string
          maxLength: 500
          description: Portfolio URL
        current_company:
          type: string
          maxLength: 200
          description: Current company
        current_position:
          type: string
          maxLength: 200
          description: Current position
        experience_years:
          type: integer
          minimum: 0
          description: Years of experience
        education:
          type: string
          description: Education background
        skills:
          type: string
          description: Skills (JSON string)
        cover_letter:
          type: string
          description: Cover letter
        source:
          type: string
          maxLength: 100
          description: Application source

    CandidateResponse:
      type: object
      properties:
        id:
          type: string
          description: Candidate ID
        first_name:
          type: string
          description: First name
        last_name:
          type: string
          description: Last name
        email:
          type: string
          format: email
          description: Email address
        phone:
          type: string
          description: Phone number
        linkedin_url:
          type: string
          description: LinkedIn profile URL
        portfolio_url:
          type: string
          description: Portfolio URL
        current_company:
          type: string
          description: Current company
        current_position:
          type: string
          description: Current position
        experience_years:
          type: integer
          description: Years of experience
        education:
          type: string
          description: Education background
        skills:
          type: string
          description: Skills
        resume_file_path:
          type: string
          description: Resume file path
        cover_letter:
          type: string
          description: Cover letter
        source:
          type: string
          description: Application source
        notes:
          type: string
          description: Notes
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Error:
      type: object
      properties:
        detail:
          type: string
          description: Error message
        code:
          type: string
          description: Error code

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
