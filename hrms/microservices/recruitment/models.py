"""
Pydantic models and database schemas for Recruitment microservice.

This module defines all data models, request/response schemas, and database models
for the recruitment service including jobs, candidates, applications, interviews, and offers.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator
from sqlalchemy import Boolean, Column, Date, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Foreign<PERSON>ey, Integer, Numeric, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from hrms.microservices.shared.models import Base, TenantMixin, TimestampMixin


# Enums
class JobStatus(str, Enum):
    """Job posting status enumeration."""

    DRAFT = "draft"
    PUBLISHED = "published"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class RequisitionStatus(str, Enum):
    """Job requisition status enumeration."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class RequisitionPriority(str, Enum):
    """Job requisition priority enumeration."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class StaffingPlanStatus(str, Enum):
    """Staffing plan status enumeration."""

    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ApplicationStatus(str, Enum):
    """Application status enumeration."""

    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    SHORTLISTED = "shortlisted"
    INTERVIEWED = "interviewed"
    OFFERED = "offered"
    HIRED = "hired"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"


class InterviewStatus(str, Enum):
    """Interview status enumeration."""

    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"


class OfferStatus(str, Enum):
    """Offer status enumeration."""

    DRAFT = "draft"
    SENT = "sent"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"
    WITHDRAWN = "withdrawn"


# Database Models
class JobRequisition(Base, TenantMixin, TimestampMixin):
    """Job requisition database model."""

    __tablename__ = "recruitment_job_requisitions"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    department_id = Column(String(36), nullable=False, index=True)
    position_id = Column(String(36), nullable=False, index=True)
    location = Column(String(200))
    employment_type = Column(String(50))  # Full-time, Part-time, Contract, etc.
    experience_level = Column(String(50))  # Entry, Mid, Senior, etc.
    headcount_needed = Column(Integer, default=1)
    estimated_salary_min = Column(Numeric(12, 2))
    estimated_salary_max = Column(Numeric(12, 2))
    currency = Column(String(3), default="USD")
    budget_allocation = Column(Numeric(12, 2))
    requirements = Column(Text)
    responsibilities = Column(Text)
    business_justification = Column(Text)
    priority = Column(SQLEnum(RequisitionPriority), default=RequisitionPriority.MEDIUM, nullable=False)
    status = Column(SQLEnum(RequisitionStatus), default=RequisitionStatus.DRAFT, nullable=False)
    requested_by = Column(String(50), nullable=False)  # Employee ID of requester
    hiring_manager_id = Column(String(50))
    department_head_id = Column(String(50))
    hr_approver_id = Column(String(50))
    finance_approver_id = Column(String(50))
    submitted_date = Column(DateTime)
    approved_date = Column(DateTime)
    rejection_reason = Column(Text)
    expected_start_date = Column(Date)
    is_urgent = Column(Boolean, default=False)
    replacement_for = Column(String(50))  # Employee ID if replacing someone
    additional_notes = Column(Text)


class StaffingPlan(Base, TenantMixin, TimestampMixin):
    """Staffing plan database model."""

    __tablename__ = "recruitment_staffing_plans"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text)
    department_id = Column(String(36), nullable=False, index=True)
    fiscal_year = Column(Integer, nullable=False)
    quarter = Column(Integer)  # 1-4 for quarters
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    total_budget = Column(Numeric(12, 2), nullable=False)
    allocated_budget = Column(Numeric(12, 2), default=0)
    current_headcount = Column(Integer, default=0)
    target_headcount = Column(Integer, default=0)
    planned_hires = Column(Integer, default=0)
    completed_hires = Column(Integer, default=0)
    status = Column(SQLEnum(StaffingPlanStatus), default=StaffingPlanStatus.DRAFT, nullable=False)
    created_by = Column(String(50), nullable=False)
    approved_by = Column(String(50))
    approved_date = Column(DateTime)
    notes = Column(Text)


class StaffingPlanItem(Base, TenantMixin, TimestampMixin):
    """Staffing plan item database model."""

    __tablename__ = "recruitment_staffing_plan_items"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    staffing_plan_id = Column(
        UUID(as_uuid=True), ForeignKey("recruitment_staffing_plans.id"), nullable=False, index=True
    )
    position_id = Column(String(36), nullable=False)
    requisition_id = Column(UUID(as_uuid=True), ForeignKey("recruitment_job_requisitions.id"), nullable=True)
    job_posting_id = Column(UUID(as_uuid=True), ForeignKey("recruitment_jobs.id"), nullable=True)
    headcount_needed = Column(Integer, default=1)
    estimated_salary_min = Column(Numeric(12, 2))
    estimated_salary_max = Column(Numeric(12, 2))
    budget_allocation = Column(Numeric(12, 2))
    priority = Column(SQLEnum(RequisitionPriority), default=RequisitionPriority.MEDIUM, nullable=False)
    status = Column(String(50), default="planned")  # planned, in_progress, completed, cancelled
    expected_start_date = Column(Date)
    actual_start_date = Column(Date)
    notes = Column(Text)

    # Relationships
    staffing_plan = relationship("StaffingPlan", backref="items")
    requisition = relationship("JobRequisition")
    job_posting = relationship("Job")


class Job(Base, TenantMixin, TimestampMixin):
    """Job posting database model."""

    __tablename__ = "recruitment_jobs"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    department = Column(String(100), nullable=False)
    location = Column(String(200))
    employment_type = Column(String(50))  # Full-time, Part-time, Contract, etc.
    experience_level = Column(String(50))  # Entry, Mid, Senior, etc.
    salary_min = Column(Numeric(12, 2))
    salary_max = Column(Numeric(12, 2))
    currency = Column(String(3), default="USD")
    requirements = Column(Text)
    responsibilities = Column(Text)
    benefits = Column(Text)
    status = Column(SQLEnum(JobStatus), default=JobStatus.DRAFT, nullable=False)
    posted_date = Column(DateTime)
    closing_date = Column(Date)
    hiring_manager_id = Column(String(50))
    recruiter_id = Column(String(50))
    is_remote = Column(Boolean, default=False)
    application_count = Column(Integer, default=0)
    requisition_id = Column(UUID(as_uuid=True), ForeignKey("recruitment_job_requisitions.id"), nullable=True)

    # Relationships
    requisition = relationship("JobRequisition", backref="job_postings")


class Candidate(Base, TenantMixin, TimestampMixin):
    """Candidate database model."""

    __tablename__ = "recruitment_candidates"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=False, index=True)
    phone = Column(String(20))
    linkedin_url = Column(String(500))
    portfolio_url = Column(String(500))
    current_company = Column(String(200))
    current_position = Column(String(200))
    experience_years = Column(Integer)
    education = Column(Text)
    skills = Column(Text)  # JSON string of skills
    resume_file_path = Column(String(500))
    cover_letter = Column(Text)
    source = Column(String(100))  # Job board, referral, direct, etc.
    notes = Column(Text)


class Application(Base, TenantMixin, TimestampMixin):
    """Job application database model."""

    __tablename__ = "recruitment_applications"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    job_id = Column(UUID(as_uuid=True), ForeignKey("recruitment_jobs.id"), nullable=False, index=True)
    candidate_id = Column(
        UUID(as_uuid=True), ForeignKey("recruitment_candidates.id"), nullable=False, index=True
    )
    status = Column(SQLEnum(ApplicationStatus), default=ApplicationStatus.SUBMITTED, nullable=False)
    applied_date = Column(DateTime, default=datetime.utcnow)
    cover_letter = Column(Text)
    custom_responses = Column(Text)  # JSON string of custom question responses
    screening_score = Column(Numeric(5, 2))
    reviewer_id = Column(String(50))
    review_notes = Column(Text)
    rejection_reason = Column(Text)

    # Relationships
    job = relationship("Job", backref="applications")
    candidate = relationship("Candidate", backref="applications")


class Interview(Base, TenantMixin, TimestampMixin):
    """Interview database model."""

    __tablename__ = "recruitment_interviews"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    application_id = Column(
        UUID(as_uuid=True), ForeignKey("recruitment_applications.id"), nullable=False, index=True
    )
    interviewer_id = Column(String(50), nullable=False)
    interview_type = Column(String(50))  # Phone, Video, In-person, Technical, etc.
    scheduled_date = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer, default=60)
    location = Column(String(200))
    meeting_link = Column(String(500))
    status = Column(SQLEnum(InterviewStatus), default=InterviewStatus.SCHEDULED, nullable=False)
    feedback = Column(Text)
    rating = Column(Numeric(3, 1))  # 1.0 to 5.0
    recommendation = Column(String(50))  # Hire, No Hire, Maybe
    notes = Column(Text)

    # Relationships
    application = relationship("Application", backref="interviews")


class Offer(Base, TenantMixin, TimestampMixin):
    """Job offer database model."""

    __tablename__ = "recruitment_offers"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    application_id = Column(
        UUID(as_uuid=True), ForeignKey("recruitment_applications.id"), nullable=False, index=True
    )
    salary = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), default="USD")
    start_date = Column(Date)
    benefits = Column(Text)
    terms_conditions = Column(Text)
    status = Column(SQLEnum(OfferStatus), default=OfferStatus.DRAFT, nullable=False)
    sent_date = Column(DateTime)
    response_deadline = Column(Date)
    accepted_date = Column(DateTime)
    rejected_date = Column(DateTime)
    rejection_reason = Column(Text)
    created_by = Column(String(50))

    # Relationships
    application = relationship("Application", backref="offers")


# Pydantic Models for API
class JobRequisitionCreate(BaseModel):
    """Schema for creating a job requisition."""

    title: str = Field(..., max_length=200, description="Job title")
    description: str = Field(..., description="Job description")
    department_id: str = Field(..., description="Department ID")
    position_id: str = Field(..., description="Position ID")
    location: Optional[str] = Field(None, max_length=200, description="Job location")
    employment_type: Optional[str] = Field(None, max_length=50, description="Employment type")
    experience_level: Optional[str] = Field(None, max_length=50, description="Experience level")
    headcount_needed: int = Field(1, ge=1, description="Number of positions needed")
    estimated_salary_min: Optional[Decimal] = Field(None, description="Estimated minimum salary")
    estimated_salary_max: Optional[Decimal] = Field(None, description="Estimated maximum salary")
    currency: str = Field("USD", max_length=3, description="Currency code")
    budget_allocation: Optional[Decimal] = Field(None, description="Budget allocation")
    requirements: Optional[str] = Field(None, description="Job requirements")
    responsibilities: Optional[str] = Field(None, description="Job responsibilities")
    business_justification: str = Field(..., description="Business justification for the hire")
    priority: RequisitionPriority = Field(RequisitionPriority.MEDIUM, description="Requisition priority")
    expected_start_date: Optional[date] = Field(None, description="Expected start date")
    is_urgent: bool = Field(False, description="Is this an urgent hire")
    replacement_for: Optional[str] = Field(None, description="Employee ID if replacing someone")
    additional_notes: Optional[str] = Field(None, description="Additional notes")

    @validator("estimated_salary_max")
    def validate_salary_range(cls, v, values):
        if v and "estimated_salary_min" in values and values["estimated_salary_min"]:
            if v <= values["estimated_salary_min"]:
                raise ValueError("Maximum salary must be greater than minimum salary")
        return v


class JobRequisitionUpdate(BaseModel):
    """Schema for updating a job requisition."""

    title: Optional[str] = Field(None, max_length=200, description="Job title")
    description: Optional[str] = Field(None, description="Job description")
    department_id: Optional[str] = Field(None, description="Department ID")
    position_id: Optional[str] = Field(None, description="Position ID")
    location: Optional[str] = Field(None, max_length=200, description="Job location")
    employment_type: Optional[str] = Field(None, max_length=50, description="Employment type")
    experience_level: Optional[str] = Field(None, max_length=50, description="Experience level")
    headcount_needed: Optional[int] = Field(None, ge=1, description="Number of positions needed")
    estimated_salary_min: Optional[Decimal] = Field(None, description="Estimated minimum salary")
    estimated_salary_max: Optional[Decimal] = Field(None, description="Estimated maximum salary")
    currency: Optional[str] = Field(None, max_length=3, description="Currency code")
    budget_allocation: Optional[Decimal] = Field(None, description="Budget allocation")
    requirements: Optional[str] = Field(None, description="Job requirements")
    responsibilities: Optional[str] = Field(None, description="Job responsibilities")
    business_justification: Optional[str] = Field(None, description="Business justification for the hire")
    priority: Optional[RequisitionPriority] = Field(None, description="Requisition priority")
    expected_start_date: Optional[date] = Field(None, description="Expected start date")
    is_urgent: Optional[bool] = Field(None, description="Is this an urgent hire")
    replacement_for: Optional[str] = Field(None, description="Employee ID if replacing someone")
    additional_notes: Optional[str] = Field(None, description="Additional notes")


class JobRequisitionResponse(BaseModel):
    """Schema for job requisition response."""

    id: str
    title: str
    description: str
    department_id: str
    position_id: str
    location: Optional[str]
    employment_type: Optional[str]
    experience_level: Optional[str]
    headcount_needed: int
    estimated_salary_min: Optional[Decimal]
    estimated_salary_max: Optional[Decimal]
    currency: str
    budget_allocation: Optional[Decimal]
    requirements: Optional[str]
    responsibilities: Optional[str]
    business_justification: str
    priority: RequisitionPriority
    status: RequisitionStatus
    requested_by: str
    hiring_manager_id: Optional[str]
    department_head_id: Optional[str]
    hr_approver_id: Optional[str]
    finance_approver_id: Optional[str]
    submitted_date: Optional[datetime]
    approved_date: Optional[datetime]
    rejection_reason: Optional[str]
    expected_start_date: Optional[date]
    is_urgent: bool
    replacement_for: Optional[str]
    additional_notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Staffing Plan Models
class StaffingPlanCreate(BaseModel):
    """Schema for creating a staffing plan."""

    name: str = Field(..., max_length=200, description="Staffing plan name")
    description: Optional[str] = Field(None, description="Staffing plan description")
    department_id: str = Field(..., description="Department ID")
    fiscal_year: int = Field(..., ge=2020, le=2030, description="Fiscal year")
    quarter: Optional[int] = Field(None, ge=1, le=4, description="Quarter (1-4)")
    period_start: date = Field(..., description="Period start date")
    period_end: date = Field(..., description="Period end date")
    total_budget: Decimal = Field(..., gt=0, description="Total budget allocation")
    current_headcount: int = Field(0, ge=0, description="Current headcount")
    target_headcount: int = Field(0, ge=0, description="Target headcount")
    planned_hires: int = Field(0, ge=0, description="Planned number of hires")
    notes: Optional[str] = Field(None, description="Additional notes")

    @validator("period_end")
    def validate_period_dates(cls, v, values):
        if "period_start" in values and v <= values["period_start"]:
            raise ValueError("Period end date must be after start date")
        return v


class StaffingPlanUpdate(BaseModel):
    """Schema for updating a staffing plan."""

    name: Optional[str] = Field(None, max_length=200, description="Staffing plan name")
    description: Optional[str] = Field(None, description="Staffing plan description")
    department_id: Optional[str] = Field(None, description="Department ID")
    fiscal_year: Optional[int] = Field(None, ge=2020, le=2030, description="Fiscal year")
    quarter: Optional[int] = Field(None, ge=1, le=4, description="Quarter (1-4)")
    period_start: Optional[date] = Field(None, description="Period start date")
    period_end: Optional[date] = Field(None, description="Period end date")
    total_budget: Optional[Decimal] = Field(None, gt=0, description="Total budget allocation")
    current_headcount: Optional[int] = Field(None, ge=0, description="Current headcount")
    target_headcount: Optional[int] = Field(None, ge=0, description="Target headcount")
    planned_hires: Optional[int] = Field(None, ge=0, description="Planned number of hires")
    notes: Optional[str] = Field(None, description="Additional notes")


class StaffingPlanResponse(BaseModel):
    """Schema for staffing plan response."""

    id: str
    name: str
    description: Optional[str]
    department_id: str
    fiscal_year: int
    quarter: Optional[int]
    period_start: date
    period_end: date
    total_budget: Decimal
    allocated_budget: Decimal
    current_headcount: int
    target_headcount: int
    planned_hires: int
    completed_hires: int
    status: StaffingPlanStatus
    created_by: str
    approved_by: Optional[str]
    approved_date: Optional[datetime]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class StaffingPlanItemCreate(BaseModel):
    """Schema for creating a staffing plan item."""

    staffing_plan_id: str = Field(..., description="Staffing plan ID")
    position_id: str = Field(..., description="Position ID")
    requisition_id: Optional[str] = Field(None, description="Job requisition ID")
    job_posting_id: Optional[str] = Field(None, description="Job posting ID")
    headcount_needed: int = Field(1, ge=1, description="Number of positions needed")
    estimated_salary_min: Optional[Decimal] = Field(None, description="Estimated minimum salary")
    estimated_salary_max: Optional[Decimal] = Field(None, description="Estimated maximum salary")
    budget_allocation: Optional[Decimal] = Field(None, description="Budget allocation")
    priority: RequisitionPriority = Field(RequisitionPriority.MEDIUM, description="Priority")
    expected_start_date: Optional[date] = Field(None, description="Expected start date")
    notes: Optional[str] = Field(None, description="Additional notes")


class StaffingPlanItemUpdate(BaseModel):
    """Schema for updating a staffing plan item."""

    position_id: Optional[str] = Field(None, description="Position ID")
    requisition_id: Optional[str] = Field(None, description="Job requisition ID")
    job_posting_id: Optional[str] = Field(None, description="Job posting ID")
    headcount_needed: Optional[int] = Field(None, ge=1, description="Number of positions needed")
    estimated_salary_min: Optional[Decimal] = Field(None, description="Estimated minimum salary")
    estimated_salary_max: Optional[Decimal] = Field(None, description="Estimated maximum salary")
    budget_allocation: Optional[Decimal] = Field(None, description="Budget allocation")
    priority: Optional[RequisitionPriority] = Field(None, description="Priority")
    expected_start_date: Optional[date] = Field(None, description="Expected start date")
    notes: Optional[str] = Field(None, description="Additional notes")


class StaffingPlanItemResponse(BaseModel):
    """Schema for staffing plan item response."""

    id: str
    staffing_plan_id: str
    position_id: str
    requisition_id: Optional[str]
    job_posting_id: Optional[str]
    headcount_needed: int
    estimated_salary_min: Optional[Decimal]
    estimated_salary_max: Optional[Decimal]
    budget_allocation: Optional[Decimal]
    priority: RequisitionPriority
    status: str
    expected_start_date: Optional[date]
    actual_start_date: Optional[date]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobCreate(BaseModel):
    """Schema for creating a job posting."""

    title: str = Field(..., max_length=200, description="Job title")
    description: str = Field(..., description="Job description")
    department: str = Field(..., max_length=100, description="Department")
    location: Optional[str] = Field(None, max_length=200, description="Job location")
    employment_type: Optional[str] = Field(None, max_length=50, description="Employment type")
    experience_level: Optional[str] = Field(None, max_length=50, description="Experience level")
    salary_min: Optional[Decimal] = Field(None, description="Minimum salary")
    salary_max: Optional[Decimal] = Field(None, description="Maximum salary")
    currency: str = Field("USD", max_length=3, description="Currency code")
    requirements: Optional[str] = Field(None, description="Job requirements")
    responsibilities: Optional[str] = Field(None, description="Job responsibilities")
    benefits: Optional[str] = Field(None, description="Job benefits")
    closing_date: Optional[date] = Field(None, description="Application closing date")
    hiring_manager_id: Optional[str] = Field(None, description="Hiring manager ID")
    is_remote: bool = Field(False, description="Is remote position")

    @validator("salary_max")
    def validate_salary_range(cls, v, values):
        if v and "salary_min" in values and values["salary_min"] and v < values["salary_min"]:
            raise ValueError("salary_max must be greater than salary_min")
        return v


class JobResponse(BaseModel):
    """Schema for job posting response."""

    id: str
    title: str
    description: str
    department: str
    location: Optional[str]
    employment_type: Optional[str]
    experience_level: Optional[str]
    salary_min: Optional[Decimal]
    salary_max: Optional[Decimal]
    currency: str
    requirements: Optional[str]
    responsibilities: Optional[str]
    benefits: Optional[str]
    status: JobStatus
    posted_date: Optional[datetime]
    closing_date: Optional[date]
    hiring_manager_id: Optional[str]
    recruiter_id: Optional[str]
    is_remote: bool
    application_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CandidateCreate(BaseModel):
    """Schema for creating a candidate."""

    first_name: str = Field(..., max_length=100, description="First name")
    last_name: str = Field(..., max_length=100, description="Last name")
    email: str = Field(..., max_length=255, description="Email address")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    linkedin_url: Optional[str] = Field(None, max_length=500, description="LinkedIn profile URL")
    portfolio_url: Optional[str] = Field(None, max_length=500, description="Portfolio URL")
    current_company: Optional[str] = Field(None, max_length=200, description="Current company")
    current_position: Optional[str] = Field(None, max_length=200, description="Current position")
    experience_years: Optional[int] = Field(None, ge=0, description="Years of experience")
    education: Optional[str] = Field(None, description="Education background")
    skills: Optional[str] = Field(None, description="Skills (JSON string)")
    cover_letter: Optional[str] = Field(None, description="Cover letter")
    source: Optional[str] = Field(None, max_length=100, description="Application source")


class CandidateResponse(BaseModel):
    """Schema for candidate response."""

    id: str
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]
    linkedin_url: Optional[str]
    portfolio_url: Optional[str]
    current_company: Optional[str]
    current_position: Optional[str]
    experience_years: Optional[int]
    education: Optional[str]
    skills: Optional[str]
    resume_file_path: Optional[str]
    cover_letter: Optional[str]
    source: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ApplicationCreate(BaseModel):
    """Schema for creating an application."""

    job_id: str = Field(..., description="Job ID")
    candidate_id: str = Field(..., description="Candidate ID")
    cover_letter: Optional[str] = Field(None, description="Cover letter")
    custom_responses: Optional[str] = Field(None, description="Custom question responses (JSON)")


class ApplicationResponse(BaseModel):
    """Schema for application response."""

    id: str
    job_id: str
    candidate_id: str
    status: ApplicationStatus
    applied_date: datetime
    cover_letter: Optional[str]
    custom_responses: Optional[str]
    screening_score: Optional[Decimal]
    reviewer_id: Optional[str]
    review_notes: Optional[str]
    rejection_reason: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InterviewCreate(BaseModel):
    """Schema for creating an interview."""

    application_id: str = Field(..., description="Application ID")
    interviewer_id: str = Field(..., description="Interviewer ID")
    interview_type: Optional[str] = Field(None, max_length=50, description="Interview type")
    scheduled_date: datetime = Field(..., description="Scheduled date and time")
    duration_minutes: int = Field(60, ge=15, le=480, description="Duration in minutes")
    location: Optional[str] = Field(None, max_length=200, description="Interview location")
    meeting_link: Optional[str] = Field(None, max_length=500, description="Meeting link")

    @validator("scheduled_date")
    def validate_future_date(cls, v):
        if v <= datetime.now():
            raise ValueError("scheduled_date must be in the future")
        return v


class InterviewResponse(BaseModel):
    """Schema for interview response."""

    id: str
    application_id: str
    interviewer_id: str
    interview_type: Optional[str]
    scheduled_date: datetime
    duration_minutes: int
    location: Optional[str]
    meeting_link: Optional[str]
    status: InterviewStatus
    feedback: Optional[str]
    rating: Optional[Decimal]
    recommendation: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OfferCreate(BaseModel):
    """Schema for creating an offer."""

    application_id: str = Field(..., description="Application ID")
    salary: Decimal = Field(..., gt=0, description="Offered salary")
    currency: str = Field("USD", max_length=3, description="Currency code")
    start_date: Optional[date] = Field(None, description="Proposed start date")
    benefits: Optional[str] = Field(None, description="Benefits package")
    terms_conditions: Optional[str] = Field(None, description="Terms and conditions")
    response_deadline: Optional[date] = Field(None, description="Response deadline")

    @validator("start_date")
    def validate_start_date(cls, v):
        if v and v <= date.today():
            raise ValueError("start_date must be in the future")
        return v


class OfferResponse(BaseModel):
    """Schema for offer response."""

    id: str
    application_id: str
    salary: Decimal
    currency: str
    start_date: Optional[date]
    benefits: Optional[str]
    terms_conditions: Optional[str]
    status: OfferStatus
    sent_date: Optional[datetime]
    response_deadline: Optional[date]
    accepted_date: Optional[datetime]
    rejected_date: Optional[datetime]
    rejection_reason: Optional[str]
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
