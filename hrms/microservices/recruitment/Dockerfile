# Multi-stage Dockerfile for Recruitment microservice
# Optimized for development with hot reload and production deployment

# Base stage with common dependencies
FROM python:3.10-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Install uv for faster package management
RUN pip install uv

# Copy requirements
COPY pyproject.toml .
COPY requirements*.txt ./

# Development stage
FROM base as development

# Install development dependencies
RUN uv pip install --system -e ".[dev]"

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p uploads logs && \
    chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8104

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8104/health || exit 1

# Development command with hot reload
CMD ["uvicorn", "hrms.microservices.recruitment.api:app", "--host", "0.0.0.0", "--port", "8104", "--reload", "--log-level", "debug"]

# Production stage
FROM base as production

# Install only production dependencies
RUN uv pip install --system -e ".[prod]"

# Copy source code
COPY . .

# Create necessary directories and set permissions
RUN mkdir -p uploads logs && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8104

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8104/health || exit 1

# Production command with gunicorn
CMD ["gunicorn", "hrms.microservices.recruitment.api:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8104", "--access-logfile", "-", "--error-logfile", "-"]

# Testing stage
FROM development as testing

# Install additional testing dependencies
RUN uv pip install --system pytest-cov pytest-xdist pytest-mock pytest-asyncio

# Copy test configuration
COPY pytest.ini .
COPY .coverage .

# Run tests
CMD ["python", "-m", "pytest", "tests/microservices/recruitment/", "-v", "--cov=hrms.microservices.recruitment", "--cov-report=html", "--cov-report=xml"]
