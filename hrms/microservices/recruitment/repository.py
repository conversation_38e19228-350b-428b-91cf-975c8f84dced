"""
Repository layer for Recruitment microservice.

This module provides data access layer for recruitment entities including jobs,
candidates, applications, interviews, and offers with tenant isolation and async operations.
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from hrms.microservices.shared.database import DatabaseManager
from hrms.microservices.shared.exceptions import NotFoundError
from hrms.microservices.shared.logging import get_logger
from hrms.microservices.shared.utils import generate_id

from .models import (
    Application,
    ApplicationStatus,
    Candidate,
    Interview,
    InterviewStatus,
    Job,
    JobRequisition,
    JobStatus,
    Offer,
    OfferStatus,
    RequisitionPriority,
    RequisitionStatus,
    StaffingPlan,
    StaffingPlanItem,
    StaffingPlanStatus,
)

logger = get_logger(__name__)


class JobRepository:
    """Repository for job posting data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, job_data: dict) -> Job:
        """Create a new job posting."""
        async with self.db_manager.get_session() as session:
            job = Job(id=generate_id(), tenant_id=tenant_id, **job_data)

            session.add(job)
            await session.commit()
            await session.refresh(job)

            logger.info(f"Created job {job.id} for tenant {tenant_id}")
            return job

    async def get_by_id(self, tenant_id: str, job_id: str) -> Optional[Job]:
        """Get job by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Job).where(and_(Job.tenant_id == tenant_id, Job.id == job_id))
            )
            return result.scalar_one_or_none()

    async def get_all(
        self,
        tenant_id: str,
        status: Optional[JobStatus] = None,
        department: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[Job]:
        """Get all jobs with optional filtering."""
        async with self.db_manager.get_session() as session:
            query = select(Job).where(Job.tenant_id == tenant_id)

            if status:
                query = query.where(Job.status == status)
            if department:
                query = query.where(Job.department == department)

            query = query.order_by(desc(Job.created_at)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update(self, tenant_id: str, job_id: str, job_data: dict) -> Optional[Job]:
        """Update job posting."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Job).where(and_(Job.tenant_id == tenant_id, Job.id == job_id))
            )
            job = result.scalar_one_or_none()

            if not job:
                return None

            for key, value in job_data.items():
                setattr(job, key, value)

            await session.commit()
            await session.refresh(job)

            logger.info(f"Updated job {job_id}")
            return job

    async def update_status(self, tenant_id: str, job_id: str, status: JobStatus) -> Optional[Job]:
        """Update job status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Job).where(and_(Job.tenant_id == tenant_id, Job.id == job_id))
            )
            job = result.scalar_one_or_none()

            if not job:
                return None

            job.status = status
            if status == JobStatus.PUBLISHED and not job.posted_date:
                job.posted_date = datetime.utcnow()

            await session.commit()
            await session.refresh(job)

            logger.info(f"Updated job {job_id} status to {status}")
            return job

    async def increment_application_count(self, tenant_id: str, job_id: str) -> bool:
        """Increment application count for a job."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                update(Job)
                .where(and_(Job.tenant_id == tenant_id, Job.id == job_id))
                .values(application_count=Job.application_count + 1)
            )
            await session.commit()
            return result.rowcount > 0


class CandidateRepository:
    """Repository for candidate data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, candidate_data: dict) -> Candidate:
        """Create a new candidate."""
        async with self.db_manager.get_session() as session:
            candidate = Candidate(id=generate_id(), tenant_id=tenant_id, **candidate_data)

            session.add(candidate)
            await session.commit()
            await session.refresh(candidate)

            logger.info(f"Created candidate {candidate.id} for tenant {tenant_id}")
            return candidate

    async def get_by_id(self, tenant_id: str, candidate_id: str) -> Optional[Candidate]:
        """Get candidate by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Candidate).where(and_(Candidate.tenant_id == tenant_id, Candidate.id == candidate_id))
            )
            return result.scalar_one_or_none()

    async def get_by_email(self, tenant_id: str, email: str) -> Optional[Candidate]:
        """Get candidate by email."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Candidate).where(and_(Candidate.tenant_id == tenant_id, Candidate.email == email))
            )
            return result.scalar_one_or_none()

    async def get_all(
        self, tenant_id: str, search: Optional[str] = None, limit: int = 50, offset: int = 0
    ) -> List[Candidate]:
        """Get all candidates with optional search."""
        async with self.db_manager.get_session() as session:
            query = select(Candidate).where(Candidate.tenant_id == tenant_id)

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    Candidate.first_name.ilike(search_term)
                    | Candidate.last_name.ilike(search_term)
                    | Candidate.email.ilike(search_term)
                    | Candidate.current_company.ilike(search_term)
                )

            query = query.order_by(desc(Candidate.created_at)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update(self, tenant_id: str, candidate_id: str, candidate_data: dict) -> Optional[Candidate]:
        """Update candidate."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Candidate).where(and_(Candidate.tenant_id == tenant_id, Candidate.id == candidate_id))
            )
            candidate = result.scalar_one_or_none()

            if not candidate:
                return None

            for key, value in candidate_data.items():
                setattr(candidate, key, value)

            await session.commit()
            await session.refresh(candidate)

            logger.info(f"Updated candidate {candidate_id}")
            return candidate


class ApplicationRepository:
    """Repository for application data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, application_data: dict) -> Application:
        """Create a new application."""
        async with self.db_manager.get_session() as session:
            application = Application(id=generate_id(), tenant_id=tenant_id, **application_data)

            session.add(application)
            await session.commit()
            await session.refresh(application)

            logger.info(f"Created application {application.id} for tenant {tenant_id}")
            return application

    async def get_by_id(self, tenant_id: str, application_id: str) -> Optional[Application]:
        """Get application by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Application).where(
                    and_(Application.tenant_id == tenant_id, Application.id == application_id)
                )
            )
            return result.scalar_one_or_none()

    async def get_by_job(
        self,
        tenant_id: str,
        job_id: str,
        status: Optional[ApplicationStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[Application]:
        """Get applications by job."""
        async with self.db_manager.get_session() as session:
            query = select(Application).where(
                and_(Application.tenant_id == tenant_id, Application.job_id == job_id)
            )

            if status:
                query = query.where(Application.status == status)

            query = query.order_by(desc(Application.applied_date)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_candidate(
        self, tenant_id: str, candidate_id: str, limit: int = 50, offset: int = 0
    ) -> List[Application]:
        """Get applications by candidate."""
        async with self.db_manager.get_session() as session:
            query = (
                select(Application)
                .where(and_(Application.tenant_id == tenant_id, Application.candidate_id == candidate_id))
                .order_by(desc(Application.applied_date))
                .limit(limit)
                .offset(offset)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def update_status(
        self,
        tenant_id: str,
        application_id: str,
        status: ApplicationStatus,
        reviewer_id: Optional[str] = None,
        review_notes: Optional[str] = None,
        rejection_reason: Optional[str] = None,
    ) -> Optional[Application]:
        """Update application status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Application).where(
                    and_(Application.tenant_id == tenant_id, Application.id == application_id)
                )
            )
            application = result.scalar_one_or_none()

            if not application:
                return None

            application.status = status
            if reviewer_id:
                application.reviewer_id = reviewer_id
            if review_notes:
                application.review_notes = review_notes
            if rejection_reason:
                application.rejection_reason = rejection_reason

            await session.commit()
            await session.refresh(application)

            logger.info(f"Updated application {application_id} status to {status}")
            return application

    async def check_existing_application(self, tenant_id: str, job_id: str, candidate_id: str) -> bool:
        """Check if candidate has already applied for the job."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(func.count(Application.id)).where(
                    and_(
                        Application.tenant_id == tenant_id,
                        Application.job_id == job_id,
                        Application.candidate_id == candidate_id,
                    )
                )
            )
            count = result.scalar() or 0
            return count > 0


class InterviewRepository:
    """Repository for interview data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, interview_data: dict) -> Interview:
        """Create a new interview."""
        async with self.db_manager.get_session() as session:
            interview = Interview(id=generate_id(), tenant_id=tenant_id, **interview_data)

            session.add(interview)
            await session.commit()
            await session.refresh(interview)

            logger.info(f"Created interview {interview.id} for tenant {tenant_id}")
            return interview

    async def get_by_id(self, tenant_id: str, interview_id: str) -> Optional[Interview]:
        """Get interview by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Interview).where(and_(Interview.tenant_id == tenant_id, Interview.id == interview_id))
            )
            return result.scalar_one_or_none()

    async def get_by_application(
        self, tenant_id: str, application_id: str, limit: int = 50, offset: int = 0
    ) -> List[Interview]:
        """Get interviews by application."""
        async with self.db_manager.get_session() as session:
            query = (
                select(Interview)
                .where(and_(Interview.tenant_id == tenant_id, Interview.application_id == application_id))
                .order_by(Interview.scheduled_date)
                .limit(limit)
                .offset(offset)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_interviewer(
        self,
        tenant_id: str,
        interviewer_id: str,
        status: Optional[InterviewStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[Interview]:
        """Get interviews by interviewer."""
        async with self.db_manager.get_session() as session:
            query = select(Interview).where(
                and_(Interview.tenant_id == tenant_id, Interview.interviewer_id == interviewer_id)
            )

            if status:
                query = query.where(Interview.status == status)

            query = query.order_by(Interview.scheduled_date).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update_status(
        self,
        tenant_id: str,
        interview_id: str,
        status: InterviewStatus,
        feedback: Optional[str] = None,
        rating: Optional[float] = None,
        recommendation: Optional[str] = None,
    ) -> Optional[Interview]:
        """Update interview status and feedback."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Interview).where(and_(Interview.tenant_id == tenant_id, Interview.id == interview_id))
            )
            interview = result.scalar_one_or_none()

            if not interview:
                return None

            interview.status = status
            if feedback:
                interview.feedback = feedback
            if rating:
                interview.rating = rating
            if recommendation:
                interview.recommendation = recommendation

            await session.commit()
            await session.refresh(interview)

            logger.info(f"Updated interview {interview_id} status to {status}")
            return interview


class OfferRepository:
    """Repository for offer data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, offer_data: dict) -> Offer:
        """Create a new offer."""
        async with self.db_manager.get_session() as session:
            offer = Offer(id=generate_id(), tenant_id=tenant_id, **offer_data)

            session.add(offer)
            await session.commit()
            await session.refresh(offer)

            logger.info(f"Created offer {offer.id} for tenant {tenant_id}")
            return offer

    async def get_by_id(self, tenant_id: str, offer_id: str) -> Optional[Offer]:
        """Get offer by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Offer).where(and_(Offer.tenant_id == tenant_id, Offer.id == offer_id))
            )
            return result.scalar_one_or_none()

    async def get_by_application(self, tenant_id: str, application_id: str) -> Optional[Offer]:
        """Get offer by application."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Offer).where(
                    and_(Offer.tenant_id == tenant_id, Offer.application_id == application_id)
                )
            )
            return result.scalar_one_or_none()

    async def update_status(
        self, tenant_id: str, offer_id: str, status: OfferStatus, rejection_reason: Optional[str] = None
    ) -> Optional[Offer]:
        """Update offer status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Offer).where(and_(Offer.tenant_id == tenant_id, Offer.id == offer_id))
            )
            offer = result.scalar_one_or_none()

            if not offer:
                return None

            offer.status = status
            if status == OfferStatus.SENT and not offer.sent_date:
                offer.sent_date = datetime.utcnow()
            elif status == OfferStatus.ACCEPTED:
                offer.accepted_date = datetime.utcnow()
            elif status == OfferStatus.REJECTED:
                offer.rejected_date = datetime.utcnow()
                if rejection_reason:
                    offer.rejection_reason = rejection_reason

            await session.commit()
            await session.refresh(offer)

            logger.info(f"Updated offer {offer_id} status to {status}")
            return offer


class JobRequisitionRepository:
    """Repository for job requisition data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, requisition_data: dict) -> JobRequisition:
        """Create a new job requisition."""
        async with self.db_manager.get_session() as session:
            requisition = JobRequisition(id=generate_id(), tenant_id=tenant_id, **requisition_data)

            session.add(requisition)
            await session.commit()
            await session.refresh(requisition)

            logger.info(f"Created job requisition {requisition.id} for tenant {tenant_id}")
            return requisition

    async def get_by_id(self, tenant_id: str, requisition_id: str) -> Optional[JobRequisition]:
        """Get job requisition by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(JobRequisition).where(
                    and_(JobRequisition.tenant_id == tenant_id, JobRequisition.id == requisition_id)
                )
            )
            return result.scalar_one_or_none()

    async def get_all(
        self,
        tenant_id: str,
        status: Optional[RequisitionStatus] = None,
        department_id: Optional[str] = None,
        priority: Optional[RequisitionPriority] = None,
        requested_by: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[JobRequisition]:
        """Get all job requisitions with optional filtering."""
        async with self.db_manager.get_session() as session:
            query = select(JobRequisition).where(JobRequisition.tenant_id == tenant_id)

            if status:
                query = query.where(JobRequisition.status == status)
            if department_id:
                query = query.where(JobRequisition.department_id == department_id)
            if priority:
                query = query.where(JobRequisition.priority == priority)
            if requested_by:
                query = query.where(JobRequisition.requested_by == requested_by)

            query = query.order_by(desc(JobRequisition.created_at)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update(
        self, tenant_id: str, requisition_id: str, requisition_data: dict
    ) -> Optional[JobRequisition]:
        """Update job requisition."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(JobRequisition).where(
                    and_(JobRequisition.tenant_id == tenant_id, JobRequisition.id == requisition_id)
                )
            )
            requisition = result.scalar_one_or_none()

            if not requisition:
                return None

            for key, value in requisition_data.items():
                setattr(requisition, key, value)

            await session.commit()
            await session.refresh(requisition)

            logger.info(f"Updated job requisition {requisition_id}")
            return requisition

    async def update_status(
        self, tenant_id: str, requisition_id: str, status: RequisitionStatus, **kwargs
    ) -> Optional[JobRequisition]:
        """Update job requisition status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(JobRequisition).where(
                    and_(JobRequisition.tenant_id == tenant_id, JobRequisition.id == requisition_id)
                )
            )
            requisition = result.scalar_one_or_none()

            if not requisition:
                return None

            requisition.status = status
            for key, value in kwargs.items():
                setattr(requisition, key, value)

            await session.commit()
            await session.refresh(requisition)

            logger.info(f"Updated job requisition {requisition_id} status to {status}")
            return requisition


class StaffingPlanRepository:
    """Repository for staffing plan data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, plan_data: dict) -> StaffingPlan:
        """Create a new staffing plan."""
        async with self.db_manager.get_session() as session:
            plan = StaffingPlan(id=generate_id(), tenant_id=tenant_id, **plan_data)

            session.add(plan)
            await session.commit()
            await session.refresh(plan)

            logger.info(f"Created staffing plan {plan.id} for tenant {tenant_id}")
            return plan

    async def get_by_id(self, tenant_id: str, plan_id: str) -> Optional[StaffingPlan]:
        """Get staffing plan by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlan).where(
                    and_(StaffingPlan.tenant_id == tenant_id, StaffingPlan.id == plan_id)
                )
            )
            return result.scalar_one_or_none()

    async def get_all(
        self,
        tenant_id: str,
        department_id: Optional[str] = None,
        fiscal_year: Optional[int] = None,
        status: Optional[StaffingPlanStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[StaffingPlan]:
        """Get all staffing plans with optional filtering."""
        async with self.db_manager.get_session() as session:
            query = select(StaffingPlan).where(StaffingPlan.tenant_id == tenant_id)

            if department_id:
                query = query.where(StaffingPlan.department_id == department_id)
            if fiscal_year:
                query = query.where(StaffingPlan.fiscal_year == fiscal_year)
            if status:
                query = query.where(StaffingPlan.status == status)

            query = query.order_by(desc(StaffingPlan.created_at)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update(self, tenant_id: str, plan_id: str, plan_data: dict) -> Optional[StaffingPlan]:
        """Update staffing plan."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlan).where(
                    and_(StaffingPlan.tenant_id == tenant_id, StaffingPlan.id == plan_id)
                )
            )
            plan = result.scalar_one_or_none()

            if not plan:
                return None

            for key, value in plan_data.items():
                setattr(plan, key, value)

            await session.commit()
            await session.refresh(plan)

            logger.info(f"Updated staffing plan {plan_id}")
            return plan

    async def update_status(
        self, tenant_id: str, plan_id: str, status: StaffingPlanStatus, **kwargs
    ) -> Optional[StaffingPlan]:
        """Update staffing plan status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlan).where(
                    and_(StaffingPlan.tenant_id == tenant_id, StaffingPlan.id == plan_id)
                )
            )
            plan = result.scalar_one_or_none()

            if not plan:
                return None

            plan.status = status
            for key, value in kwargs.items():
                setattr(plan, key, value)

            await session.commit()
            await session.refresh(plan)

            logger.info(f"Updated staffing plan {plan_id} status to {status}")
            return plan


class StaffingPlanItemRepository:
    """Repository for staffing plan item data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, item_data: dict) -> StaffingPlanItem:
        """Create a new staffing plan item."""
        async with self.db_manager.get_session() as session:
            item = StaffingPlanItem(id=generate_id(), tenant_id=tenant_id, **item_data)

            session.add(item)
            await session.commit()
            await session.refresh(item)

            logger.info(f"Created staffing plan item {item.id} for tenant {tenant_id}")
            return item

    async def get_by_id(self, tenant_id: str, item_id: str) -> Optional[StaffingPlanItem]:
        """Get staffing plan item by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlanItem).where(
                    and_(StaffingPlanItem.tenant_id == tenant_id, StaffingPlanItem.id == item_id)
                )
            )
            return result.scalar_one_or_none()

    async def get_by_plan(
        self,
        tenant_id: str,
        plan_id: str,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[StaffingPlanItem]:
        """Get all staffing plan items for a specific plan."""
        async with self.db_manager.get_session() as session:
            query = select(StaffingPlanItem).where(
                and_(StaffingPlanItem.tenant_id == tenant_id, StaffingPlanItem.staffing_plan_id == plan_id)
            )

            if status:
                query = query.where(StaffingPlanItem.status == status)

            query = query.order_by(StaffingPlanItem.created_at).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update(self, tenant_id: str, item_id: str, item_data: dict) -> Optional[StaffingPlanItem]:
        """Update staffing plan item."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlanItem).where(
                    and_(StaffingPlanItem.tenant_id == tenant_id, StaffingPlanItem.id == item_id)
                )
            )
            item = result.scalar_one_or_none()

            if not item:
                return None

            for key, value in item_data.items():
                setattr(item, key, value)

            await session.commit()
            await session.refresh(item)

            logger.info(f"Updated staffing plan item {item_id}")
            return item

    async def delete(self, tenant_id: str, item_id: str) -> bool:
        """Delete staffing plan item."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(StaffingPlanItem).where(
                    and_(StaffingPlanItem.tenant_id == tenant_id, StaffingPlanItem.id == item_id)
                )
            )
            item = result.scalar_one_or_none()

            if not item:
                return False

            await session.delete(item)
            await session.commit()

            logger.info(f"Deleted staffing plan item {item_id}")
            return True
