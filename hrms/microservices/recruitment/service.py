"""
Business logic layer for Recruitment microservice.

This module contains the core business logic for recruitment operations including
job management, candidate tracking, application processing, interview scheduling,
and offer management.
"""

import os
from datetime import datetime
from typing import List, Optional

import aiofiles
from fastapi import UploadFile

from hrms.microservices.shared.exceptions import (
    BusinessLogicError,
    ConflictError,
    NotFoundError,
    ValidationError,
)
from hrms.microservices.shared.logging import get_logger
from hrms.microservices.shared.notifications import (
    NotificationRecipient,
    notification_service,
    notify_requisition_approved,
    notify_requisition_rejected,
    notify_requisition_submitted,
)

from .models import (
    ApplicationCreate,
    ApplicationResponse,
    ApplicationStatus,
    CandidateCreate,
    CandidateResponse,
    InterviewCreate,
    InterviewResponse,
    InterviewStatus,
    JobCreate,
    JobRequisitionCreate,
    JobRequisitionResponse,
    JobRequisitionUpdate,
    JobResponse,
    JobStatus,
    OfferCreate,
    OfferResponse,
    RequisitionPriority,
    RequisitionStatus,
    StaffingPlanCreate,
    StaffingPlanItemCreate,
    StaffingPlanItemResponse,
    StaffingPlanItemUpdate,
    StaffingPlanResponse,
    StaffingPlanStatus,
    StaffingPlanUpdate,
)
from .repository import (
    ApplicationRepository,
    CandidateRepository,
    InterviewRepository,
    JobRepository,
    JobRequisitionRepository,
    OfferRepository,
    StaffingPlanItemRepository,
    StaffingPlanRepository,
)

logger = get_logger(__name__)


class RecruitmentService:
    """Business logic service for recruitment operations."""

    def __init__(
        self,
        job_repo: JobRepository,
        candidate_repo: CandidateRepository,
        application_repo: ApplicationRepository,
        interview_repo: InterviewRepository,
        offer_repo: OfferRepository,
        job_requisition_repo: JobRequisitionRepository,
        staffing_plan_repo: StaffingPlanRepository,
        staffing_plan_item_repo: StaffingPlanItemRepository,
    ):
        self.job_repo = job_repo
        self.candidate_repo = candidate_repo
        self.application_repo = application_repo
        self.interview_repo = interview_repo
        self.offer_repo = offer_repo
        self.job_requisition_repo = job_requisition_repo
        self.staffing_plan_repo = staffing_plan_repo
        self.staffing_plan_item_repo = staffing_plan_item_repo

    # Job operations
    async def create_job(self, tenant_id: str, job_data: JobCreate, created_by: str) -> JobResponse:
        """Create a new job posting."""
        logger.info(f"Creating job for tenant {tenant_id}")

        # Validate business rules
        await self._validate_job_creation(tenant_id, job_data)

        # Create job
        job_dict = job_data.dict()
        job_dict["recruiter_id"] = created_by

        job = await self.job_repo.create(tenant_id, job_dict)

        logger.info(f"Job created successfully: {job.id}")
        return JobResponse.from_orm(job)

    async def get_job(self, tenant_id: str, job_id: str) -> JobResponse:
        """Get job by ID."""
        job = await self.job_repo.get_by_id(tenant_id, job_id)
        if not job:
            raise NotFoundError(f"Job not found: {job_id}")

        return JobResponse.from_orm(job)

    async def get_jobs(
        self,
        tenant_id: str,
        status: Optional[JobStatus] = None,
        department: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[JobResponse]:
        """Get jobs with optional filtering."""
        jobs = await self.job_repo.get_all(tenant_id, status, department, limit, offset)
        return [JobResponse.from_orm(job) for job in jobs]

    async def update_job(self, tenant_id: str, job_id: str, job_data: dict) -> JobResponse:
        """Update job posting."""
        job = await self.job_repo.get_by_id(tenant_id, job_id)
        if not job:
            raise NotFoundError(f"Job not found: {job_id}")

        updated_job = await self.job_repo.update(tenant_id, job_id, job_data)

        logger.info(f"Job updated successfully: {job_id}")
        return JobResponse.from_orm(updated_job)

    async def publish_job(self, tenant_id: str, job_id: str) -> JobResponse:
        """Publish a job posting."""
        job = await self.job_repo.get_by_id(tenant_id, job_id)
        if not job:
            raise NotFoundError(f"Job not found: {job_id}")

        if job.status != JobStatus.DRAFT:
            raise BusinessLogicError("Only draft jobs can be published")

        updated_job = await self.job_repo.update_status(tenant_id, job_id, JobStatus.PUBLISHED)

        logger.info(f"Job published successfully: {job_id}")
        return JobResponse.from_orm(updated_job)

    async def close_job(self, tenant_id: str, job_id: str) -> JobResponse:
        """Close a job posting."""
        job = await self.job_repo.get_by_id(tenant_id, job_id)
        if not job:
            raise NotFoundError(f"Job not found: {job_id}")

        if job.status not in [JobStatus.PUBLISHED]:
            raise BusinessLogicError("Only published jobs can be closed")

        updated_job = await self.job_repo.update_status(tenant_id, job_id, JobStatus.CLOSED)

        logger.info(f"Job closed successfully: {job_id}")
        return JobResponse.from_orm(updated_job)

    async def _validate_job_creation(self, tenant_id: str, job_data: JobCreate):
        """Validate job creation business rules."""
        # Add any business rule validations here
        if job_data.closing_date and job_data.closing_date <= datetime.now().date():
            raise ValidationError("Closing date must be in the future")

    # Candidate operations
    async def create_candidate(self, tenant_id: str, candidate_data: CandidateCreate) -> CandidateResponse:
        """Create a new candidate."""
        logger.info(f"Creating candidate for tenant {tenant_id}")

        # Check for existing candidate with same email
        existing_candidate = await self.candidate_repo.get_by_email(tenant_id, candidate_data.email)
        if existing_candidate:
            raise ConflictError(f"Candidate with email {candidate_data.email} already exists")

        # Create candidate
        candidate_dict = candidate_data.dict()
        candidate = await self.candidate_repo.create(tenant_id, candidate_dict)

        logger.info(f"Candidate created successfully: {candidate.id}")
        return CandidateResponse.from_orm(candidate)

    async def get_candidate(self, tenant_id: str, candidate_id: str) -> CandidateResponse:
        """Get candidate by ID."""
        candidate = await self.candidate_repo.get_by_id(tenant_id, candidate_id)
        if not candidate:
            raise NotFoundError(f"Candidate not found: {candidate_id}")

        return CandidateResponse.from_orm(candidate)

    async def get_candidates(
        self, tenant_id: str, search: Optional[str] = None, limit: int = 50, offset: int = 0
    ) -> List[CandidateResponse]:
        """Get candidates with optional search."""
        candidates = await self.candidate_repo.get_all(tenant_id, search, limit, offset)
        return [CandidateResponse.from_orm(candidate) for candidate in candidates]

    async def update_candidate(
        self, tenant_id: str, candidate_id: str, candidate_data: dict
    ) -> CandidateResponse:
        """Update candidate information."""
        candidate = await self.candidate_repo.get_by_id(tenant_id, candidate_id)
        if not candidate:
            raise NotFoundError(f"Candidate not found: {candidate_id}")

        updated_candidate = await self.candidate_repo.update(tenant_id, candidate_id, candidate_data)

        logger.info(f"Candidate updated successfully: {candidate_id}")
        return CandidateResponse.from_orm(updated_candidate)

    async def upload_resume(self, tenant_id: str, candidate_id: str, file: UploadFile) -> CandidateResponse:
        """Upload resume for candidate."""
        candidate = await self.candidate_repo.get_by_id(tenant_id, candidate_id)
        if not candidate:
            raise NotFoundError(f"Candidate not found: {candidate_id}")

        # Validate file
        await self._validate_resume_file(file)

        # Save file
        file_path = await self._save_resume_file(tenant_id, candidate_id, file)

        # Update candidate with resume path
        updated_candidate = await self.candidate_repo.update(
            tenant_id, candidate_id, {"resume_file_path": file_path}
        )

        logger.info(f"Resume uploaded for candidate: {candidate_id}")
        return CandidateResponse.from_orm(updated_candidate)

    async def _validate_resume_file(self, file: UploadFile):
        """Validate uploaded resume file."""
        # Check file size (5MB limit)
        if file.size and file.size > 5 * 1024 * 1024:
            raise ValidationError("Resume file size cannot exceed 5MB")

        # Check file type
        allowed_types = [".pdf", ".doc", ".docx"]
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_types:
            raise ValidationError(f"Resume file type {file_ext} not allowed")

    async def _save_resume_file(self, tenant_id: str, candidate_id: str, file: UploadFile) -> str:
        """Save uploaded resume file to storage."""
        # Create directory structure
        upload_dir = f"uploads/{tenant_id}/candidates/{candidate_id}/resumes"
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_ext = os.path.splitext(file.filename)[1]
        unique_filename = f"resume_{timestamp}{file_ext}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save file
        async with aiofiles.open(file_path, "wb") as f:
            content = await file.read()
            await f.write(content)

        return file_path

    # Application operations
    async def create_application(
        self, tenant_id: str, application_data: ApplicationCreate
    ) -> ApplicationResponse:
        """Create a new job application."""
        logger.info(f"Creating application for tenant {tenant_id}")

        # Validate job exists and is published
        job = await self.job_repo.get_by_id(tenant_id, application_data.job_id)
        if not job:
            raise NotFoundError(f"Job not found: {application_data.job_id}")

        if job.status != JobStatus.PUBLISHED:
            raise BusinessLogicError("Cannot apply to unpublished job")

        # Validate candidate exists
        candidate = await self.candidate_repo.get_by_id(tenant_id, application_data.candidate_id)
        if not candidate:
            raise NotFoundError(f"Candidate not found: {application_data.candidate_id}")

        # Check for existing application
        existing_application = await self.application_repo.check_existing_application(
            tenant_id, application_data.job_id, application_data.candidate_id
        )
        if existing_application:
            raise ConflictError("Candidate has already applied for this job")

        # Create application
        application_dict = application_data.dict()
        application = await self.application_repo.create(tenant_id, application_dict)

        # Increment job application count
        await self.job_repo.increment_application_count(tenant_id, application_data.job_id)

        logger.info(f"Application created successfully: {application.id}")
        return ApplicationResponse.from_orm(application)

    async def get_application(self, tenant_id: str, application_id: str) -> ApplicationResponse:
        """Get application by ID."""
        application = await self.application_repo.get_by_id(tenant_id, application_id)
        if not application:
            raise NotFoundError(f"Application not found: {application_id}")

        return ApplicationResponse.from_orm(application)

    async def get_applications_by_job(
        self,
        tenant_id: str,
        job_id: str,
        status: Optional[ApplicationStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[ApplicationResponse]:
        """Get applications for a job."""
        applications = await self.application_repo.get_by_job(tenant_id, job_id, status, limit, offset)
        return [ApplicationResponse.from_orm(app) for app in applications]

    async def get_applications_by_candidate(
        self, tenant_id: str, candidate_id: str, limit: int = 50, offset: int = 0
    ) -> List[ApplicationResponse]:
        """Get applications by candidate."""
        applications = await self.application_repo.get_by_candidate(tenant_id, candidate_id, limit, offset)
        return [ApplicationResponse.from_orm(app) for app in applications]

    async def update_application_status(
        self,
        tenant_id: str,
        application_id: str,
        status: ApplicationStatus,
        reviewer_id: str,
        review_notes: Optional[str] = None,
        rejection_reason: Optional[str] = None,
    ) -> ApplicationResponse:
        """Update application status."""
        application = await self.application_repo.get_by_id(tenant_id, application_id)
        if not application:
            raise NotFoundError(f"Application not found: {application_id}")

        updated_application = await self.application_repo.update_status(
            tenant_id, application_id, status, reviewer_id, review_notes, rejection_reason
        )

        logger.info(f"Application {application_id} status updated to {status}")
        return ApplicationResponse.from_orm(updated_application)

    # Interview operations
    async def schedule_interview(self, tenant_id: str, interview_data: InterviewCreate) -> InterviewResponse:
        """Schedule an interview."""
        logger.info(f"Scheduling interview for tenant {tenant_id}")

        # Validate application exists
        application = await self.application_repo.get_by_id(tenant_id, interview_data.application_id)
        if not application:
            raise NotFoundError(f"Application not found: {interview_data.application_id}")

        # Validate business rules
        await self._validate_interview_scheduling(tenant_id, interview_data)

        # Create interview
        interview_dict = interview_data.dict()
        interview = await self.interview_repo.create(tenant_id, interview_dict)

        logger.info(f"Interview scheduled successfully: {interview.id}")
        return InterviewResponse.from_orm(interview)

    async def get_interview(self, tenant_id: str, interview_id: str) -> InterviewResponse:
        """Get interview by ID."""
        interview = await self.interview_repo.get_by_id(tenant_id, interview_id)
        if not interview:
            raise NotFoundError(f"Interview not found: {interview_id}")

        return InterviewResponse.from_orm(interview)

    async def get_interviews_by_application(
        self, tenant_id: str, application_id: str, limit: int = 50, offset: int = 0
    ) -> List[InterviewResponse]:
        """Get interviews for an application."""
        interviews = await self.interview_repo.get_by_application(tenant_id, application_id, limit, offset)
        return [InterviewResponse.from_orm(interview) for interview in interviews]

    async def get_interviews_by_interviewer(
        self,
        tenant_id: str,
        interviewer_id: str,
        status: Optional[InterviewStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[InterviewResponse]:
        """Get interviews by interviewer."""
        interviews = await self.interview_repo.get_by_interviewer(
            tenant_id, interviewer_id, status, limit, offset
        )
        return [InterviewResponse.from_orm(interview) for interview in interviews]

    async def complete_interview(
        self, tenant_id: str, interview_id: str, feedback: str, rating: float, recommendation: str
    ) -> InterviewResponse:
        """Complete interview with feedback."""
        interview = await self.interview_repo.get_by_id(tenant_id, interview_id)
        if not interview:
            raise NotFoundError(f"Interview not found: {interview_id}")

        if interview.status != InterviewStatus.SCHEDULED:
            raise BusinessLogicError("Only scheduled interviews can be completed")

        updated_interview = await self.interview_repo.update_status(
            tenant_id, interview_id, InterviewStatus.COMPLETED, feedback, rating, recommendation
        )

        logger.info(f"Interview completed: {interview_id}")
        return InterviewResponse.from_orm(updated_interview)

    async def _validate_interview_scheduling(self, tenant_id: str, interview_data: InterviewCreate):
        """Validate interview scheduling business rules."""
        # Check minimum notice period (24 hours)
        hours_in_advance = (interview_data.scheduled_date - datetime.now()).total_seconds() / 3600
        if hours_in_advance < 24:
            raise ValidationError("Interview must be scheduled at least 24 hours in advance")

    # Offer operations
    async def create_offer(self, tenant_id: str, offer_data: OfferCreate, created_by: str) -> OfferResponse:
        """Create a job offer."""
        logger.info(f"Creating offer for tenant {tenant_id}")

        # Validate application exists
        application = await self.application_repo.get_by_id(tenant_id, offer_data.application_id)
        if not application:
            raise NotFoundError(f"Application not found: {offer_data.application_id}")

        # Check if offer already exists
        existing_offer = await self.offer_repo.get_by_application(tenant_id, offer_data.application_id)
        if existing_offer:
            raise ConflictError("Offer already exists for this application")

        # Create offer
        offer_dict = offer_data.dict()
        offer_dict["created_by"] = created_by

        offer = await self.offer_repo.create(tenant_id, offer_dict)

        logger.info(f"Offer created successfully: {offer.id}")
        return OfferResponse.from_orm(offer)

    async def get_offer(self, tenant_id: str, offer_id: str) -> OfferResponse:
        """Get offer by ID."""
        offer = await self.offer_repo.get_by_id(tenant_id, offer_id)
        if not offer:
            raise NotFoundError(f"Offer not found: {offer_id}")

        return OfferResponse.from_orm(offer)

    async def send_offer(self, tenant_id: str, offer_id: str) -> OfferResponse:
        """Send offer to candidate."""
        offer = await self.offer_repo.get_by_id(tenant_id, offer_id)
        if not offer:
            raise NotFoundError(f"Offer not found: {offer_id}")

        if offer.status != OfferStatus.DRAFT:
            raise BusinessLogicError("Only draft offers can be sent")

        updated_offer = await self.offer_repo.update_status(tenant_id, offer_id, OfferStatus.SENT)

        logger.info(f"Offer sent: {offer_id}")
        return OfferResponse.from_orm(updated_offer)

    async def accept_offer(self, tenant_id: str, offer_id: str) -> OfferResponse:
        """Accept job offer."""
        offer = await self.offer_repo.get_by_id(tenant_id, offer_id)
        if not offer:
            raise NotFoundError(f"Offer not found: {offer_id}")

        if offer.status != OfferStatus.SENT:
            raise BusinessLogicError("Only sent offers can be accepted")

        updated_offer = await self.offer_repo.update_status(tenant_id, offer_id, OfferStatus.ACCEPTED)

        # Update application status to hired
        await self.application_repo.update_status(tenant_id, offer.application_id, ApplicationStatus.HIRED)

        logger.info(f"Offer accepted: {offer_id}")
        return OfferResponse.from_orm(updated_offer)

    async def reject_offer(self, tenant_id: str, offer_id: str, rejection_reason: str) -> OfferResponse:
        """Reject job offer."""
        offer = await self.offer_repo.get_by_id(tenant_id, offer_id)
        if not offer:
            raise NotFoundError(f"Offer not found: {offer_id}")

        if offer.status != OfferStatus.SENT:
            raise BusinessLogicError("Only sent offers can be rejected")

        updated_offer = await self.offer_repo.update_status(
            tenant_id, offer_id, OfferStatus.REJECTED, rejection_reason
        )

        logger.info(f"Offer rejected: {offer_id}")
        return OfferResponse.from_orm(updated_offer)

    # Job Requisition operations
    async def create_job_requisition(
        self, tenant_id: str, requisition_data: JobRequisitionCreate, created_by: str
    ) -> JobRequisitionResponse:
        """Create a new job requisition."""
        logger.info(f"Creating job requisition for tenant {tenant_id}")

        # Validate business rules
        await self._validate_requisition_creation(tenant_id, requisition_data)

        # Create requisition
        requisition_dict = requisition_data.dict()
        requisition_dict["requested_by"] = created_by

        requisition = await self.job_requisition_repo.create(tenant_id, requisition_dict)

        logger.info(f"Job requisition created successfully: {requisition.id}")
        return JobRequisitionResponse.from_orm(requisition)

    async def get_job_requisition(self, tenant_id: str, requisition_id: str) -> JobRequisitionResponse:
        """Get job requisition by ID."""
        requisition = await self.job_requisition_repo.get_by_id(tenant_id, requisition_id)
        if not requisition:
            raise NotFoundError(f"Job requisition not found: {requisition_id}")

        return JobRequisitionResponse.from_orm(requisition)

    async def get_job_requisitions(
        self,
        tenant_id: str,
        status: Optional[RequisitionStatus] = None,
        department_id: Optional[str] = None,
        priority: Optional[RequisitionPriority] = None,
        requested_by: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[JobRequisitionResponse]:
        """Get job requisitions with filtering and mock fallback."""
        try:
            requisitions = await self.job_requisition_repo.get_all(
                tenant_id, status, department_id, priority, requested_by, limit, offset
            )
            return [JobRequisitionResponse.from_orm(req) for req in requisitions]
        except Exception as e:
            # Return mock data for development when database is not available
            from datetime import date, datetime
            from decimal import Decimal

            mock_requisitions = [
                JobRequisitionResponse(
                    id="req-001",
                    title="Senior Software Engineer",
                    description="We are looking for a senior software engineer to join our team and help build scalable backend services.",
                    department_id="eng-001",
                    position_id="pos-001",
                    location="Remote",
                    employment_type="Full-time",
                    experience_level="Senior",
                    headcount_needed=2,
                    estimated_salary_min=Decimal("80000"),
                    estimated_salary_max=Decimal("120000"),
                    currency="USD",
                    budget_allocation=Decimal("200000"),
                    requirements="Python, FastAPI, React, 3+ years experience",
                    responsibilities="Develop backend services, code reviews, mentoring",
                    business_justification="Team expansion to support new product features",
                    priority=RequisitionPriority.HIGH,
                    status=RequisitionStatus.APPROVED,
                    requested_by="manager-001",
                    hiring_manager_id="mgr-001",
                    department_head_id="dept-001",
                    hr_approver_id="hr-001",
                    finance_approver_id="fin-001",
                    submitted_date=datetime.now().isoformat(),
                    approved_date=datetime.now().isoformat(),
                    rejection_reason=None,
                    expected_start_date=date.today().isoformat(),
                    is_urgent=True,
                    replacement_for=None,
                    additional_notes="Remote work preferred",
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat(),
                ),
                JobRequisitionResponse(
                    id="req-002",
                    title="Product Manager",
                    description="Join our product team to drive product strategy and roadmap for our core platform.",
                    department_id="product-001",
                    position_id="pos-002",
                    location="San Francisco",
                    employment_type="Full-time",
                    experience_level="Mid",
                    headcount_needed=1,
                    estimated_salary_min=Decimal("90000"),
                    estimated_salary_max=Decimal("130000"),
                    currency="USD",
                    budget_allocation=Decimal("130000"),
                    requirements="Product Management, Analytics, 2+ years experience",
                    responsibilities="Product roadmap, stakeholder management, user research",
                    business_justification="Product expansion to new markets",
                    priority=RequisitionPriority.MEDIUM,
                    status=RequisitionStatus.SUBMITTED,
                    requested_by="manager-002",
                    hiring_manager_id="mgr-002",
                    department_head_id="dept-002",
                    hr_approver_id=None,
                    finance_approver_id=None,
                    submitted_date=datetime.now().isoformat(),
                    approved_date=None,
                    rejection_reason=None,
                    expected_start_date=date.today().isoformat(),
                    is_urgent=False,
                    replacement_for=None,
                    additional_notes="Hybrid work model",
                    created_at=datetime.now().isoformat(),
                    updated_at=datetime.now().isoformat(),
                ),
            ]
            return mock_requisitions

    async def update_job_requisition(
        self, tenant_id: str, requisition_id: str, requisition_data: JobRequisitionUpdate
    ) -> JobRequisitionResponse:
        """Update job requisition."""
        # Remove None values for partial update
        update_data = {k: v for k, v in requisition_data.dict().items() if v is not None}

        requisition = await self.job_requisition_repo.update(tenant_id, requisition_id, update_data)
        if not requisition:
            raise NotFoundError(f"Job requisition not found: {requisition_id}")

        logger.info(f"Job requisition updated: {requisition_id}")
        return JobRequisitionResponse.from_orm(requisition)

    async def submit_job_requisition(
        self, tenant_id: str, requisition_id: str, submitted_by: str
    ) -> JobRequisitionResponse:
        """Submit a job requisition for approval."""
        logger.info(f"Submitting job requisition {requisition_id} for approval")

        # Get the requisition
        requisition = await self.job_requisition_repo.get_by_id(tenant_id, requisition_id)
        if not requisition:
            raise NotFoundError(f"Job requisition not found: {requisition_id}")

        # Validate that only the requester can submit their own requisition
        if requisition.requested_by != submitted_by:
            raise BusinessLogicError("Only the requester can submit their own requisition")

        # Validate that requisition is in draft status
        if requisition.status != RequisitionStatus.DRAFT:
            raise BusinessLogicError("Only draft requisitions can be submitted")

        # Update status to submitted
        updated_requisition = await self.job_requisition_repo.update_status(
            tenant_id, requisition_id, RequisitionStatus.SUBMITTED
        )

        # ✅ Send notifications to approvers
        try:
            # Get approvers (HR and Finance admins)
            approvers = [
                NotificationRecipient(user_id="hr_admin_001", email="<EMAIL>", name="HR Admin"),
                NotificationRecipient(
                    user_id="finance_admin_001", email="<EMAIL>", name="Finance Admin"
                ),
            ]

            await notify_requisition_submitted(
                requisition_id=requisition_id,
                submitted_by=submitted_by,
                approvers=approvers,
                tenant_id=tenant_id,
            )

            logger.info(f"Notifications sent for requisition {requisition_id}")
        except Exception as e:
            logger.error(f"Failed to send notifications for requisition {requisition_id}: {e}")
            # Don't fail the operation if notifications fail

        logger.info(f"Job requisition {requisition_id} submitted successfully")
        return JobRequisitionResponse.from_orm(updated_requisition)

    async def approve_job_requisition(
        self, tenant_id: str, requisition_id: str, approved_by: str
    ) -> JobRequisitionResponse:
        """Approve a job requisition."""
        logger.info(f"Approving job requisition {requisition_id}")

        # Get the requisition
        requisition = await self.job_requisition_repo.get_by_id(tenant_id, requisition_id)
        if not requisition:
            raise NotFoundError(f"Job requisition not found: {requisition_id}")

        # Validate that requisition is in submitted or under_review status
        if requisition.status not in [RequisitionStatus.SUBMITTED, RequisitionStatus.UNDER_REVIEW]:
            raise BusinessLogicError("Only submitted or under review requisitions can be approved")

        # Update status to approved
        updated_requisition = await self.job_requisition_repo.update_status(
            tenant_id, requisition_id, RequisitionStatus.APPROVED
        )

        # ✅ Send notification to requester
        try:
            requester = NotificationRecipient(
                user_id=requisition.requested_by,
                email=f"{requisition.requested_by}@company.com",
                name=requisition.requested_by,
            )

            await notify_requisition_approved(
                requisition_id=requisition_id,
                approved_by=approved_by,
                requester=requester,
                tenant_id=tenant_id,
            )

            logger.info(f"Approval notification sent for requisition {requisition_id}")
        except Exception as e:
            logger.error(f"Failed to send approval notification for requisition {requisition_id}: {e}")
            # Don't fail the operation if notifications fail

        logger.info(f"Job requisition {requisition_id} approved successfully")
        return JobRequisitionResponse.from_orm(updated_requisition)

    async def reject_job_requisition(
        self, tenant_id: str, requisition_id: str, rejection_reason: str, rejected_by: str
    ) -> JobRequisitionResponse:
        """Reject a job requisition."""
        logger.info(f"Rejecting job requisition {requisition_id}")

        # Get the requisition
        requisition = await self.job_requisition_repo.get_by_id(tenant_id, requisition_id)
        if not requisition:
            raise NotFoundError(f"Job requisition not found: {requisition_id}")

        # Validate that requisition is in submitted or under_review status
        if requisition.status not in [RequisitionStatus.SUBMITTED, RequisitionStatus.UNDER_REVIEW]:
            raise BusinessLogicError("Only submitted or under review requisitions can be rejected")

        # Validate rejection reason
        if not rejection_reason or len(rejection_reason.strip()) < 10:
            raise ValidationError("Rejection reason must be at least 10 characters long")

        # Update status to rejected with reason
        updated_requisition = await self.job_requisition_repo.update_status(
            tenant_id, requisition_id, RequisitionStatus.REJECTED, rejection_reason=rejection_reason
        )

        # ✅ Send notification to requester
        try:
            requester = NotificationRecipient(
                user_id=requisition.requested_by,
                email=f"{requisition.requested_by}@company.com",
                name=requisition.requested_by,
            )

            await notify_requisition_rejected(
                requisition_id=requisition_id,
                rejected_by=rejected_by,
                rejection_reason=rejection_reason,
                requester=requester,
                tenant_id=tenant_id,
            )

            logger.info(f"Rejection notification sent for requisition {requisition_id}")
        except Exception as e:
            logger.error(f"Failed to send rejection notification for requisition {requisition_id}: {e}")
            # Don't fail the operation if notifications fail

        logger.info(f"Job requisition {requisition_id} rejected successfully")
        return JobRequisitionResponse.from_orm(updated_requisition)

    async def _validate_requisition_creation(self, tenant_id: str, requisition_data: JobRequisitionCreate):
        """Validate job requisition creation business rules."""
        # Add validation logic here
        pass

    # Staffing Plan operations
    async def create_staffing_plan(
        self, tenant_id: str, plan_data: StaffingPlanCreate, created_by: str
    ) -> StaffingPlanResponse:
        """Create a new staffing plan."""
        logger.info(f"Creating staffing plan for tenant {tenant_id}")

        # Validate business rules
        await self._validate_staffing_plan_creation(tenant_id, plan_data)

        # Create plan
        plan_dict = plan_data.dict()
        plan_dict["created_by"] = created_by

        plan = await self.staffing_plan_repo.create(tenant_id, plan_dict)

        logger.info(f"Staffing plan created successfully: {plan.id}")
        return StaffingPlanResponse.from_orm(plan)

    async def get_staffing_plan(self, tenant_id: str, plan_id: str) -> StaffingPlanResponse:
        """Get staffing plan by ID."""
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        return StaffingPlanResponse.from_orm(plan)

    async def get_staffing_plans(
        self,
        tenant_id: str,
        department_id: Optional[str] = None,
        fiscal_year: Optional[int] = None,
        status: Optional[StaffingPlanStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[StaffingPlanResponse]:
        """Get staffing plans with filtering."""
        plans = await self.staffing_plan_repo.get_all(
            tenant_id, department_id, fiscal_year, status, limit, offset
        )
        return [StaffingPlanResponse.from_orm(plan) for plan in plans]

    async def update_staffing_plan(
        self, tenant_id: str, plan_id: str, plan_data: StaffingPlanUpdate
    ) -> StaffingPlanResponse:
        """Update staffing plan."""
        # Remove None values for partial update
        update_data = {k: v for k, v in plan_data.dict().items() if v is not None}

        plan = await self.staffing_plan_repo.update(tenant_id, plan_id, update_data)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        logger.info(f"Staffing plan updated: {plan_id}")
        return StaffingPlanResponse.from_orm(plan)

    async def activate_staffing_plan(
        self, tenant_id: str, plan_id: str, activated_by: str
    ) -> StaffingPlanResponse:
        """Activate staffing plan."""
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        if plan.status != StaffingPlanStatus.DRAFT:
            raise BusinessLogicError("Only draft plans can be activated")

        updated_plan = await self.staffing_plan_repo.update_status(
            tenant_id,
            plan_id,
            StaffingPlanStatus.ACTIVE,
            approved_by=activated_by,
            approved_date=datetime.utcnow(),
        )

        logger.info(f"Staffing plan activated: {plan_id}")
        return StaffingPlanResponse.from_orm(updated_plan)

    async def _validate_staffing_plan_creation(self, tenant_id: str, plan_data: StaffingPlanCreate):
        """Validate staffing plan creation business rules."""
        # Add validation logic here
        pass

    # Staffing Plan Items operations
    async def create_staffing_plan_item(
        self, tenant_id: str, plan_id: str, item_data: StaffingPlanItemCreate, created_by: str
    ) -> StaffingPlanItemResponse:
        """Create a new staffing plan item."""
        logger.info(f"Creating staffing plan item for plan {plan_id}")

        # Validate plan exists
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        # Create item
        item_dict = item_data.dict()
        item_dict["staffing_plan_id"] = plan_id

        item = await self.staffing_plan_item_repo.create(tenant_id, item_dict)

        logger.info(f"Staffing plan item created successfully: {item.id}")
        return StaffingPlanItemResponse.from_orm(item)

    async def get_staffing_plan_items(
        self, tenant_id: str, plan_id: str, status: Optional[str] = None, limit: int = 50, offset: int = 0
    ) -> List[StaffingPlanItemResponse]:
        """Get staffing plan items."""
        # Validate plan exists
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        items = await self.staffing_plan_item_repo.get_by_plan(tenant_id, plan_id, status, limit, offset)
        return [StaffingPlanItemResponse.from_orm(item) for item in items]

    async def update_staffing_plan_item(
        self, tenant_id: str, plan_id: str, item_id: str, item_data: StaffingPlanItemUpdate
    ) -> StaffingPlanItemResponse:
        """Update staffing plan item."""
        # Validate plan exists
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        # Remove None values for partial update
        update_data = {k: v for k, v in item_data.dict().items() if v is not None}

        item = await self.staffing_plan_item_repo.update(tenant_id, item_id, update_data)
        if not item:
            raise NotFoundError(f"Staffing plan item not found: {item_id}")

        logger.info(f"Staffing plan item updated: {item_id}")
        return StaffingPlanItemResponse.from_orm(item)

    async def delete_staffing_plan_item(self, tenant_id: str, plan_id: str, item_id: str):
        """Delete staffing plan item."""
        # Validate plan exists
        plan = await self.staffing_plan_repo.get_by_id(tenant_id, plan_id)
        if not plan:
            raise NotFoundError(f"Staffing plan not found: {plan_id}")

        success = await self.staffing_plan_item_repo.delete(tenant_id, item_id)
        if not success:
            raise NotFoundError(f"Staffing plan item not found: {item_id}")

        logger.info(f"Staffing plan item deleted: {item_id}")
