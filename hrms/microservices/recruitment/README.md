# Recruitment Management Microservice

A comprehensive recruitment and candidate management service for streamlined hiring workflows.

## Overview

The Recruitment Management microservice provides a complete platform for managing the entire hiring process:

- Job posting and management
- Candidate application tracking
- Interview scheduling and management
- Hiring workflow automation
- Candidate evaluation and scoring
- Offer management and onboarding
- Recruitment analytics and reporting

## Features

### 💼 Job Management
- Create and publish job postings
- Manage job requirements and descriptions
- Track application counts and metrics
- Close and archive completed positions

### 👥 Candidate Management
- Candidate profile creation and management
- Resume upload and storage
- Skills and experience tracking
- Application history and notes

### 📝 Application Tracking
- Application submission and processing
- Status tracking throughout hiring pipeline
- Reviewer notes and feedback
- Automated screening capabilities

### 🎯 Interview Management
- Interview scheduling with multiple participants
- Interview type management (phone, video, in-person)
- Feedback collection and rating system
- Recommendation tracking

### 💰 Offer Management
- Job offer creation and customization
- Offer approval and sending workflow
- Acceptance/rejection tracking
- Terms and conditions management

## API Endpoints

### Jobs
- `GET /api/v1/recruitment/jobs` - List job postings
- `POST /api/v1/recruitment/jobs` - Create job posting
- `GET /api/v1/recruitment/jobs/{id}` - Get job details
- `PUT /api/v1/recruitment/jobs/{id}` - Update job posting
- `PATCH /api/v1/recruitment/jobs/{id}/publish` - Publish job
- `PATCH /api/v1/recruitment/jobs/{id}/close` - Close job

### Candidates
- `GET /api/v1/recruitment/candidates` - List candidates
- `POST /api/v1/recruitment/candidates` - Create candidate
- `GET /api/v1/recruitment/candidates/{id}` - Get candidate details
- `PUT /api/v1/recruitment/candidates/{id}` - Update candidate
- `POST /api/v1/recruitment/candidates/{id}/resume` - Upload resume

### Applications
- `GET /api/v1/recruitment/applications` - List applications
- `POST /api/v1/recruitment/applications` - Submit application
- `GET /api/v1/recruitment/applications/{id}` - Get application details
- `PATCH /api/v1/recruitment/applications/{id}/status` - Update status

### Interviews
- `GET /api/v1/recruitment/interviews` - List interviews
- `POST /api/v1/recruitment/interviews` - Schedule interview
- `GET /api/v1/recruitment/interviews/{id}` - Get interview details
- `PATCH /api/v1/recruitment/interviews/{id}/complete` - Complete interview

### Offers
- `GET /api/v1/recruitment/offers/{id}` - Get offer details
- `POST /api/v1/recruitment/offers` - Create offer
- `PATCH /api/v1/recruitment/offers/{id}/send` - Send offer
- `PATCH /api/v1/recruitment/offers/{id}/accept` - Accept offer
- `PATCH /api/v1/recruitment/offers/{id}/reject` - Reject offer

## Architecture

### Service Structure
```
hrms/microservices/recruitment/
├── __init__.py          # Service package initialization
├── api.py               # FastAPI application and endpoints
├── service.py           # Business logic layer
├── repository.py        # Data access layer
├── models.py            # Pydantic models and database schemas
├── openapi.yaml         # OpenAPI specification
├── Dockerfile           # Multi-stage Docker configuration
└── README.md            # This documentation
```

### Database Schema

#### Jobs
- Job postings with requirements and descriptions
- Salary ranges and employment types
- Status tracking and application counts

#### Candidates
- Candidate profiles with contact information
- Skills, experience, and education tracking
- Resume storage and portfolio links

#### Applications
- Job applications linking candidates to jobs
- Status tracking through hiring pipeline
- Screening scores and reviewer feedback

#### Interviews
- Interview scheduling and management
- Feedback collection and rating system
- Multiple interview rounds support

#### Offers
- Job offers with salary and terms
- Approval workflow and sending tracking
- Acceptance/rejection with reasons

## Business Rules

### Job Management
- Draft → Published → Closed workflow
- Application deadline enforcement
- Maximum applications per job limit

### Application Process
- One application per candidate per job
- Automatic duplicate prevention
- Status progression validation

### Interview Scheduling
- Minimum 24-hour advance notice
- Maximum interviews per day limit
- Interviewer availability checking

### Offer Management
- One offer per application
- Response deadline tracking
- Automatic status updates

## Multi-Tenancy

All recruitment data is isolated by tenant:
- Job postings scoped to tenant
- Candidate pools separated by tenant
- Application tracking per tenant
- Interview and offer management isolated

## Authentication & Authorization

- JWT-based authentication required
- Role-based access control (HR, Hiring Manager, Interviewer)
- Tenant-aware data access
- Mocked for development environment

## Development

### Running the Service

```bash
# Development with hot reload
uvicorn hrms.microservices.recruitment.api:app --host 0.0.0.0 --port 8104 --reload

# Production
gunicorn hrms.microservices.recruitment.api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8104
```

### Docker

```bash
# Build development image
docker build --target development -t recruitment-service:dev .

# Build production image
docker build --target production -t recruitment-service:prod .

# Run container
docker run -p 8104:8104 -e DATABASE_URL=postgresql://... recruitment-service:dev
```

### Testing

```bash
# Run all tests
pytest tests/microservices/recruitment/ -v

# Run with coverage
pytest tests/microservices/recruitment/ --cov=hrms.microservices.recruitment --cov-report=html

# Run specific test file
pytest tests/microservices/recruitment/test_service.py -v
```

## Configuration

### Environment Variables

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET_KEY` - JWT signing key (mocked in dev)
- `UPLOAD_DIR` - Resume upload directory
- `MAX_RESUME_SIZE_MB` - Maximum resume file size
- `ALLOWED_RESUME_TYPES` - Allowed resume file types

### Feature Flags

```python
FEATURES = {
    "job_posting": True,
    "application_tracking": True,
    "interview_scheduling": True,
    "candidate_evaluation": True,
    "offer_management": True,
    "onboarding_integration": True,
    "recruitment_analytics": True,
    "automated_screening": True,
}
```

## Integration

### Kong API Gateway
- Routes configured in `/kong/kong.yml`
- CORS enabled for frontend integration
- JWT authentication (disabled in development)

### Employee Service
- Hiring manager validation
- Employee onboarding integration
- Organizational structure support

### Future Integrations
- Email notifications for candidates
- Calendar integration for interviews
- ATS (Applicant Tracking System) integration
- Background check services

## Workflows

### Hiring Process
1. **Job Creation** - HR creates and publishes job posting
2. **Application** - Candidates submit applications
3. **Screening** - HR reviews and shortlists candidates
4. **Interview** - Multiple interview rounds scheduled
5. **Evaluation** - Interviewers provide feedback and ratings
6. **Offer** - Successful candidates receive job offers
7. **Onboarding** - Accepted offers trigger onboarding process

### Status Transitions

#### Application Status
- Submitted → Under Review → Shortlisted → Interviewed → Offered → Hired/Rejected

#### Interview Status
- Scheduled → Confirmed → Completed → Cancelled/No Show

#### Offer Status
- Draft → Sent → Accepted/Rejected/Expired

## Monitoring & Logging

- Structured logging with correlation IDs
- Health check endpoint at `/health`
- Recruitment metrics and KPIs
- Performance tracking and analytics

## Security

### Data Protection
- Candidate data encryption
- Resume file security
- Access control by role
- Audit trail for all actions

### Compliance
- GDPR compliance for candidate data
- Data retention policies
- Right to be forgotten support
- Consent management

## Performance

### Optimizations
- Async database operations
- File streaming for resume uploads
- Search indexing for candidates
- Pagination for large datasets

### Caching
- Job posting caching
- Candidate search results
- Interview availability caching

## Analytics & Reporting

### Metrics Tracked
- Time to hire
- Application conversion rates
- Interview success rates
- Offer acceptance rates
- Source effectiveness

### Reports Available
- Recruitment pipeline status
- Hiring manager performance
- Candidate source analysis
- Time and cost per hire

## Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] File storage configured
- [ ] Kong routes deployed
- [ ] Health checks enabled
- [ ] Monitoring configured
- [ ] Email service integrated

### Scaling
- Horizontal scaling supported
- Stateless service design
- Shared file storage for resumes
- Database connection pooling

## Support

For technical support or questions:
- Check the API documentation at `/docs`
- Review test cases for usage examples
- Consult the OpenAPI specification
- Contact the development team

## Version History

- **v1.0.0** - Initial release with core recruitment functionality
- Complete hiring workflow automation
- Candidate and job management
- Interview scheduling system
- Offer management workflow
- Multi-tenant architecture
- Comprehensive test coverage
