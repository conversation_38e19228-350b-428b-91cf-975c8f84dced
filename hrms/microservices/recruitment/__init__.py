"""
Recruitment Management Microservice

Comprehensive recruitment and candidate management service for hiring workflows.

Features:
- Job posting and management
- Candidate application tracking
- Interview scheduling and management
- Hiring workflow automation
- Candidate evaluation and scoring
- Offer management and onboarding
- Recruitment analytics and reporting
- Multi-tenant data isolation
- Integration with Employee and HR services

API Endpoints:
- GET /api/v1/recruitment/jobs - List job postings
- POST /api/v1/recruitment/jobs - Create job posting
- GET /api/v1/recruitment/applications - List applications
- POST /api/v1/recruitment/applications - Submit application
- GET /api/v1/recruitment/interviews - List interviews
- POST /api/v1/recruitment/interviews - Schedule interview
- GET /api/v1/recruitment/candidates - List candidates
- POST /api/v1/recruitment/candidates - Create candidate
"""

__version__ = "1.0.0"
__service_name__ = "recruitment-service"
__service_port__ = 8104

# Service metadata
SERVICE_INFO = {
    "name": "Recruitment Management",
    "version": __version__,
    "description": "Recruitment and candidate management service",
    "port": __service_port__,
    "health_endpoint": "/health",
    "docs_endpoint": "/docs",
    "openapi_endpoint": "/openapi.json",
}

# Feature flags
FEATURES = {
    "job_posting": True,
    "application_tracking": True,
    "interview_scheduling": True,
    "candidate_evaluation": True,
    "offer_management": True,
    "onboarding_integration": True,
    "recruitment_analytics": True,
    "automated_screening": True,
}

# Business rules and constraints
BUSINESS_RULES = {
    "max_applications_per_job": 1000,
    "interview_duration_minutes": 60,
    "application_deadline_days": 30,
    "max_resume_size_mb": 5,
    "allowed_resume_types": [".pdf", ".doc", ".docx"],
    "min_interview_notice_hours": 24,
    "max_interviews_per_day": 8,
}

# Integration settings
INTEGRATIONS = {
    "employee_service": {"enabled": True, "base_url": "http://employee-service:8100", "timeout_seconds": 30},
    "ess_service": {"enabled": True, "base_url": "http://ess-service:8103", "timeout_seconds": 30},
    "email_service": {
        "enabled": False,  # Future integration
        "base_url": "http://email-service:8106",
        "timeout_seconds": 10,
    },
    "calendar_service": {
        "enabled": False,  # Future integration
        "base_url": "http://calendar-service:8107",
        "timeout_seconds": 10,
    },
}

# Export main components
from .api import app
from .models import (
    ApplicationCreate,
    ApplicationResponse,
    ApplicationStatus,
    CandidateCreate,
    CandidateResponse,
    InterviewCreate,
    InterviewResponse,
    InterviewStatus,
    JobCreate,
    JobResponse,
    JobStatus,
    OfferCreate,
    OfferResponse,
    OfferStatus,
)
from .repository import (
    ApplicationRepository,
    CandidateRepository,
    InterviewRepository,
    JobRepository,
    OfferRepository,
)
from .service import RecruitmentService

__all__ = [
    # Main application
    "app",
    # Service layer
    "RecruitmentService",
    # Repository layer
    "JobRepository",
    "CandidateRepository",
    "ApplicationRepository",
    "InterviewRepository",
    "OfferRepository",
    # Models and schemas
    "JobCreate",
    "JobResponse",
    "CandidateCreate",
    "CandidateResponse",
    "ApplicationCreate",
    "ApplicationResponse",
    "InterviewCreate",
    "InterviewResponse",
    "OfferCreate",
    "OfferResponse",
    # Enums
    "JobStatus",
    "ApplicationStatus",
    "InterviewStatus",
    "OfferStatus",
    # Metadata
    "SERVICE_INFO",
    "FEATURES",
    "BUSINESS_RULES",
    "INTEGRATIONS",
]
