#!/usr/bin/env python3
"""
Main entry point for the Recruitment microservice.

This module starts the FastAPI application for the recruitment service,
configures logging, database connections, and other service dependencies.
"""

import asyncio
import os
import sys
from pathlib import Path

import uvicorn
from fastapi import FastAPI

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from hrms.microservices.recruitment.api import app
from hrms.microservices.shared.database import DatabaseManager
from hrms.microservices.shared.logging import setup_logging


async def startup_event():
    """Initialize service dependencies on startup."""
    # Setup logging
    setup_logging(service_name="recruitment-service")

    # Initialize database
    db_manager = DatabaseManager()
    await db_manager.initialize()

    print("🚀 Recruitment service started successfully!")
    print(f"📊 Service running on: http://localhost:8104")
    print(f"📖 API Documentation: http://localhost:8104/docs")
    print(f"🔍 Health Check: http://localhost:8104/health")


async def shutdown_event():
    """Cleanup service dependencies on shutdown."""
    print("🛑 Shutting down recruitment service...")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    # Add startup and shutdown events
    app.add_event_handler("startup", startup_event)
    app.add_event_handler("shutdown", shutdown_event)

    return app


def main():
    """Main entry point for the recruitment service."""
    # Get configuration from environment variables
    host = os.getenv("RECRUITMENT_HOST", "0.0.0.0")
    port = int(os.getenv("RECRUITMENT_PORT", "8104"))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    reload = os.getenv("RELOAD", "true").lower() == "true"

    print(f"🎯 Starting Recruitment Service...")
    print(f"🌐 Host: {host}")
    print(f"🔌 Port: {port}")
    print(f"🐛 Debug: {debug}")
    print(f"🔄 Reload: {reload}")

    # Create the FastAPI app
    application = create_app()

    # Run the server
    uvicorn.run(
        "main:application",
        host=host,
        port=port,
        reload=reload,
        log_level="debug" if debug else "info",
        access_log=True,
    )


if __name__ == "__main__":
    main()
