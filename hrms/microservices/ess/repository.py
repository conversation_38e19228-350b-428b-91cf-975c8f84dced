"""
Repository layer for Employee Self-Service (ESS) microservice.

This module provides data access layer for ESS entities including leave applications,
documents, and timesheets with tenant isolation and async database operations.
"""

from datetime import date, datetime
from typing import List, Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from hrms.microservices.shared.database import DatabaseManager
from hrms.microservices.shared.exceptions import NotFoundError
from hrms.microservices.shared.logging import get_logger
from hrms.microservices.shared.utils import generate_id

from .models import Document, LeaveApplication, LeaveApplicationStatus, Timesheet, TimesheetStatus

logger = get_logger(__name__)


class LeaveApplicationRepository:
    """Repository for leave application data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, leave_data: dict) -> LeaveApplication:
        """Create a new leave application."""
        async with self.db_manager.get_session() as session:
            # Calculate total days
            from_date = leave_data["from_date"]
            to_date = leave_data["to_date"]
            total_days = (to_date - from_date).days + 1

            if leave_data.get("is_half_day"):
                total_days = 0.5

            leave_application = LeaveApplication(
                id=generate_id(), tenant_id=tenant_id, total_days=total_days, **leave_data
            )

            session.add(leave_application)
            await session.commit()
            await session.refresh(leave_application)

            logger.info(f"Created leave application {leave_application.id} for tenant {tenant_id}")
            return leave_application

    async def get_by_id(self, tenant_id: str, application_id: str) -> Optional[LeaveApplication]:
        """Get leave application by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(LeaveApplication).where(
                    and_(LeaveApplication.tenant_id == tenant_id, LeaveApplication.id == application_id)
                )
            )
            return result.scalar_one_or_none()

    async def get_by_employee(
        self,
        tenant_id: str,
        employee_id: str,
        status: Optional[LeaveApplicationStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[LeaveApplication]:
        """Get leave applications by employee."""
        async with self.db_manager.get_session() as session:
            query = select(LeaveApplication).where(
                and_(LeaveApplication.tenant_id == tenant_id, LeaveApplication.employee_id == employee_id)
            )

            if status:
                query = query.where(LeaveApplication.status == status)

            query = query.order_by(desc(LeaveApplication.created_at)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update_status(
        self,
        tenant_id: str,
        application_id: str,
        status: LeaveApplicationStatus,
        approver_id: Optional[str] = None,
        rejection_reason: Optional[str] = None,
    ) -> Optional[LeaveApplication]:
        """Update leave application status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(LeaveApplication).where(
                    and_(LeaveApplication.tenant_id == tenant_id, LeaveApplication.id == application_id)
                )
            )
            leave_application = result.scalar_one_or_none()

            if not leave_application:
                return None

            leave_application.status = status
            if approver_id:
                leave_application.approver_id = approver_id
            if status == LeaveApplicationStatus.APPROVED:
                leave_application.approved_date = datetime.utcnow()
            elif status == LeaveApplicationStatus.REJECTED and rejection_reason:
                leave_application.rejection_reason = rejection_reason

            await session.commit()
            await session.refresh(leave_application)

            logger.info(f"Updated leave application {application_id} status to {status}")
            return leave_application

    async def count_by_employee_and_status(
        self, tenant_id: str, employee_id: str, status: LeaveApplicationStatus
    ) -> int:
        """Count leave applications by employee and status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(func.count(LeaveApplication.id)).where(
                    and_(
                        LeaveApplication.tenant_id == tenant_id,
                        LeaveApplication.employee_id == employee_id,
                        LeaveApplication.status == status,
                    )
                )
            )
            return result.scalar() or 0


class DocumentRepository:
    """Repository for document data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, document_data: dict) -> Document:
        """Create a new document."""
        async with self.db_manager.get_session() as session:
            document = Document(id=generate_id(), tenant_id=tenant_id, **document_data)

            session.add(document)
            await session.commit()
            await session.refresh(document)

            logger.info(f"Created document {document.id} for tenant {tenant_id}")
            return document

    async def get_by_id(self, tenant_id: str, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Document).where(and_(Document.tenant_id == tenant_id, Document.id == document_id))
            )
            return result.scalar_one_or_none()

    async def get_by_employee(
        self, tenant_id: str, employee_id: str, limit: int = 50, offset: int = 0
    ) -> List[Document]:
        """Get documents by employee."""
        async with self.db_manager.get_session() as session:
            query = (
                select(Document)
                .where(and_(Document.tenant_id == tenant_id, Document.employee_id == employee_id))
                .order_by(desc(Document.created_at))
                .limit(limit)
                .offset(offset)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def delete(self, tenant_id: str, document_id: str) -> bool:
        """Delete document."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Document).where(and_(Document.tenant_id == tenant_id, Document.id == document_id))
            )
            document = result.scalar_one_or_none()

            if not document:
                return False

            await session.delete(document)
            await session.commit()

            logger.info(f"Deleted document {document_id}")
            return True


class TimesheetRepository:
    """Repository for timesheet data operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def create(self, tenant_id: str, timesheet_data: dict) -> Timesheet:
        """Create a new timesheet."""
        async with self.db_manager.get_session() as session:
            # Calculate week end date (Sunday)
            week_start = timesheet_data["week_start_date"]
            week_end = week_start + datetime.timedelta(days=6)

            timesheet = Timesheet(
                id=generate_id(), tenant_id=tenant_id, week_end_date=week_end, **timesheet_data
            )

            session.add(timesheet)
            await session.commit()
            await session.refresh(timesheet)

            logger.info(f"Created timesheet {timesheet.id} for tenant {tenant_id}")
            return timesheet

    async def get_by_id(self, tenant_id: str, timesheet_id: str) -> Optional[Timesheet]:
        """Get timesheet by ID."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Timesheet).where(and_(Timesheet.tenant_id == tenant_id, Timesheet.id == timesheet_id))
            )
            return result.scalar_one_or_none()

    async def get_by_employee(
        self,
        tenant_id: str,
        employee_id: str,
        status: Optional[TimesheetStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[Timesheet]:
        """Get timesheets by employee."""
        async with self.db_manager.get_session() as session:
            query = select(Timesheet).where(
                and_(Timesheet.tenant_id == tenant_id, Timesheet.employee_id == employee_id)
            )

            if status:
                query = query.where(Timesheet.status == status)

            query = query.order_by(desc(Timesheet.week_start_date)).limit(limit).offset(offset)

            result = await session.execute(query)
            return result.scalars().all()

    async def update_status(
        self,
        tenant_id: str,
        timesheet_id: str,
        status: TimesheetStatus,
        approver_id: Optional[str] = None,
        rejection_reason: Optional[str] = None,
    ) -> Optional[Timesheet]:
        """Update timesheet status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(Timesheet).where(and_(Timesheet.tenant_id == tenant_id, Timesheet.id == timesheet_id))
            )
            timesheet = result.scalar_one_or_none()

            if not timesheet:
                return None

            timesheet.status = status
            if approver_id:
                timesheet.approver_id = approver_id
            if status == TimesheetStatus.APPROVED:
                timesheet.approved_date = datetime.utcnow()
            elif status == TimesheetStatus.REJECTED and rejection_reason:
                timesheet.rejection_reason = rejection_reason

            await session.commit()
            await session.refresh(timesheet)

            logger.info(f"Updated timesheet {timesheet_id} status to {status}")
            return timesheet

    async def count_by_employee_and_status(
        self, tenant_id: str, employee_id: str, status: TimesheetStatus
    ) -> int:
        """Count timesheets by employee and status."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(func.count(Timesheet.id)).where(
                    and_(
                        Timesheet.tenant_id == tenant_id,
                        Timesheet.employee_id == employee_id,
                        Timesheet.status == status,
                    )
                )
            )
            return result.scalar() or 0
