openapi: 3.0.3
info:
  title: Employee Self-Service (ESS) API
  description: |
    Microservice for employee self-service portal and workflow management.

    This service provides comprehensive self-service capabilities including:
    - Employee dashboard and profile management
    - Leave application and approval workflows
    - Document management and access
    - Timesheet submission and tracking
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.

    ## Features
    - **Dashboard**: Personalized employee dashboard with key metrics
    - **Profile Management**: Update personal information and emergency contacts
    - **Leave Management**: Submit, track, and manage leave applications
    - **Document Management**: Upload, organize, and access personal documents
    - **Timesheet Management**: Submit and track weekly timesheets
    - **Self-Service Workflows**: Streamlined approval processes

  version: 1.0.0
  contact:
    name: oneHRMS Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8103
    description: Development server
  - url: https://api.onehrms.com/ess
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check service health and dependencies
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"

  /api/v1/ess/dashboard:
    get:
      tags:
        - Dashboard
      summary: Get employee dashboard
      description: Get personalized dashboard with summary information
      responses:
        "200":
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DashboardResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/ess/profile:
    get:
      tags:
        - Profile
      summary: Get employee profile
      description: Get current employee profile information
      responses:
        "200":
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProfileResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

    put:
      tags:
        - Profile
      summary: Update employee profile
      description: Update employee profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProfileUpdate"
      responses:
        "200":
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProfileResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/ess/leave-applications:
    post:
      tags:
        - Leave Applications
      summary: Submit leave application
      description: Submit a new leave application for approval
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LeaveApplicationCreate"
      responses:
        "201":
          description: Leave application submitted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveApplicationResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

    get:
      tags:
        - Leave Applications
      summary: Get leave applications
      description: Get employee's leave applications with optional filtering
      parameters:
        - name: status
          in: query
          description: Filter by application status
          schema:
            $ref: "#/components/schemas/LeaveApplicationStatus"
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: Leave applications retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/LeaveApplicationResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/ess/leave-applications/{application_id}:
    get:
      tags:
        - Leave Applications
      summary: Get leave application
      description: Get specific leave application details
      parameters:
        - name: application_id
          in: path
          required: true
          description: Leave application ID
          schema:
            type: string
      responses:
        "200":
          description: Leave application retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveApplicationResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/ess/leave-applications/{application_id}/cancel:
    patch:
      tags:
        - Leave Applications
      summary: Cancel leave application
      description: Cancel a pending leave application
      parameters:
        - name: application_id
          in: path
          required: true
          description: Leave application ID
          schema:
            type: string
      responses:
        "200":
          description: Leave application cancelled successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveApplicationResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/ess/documents:
    post:
      tags:
        - Documents
      summary: Upload document
      description: Upload a new document
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Document file
                title:
                  type: string
                  description: Document title
                description:
                  type: string
                  description: Document description
                document_type:
                  $ref: "#/components/schemas/DocumentType"
                is_confidential:
                  type: boolean
                  default: false
              required:
                - file
                - title
                - document_type
      responses:
        "201":
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DocumentResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

    get:
      tags:
        - Documents
      summary: Get documents
      description: Get employee's documents
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: Documents retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/DocumentResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/ess/documents/{document_id}:
    get:
      tags:
        - Documents
      summary: Get document
      description: Get specific document details
      parameters:
        - name: document_id
          in: path
          required: true
          description: Document ID
          schema:
            type: string
      responses:
        "200":
          description: Document retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DocumentResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

    delete:
      tags:
        - Documents
      summary: Delete document
      description: Delete a document
      parameters:
        - name: document_id
          in: path
          required: true
          description: Document ID
          schema:
            type: string
      responses:
        "200":
          description: Document deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/v1/ess/timesheets:
    post:
      tags:
        - Timesheets
      summary: Create timesheet
      description: Create a new weekly timesheet
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TimesheetCreate"
      responses:
        "201":
          description: Timesheet created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TimesheetResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

    get:
      tags:
        - Timesheets
      summary: Get timesheets
      description: Get employee's timesheets
      parameters:
        - name: status
          in: query
          description: Filter by timesheet status
          schema:
            $ref: "#/components/schemas/TimesheetStatus"
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Page size
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: Timesheets retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TimesheetResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/v1/ess/timesheets/{timesheet_id}/submit:
    patch:
      tags:
        - Timesheets
      summary: Submit timesheet
      description: Submit timesheet for approval
      parameters:
        - name: timesheet_id
          in: path
          required: true
          description: Timesheet ID
          schema:
            type: string
      responses:
        "200":
          description: Timesheet submitted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TimesheetResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    HealthStatus:
      type: object
      properties:
        status:
          type: string
          example: "healthy"
        service:
          type: string
          example: "ess-service"
        dependencies:
          type: object
          additionalProperties:
            type: string

    LeaveApplicationStatus:
      type: string
      enum:
        - draft
        - submitted
        - approved
        - rejected
        - cancelled

    DocumentType:
      type: string
      enum:
        - personal
        - official
        - certificate
        - contract
        - policy
        - form
        - report
        - other

    TimesheetStatus:
      type: string
      enum:
        - draft
        - submitted
        - approved
        - rejected

    LeaveApplicationCreate:
      type: object
      required:
        - leave_type
        - from_date
        - to_date
      properties:
        leave_type:
          type: string
          description: Type of leave
          example: "Annual Leave"
        from_date:
          type: string
          format: date
          description: Leave start date
        to_date:
          type: string
          format: date
          description: Leave end date
        reason:
          type: string
          description: Reason for leave
          example: "Family vacation"
        is_half_day:
          type: boolean
          default: false
          description: Is this a half day leave
        half_day_date:
          type: string
          format: date
          description: Half day date if applicable

    LeaveApplicationResponse:
      type: object
      properties:
        id:
          type: string
          description: Application ID
        employee_id:
          type: string
          description: Employee ID
        leave_type:
          type: string
          description: Type of leave
        from_date:
          type: string
          format: date
          description: Leave start date
        to_date:
          type: string
          format: date
          description: Leave end date
        total_days:
          type: number
          format: decimal
          description: Total leave days
        reason:
          type: string
          description: Reason for leave
        status:
          $ref: "#/components/schemas/LeaveApplicationStatus"
        approver_id:
          type: string
          description: Approver ID
        approved_date:
          type: string
          format: date-time
          description: Approval date
        rejection_reason:
          type: string
          description: Rejection reason
        is_half_day:
          type: boolean
          description: Is half day leave
        half_day_date:
          type: string
          format: date
          description: Half day date
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    DocumentResponse:
      type: object
      properties:
        id:
          type: string
          description: Document ID
        employee_id:
          type: string
          description: Employee ID
        title:
          type: string
          description: Document title
        description:
          type: string
          description: Document description
        document_type:
          $ref: "#/components/schemas/DocumentType"
        file_name:
          type: string
          description: Original file name
        file_size:
          type: integer
          description: File size in bytes
        mime_type:
          type: string
          description: MIME type
        is_confidential:
          type: boolean
          description: Is document confidential
        expiry_date:
          type: string
          format: date
          description: Document expiry date
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    TimesheetCreate:
      type: object
      required:
        - week_start_date
      properties:
        week_start_date:
          type: string
          format: date
          description: Week start date (Monday)
        notes:
          type: string
          description: Additional notes

    TimesheetResponse:
      type: object
      properties:
        id:
          type: string
          description: Timesheet ID
        employee_id:
          type: string
          description: Employee ID
        week_start_date:
          type: string
          format: date
          description: Week start date
        week_end_date:
          type: string
          format: date
          description: Week end date
        total_hours:
          type: number
          format: decimal
          description: Total hours worked
        status:
          $ref: "#/components/schemas/TimesheetStatus"
        approver_id:
          type: string
          description: Approver ID
        approved_date:
          type: string
          format: date-time
          description: Approval date
        rejection_reason:
          type: string
          description: Rejection reason
        notes:
          type: string
          description: Additional notes
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ProfileUpdate:
      type: object
      properties:
        phone:
          type: string
          maxLength: 20
          description: Phone number
        emergency_contact_name:
          type: string
          maxLength: 100
          description: Emergency contact name
        emergency_contact_phone:
          type: string
          maxLength: 20
          description: Emergency contact phone
        address:
          type: string
          description: Current address

    ProfileResponse:
      type: object
      properties:
        employee_id:
          type: string
          description: Employee ID
        first_name:
          type: string
          description: First name
        last_name:
          type: string
          description: Last name
        email:
          type: string
          format: email
          description: Email address
        phone:
          type: string
          description: Phone number
        department:
          type: string
          description: Department name
        position:
          type: string
          description: Position title
        manager:
          type: string
          description: Manager name
        hire_date:
          type: string
          format: date
          description: Hire date
        emergency_contact_name:
          type: string
          description: Emergency contact name
        emergency_contact_phone:
          type: string
          description: Emergency contact phone
        address:
          type: string
          description: Current address

    DashboardResponse:
      type: object
      properties:
        employee_info:
          $ref: "#/components/schemas/ProfileResponse"
        pending_leave_applications:
          type: integer
          description: Number of pending leave applications
        approved_leave_applications:
          type: integer
          description: Number of approved leave applications
        remaining_leave_balance:
          type: object
          additionalProperties:
            type: number
            format: decimal
          description: Remaining leave balance by type
        recent_documents:
          type: array
          items:
            $ref: "#/components/schemas/DocumentResponse"
          description: Recent documents
        pending_timesheets:
          type: integer
          description: Number of pending timesheets
        upcoming_holidays:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              date:
                type: string
                format: date
          description: Upcoming holidays
        announcements:
          type: array
          items:
            type: object
            properties:
              title:
                type: string
              date:
                type: string
                format: date
              priority:
                type: string
                enum: [low, medium, high]
          description: Company announcements

    Error:
      type: object
      properties:
        detail:
          type: string
          description: Error message
        code:
          type: string
          description: Error code

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
