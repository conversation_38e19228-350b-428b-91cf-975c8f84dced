"""
Business logic layer for Employee Self-Service (ESS) microservice.

This module contains the core business logic for ESS operations including
leave management, document handling, timesheet processing, and profile management.
"""

import os
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional

import aiofiles
from fastapi import UploadFile

from hrms.microservices.shared.exceptions import BusinessLogicError, NotFoundError, ValidationError
from hrms.microservices.shared.logging import get_logger

from .models import (
    DashboardResponse,
    DocumentCreate,
    DocumentResponse,
    LeaveApplicationCreate,
    LeaveApplicationResponse,
    LeaveApplicationStatus,
    ProfileResponse,
    ProfileUpdate,
    TimesheetCreate,
    TimesheetResponse,
    TimesheetStatus,
)
from .repository import DocumentRepository, LeaveApplicationRepository, TimesheetRepository

logger = get_logger(__name__)


class ESSService:
    """Business logic service for Employee Self-Service operations."""

    def __init__(
        self,
        leave_repo: LeaveApplicationRepository,
        document_repo: DocumentRepository,
        timesheet_repo: TimesheetRepository,
    ):
        self.leave_repo = leave_repo
        self.document_repo = document_repo
        self.timesheet_repo = timesheet_repo

    # Dashboard operations
    async def get_employee_dashboard(self, tenant_id: str, employee_id: str) -> DashboardResponse:
        """Get employee dashboard with summary information."""
        logger.info(f"Getting dashboard for employee {employee_id} in tenant {tenant_id}")

        # Get employee profile (would integrate with employee service)
        employee_info = await self._get_employee_profile(tenant_id, employee_id)

        # Get leave application counts
        pending_leaves = await self.leave_repo.count_by_employee_and_status(
            tenant_id, employee_id, LeaveApplicationStatus.SUBMITTED
        )
        approved_leaves = await self.leave_repo.count_by_employee_and_status(
            tenant_id, employee_id, LeaveApplicationStatus.APPROVED
        )

        # Get recent documents
        recent_documents = await self.document_repo.get_by_employee(tenant_id, employee_id, limit=5)
        document_responses = [DocumentResponse.from_orm(doc) for doc in recent_documents]

        # Get pending timesheets
        pending_timesheets = await self.timesheet_repo.count_by_employee_and_status(
            tenant_id, employee_id, TimesheetStatus.DRAFT
        )

        # Mock data for remaining leave balance, holidays, and announcements
        remaining_leave_balance = {
            "Annual Leave": Decimal("15.0"),
            "Sick Leave": Decimal("5.0"),
            "Personal Leave": Decimal("3.0"),
        }

        upcoming_holidays = [
            {"name": "New Year's Day", "date": "2024-01-01"},
            {"name": "Independence Day", "date": "2024-07-04"},
        ]

        announcements = [
            {"title": "Company All-Hands Meeting", "date": "2024-01-15", "priority": "high"},
            {"title": "New Benefits Enrollment", "date": "2024-01-20", "priority": "medium"},
        ]

        return DashboardResponse(
            employee_info=employee_info,
            pending_leave_applications=pending_leaves,
            approved_leave_applications=approved_leaves,
            remaining_leave_balance=remaining_leave_balance,
            recent_documents=document_responses,
            pending_timesheets=pending_timesheets,
            upcoming_holidays=upcoming_holidays,
            announcements=announcements,
        )

    async def _get_employee_profile(self, tenant_id: str, employee_id: str) -> ProfileResponse:
        """Get employee profile information (mock implementation)."""
        # In a real implementation, this would call the employee service
        return ProfileResponse(
            employee_id=employee_id,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="******-0123",
            department="Engineering",
            position="Software Developer",
            manager="Jane Smith",
            hire_date=date(2023, 1, 15),
            emergency_contact_name="Jane Doe",
            emergency_contact_phone="******-0124",
            address="123 Main St, City, State 12345",
        )

    # Profile operations
    async def get_employee_profile(self, tenant_id: str, employee_id: str) -> ProfileResponse:
        """Get employee profile."""
        return await self._get_employee_profile(tenant_id, employee_id)

    async def update_employee_profile(
        self, tenant_id: str, employee_id: str, profile_data: ProfileUpdate
    ) -> ProfileResponse:
        """Update employee profile."""
        logger.info(f"Updating profile for employee {employee_id} in tenant {tenant_id}")

        # In a real implementation, this would call the employee service
        # For now, return the current profile
        return await self._get_employee_profile(tenant_id, employee_id)

    # Leave application operations
    async def create_leave_application(
        self, tenant_id: str, employee_id: str, leave_data: LeaveApplicationCreate
    ) -> LeaveApplicationResponse:
        """Create a new leave application."""
        logger.info(f"Creating leave application for employee {employee_id} in tenant {tenant_id}")

        # Validate business rules
        await self._validate_leave_application(tenant_id, employee_id, leave_data)

        # Create leave application
        leave_dict = leave_data.dict()
        leave_dict["employee_id"] = employee_id
        leave_dict["status"] = LeaveApplicationStatus.SUBMITTED

        leave_application = await self.leave_repo.create(tenant_id, leave_dict)

        logger.info(f"Leave application created successfully: {leave_application.id}")
        return LeaveApplicationResponse.from_orm(leave_application)

    async def get_leave_applications(
        self,
        tenant_id: str,
        employee_id: str,
        status: Optional[LeaveApplicationStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[LeaveApplicationResponse]:
        """Get leave applications for employee."""
        leave_applications = await self.leave_repo.get_by_employee(
            tenant_id, employee_id, status, limit, offset
        )
        return [LeaveApplicationResponse.from_orm(app) for app in leave_applications]

    async def get_leave_application(
        self, tenant_id: str, employee_id: str, application_id: str
    ) -> LeaveApplicationResponse:
        """Get specific leave application."""
        leave_application = await self.leave_repo.get_by_id(tenant_id, application_id)
        if not leave_application or leave_application.employee_id != employee_id:
            raise NotFoundError(f"Leave application not found: {application_id}")

        return LeaveApplicationResponse.from_orm(leave_application)

    async def cancel_leave_application(
        self, tenant_id: str, employee_id: str, application_id: str
    ) -> LeaveApplicationResponse:
        """Cancel a leave application."""
        leave_application = await self.leave_repo.get_by_id(tenant_id, application_id)
        if not leave_application or leave_application.employee_id != employee_id:
            raise NotFoundError(f"Leave application not found: {application_id}")

        if leave_application.status not in [LeaveApplicationStatus.DRAFT, LeaveApplicationStatus.SUBMITTED]:
            raise BusinessLogicError("Cannot cancel leave application in current status")

        updated_application = await self.leave_repo.update_status(
            tenant_id, application_id, LeaveApplicationStatus.CANCELLED
        )

        logger.info(f"Leave application cancelled: {application_id}")
        return LeaveApplicationResponse.from_orm(updated_application)

    async def _validate_leave_application(
        self, tenant_id: str, employee_id: str, leave_data: LeaveApplicationCreate
    ):
        """Validate leave application business rules."""
        # Check advance notice requirement
        days_in_advance = (leave_data.from_date - date.today()).days
        if days_in_advance < 1:  # Business rule: at least 1 day advance
            raise ValidationError("Leave must be applied at least 1 day in advance")

        # Check maximum days per request
        total_days = (leave_data.to_date - leave_data.from_date).days + 1
        if leave_data.is_half_day:
            total_days = 0.5

        if total_days > 30:  # Business rule: max 30 days per request
            raise ValidationError("Cannot apply for more than 30 days in a single request")

        # Additional validations would go here (leave balance, overlapping applications, etc.)

    # Document operations
    async def upload_document(
        self, tenant_id: str, employee_id: str, document_data: DocumentCreate, file: UploadFile
    ) -> DocumentResponse:
        """Upload a document for employee."""
        logger.info(f"Uploading document for employee {employee_id} in tenant {tenant_id}")

        # Validate file
        await self._validate_document_file(file)

        # Save file
        file_path = await self._save_document_file(tenant_id, employee_id, file)

        # Create document record
        document_dict = document_data.dict()
        document_dict.update(
            {
                "employee_id": employee_id,
                "file_name": file.filename,
                "file_path": file_path,
                "file_size": file.size,
                "mime_type": file.content_type,
            }
        )

        document = await self.document_repo.create(tenant_id, document_dict)

        logger.info(f"Document uploaded successfully: {document.id}")
        return DocumentResponse.from_orm(document)

    async def get_documents(
        self, tenant_id: str, employee_id: str, limit: int = 50, offset: int = 0
    ) -> List[DocumentResponse]:
        """Get documents for employee."""
        documents = await self.document_repo.get_by_employee(tenant_id, employee_id, limit, offset)
        return [DocumentResponse.from_orm(doc) for doc in documents]

    async def get_document(self, tenant_id: str, employee_id: str, document_id: str) -> DocumentResponse:
        """Get specific document."""
        document = await self.document_repo.get_by_id(tenant_id, document_id)
        if not document or document.employee_id != employee_id:
            raise NotFoundError(f"Document not found: {document_id}")

        return DocumentResponse.from_orm(document)

    async def delete_document(self, tenant_id: str, employee_id: str, document_id: str) -> bool:
        """Delete a document."""
        document = await self.document_repo.get_by_id(tenant_id, document_id)
        if not document or document.employee_id != employee_id:
            raise NotFoundError(f"Document not found: {document_id}")

        # Delete file from storage
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # Delete database record
        result = await self.document_repo.delete(tenant_id, document_id)

        logger.info(f"Document deleted: {document_id}")
        return result

    async def _validate_document_file(self, file: UploadFile):
        """Validate uploaded document file."""
        # Check file size (10MB limit)
        if file.size and file.size > 10 * 1024 * 1024:
            raise ValidationError("File size cannot exceed 10MB")

        # Check file type
        allowed_types = [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"]
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_types:
            raise ValidationError(f"File type {file_ext} not allowed")

    async def _save_document_file(self, tenant_id: str, employee_id: str, file: UploadFile) -> str:
        """Save uploaded file to storage."""
        # Create directory structure
        upload_dir = f"uploads/{tenant_id}/employees/{employee_id}/documents"
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_ext = os.path.splitext(file.filename)[1]
        unique_filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save file
        async with aiofiles.open(file_path, "wb") as f:
            content = await file.read()
            await f.write(content)

        return file_path

    # Timesheet operations
    async def create_timesheet(
        self, tenant_id: str, employee_id: str, timesheet_data: TimesheetCreate
    ) -> TimesheetResponse:
        """Create a new timesheet."""
        logger.info(f"Creating timesheet for employee {employee_id} in tenant {tenant_id}")

        # Check for existing timesheet for the same week
        existing_timesheets = await self.timesheet_repo.get_by_employee(
            tenant_id, employee_id, limit=1, offset=0
        )

        for timesheet in existing_timesheets:
            if timesheet.week_start_date == timesheet_data.week_start_date:
                raise BusinessLogicError("Timesheet already exists for this week")

        # Create timesheet
        timesheet_dict = timesheet_data.dict()
        timesheet_dict["employee_id"] = employee_id
        timesheet_dict["status"] = TimesheetStatus.DRAFT

        timesheet = await self.timesheet_repo.create(tenant_id, timesheet_dict)

        logger.info(f"Timesheet created successfully: {timesheet.id}")
        return TimesheetResponse.from_orm(timesheet)

    async def get_timesheets(
        self,
        tenant_id: str,
        employee_id: str,
        status: Optional[TimesheetStatus] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[TimesheetResponse]:
        """Get timesheets for employee."""
        timesheets = await self.timesheet_repo.get_by_employee(tenant_id, employee_id, status, limit, offset)
        return [TimesheetResponse.from_orm(ts) for ts in timesheets]

    async def submit_timesheet(
        self, tenant_id: str, employee_id: str, timesheet_id: str
    ) -> TimesheetResponse:
        """Submit timesheet for approval."""
        timesheet = await self.timesheet_repo.get_by_id(tenant_id, timesheet_id)
        if not timesheet or timesheet.employee_id != employee_id:
            raise NotFoundError(f"Timesheet not found: {timesheet_id}")

        if timesheet.status != TimesheetStatus.DRAFT:
            raise BusinessLogicError("Can only submit draft timesheets")

        updated_timesheet = await self.timesheet_repo.update_status(
            tenant_id, timesheet_id, TimesheetStatus.SUBMITTED
        )

        logger.info(f"Timesheet submitted: {timesheet_id}")
        return TimesheetResponse.from_orm(updated_timesheet)
