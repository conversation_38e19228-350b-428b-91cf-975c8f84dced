"""
FastAPI application for Employee Self-Service (ESS) microservice.

This module defines the REST API endpoints for employee self-service operations
including dashboard, profile management, leave applications, document management,
and timesheet submission.
"""

from typing import List, Optional

from fastapi import Depends, FastAPI, File, Form, HTTPException, Query, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from hrms.microservices.shared.auth import Tenant, User, get_current_tenant, get_current_user
from hrms.microservices.shared.database import DatabaseManager, get_db_manager
from hrms.microservices.shared.exceptions import HRMSException, create_http_exception
from hrms.microservices.shared.logging import get_logger, setup_logging
from hrms.microservices.shared.models import HealthStatus, PaginatedResponse
from hrms.microservices.shared.utils import paginate_query_params

from .models import (
    DashboardResponse,
    DocumentCreate,
    DocumentResponse,
    DocumentType,
    LeaveApplicationCreate,
    LeaveApplicationResponse,
    LeaveApplicationStatus,
    ProfileResponse,
    ProfileUpdate,
    TimesheetCreate,
    TimesheetResponse,
    TimesheetStatus,
)
from .repository import DocumentRepository, LeaveApplicationRepository, TimesheetRepository
from .service import ESSService

# Setup logging
setup_logging("ess-service")
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Employee Self-Service (ESS)",
    description="""
    Microservice for employee self-service portal and workflow management.

    This service provides comprehensive self-service capabilities including:
    - Employee dashboard and profile management
    - Leave application and approval workflows
    - Document management and access
    - Timesheet submission and tracking
    - Multi-tenant data isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Initialize database and repositories
db_manager = get_db_manager()
leave_repo = LeaveApplicationRepository(db_manager)
document_repo = DocumentRepository(db_manager)
timesheet_repo = TimesheetRepository(db_manager)

# Initialize service
ess_service = ESSService(leave_repo, document_repo, timesheet_repo)


# Exception handler
@app.exception_handler(HRMSException)
async def hrms_exception_handler(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    http_exc = create_http_exception(exc)
    return JSONResponse(status_code=http_exc.status_code, content=http_exc.detail)


# Health check endpoint
@app.get("/health", response_model=HealthStatus, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    db_health = await db_manager.health_check()
    return HealthStatus(service="ess-service", dependencies={"database": db_health})


# Dashboard endpoints
@app.get("/api/v1/ess/dashboard", response_model=DashboardResponse, tags=["Dashboard"])
async def get_dashboard(
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get employee dashboard with summary information."""
    try:
        return await ess_service.get_employee_dashboard(tenant.id, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Profile endpoints
@app.get("/api/v1/ess/profile", response_model=ProfileResponse, tags=["Profile"])
async def get_profile(
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get employee profile information."""
    try:
        return await ess_service.get_employee_profile(tenant.id, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put("/api/v1/ess/profile", response_model=ProfileResponse, tags=["Profile"])
async def update_profile(
    profile_data: ProfileUpdate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update employee profile information."""
    try:
        return await ess_service.update_employee_profile(tenant.id, user.id, profile_data)
    except HRMSException as e:
        raise create_http_exception(e)


# Leave application endpoints
@app.post(
    "/api/v1/ess/leave-applications", response_model=LeaveApplicationResponse, tags=["Leave Applications"]
)
async def create_leave_application(
    leave_data: LeaveApplicationCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Submit a new leave application."""
    try:
        return await ess_service.create_leave_application(tenant.id, user.id, leave_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/ess/leave-applications",
    response_model=List[LeaveApplicationResponse],
    tags=["Leave Applications"],
)
async def get_leave_applications(
    status: Optional[LeaveApplicationStatus] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get employee's leave applications."""
    try:
        pagination = paginate_query_params(page, size)
        return await ess_service.get_leave_applications(
            tenant.id, user.id, status, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/ess/leave-applications/{application_id}",
    response_model=LeaveApplicationResponse,
    tags=["Leave Applications"],
)
async def get_leave_application(
    application_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific leave application."""
    try:
        return await ess_service.get_leave_application(tenant.id, user.id, application_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/ess/leave-applications/{application_id}/cancel",
    response_model=LeaveApplicationResponse,
    tags=["Leave Applications"],
)
async def cancel_leave_application(
    application_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Cancel a leave application."""
    try:
        return await ess_service.cancel_leave_application(tenant.id, user.id, application_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Document endpoints
@app.post("/api/v1/ess/documents", response_model=DocumentResponse, tags=["Documents"])
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(...),
    description: Optional[str] = Form(None),
    document_type: DocumentType = Form(...),
    is_confidential: bool = Form(False),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Upload a document."""
    try:
        document_data = DocumentCreate(
            title=title,
            description=description,
            document_type=document_type,
            is_confidential=is_confidential,
        )
        return await ess_service.upload_document(tenant.id, user.id, document_data, file)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/ess/documents", response_model=List[DocumentResponse], tags=["Documents"])
async def get_documents(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get employee's documents."""
    try:
        pagination = paginate_query_params(page, size)
        return await ess_service.get_documents(tenant.id, user.id, pagination.limit, pagination.offset)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/ess/documents/{document_id}", response_model=DocumentResponse, tags=["Documents"])
async def get_document(
    document_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get specific document."""
    try:
        return await ess_service.get_document(tenant.id, user.id, document_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.delete("/api/v1/ess/documents/{document_id}", tags=["Documents"])
async def delete_document(
    document_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Delete a document."""
    try:
        result = await ess_service.delete_document(tenant.id, user.id, document_id)
        return {"success": result}
    except HRMSException as e:
        raise create_http_exception(e)


# Timesheet endpoints
@app.post("/api/v1/ess/timesheets", response_model=TimesheetResponse, tags=["Timesheets"])
async def create_timesheet(
    timesheet_data: TimesheetCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new timesheet."""
    try:
        return await ess_service.create_timesheet(tenant.id, user.id, timesheet_data)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/ess/timesheets", response_model=List[TimesheetResponse], tags=["Timesheets"])
async def get_timesheets(
    status: Optional[TimesheetStatus] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get employee's timesheets."""
    try:
        pagination = paginate_query_params(page, size)
        return await ess_service.get_timesheets(
            tenant.id, user.id, status, pagination.limit, pagination.offset
        )
    except HRMSException as e:
        raise create_http_exception(e)


@app.patch(
    "/api/v1/ess/timesheets/{timesheet_id}/submit", response_model=TimesheetResponse, tags=["Timesheets"]
)
async def submit_timesheet(
    timesheet_id: str,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Submit timesheet for approval."""
    try:
        return await ess_service.submit_timesheet(tenant.id, user.id, timesheet_id)
    except HRMSException as e:
        raise create_http_exception(e)
