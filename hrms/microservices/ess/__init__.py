"""
Employee Self-Service (ESS) Microservice

Comprehensive self-service portal for employees to manage their HR-related tasks.

Features:
- Employee self-service portal and dashboard
- Leave application and approval workflows
- Document management and access
- Personal information updates
- Timesheet and attendance self-management
- Performance review participation
- Benefits enrollment and management
- Multi-tenant data isolation
- Integration with Employee and Leave services

API Endpoints:
- GET /api/v1/ess/dashboard - Employee dashboard
- GET /api/v1/ess/profile - Employee profile
- PUT /api/v1/ess/profile - Update profile
- GET /api/v1/ess/leave-applications - List leave applications
- POST /api/v1/ess/leave-applications - Submit leave application
- GET /api/v1/ess/documents - List employee documents
- POST /api/v1/ess/documents - Upload document
- GET /api/v1/ess/timesheets - List timesheets
- POST /api/v1/ess/timesheets - Submit timesheet
"""

__version__ = "1.0.0"
__service_name__ = "ess-service"
__service_port__ = 8103

# Service metadata
SERVICE_INFO = {
    "name": "Employee Self-Service",
    "version": __version__,
    "description": "Employee self-service portal and workflow management",
    "port": __service_port__,
    "health_endpoint": "/health",
    "docs_endpoint": "/docs",
    "openapi_endpoint": "/openapi.json",
}

# Feature flags
FEATURES = {
    "leave_management": True,
    "document_management": True,
    "timesheet_management": True,
    "profile_management": True,
    "benefits_enrollment": True,
    "performance_reviews": True,
    "expense_claims": True,
    "goal_management": True,
}

# Business rules and constraints
BUSINESS_RULES = {
    "max_leave_days_per_request": 30,
    "max_document_size_mb": 10,
    "allowed_document_types": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"],
    "timesheet_submission_deadline_days": 7,
    "profile_update_approval_required": False,
    "leave_application_advance_days": 1,
}

# Integration settings
INTEGRATIONS = {
    "employee_service": {"enabled": True, "base_url": "http://employee-service:8100", "timeout_seconds": 30},
    "leave_service": {"enabled": True, "base_url": "http://leave-service:8104", "timeout_seconds": 30},
    "payroll_service": {"enabled": True, "base_url": "http://payroll-service:8101", "timeout_seconds": 30},
    "attendance_service": {
        "enabled": True,
        "base_url": "http://attendance-service:8102",
        "timeout_seconds": 30,
    },
    "notification_service": {
        "enabled": False,  # Future integration
        "base_url": "http://notification-service:8105",
        "timeout_seconds": 10,
    },
}

# Export main components
from .api import app
from .models import (
    DashboardResponse,
    DocumentCreate,
    DocumentResponse,
    DocumentType,
    LeaveApplicationCreate,
    LeaveApplicationResponse,
    LeaveApplicationStatus,
    ProfileResponse,
    ProfileUpdate,
    TimesheetCreate,
    TimesheetResponse,
    TimesheetStatus,
)
from .repository import DocumentRepository, LeaveApplicationRepository, TimesheetRepository
from .service import ESSService

__all__ = [
    # Main application
    "app",
    # Service layer
    "ESSService",
    # Repository layer
    "DocumentRepository",
    "LeaveApplicationRepository",
    "TimesheetRepository",
    # Models and schemas
    "DashboardResponse",
    "ProfileResponse",
    "ProfileUpdate",
    "LeaveApplicationCreate",
    "LeaveApplicationResponse",
    "DocumentCreate",
    "DocumentResponse",
    "TimesheetCreate",
    "TimesheetResponse",
    # Enums
    "LeaveApplicationStatus",
    "DocumentType",
    "TimesheetStatus",
    # Metadata
    "SERVICE_INFO",
    "FEATURES",
    "BUSINESS_RULES",
    "INTEGRATIONS",
]
