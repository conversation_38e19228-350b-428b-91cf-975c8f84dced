# Employee Self-Service (ESS) Microservice

A comprehensive self-service portal microservice for employees to manage their HR-related tasks and workflows.

## Overview

The Employee Self-Service (ESS) microservice provides a complete self-service platform for employees to:

- Access personalized dashboard with key metrics
- Manage personal profile and emergency contacts
- Submit and track leave applications
- Upload and manage personal documents
- Submit weekly timesheets
- View company announcements and holidays

## Features

### 🏠 Dashboard
- Personalized employee dashboard
- Leave application summary
- Recent documents overview
- Pending timesheet notifications
- Upcoming holidays and announcements

### 👤 Profile Management
- View and update personal information
- Manage emergency contacts
- Update contact details and address

### 🏖️ Leave Management
- Submit leave applications with validation
- Track application status and history
- Cancel pending applications
- View leave balance by type

### 📄 Document Management
- Upload personal documents (PDF, DOC, images)
- Organize documents by type
- Download and view documents
- Delete outdated documents

### ⏰ Timesheet Management
- Create weekly timesheets
- Submit for approval
- Track submission status
- View timesheet history

## API Endpoints

### Dashboard
- `GET /api/v1/ess/dashboard` - Get employee dashboard

### Profile
- `GET /api/v1/ess/profile` - Get employee profile
- `PUT /api/v1/ess/profile` - Update employee profile

### Leave Applications
- `GET /api/v1/ess/leave-applications` - List leave applications
- `POST /api/v1/ess/leave-applications` - Submit leave application
- `GET /api/v1/ess/leave-applications/{id}` - Get specific application
- `PATCH /api/v1/ess/leave-applications/{id}/cancel` - Cancel application

### Documents
- `GET /api/v1/ess/documents` - List documents
- `POST /api/v1/ess/documents` - Upload document
- `GET /api/v1/ess/documents/{id}` - Get document details
- `DELETE /api/v1/ess/documents/{id}` - Delete document

### Timesheets
- `GET /api/v1/ess/timesheets` - List timesheets
- `POST /api/v1/ess/timesheets` - Create timesheet
- `PATCH /api/v1/ess/timesheets/{id}/submit` - Submit timesheet

## Architecture

### Service Structure
```
hrms/microservices/ess/
├── __init__.py          # Service package initialization
├── api.py               # FastAPI application and endpoints
├── service.py           # Business logic layer
├── repository.py        # Data access layer
├── models.py            # Pydantic models and database schemas
├── openapi.yaml         # OpenAPI specification
├── Dockerfile           # Multi-stage Docker configuration
└── README.md            # This documentation
```

### Database Schema

#### Leave Applications
- Employee leave requests with approval workflow
- Support for full-day and half-day leaves
- Status tracking and approval history

#### Documents
- Personal document storage with metadata
- File type validation and size limits
- Confidentiality flags and expiry dates

#### Timesheets
- Weekly timesheet submissions
- Status tracking and approval workflow
- Notes and hour tracking

## Business Rules

### Leave Applications
- Minimum 1 day advance notice required
- Maximum 30 days per single request
- Half-day leave support
- Automatic total days calculation

### Document Management
- Maximum file size: 10MB
- Allowed types: PDF, DOC, DOCX, JPG, JPEG, PNG
- Automatic file organization by employee

### Timesheets
- Week must start on Monday
- One timesheet per week limit
- Draft → Submitted → Approved workflow

## Multi-Tenancy

All data is automatically isolated by tenant:
- Database queries include `tenant_id` filtering
- File uploads organized by tenant structure
- API responses scoped to tenant data

## Authentication & Authorization

- JWT-based authentication required
- Employee-level access control
- Tenant-aware data access
- Mocked for development environment

## Development

### Running the Service

```bash
# Development with hot reload
uvicorn hrms.microservices.ess.api:app --host 0.0.0.0 --port 8103 --reload

# Production
gunicorn hrms.microservices.ess.api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8103
```

### Docker

```bash
# Build development image
docker build --target development -t ess-service:dev .

# Build production image
docker build --target production -t ess-service:prod .

# Run container
docker run -p 8103:8103 -e DATABASE_URL=postgresql://... ess-service:dev
```

### Testing

```bash
# Run all tests
pytest tests/microservices/ess/ -v

# Run with coverage
pytest tests/microservices/ess/ --cov=hrms.microservices.ess --cov-report=html

# Run specific test file
pytest tests/microservices/ess/test_service.py -v
```

## Configuration

### Environment Variables

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET_KEY` - JWT signing key (mocked in dev)
- `UPLOAD_DIR` - Document upload directory
- `MAX_FILE_SIZE_MB` - Maximum file upload size
- `ALLOWED_FILE_TYPES` - Comma-separated allowed file extensions

### Feature Flags

```python
FEATURES = {
    "leave_management": True,
    "document_management": True,
    "timesheet_management": True,
    "profile_management": True,
    "benefits_enrollment": True,
    "performance_reviews": True,
    "expense_claims": True,
    "goal_management": True,
}
```

## Integration

### Kong API Gateway
- Routes configured in `/kong/kong.yml`
- CORS enabled for frontend integration
- JWT authentication (disabled in development)

### Employee Service
- Profile data integration
- Employee validation
- Manager hierarchy support

### Future Integrations
- Email notifications for approvals
- Calendar integration for leave
- Mobile push notifications

## Monitoring & Logging

- Structured logging with correlation IDs
- Health check endpoint at `/health`
- Metrics collection ready
- Error tracking and alerting

## Security

### Data Protection
- Tenant data isolation
- File access controls
- Sensitive data encryption
- Audit trail logging

### Input Validation
- Pydantic model validation
- File type and size checks
- Business rule enforcement
- SQL injection prevention

## Performance

### Optimizations
- Async database operations
- Connection pooling
- File streaming for uploads
- Pagination for large datasets

### Caching
- Ready for Redis integration
- Query result caching
- File metadata caching

## Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] File storage configured
- [ ] Kong routes deployed
- [ ] Health checks enabled
- [ ] Monitoring configured

### Scaling
- Horizontal scaling supported
- Stateless service design
- Shared file storage required
- Database connection pooling

## Support

For technical support or questions:
- Check the API documentation at `/docs`
- Review test cases for usage examples
- Consult the OpenAPI specification
- Contact the development team

## Version History

- **v1.0.0** - Initial release with core ESS functionality
- Complete leave management workflow
- Document upload and management
- Timesheet submission system
- Multi-tenant architecture
- Comprehensive test coverage
