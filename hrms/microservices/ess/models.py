"""
Pydantic models and database schemas for Employee Self-Service (ESS) microservice.

This module defines all data models, request/response schemas, and database models
for the ESS service including leave applications, documents, timesheets, and profiles.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator
from sqlalchemy import Boolean, Column, Date, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Foreign<PERSON>ey, Integer, Numeric, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from hrms.microservices.shared.models import Base, TenantMixin, TimestampMixin


# Enums
class LeaveApplicationStatus(str, Enum):
    """Leave application status enumeration."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class DocumentType(str, Enum):
    """Document type enumeration."""

    PERSONAL = "personal"
    OFFICIAL = "official"
    CERTIFICATE = "certificate"
    CONTRACT = "contract"
    POLICY = "policy"
    FORM = "form"
    REPORT = "report"
    OTHER = "other"


class TimesheetStatus(str, Enum):
    """Timesheet status enumeration."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"


# Database Models
class LeaveApplication(Base, TenantMixin, TimestampMixin):
    """Leave application database model."""

    __tablename__ = "ess_leave_applications"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    employee_id = Column(String(50), nullable=False, index=True)
    leave_type = Column(String(100), nullable=False)
    from_date = Column(Date, nullable=False)
    to_date = Column(Date, nullable=False)
    total_days = Column(Numeric(5, 2), nullable=False)
    reason = Column(Text)
    status = Column(SQLEnum(LeaveApplicationStatus), default=LeaveApplicationStatus.DRAFT, nullable=False)
    approver_id = Column(String(50))
    approved_date = Column(DateTime)
    rejection_reason = Column(Text)
    is_half_day = Column(Boolean, default=False)
    half_day_date = Column(Date)


class Document(Base, TenantMixin, TimestampMixin):
    """Employee document database model."""

    __tablename__ = "ess_documents"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    employee_id = Column(String(50), nullable=False, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    document_type = Column(SQLEnum(DocumentType), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    mime_type = Column(String(100))
    is_confidential = Column(Boolean, default=False)
    expiry_date = Column(Date)


class Timesheet(Base, TenantMixin, TimestampMixin):
    """Employee timesheet database model."""

    __tablename__ = "ess_timesheets"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True)
    employee_id = Column(String(50), nullable=False, index=True)
    week_start_date = Column(Date, nullable=False)
    week_end_date = Column(Date, nullable=False)
    total_hours = Column(Numeric(5, 2), default=0)
    status = Column(SQLEnum(TimesheetStatus), default=TimesheetStatus.DRAFT, nullable=False)
    approver_id = Column(String(50))
    approved_date = Column(DateTime)
    rejection_reason = Column(Text)
    notes = Column(Text)


# Pydantic Models for API
class LeaveApplicationCreate(BaseModel):
    """Schema for creating a leave application."""

    leave_type: str = Field(..., description="Type of leave")
    from_date: date = Field(..., description="Leave start date")
    to_date: date = Field(..., description="Leave end date")
    reason: Optional[str] = Field(None, description="Reason for leave")
    is_half_day: bool = Field(False, description="Is this a half day leave")
    half_day_date: Optional[date] = Field(None, description="Half day date if applicable")

    @validator("to_date")
    def validate_dates(cls, v, values):
        if "from_date" in values and v < values["from_date"]:
            raise ValueError("to_date must be after from_date")
        return v

    @validator("half_day_date")
    def validate_half_day_date(cls, v, values):
        if values.get("is_half_day") and not v:
            raise ValueError("half_day_date is required when is_half_day is True")
        if v and "from_date" in values and "to_date" in values:
            if not (values["from_date"] <= v <= values["to_date"]):
                raise ValueError("half_day_date must be within the leave period")
        return v


class LeaveApplicationResponse(BaseModel):
    """Schema for leave application response."""

    id: str
    employee_id: str
    leave_type: str
    from_date: date
    to_date: date
    total_days: Decimal
    reason: Optional[str]
    status: LeaveApplicationStatus
    approver_id: Optional[str]
    approved_date: Optional[datetime]
    rejection_reason: Optional[str]
    is_half_day: bool
    half_day_date: Optional[date]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DocumentCreate(BaseModel):
    """Schema for creating a document."""

    title: str = Field(..., max_length=200, description="Document title")
    description: Optional[str] = Field(None, description="Document description")
    document_type: DocumentType = Field(..., description="Type of document")
    is_confidential: bool = Field(False, description="Is document confidential")
    expiry_date: Optional[date] = Field(None, description="Document expiry date")


class DocumentResponse(BaseModel):
    """Schema for document response."""

    id: str
    employee_id: str
    title: str
    description: Optional[str]
    document_type: DocumentType
    file_name: str
    file_size: Optional[int]
    mime_type: Optional[str]
    is_confidential: bool
    expiry_date: Optional[date]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TimesheetCreate(BaseModel):
    """Schema for creating a timesheet."""

    week_start_date: date = Field(..., description="Week start date (Monday)")
    notes: Optional[str] = Field(None, description="Additional notes")

    @validator("week_start_date")
    def validate_week_start(cls, v):
        if v.weekday() != 0:  # Monday is 0
            raise ValueError("week_start_date must be a Monday")
        return v


class TimesheetResponse(BaseModel):
    """Schema for timesheet response."""

    id: str
    employee_id: str
    week_start_date: date
    week_end_date: date
    total_hours: Decimal
    status: TimesheetStatus
    approver_id: Optional[str]
    approved_date: Optional[datetime]
    rejection_reason: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProfileUpdate(BaseModel):
    """Schema for updating employee profile."""

    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    emergency_contact_name: Optional[str] = Field(None, max_length=100, description="Emergency contact name")
    emergency_contact_phone: Optional[str] = Field(None, max_length=20, description="Emergency contact phone")
    address: Optional[str] = Field(None, description="Current address")


class ProfileResponse(BaseModel):
    """Schema for employee profile response."""

    employee_id: str
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]
    department: Optional[str]
    position: Optional[str]
    manager: Optional[str]
    hire_date: Optional[date]
    emergency_contact_name: Optional[str]
    emergency_contact_phone: Optional[str]
    address: Optional[str]

    class Config:
        from_attributes = True


class DashboardResponse(BaseModel):
    """Schema for employee dashboard response."""

    employee_info: ProfileResponse
    pending_leave_applications: int
    approved_leave_applications: int
    remaining_leave_balance: Dict[str, Decimal]
    recent_documents: List[DocumentResponse]
    pending_timesheets: int
    upcoming_holidays: List[Dict[str, Any]]
    announcements: List[Dict[str, Any]]

    class Config:
        from_attributes = True
