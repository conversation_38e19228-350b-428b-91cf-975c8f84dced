# Microservice Integration Guide

## 🚀 **Enhanced Microservice Architecture**

This document outlines the comprehensive microservice integration improvements implemented in oneHRMS, including service discovery, health monitoring, circuit breakers, and resilient communication patterns.

---

## 🏗️ **Architecture Overview**

### **Service Communication Stack**
```
Frontend Application
       ↓
Kong API Gateway (Production) / Direct (Development)
       ↓
Service Discovery & Load Balancer
       ↓
Circuit Breaker & Retry Logic
       ↓
Individual Microservices
       ↓
Shared Database / Service-specific Databases
```

### **Core Components**

#### **1. Service Client (`shared/service_client.py`)**
- **HTTP Client** with retry logic and exponential backoff
- **Circuit Breaker** protection against cascading failures
- **Health Monitoring** with automatic endpoint validation
- **Request/Response Logging** for debugging and monitoring

#### **2. Service Discovery (`shared/service_discovery.py`)**
- **Dynamic Service Registration** from environment variables
- **Load Balancing** with multiple strategies (round-robin, weighted, least-connections)
- **Health-based Routing** to healthy endpoints only
- **Automatic Failover** when endpoints become unhealthy

#### **3. Health Check System (`shared/health_check.py`)**
- **Comprehensive Health Validation** including database, dependencies, and system resources
- **Dependency Tracking** for required and optional services
- **Performance Monitoring** with response time tracking
- **Detailed Status Reporting** for debugging and monitoring

#### **4. Service-Specific Clients (`shared/clients/`)**
- **Typed Interfaces** for each microservice
- **Business Logic Abstraction** hiding HTTP details
- **Error Handling** with service-specific error types
- **Caching and Optimization** for frequently accessed data

---

## 🔧 **Implementation Details**

### **Service Client Features**

#### **Circuit Breaker Protection**
```python
from hrms.microservices.shared.service_client import ServiceClient

client = ServiceClient(
    service_name="employee",
    base_url="http://employee-service:8100",
    circuit_breaker_enabled=True,
    failure_threshold=5,
    recovery_timeout=60
)

# Circuit breaker automatically protects against failures
try:
    response = await client.get("/api/v1/employees/123")
except ServiceUnavailableError:
    # Circuit breaker is open, service is down
    pass
```

#### **Retry Logic with Exponential Backoff**
```python
client = ServiceClient(
    service_name="payroll",
    base_url="http://payroll-service:8101",
    max_retries=3,
    retry_delay=1.0  # Starts at 1s, then 2s, 4s
)

# Automatic retry for transient failures
response = await client.post("/api/v1/payroll/calculate", data=payload)
```

### **Service Discovery Configuration**

#### **Environment-Based Registration**
```bash
# Single endpoint
EMPLOYEE_SERVICE_HOST=employee-service
EMPLOYEE_SERVICE_PORT=8100

# Multiple endpoints for load balancing
EMPLOYEE_SERVICE_HOSTS=employee-1:8100,employee-2:8100,employee-3:8100
EMPLOYEE_SERVICE_VERSION=1.0.0
```

#### **Load Balancing Strategies**
```python
from hrms.microservices.shared.service_discovery import ServiceDefinition, ServiceEndpoint

service = ServiceDefinition(
    name="employee",
    version="1.0.0",
    endpoints=[
        ServiceEndpoint(host="employee-1", port=8100, weight=100),
        ServiceEndpoint(host="employee-2", port=8100, weight=150),  # Higher weight
        ServiceEndpoint(host="employee-3", port=8100, weight=100),
    ],
    load_balancing_strategy="weighted"  # or "round_robin", "least_connections"
)
```

### **Health Check Integration**

#### **Service Health Monitoring**
```python
from hrms.microservices.shared.health_check import get_health_checker

# Configure health checker
checker = get_health_checker("attendance-service", "1.0.0")
checker.add_required_dependency("employee")
checker.add_optional_dependency("payroll")

# Perform comprehensive health check
health = await checker.perform_comprehensive_health_check()

# Health check includes:
# - Database connectivity
# - Service dependencies
# - System resources (CPU, memory, disk)
# - Environment configuration
```

#### **Health Check Endpoints**
```python
@app.get("/health")
async def health_check():
    """Basic health check for load balancers."""
    # Returns 200 OK if healthy, 503 if unhealthy

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check for monitoring."""
    # Returns comprehensive health information
```

---

## 🔌 **Service Integration Patterns**

### **Employee Service Client Usage**
```python
from hrms.microservices.shared.service_client import get_service_client
from hrms.microservices.shared.clients.employee_client import create_employee_client

# Get service client
client = get_service_client("employee")
employee_service = create_employee_client(client)

# Use typed interface
employee = await employee_service.get_employee("emp-123", tenant_id="tenant-1")
is_valid = await employee_service.validate_employee("emp-123", tenant_id="tenant-1")
manager = await employee_service.get_employee_manager("emp-123", tenant_id="tenant-1")
```

### **Dependency Injection in Services**
```python
# In attendance service
async def get_attendance_service(session=Depends(get_db_session)) -> AttendanceService:
    # Initialize repositories
    attendance_repo = AttendanceRepository(session)
    
    # Initialize service clients
    employee_client = get_service_client('employee')
    employee_service = create_employee_client(employee_client) if employee_client else None
    
    return AttendanceService(
        attendance_repo=attendance_repo,
        employee_service=employee_service,
    )
```

### **Error Handling Patterns**
```python
from hrms.microservices.shared.exceptions import ServiceUnavailableError

try:
    employee = await employee_service.get_employee(employee_id, tenant_id)
    if not employee:
        raise NotFoundError(f"Employee {employee_id} not found")
except ServiceUnavailableError:
    # Employee service is down, use fallback
    logger.warning("Employee service unavailable, using cached data")
    employee = get_cached_employee(employee_id)
```

---

## 📊 **Monitoring and Observability**

### **Service Metrics**
```python
# Health check with metrics
health = await client.health_check()
print(f"Response time: {health.response_time_ms}ms")

# Service registry metrics
metrics = service_registry.get_metrics("employee")
print(f"Request count: {metrics['requestCount']}")
print(f"Error rate: {metrics['errorCount'] / metrics['requestCount'] * 100}%")
```

### **Integration Testing**
```python
from hrms.microservices.shared.integration_test import run_integration_tests

# Run comprehensive integration tests
results = await run_integration_tests()
print(f"Success rate: {results['success_rate']}%")

# Tests include:
# - Service registry initialization
# - Service discovery functionality
# - Health checks
# - Inter-service communication
# - Circuit breaker behavior
# - Load balancing
# - Error handling
```

---

## 🚀 **Deployment Configuration**

### **Docker Compose Updates**
```yaml
services:
  employee-service:
    environment:
      - EMPLOYEE_SERVICE_HOST=employee-service
      - EMPLOYEE_SERVICE_PORT=8100
      - PAYROLL_SERVICE_HOST=payroll-service
      - PAYROLL_SERVICE_PORT=8101
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### **Kong Gateway Configuration**
```yaml
services:
  - name: employee-service
    url: http://employee-service:8100
    retries: 3
    connect_timeout: 60000
    
routes:
  - name: employee-api
    service: employee-service
    paths: ["/api/v1/employees"]
```

### **Environment Variables**
```bash
# Service URLs (development)
VITE_EMPLOYEE_SERVICE_URL=http://localhost:8100
VITE_PAYROLL_SERVICE_URL=http://localhost:8101
VITE_ATTENDANCE_SERVICE_URL=http://localhost:8102

# Service discovery (production)
EMPLOYEE_SERVICE_HOSTS=employee-1:8100,employee-2:8100
PAYROLL_SERVICE_HOSTS=payroll-1:8101,payroll-2:8101
```

---

## 🔧 **Configuration Management**

### **Service Registry Configuration**
```typescript
// Frontend service registry
const serviceConfig = {
  employee: {
    baseUrl: process.env.VITE_EMPLOYEE_SERVICE_URL || 'http://localhost:8100',
    timeout: 30000,
    retryAttempts: 3,
  },
  // ... other services
};
```

### **Backend Service Configuration**
```python
# Backend service client configuration
INTEGRATIONS = {
    "employee_service": {
        "enabled": True,
        "base_url": "http://employee-service:8100",
        "timeout_seconds": 30,
        "max_retries": 3,
        "circuit_breaker_enabled": True,
    },
}
```

---

## 🎯 **Benefits Achieved**

### **Reliability Improvements**
- **Circuit Breaker Protection** prevents cascading failures
- **Automatic Retry** handles transient network issues
- **Health-based Routing** avoids unhealthy service instances
- **Graceful Degradation** maintains functionality during partial outages

### **Performance Enhancements**
- **Load Balancing** distributes traffic across multiple instances
- **Connection Pooling** reduces connection overhead
- **Response Time Monitoring** identifies performance bottlenecks
- **Efficient Error Handling** reduces unnecessary retries

### **Operational Excellence**
- **Comprehensive Health Checks** provide detailed system status
- **Integration Testing** validates service communication
- **Centralized Logging** improves debugging and monitoring
- **Configuration Management** simplifies deployment and scaling

### **Developer Experience**
- **Typed Service Clients** provide IDE support and error checking
- **Dependency Injection** simplifies service composition
- **Error Handling Patterns** standardize error management
- **Documentation and Examples** accelerate development

---

## 🔮 **Next Steps**

### **Immediate Improvements**
1. **Add Metrics Collection** for request/response monitoring
2. **Implement Distributed Tracing** for request flow visibility
3. **Add Service Mesh** for advanced traffic management
4. **Enhance Security** with mutual TLS and service authentication

### **Future Enhancements**
1. **Auto-scaling** based on service metrics
2. **Blue-green Deployments** for zero-downtime updates
3. **Chaos Engineering** for resilience testing
4. **Advanced Load Balancing** with custom algorithms

---

*This microservice integration provides a robust foundation for scalable, reliable, and maintainable distributed systems in oneHRMS.*
