"""
Employee Management Microservice

Core service for employee data management, profiles, and organizational structure.

Features:
- Employee profile management
- Organizational hierarchy
- Department and position management
- Employee onboarding/offboarding
- Contact information management
- Document management
- Multi-tenant isolation

API Endpoints:
- GET /api/v1/employees - List employees
- POST /api/v1/employees - Create employee
- GET /api/v1/employees/{id} - Get employee details
- PUT /api/v1/employees/{id} - Update employee
- DELETE /api/v1/employees/{id} - Delete employee
- GET /api/v1/employees/{id}/documents - Get employee documents
- POST /api/v1/employees/{id}/documents - Upload document
"""

__version__ = "1.0.0"
__service_name__ = "employee-service"
__service_port__ = 8100
