# Multi-stage Dockerfile for Employee Management Service
FROM python:3.10-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast package management
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# Create app directory
WORKDIR /app

# Development stage
FROM base as development

# Copy dependency files
COPY pyproject.toml .
COPY requirements-dev.txt .

# Create virtual environment and install dependencies
RUN uv venv .venv
ENV PATH="/app/.venv/bin:$PATH"

# Install development dependencies
RUN uv pip install -r requirements-dev.txt
RUN uv pip install fastapi uvicorn sqlalchemy asyncpg psycopg2-binary alembic

# Copy source code
COPY hrms/ ./hrms/
COPY tests/ ./tests/

# Create necessary directories
RUN mkdir -p logs uploads

# Set up development user (non-root)
RUN useradd -m -u 1000 developer && \
    chown -R developer:developer /app
USER developer

# Expose port
EXPOSE 8100

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8100/health || exit 1

# Development command with hot reload
CMD ["uvicorn", "hrms.microservices.employee.api:app", "--host", "0.0.0.0", "--port", "8100", "--reload"]

# Production stage
FROM base as production

# Copy dependency files
COPY pyproject.toml .
COPY requirements.txt .

# Create virtual environment and install production dependencies
RUN uv venv .venv
ENV PATH="/app/.venv/bin:$PATH"

# Install production dependencies only
RUN uv pip install -r requirements.txt
RUN uv pip install fastapi uvicorn[standard] sqlalchemy asyncpg alembic gunicorn

# Copy source code (excluding tests and dev files)
COPY hrms/microservices/ ./hrms/microservices/

# Create necessary directories
RUN mkdir -p logs uploads

# Set up production user (non-root)
RUN useradd -m -u 1000 hrms && \
    chown -R hrms:hrms /app
USER hrms

# Expose port
EXPOSE 8100

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8100/health || exit 1

# Production command with gunicorn
CMD ["gunicorn", "hrms.microservices.employee.api:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8100", "--access-logfile", "-", "--error-logfile", "-"]

# Testing stage
FROM development as testing

# Install additional testing dependencies
RUN uv pip install pytest-cov pytest-xdist pytest-mock pytest-asyncio

# Copy test configuration
COPY pytest.ini .
COPY .coverage .

# Run tests
CMD ["python", "-m", "pytest", "tests/microservices/employee/", "-v", "--cov=hrms.microservices.employee", "--cov-report=html", "--cov-report=xml"]
