"""
Employee service FastAPI application.

Provides REST API endpoints for employee management with OpenAPI documentation.
"""

from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import Depends, FastAPI, File, Form, HTTPException, Query, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse

from ..shared.auth import Tenant, User, get_current_tenant, get_current_user
from ..shared.database import db_manager
from ..shared.exceptions import HRMSException, create_http_exception
from ..shared.logging import get_logger, get_request_logger, setup_logging
from ..shared.models import ErrorResponse, HealthStatus, PaginatedResponse, SuccessResponse
from ..shared.utils import paginate_query_params
from .models import (
    DepartmentCreate,
    DepartmentResponse,
    DepartmentUpdate,
    EmployeeCreate,
    EmployeeDocumentCreate,
    EmployeeDocumentResponse,
    EmployeeResponse,
    EmployeeUpdate,
    PositionCreate,
    PositionResponse,
    PositionUpdate,
)
from .service import EmployeeService

# Setup logging
setup_logging("employee-service")
logger = get_logger(__name__)
request_logger = get_request_logger("employee-service")

# Create FastAPI app
app = FastAPI(
    title="Employee Management Service",
    description="Microservice for employee data management, profiles, and organizational structure",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Initialize service
employee_service = EmployeeService(db_manager)


# Exception handler
@app.exception_handler(HRMSException)
async def hrms_exception_handler(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    http_exc = create_http_exception(exc)
    return JSONResponse(status_code=http_exc.status_code, content=http_exc.detail)


# Health check endpoint
@app.get("/health", response_model=HealthStatus, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    db_health = await db_manager.health_check()

    return HealthStatus(service="employee-service", dependencies={"database": db_health})


# Employee endpoints
@app.post("/api/v1/employees", response_model=EmployeeResponse, tags=["Employees"])
async def create_employee(
    employee_data: EmployeeCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new employee."""
    try:
        result = await employee_service.create_employee(tenant.id, employee_data, user.id)
        return result
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/employees", response_model=PaginatedResponse, tags=["Employees"])
async def list_employees(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    department_id: Optional[str] = Query(None, description="Filter by department"),
    position_id: Optional[str] = Query(None, description="Filter by position"),
    status: Optional[str] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search in name, email, or employee ID"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List employees with pagination and filtering."""
    try:
        pagination = paginate_query_params(page, size)

        filters = {}
        if department_id:
            filters["department_id"] = department_id
        if position_id:
            filters["position_id"] = position_id
        if status:
            filters["status"] = status
        if search:
            filters["search"] = search

        employees = await employee_service.list_employees(
            tenant.id, pagination["skip"], pagination["limit"], filters
        )

        # Get total count for pagination
        total = len(employees)  # Simplified for now

        return PaginatedResponse.create(items=employees, total=total, page=page, size=size)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/employees/{employee_id}", response_model=EmployeeResponse, tags=["Employees"])
async def get_employee(
    employee_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get employee by ID."""
    try:
        return await employee_service.get_employee(tenant.id, employee_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/employees/by-employee-id/{employee_id}", response_model=EmployeeResponse, tags=["Employees"]
)
async def get_employee_by_employee_id(
    employee_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get employee by employee ID."""
    try:
        return await employee_service.get_employee_by_employee_id(tenant.id, employee_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put("/api/v1/employees/{employee_id}", response_model=EmployeeResponse, tags=["Employees"])
async def update_employee(
    employee_id: str,
    employee_data: EmployeeUpdate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update employee."""
    try:
        return await employee_service.update_employee(tenant.id, employee_id, employee_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.delete("/api/v1/employees/{employee_id}", response_model=SuccessResponse, tags=["Employees"])
async def delete_employee(
    employee_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Delete employee."""
    try:
        result = await employee_service.delete_employee(tenant.id, employee_id)
        return SuccessResponse(message="Employee deleted successfully")
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/employees/statistics", response_model=Dict[str, Any], tags=["Employees"])
async def get_employee_statistics(
    user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get employee statistics."""
    try:
        return await employee_service.get_employee_statistics(tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Department endpoints
@app.post("/api/v1/departments", response_model=DepartmentResponse, tags=["Departments"])
async def create_department(
    department_data: DepartmentCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new department."""
    try:
        return await employee_service.create_department(tenant.id, department_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/departments", response_model=List[DepartmentResponse], tags=["Departments"])
async def list_departments(
    user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """List all departments."""
    try:
        return await employee_service.list_departments(tenant.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/departments/{department_id}", response_model=DepartmentResponse, tags=["Departments"])
async def get_department(
    department_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get department by ID."""
    try:
        return await employee_service.get_department(tenant.id, department_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Position endpoints
@app.post("/api/v1/positions", response_model=PositionResponse, tags=["Positions"])
async def create_position(
    position_data: PositionCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new position."""
    try:
        return await employee_service.create_position(tenant.id, position_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/positions", response_model=List[PositionResponse], tags=["Positions"])
async def list_positions(
    department_id: Optional[str] = Query(None, description="Filter by department"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List positions, optionally filtered by department."""
    try:
        return await employee_service.list_positions(tenant.id, department_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/positions/{position_id}", response_model=PositionResponse, tags=["Positions"])
async def get_position(
    position_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get position by ID."""
    try:
        return await employee_service.get_position(tenant.id, position_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Document endpoints
@app.post(
    "/api/v1/employees/{employee_id}/documents", response_model=EmployeeDocumentResponse, tags=["Documents"]
)
async def upload_employee_document(
    employee_id: str,
    document_type: str = Form(...),
    document_name: str = Form(...),
    file: UploadFile = File(...),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Upload employee document."""
    try:
        file_content = await file.read()

        document_data = EmployeeDocumentCreate(
            document_type=document_type,
            document_name=document_name,
            file_content=file_content,
            mime_type=file.content_type,
        )

        return await employee_service.upload_employee_document(tenant.id, employee_id, document_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/employees/{employee_id}/documents",
    response_model=List[EmployeeDocumentResponse],
    tags=["Documents"],
)
async def get_employee_documents(
    employee_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get all documents for an employee."""
    try:
        return await employee_service.get_employee_documents(tenant.id, employee_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Application startup
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    logger.info("Starting Employee Management Service")

    # Create database tables
    db_manager.create_tables()

    logger.info("Employee Management Service started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down Employee Management Service")


if __name__ == "__main__":
    uvicorn.run("api:app", host="0.0.0.0", port=8100, reload=True, log_level="info")
