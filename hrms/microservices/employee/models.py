"""
Employee service database models.

Defines all data models for employee management with tenant isolation.
"""

from datetime import date, datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, EmailStr, Field
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Date
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.orm import relationship

from ..shared.models import BaseModel as PydanticBaseModel
from ..shared.models import TenantAwareModel


class EmploymentStatus(str, Enum):
    """Employee status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    TERMINATED = "terminated"
    ON_LEAVE = "on_leave"
    PROBATION = "probation"


class Gender(str, Enum):
    """Gender enumeration."""

    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    PREFER_NOT_TO_SAY = "prefer_not_to_say"


class MaritalStatus(str, Enum):
    """Marital status enumeration."""

    SINGLE = "single"
    MARRIED = "married"
    DIVORCED = "divorced"
    WIDOWED = "widowed"
    SEPARATED = "separated"


# SQLAlchemy Models
class Department(TenantAwareModel):
    """Department model."""

    __tablename__ = "departments"

    name = Column(String(100), nullable=False)
    code = Column(String(20), nullable=False)
    description = Column(Text, nullable=True)
    parent_department_id = Column(String(36), ForeignKey("departments.id"), nullable=True)
    manager_id = Column(String(36), ForeignKey("employees.id"), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    parent_department = relationship("Department", remote_side="Department.id")
    employees = relationship("Employee", back_populates="department", foreign_keys="Employee.department_id")
    manager = relationship("Employee", foreign_keys="Department.manager_id")


class Position(TenantAwareModel):
    """Position/Job Title model."""

    __tablename__ = "positions"

    title = Column(String(100), nullable=False)
    code = Column(String(20), nullable=False)
    description = Column(Text, nullable=True)
    department_id = Column(String(36), ForeignKey("departments.id"), nullable=False)
    level = Column(Integer, default=1, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    department = relationship("Department")
    employees = relationship("Employee", back_populates="position")


class Employee(TenantAwareModel):
    """Employee model."""

    __tablename__ = "employees"

    # Basic Information
    employee_id = Column(String(20), nullable=False, unique=True, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    middle_name = Column(String(50), nullable=True)
    email = Column(String(100), nullable=False, unique=True, index=True)
    phone = Column(String(20), nullable=True)

    # Personal Information
    date_of_birth = Column(Date, nullable=True)
    gender = Column(SQLEnum(Gender), nullable=True)
    marital_status = Column(SQLEnum(MaritalStatus), nullable=True)
    nationality = Column(String(50), nullable=True)

    # Employment Information
    hire_date = Column(Date, nullable=False)
    termination_date = Column(Date, nullable=True)
    status = Column(SQLEnum(EmploymentStatus), default=EmploymentStatus.ACTIVE, nullable=False)
    department_id = Column(String(36), ForeignKey("departments.id"), nullable=False)
    position_id = Column(String(36), ForeignKey("positions.id"), nullable=False)
    manager_id = Column(String(36), ForeignKey("employees.id"), nullable=True)

    # Address Information
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)

    # Emergency Contact
    emergency_contact_name = Column(String(100), nullable=True)
    emergency_contact_phone = Column(String(20), nullable=True)
    emergency_contact_relationship = Column(String(50), nullable=True)

    # System Fields
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    department = relationship("Department", back_populates="employees", foreign_keys=[department_id])
    position = relationship("Position", back_populates="employees", foreign_keys=[position_id])
    manager = relationship("Employee", remote_side="Employee.id", foreign_keys=[manager_id])
    documents = relationship(
        "EmployeeDocument", back_populates="employee", foreign_keys="EmployeeDocument.employee_id"
    )


class EmployeeDocument(TenantAwareModel):
    """Employee document model."""

    __tablename__ = "employee_documents"

    employee_id = Column(String(36), ForeignKey("employees.id"), nullable=False)
    document_type = Column(String(50), nullable=False)
    document_name = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    uploaded_by = Column(String(36), nullable=False)

    # Relationships
    employee = relationship("Employee", back_populates="documents")


# Pydantic Schemas
class DepartmentBase(PydanticBaseModel):
    """Base department schema."""

    name: str = Field(..., min_length=1, max_length=100)
    code: str = Field(..., min_length=1, max_length=20)
    description: Optional[str] = None
    parent_department_id: Optional[str] = None
    manager_id: Optional[str] = None
    is_active: bool = True


class DepartmentCreate(DepartmentBase):
    """Department creation schema."""

    pass


class DepartmentUpdate(PydanticBaseModel):
    """Department update schema."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    parent_department_id: Optional[str] = None
    manager_id: Optional[str] = None
    is_active: Optional[bool] = None


class DepartmentResponse(DepartmentBase):
    """Department response schema."""

    id: str
    tenant_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class PositionBase(PydanticBaseModel):
    """Base position schema."""

    title: str = Field(..., min_length=1, max_length=100)
    code: str = Field(..., min_length=1, max_length=20)
    description: Optional[str] = None
    department_id: str
    level: int = Field(1, ge=1, le=10)
    is_active: bool = True


class PositionCreate(PositionBase):
    """Position creation schema."""

    pass


class PositionUpdate(PydanticBaseModel):
    """Position update schema."""

    title: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    department_id: Optional[str] = None
    level: Optional[int] = Field(None, ge=1, le=10)
    is_active: Optional[bool] = None


class PositionResponse(PositionBase):
    """Position response schema."""

    id: str
    tenant_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class EmployeeBase(PydanticBaseModel):
    """Base employee schema."""

    employee_id: str = Field(..., min_length=1, max_length=20)
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    middle_name: Optional[str] = Field(None, max_length=50)
    email: EmailStr
    phone: Optional[str] = Field(None, max_length=20)

    # Personal Information
    date_of_birth: Optional[date] = None
    gender: Optional[Gender] = None
    marital_status: Optional[MaritalStatus] = None
    nationality: Optional[str] = Field(None, max_length=50)

    # Employment Information
    hire_date: date
    termination_date: Optional[date] = None
    status: EmploymentStatus = EmploymentStatus.ACTIVE
    department_id: str
    position_id: str
    manager_id: Optional[str] = None

    # Address Information
    address_line1: Optional[str] = Field(None, max_length=200)
    address_line2: Optional[str] = Field(None, max_length=200)
    city: Optional[str] = Field(None, max_length=50)
    state: Optional[str] = Field(None, max_length=50)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=50)

    # Emergency Contact
    emergency_contact_name: Optional[str] = Field(None, max_length=100)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=50)

    is_active: bool = True


class EmployeeCreate(EmployeeBase):
    """Employee creation schema."""

    pass


class EmployeeUpdate(PydanticBaseModel):
    """Employee update schema."""

    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    middle_name: Optional[str] = Field(None, max_length=50)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, max_length=20)

    # Personal Information
    date_of_birth: Optional[date] = None
    gender: Optional[Gender] = None
    marital_status: Optional[MaritalStatus] = None
    nationality: Optional[str] = Field(None, max_length=50)

    # Employment Information
    termination_date: Optional[date] = None
    status: Optional[EmploymentStatus] = None
    department_id: Optional[str] = None
    position_id: Optional[str] = None
    manager_id: Optional[str] = None

    # Address Information
    address_line1: Optional[str] = Field(None, max_length=200)
    address_line2: Optional[str] = Field(None, max_length=200)
    city: Optional[str] = Field(None, max_length=50)
    state: Optional[str] = Field(None, max_length=50)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=50)

    # Emergency Contact
    emergency_contact_name: Optional[str] = Field(None, max_length=100)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=50)

    is_active: Optional[bool] = None


class EmployeeResponse(EmployeeBase):
    """Employee response schema."""

    id: str
    tenant_id: str
    created_at: datetime
    updated_at: datetime

    # Related objects
    department: Optional[DepartmentResponse] = None
    position: Optional[PositionResponse] = None

    class Config:
        orm_mode = True


class EmployeeDocumentBase(PydanticBaseModel):
    """Base employee document schema."""

    document_type: str = Field(..., min_length=1, max_length=50)
    document_name: str = Field(..., min_length=1, max_length=200)


class EmployeeDocumentCreate(EmployeeDocumentBase):
    """Employee document creation schema."""

    file_content: bytes
    mime_type: str


class EmployeeDocumentResponse(EmployeeDocumentBase):
    """Employee document response schema."""

    id: str
    employee_id: str
    tenant_id: str
    file_path: str
    file_size: int
    mime_type: str
    uploaded_by: str
    created_at: datetime

    class Config:
        orm_mode = True
