"""
Employee service repository layer.

Handles all database operations for employee management with tenant isolation.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..shared.database import DatabaseManager, TenantAwareRepository
from ..shared.exceptions import ConflictError, NotFoundError, ValidationError
from ..shared.logging import get_logger
from .models import Department, Employee, EmployeeDocument, Position

logger = get_logger(__name__)


class DepartmentRepository(TenantAwareRepository):
    """Repository for department operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, Department)

    async def get_by_code(self, tenant_id: str, code: str) -> Optional[Department]:
        """Get department by code."""
        async with self.db_manager.get_async_session() as session:
            query = select(Department).where(
                and_(
                    Department.tenant_id == tenant_id, Department.code == code, Department.is_deleted == False
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_hierarchy(self, tenant_id: str) -> List[Department]:
        """Get department hierarchy."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Department)
                .where(
                    and_(
                        Department.tenant_id == tenant_id,
                        Department.is_deleted == False,
                        Department.is_active == True,
                    )
                )
                .order_by(Department.name)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_children(self, tenant_id: str, parent_id: str) -> List[Department]:
        """Get child departments."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Department)
                .where(
                    and_(
                        Department.tenant_id == tenant_id,
                        Department.parent_department_id == parent_id,
                        Department.is_deleted == False,
                        Department.is_active == True,
                    )
                )
                .order_by(Department.name)
            )

            result = await session.execute(query)
            return result.scalars().all()


class PositionRepository(TenantAwareRepository):
    """Repository for position operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, Position)

    async def get_by_code(self, tenant_id: str, code: str) -> Optional[Position]:
        """Get position by code."""
        async with self.db_manager.get_async_session() as session:
            query = select(Position).where(
                and_(Position.tenant_id == tenant_id, Position.code == code, Position.is_deleted == False)
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_department(self, tenant_id: str, department_id: str) -> List[Position]:
        """Get positions by department."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Position)
                .where(
                    and_(
                        Position.tenant_id == tenant_id,
                        Position.department_id == department_id,
                        Position.is_deleted == False,
                        Position.is_active == True,
                    )
                )
                .order_by(Position.title)
            )

            result = await session.execute(query)
            return result.scalars().all()


class EmployeeRepository(TenantAwareRepository):
    """Repository for employee operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, Employee)

    async def get_by_employee_id(self, tenant_id: str, employee_id: str) -> Optional[Employee]:
        """Get employee by employee ID."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Employee)
                .options(
                    selectinload(Employee.department),
                    selectinload(Employee.position),
                    selectinload(Employee.manager),
                )
                .where(
                    and_(
                        Employee.tenant_id == tenant_id,
                        Employee.employee_id == employee_id,
                        Employee.is_deleted == False,
                    )
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_email(self, tenant_id: str, email: str) -> Optional[Employee]:
        """Get employee by email."""
        async with self.db_manager.get_async_session() as session:
            query = select(Employee).where(
                and_(Employee.tenant_id == tenant_id, Employee.email == email, Employee.is_deleted == False)
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def list_with_details(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[Employee]:
        """List employees with department and position details."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Employee)
                .options(
                    selectinload(Employee.department),
                    selectinload(Employee.position),
                    selectinload(Employee.manager),
                )
                .where(and_(Employee.tenant_id == tenant_id, Employee.is_deleted == False))
            )

            # Apply filters
            if filters:
                if "department_id" in filters:
                    query = query.where(Employee.department_id == filters["department_id"])

                if "position_id" in filters:
                    query = query.where(Employee.position_id == filters["position_id"])

                if "status" in filters:
                    query = query.where(Employee.status == filters["status"])

                if "is_active" in filters:
                    query = query.where(Employee.is_active == filters["is_active"])

                if "search" in filters:
                    search_term = f"%{filters['search']}%"
                    query = query.where(
                        or_(
                            Employee.first_name.ilike(search_term),
                            Employee.last_name.ilike(search_term),
                            Employee.email.ilike(search_term),
                            Employee.employee_id.ilike(search_term),
                        )
                    )

            query = query.order_by(Employee.first_name, Employee.last_name)
            query = query.offset(skip).limit(limit)

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_manager(self, tenant_id: str, manager_id: str) -> List[Employee]:
        """Get employees by manager."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Employee)
                .options(selectinload(Employee.department), selectinload(Employee.position))
                .where(
                    and_(
                        Employee.tenant_id == tenant_id,
                        Employee.manager_id == manager_id,
                        Employee.is_deleted == False,
                        Employee.is_active == True,
                    )
                )
                .order_by(Employee.first_name, Employee.last_name)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_department(self, tenant_id: str, department_id: str) -> List[Employee]:
        """Get employees by department."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(Employee)
                .options(selectinload(Employee.position), selectinload(Employee.manager))
                .where(
                    and_(
                        Employee.tenant_id == tenant_id,
                        Employee.department_id == department_id,
                        Employee.is_deleted == False,
                        Employee.is_active == True,
                    )
                )
                .order_by(Employee.first_name, Employee.last_name)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def check_employee_id_exists(
        self, tenant_id: str, employee_id: str, exclude_id: Optional[str] = None
    ) -> bool:
        """Check if employee ID already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(Employee.id)).where(
                and_(
                    Employee.tenant_id == tenant_id,
                    Employee.employee_id == employee_id,
                    Employee.is_deleted == False,
                )
            )

            if exclude_id:
                query = query.where(Employee.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0

    async def check_email_exists(self, tenant_id: str, email: str, exclude_id: Optional[str] = None) -> bool:
        """Check if email already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(Employee.id)).where(
                and_(Employee.tenant_id == tenant_id, Employee.email == email, Employee.is_deleted == False)
            )

            if exclude_id:
                query = query.where(Employee.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0

    async def get_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """Get employee statistics."""
        async with self.db_manager.get_async_session() as session:
            # Total employees
            total_query = select(func.count(Employee.id)).where(
                and_(Employee.tenant_id == tenant_id, Employee.is_deleted == False)
            )
            total_result = await session.execute(total_query)
            total_employees = total_result.scalar()

            # Active employees
            active_query = select(func.count(Employee.id)).where(
                and_(
                    Employee.tenant_id == tenant_id,
                    Employee.is_deleted == False,
                    Employee.is_active == True,
                    Employee.status == "active",
                )
            )
            active_result = await session.execute(active_query)
            active_employees = active_result.scalar()

            # By department
            dept_query = (
                select(Department.name, func.count(Employee.id).label("count"))
                .select_from(Employee.__table__.join(Department.__table__))
                .where(
                    and_(
                        Employee.tenant_id == tenant_id,
                        Employee.is_deleted == False,
                        Employee.is_active == True,
                    )
                )
                .group_by(Department.name)
            )

            dept_result = await session.execute(dept_query)
            by_department = {row.name: row.count for row in dept_result}

            return {
                "total_employees": total_employees,
                "active_employees": active_employees,
                "inactive_employees": total_employees - active_employees,
                "by_department": by_department,
            }


class EmployeeDocumentRepository(TenantAwareRepository):
    """Repository for employee document operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, EmployeeDocument)

    async def get_by_employee(self, tenant_id: str, employee_id: str) -> List[EmployeeDocument]:
        """Get documents by employee."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(EmployeeDocument)
                .where(
                    and_(
                        EmployeeDocument.tenant_id == tenant_id,
                        EmployeeDocument.employee_id == employee_id,
                        EmployeeDocument.is_deleted == False,
                    )
                )
                .order_by(EmployeeDocument.created_at.desc())
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_type(
        self, tenant_id: str, employee_id: str, document_type: str
    ) -> List[EmployeeDocument]:
        """Get documents by type."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(EmployeeDocument)
                .where(
                    and_(
                        EmployeeDocument.tenant_id == tenant_id,
                        EmployeeDocument.employee_id == employee_id,
                        EmployeeDocument.document_type == document_type,
                        EmployeeDocument.is_deleted == False,
                    )
                )
                .order_by(EmployeeDocument.created_at.desc())
            )

            result = await session.execute(query)
            return result.scalars().all()
