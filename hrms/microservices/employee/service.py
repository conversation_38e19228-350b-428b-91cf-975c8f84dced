"""
Employee service business logic layer.

Handles all business operations for employee management.
"""

import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from ..shared.database import DatabaseManager
from ..shared.exceptions import BusinessLogicError, ConflictError, NotFoundError, ValidationError
from ..shared.logging import get_logger
from ..shared.utils import generate_id, validate_email, validate_phone
from .models import (
    Department,
    DepartmentCreate,
    DepartmentResponse,
    DepartmentUpdate,
    Employee,
    EmployeeCreate,
    EmployeeDocument,
    EmployeeDocumentCreate,
    EmployeeDocumentResponse,
    EmployeeResponse,
    EmployeeUpdate,
    Position,
    PositionCreate,
    PositionResponse,
    PositionUpdate,
)
from .repository import (
    DepartmentRepository,
    EmployeeDocumentRepository,
    EmployeeRepository,
    PositionRepository,
)

logger = get_logger(__name__)


class EmployeeService:
    """Employee management service."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.employee_repo = EmployeeRepository(db_manager)
        self.department_repo = DepartmentRepository(db_manager)
        self.position_repo = PositionRepository(db_manager)
        self.document_repo = EmployeeDocumentRepository(db_manager)

    # Employee operations
    async def create_employee(
        self, tenant_id: str, employee_data: EmployeeCreate, created_by: str
    ) -> EmployeeResponse:
        """Create new employee."""
        logger.info(f"Creating employee for tenant {tenant_id}")

        # Validate business rules
        await self._validate_employee_creation(tenant_id, employee_data)

        # Check for duplicates
        if await self.employee_repo.check_employee_id_exists(tenant_id, employee_data.employee_id):
            raise ConflictError(f"Employee ID {employee_data.employee_id} already exists")

        if await self.employee_repo.check_email_exists(tenant_id, employee_data.email):
            raise ConflictError(f"Email {employee_data.email} already exists")

        # Create employee
        employee_dict = employee_data.dict()
        employee_dict["created_by"] = created_by

        employee = await self.employee_repo.create(tenant_id, employee_dict)

        logger.info(f"Employee created successfully: {employee.id}")
        return EmployeeResponse.from_orm(employee)

    async def get_employee(self, tenant_id: str, employee_id: str) -> EmployeeResponse:
        """Get employee by ID."""
        employee = await self.employee_repo.get_by_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        return EmployeeResponse.from_orm(employee)

    async def get_employee_by_employee_id(self, tenant_id: str, employee_id: str) -> EmployeeResponse:
        """Get employee by employee ID."""
        employee = await self.employee_repo.get_by_employee_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        return EmployeeResponse.from_orm(employee)

    async def update_employee(
        self, tenant_id: str, employee_id: str, employee_data: EmployeeUpdate, updated_by: str
    ) -> EmployeeResponse:
        """Update employee."""
        logger.info(f"Updating employee {employee_id} for tenant {tenant_id}")

        # Get existing employee
        employee = await self.employee_repo.get_by_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        # Validate business rules
        await self._validate_employee_update(tenant_id, employee_id, employee_data)

        # Check for duplicates (excluding current employee)
        if employee_data.email and await self.employee_repo.check_email_exists(
            tenant_id, employee_data.email, employee_id
        ):
            raise ConflictError(f"Email {employee_data.email} already exists")

        # Update employee
        update_dict = employee_data.dict(exclude_unset=True)
        update_dict["updated_by"] = updated_by

        updated_employee = await self.employee_repo.update(tenant_id, employee_id, update_dict)

        logger.info(f"Employee updated successfully: {employee_id}")
        return EmployeeResponse.from_orm(updated_employee)

    async def delete_employee(self, tenant_id: str, employee_id: str) -> bool:
        """Delete employee (soft delete)."""
        logger.info(f"Deleting employee {employee_id} for tenant {tenant_id}")

        employee = await self.employee_repo.get_by_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        # Check if employee has dependents (subordinates)
        subordinates = await self.employee_repo.get_by_manager(tenant_id, employee_id)
        if subordinates:
            raise BusinessLogicError(
                "Cannot delete employee with subordinates", details={"subordinate_count": len(subordinates)}
            )

        result = await self.employee_repo.delete(tenant_id, employee_id)

        logger.info(f"Employee deleted successfully: {employee_id}")
        return result

    async def list_employees(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[EmployeeResponse]:
        """List employees with filtering."""
        employees = await self.employee_repo.list_with_details(tenant_id, skip, limit, filters)
        return [EmployeeResponse.from_orm(emp) for emp in employees]

    async def get_employee_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """Get employee statistics."""
        return await self.employee_repo.get_statistics(tenant_id)

    # Department operations
    async def create_department(
        self, tenant_id: str, department_data: DepartmentCreate, created_by: str
    ) -> DepartmentResponse:
        """Create new department."""
        logger.info(f"Creating department for tenant {tenant_id}")

        # Check for duplicate code
        existing = await self.department_repo.get_by_code(tenant_id, department_data.code)
        if existing:
            raise ConflictError(f"Department code {department_data.code} already exists")

        # Validate parent department exists
        if department_data.parent_department_id:
            parent = await self.department_repo.get_by_id(tenant_id, department_data.parent_department_id)
            if not parent:
                raise ValidationError("Parent department not found")

        # Create department
        dept_dict = department_data.dict()
        dept_dict["created_by"] = created_by

        department = await self.department_repo.create(tenant_id, dept_dict)

        logger.info(f"Department created successfully: {department.id}")
        return DepartmentResponse.from_orm(department)

    async def get_department(self, tenant_id: str, department_id: str) -> DepartmentResponse:
        """Get department by ID."""
        department = await self.department_repo.get_by_id(tenant_id, department_id)
        if not department:
            raise NotFoundError(f"Department not found: {department_id}")

        return DepartmentResponse.from_orm(department)

    async def list_departments(self, tenant_id: str) -> List[DepartmentResponse]:
        """List all departments."""
        departments = await self.department_repo.get_hierarchy(tenant_id)
        return [DepartmentResponse.from_orm(dept) for dept in departments]

    # Position operations
    async def create_position(
        self, tenant_id: str, position_data: PositionCreate, created_by: str
    ) -> PositionResponse:
        """Create new position."""
        logger.info(f"Creating position for tenant {tenant_id}")

        # Check for duplicate code
        existing = await self.position_repo.get_by_code(tenant_id, position_data.code)
        if existing:
            raise ConflictError(f"Position code {position_data.code} already exists")

        # Validate department exists
        department = await self.department_repo.get_by_id(tenant_id, position_data.department_id)
        if not department:
            raise ValidationError("Department not found")

        # Create position
        pos_dict = position_data.dict()
        pos_dict["created_by"] = created_by

        position = await self.position_repo.create(tenant_id, pos_dict)

        logger.info(f"Position created successfully: {position.id}")
        return PositionResponse.from_orm(position)

    async def get_position(self, tenant_id: str, position_id: str) -> PositionResponse:
        """Get position by ID."""
        position = await self.position_repo.get_by_id(tenant_id, position_id)
        if not position:
            raise NotFoundError(f"Position not found: {position_id}")

        return PositionResponse.from_orm(position)

    async def list_positions(
        self, tenant_id: str, department_id: Optional[str] = None
    ) -> List[PositionResponse]:
        """List positions, optionally filtered by department."""
        if department_id:
            positions = await self.position_repo.get_by_department(tenant_id, department_id)
        else:
            positions = await self.position_repo.list_by_tenant(tenant_id)

        return [PositionResponse.from_orm(pos) for pos in positions]

    # Document operations
    async def upload_employee_document(
        self, tenant_id: str, employee_id: str, document_data: EmployeeDocumentCreate, uploaded_by: str
    ) -> EmployeeDocumentResponse:
        """Upload employee document."""
        logger.info(f"Uploading document for employee {employee_id}")

        # Validate employee exists
        employee = await self.employee_repo.get_by_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        # Save file and create document record
        file_path = await self._save_document_file(tenant_id, employee_id, document_data)

        doc_dict = {
            "employee_id": employee_id,
            "document_type": document_data.document_type,
            "document_name": document_data.document_name,
            "file_path": file_path,
            "file_size": len(document_data.file_content),
            "mime_type": document_data.mime_type,
            "uploaded_by": uploaded_by,
        }

        document = await self.document_repo.create(tenant_id, doc_dict)

        logger.info(f"Document uploaded successfully: {document.id}")
        return EmployeeDocumentResponse.from_orm(document)

    async def get_employee_documents(
        self, tenant_id: str, employee_id: str
    ) -> List[EmployeeDocumentResponse]:
        """Get all documents for an employee."""
        # Validate employee exists
        employee = await self.employee_repo.get_by_id(tenant_id, employee_id)
        if not employee:
            raise NotFoundError(f"Employee not found: {employee_id}")

        documents = await self.document_repo.get_by_employee(tenant_id, employee_id)
        return [EmployeeDocumentResponse.from_orm(doc) for doc in documents]

    # Private helper methods
    async def _validate_employee_creation(self, tenant_id: str, employee_data: EmployeeCreate):
        """Validate employee creation business rules."""
        # Validate email format
        if not validate_email(employee_data.email):
            raise ValidationError("Invalid email format")

        # Validate phone format
        if employee_data.phone and not validate_phone(employee_data.phone):
            raise ValidationError("Invalid phone format")

        # Validate department exists
        department = await self.department_repo.get_by_id(tenant_id, employee_data.department_id)
        if not department:
            raise ValidationError("Department not found")

        # Validate position exists
        position = await self.position_repo.get_by_id(tenant_id, employee_data.position_id)
        if not position:
            raise ValidationError("Position not found")

        # Validate manager exists (if specified)
        if employee_data.manager_id:
            manager = await self.employee_repo.get_by_id(tenant_id, employee_data.manager_id)
            if not manager:
                raise ValidationError("Manager not found")

        # Validate hire date is not in the future
        if employee_data.hire_date > datetime.now().date():
            raise ValidationError("Hire date cannot be in the future")

        # Validate termination date is after hire date
        if employee_data.termination_date and employee_data.termination_date <= employee_data.hire_date:
            raise ValidationError("Termination date must be after hire date")

    async def _validate_employee_update(
        self, tenant_id: str, employee_id: str, employee_data: EmployeeUpdate
    ):
        """Validate employee update business rules."""
        # Validate email format
        if employee_data.email and not validate_email(employee_data.email):
            raise ValidationError("Invalid email format")

        # Validate phone format
        if employee_data.phone and not validate_phone(employee_data.phone):
            raise ValidationError("Invalid phone format")

        # Validate department exists
        if employee_data.department_id:
            department = await self.department_repo.get_by_id(tenant_id, employee_data.department_id)
            if not department:
                raise ValidationError("Department not found")

        # Validate position exists
        if employee_data.position_id:
            position = await self.position_repo.get_by_id(tenant_id, employee_data.position_id)
            if not position:
                raise ValidationError("Position not found")

        # Validate manager exists and is not self
        if employee_data.manager_id:
            if employee_data.manager_id == employee_id:
                raise ValidationError("Employee cannot be their own manager")

            manager = await self.employee_repo.get_by_id(tenant_id, employee_data.manager_id)
            if not manager:
                raise ValidationError("Manager not found")

    async def _save_document_file(
        self, tenant_id: str, employee_id: str, document_data: EmployeeDocumentCreate
    ) -> str:
        """Save document file to storage."""
        # Create directory structure
        upload_dir = os.path.join("uploads", tenant_id, "employees", employee_id, "documents")
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        file_extension = (
            document_data.document_name.split(".")[-1] if "." in document_data.document_name else ""
        )
        filename = f"{generate_id()}.{file_extension}" if file_extension else generate_id()
        file_path = os.path.join(upload_dir, filename)

        # Save file
        with open(file_path, "wb") as f:
            f.write(document_data.file_content)

        return file_path
