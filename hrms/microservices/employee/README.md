# Employee Management Microservice

A comprehensive employee management service providing core HR functionality for employee data, organizational structure, and workforce management.

## Overview

The Employee Management microservice serves as the foundation of the oneHRMS platform, providing:

- Complete employee lifecycle management
- Organizational structure and hierarchy
- Department and position management
- Employee profile and contact information
- Multi-tenant data isolation
- Integration with all other HR modules

## Features

### 👥 Employee Management
- Create, update, and manage employee profiles
- Employee onboarding and offboarding workflows
- Personal information and contact details
- Emergency contact management
- Employee status tracking (active, inactive, terminated)

### 🏢 Organizational Structure
- Department management and hierarchy
- Position and job title management
- Manager-employee relationships
- Organizational chart generation
- Team and group assignments

### 📊 Employee Data
- Comprehensive employee profiles
- Employment history tracking
- Salary and compensation information
- Performance review integration
- Document and file management

## API Endpoints

### Employees
- `GET /api/v1/employees` - List employees with filtering and pagination
- `POST /api/v1/employees` - Create new employee
- `GET /api/v1/employees/{id}` - Get employee details
- `PUT /api/v1/employees/{id}` - Update employee information
- `DELETE /api/v1/employees/{id}` - Deactivate employee
- `GET /api/v1/employees/{id}/manager` - Get employee's manager
- `PUT /api/v1/employees/{id}/manager` - Update employee's manager

### Departments
- `GET /api/v1/departments` - List departments
- `POST /api/v1/departments` - Create department
- `GET /api/v1/departments/{id}` - Get department details
- `PUT /api/v1/departments/{id}` - Update department
- `DELETE /api/v1/departments/{id}` - Delete department
- `GET /api/v1/departments/{id}/employees` - Get department employees

### Positions
- `GET /api/v1/positions` - List positions
- `POST /api/v1/positions` - Create position
- `GET /api/v1/positions/{id}` - Get position details
- `PUT /api/v1/positions/{id}` - Update position
- `DELETE /api/v1/positions/{id}` - Delete position

## Architecture

### Service Structure
```
hrms/microservices/employee/
├── __init__.py          # Service package initialization
├── api.py               # FastAPI application and endpoints
├── service.py           # Business logic layer
├── repository.py        # Data access layer
├── models.py            # Pydantic models and database schemas
├── openapi.yaml         # OpenAPI specification
├── Dockerfile           # Multi-stage Docker configuration
└── README.md            # This documentation
```

### Database Schema

#### Employees
- Complete employee profiles with personal information
- Employment status and dates
- Manager relationships and hierarchy
- Multi-tenant isolation with tenant_id

#### Departments
- Department structure and hierarchy
- Department managers and descriptions
- Employee assignments and counts

#### Positions
- Job titles and position descriptions
- Salary ranges and requirements
- Department associations

## Business Rules

### Employee Management
- Unique employee IDs within tenant
- Manager hierarchy validation
- Employment status transitions
- Required fields validation

### Organizational Structure
- Department hierarchy constraints
- Position-department relationships
- Manager assignment rules
- Circular reference prevention

## Multi-Tenancy

All employee data is strictly isolated by tenant:
- Database queries automatically filter by `tenant_id`
- Employee IDs are unique within tenant scope
- Organizational structure is tenant-specific
- No cross-tenant data access

## Authentication & Authorization

- JWT-based authentication required for all endpoints
- Role-based access control (HR Admin, Manager, Employee)
- Tenant-aware data access controls
- Mocked authentication for development environment

## Development

### Running the Service

```bash
# Development with hot reload
uvicorn hrms.microservices.employee.api:app --host 0.0.0.0 --port 8100 --reload

# Production
gunicorn hrms.microservices.employee.api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8100
```

### Docker

```bash
# Build development image
docker build --target development -t employee-service:dev .

# Build production image
docker build --target production -t employee-service:prod .

# Run container
docker run -p 8100:8100 -e DATABASE_URL=postgresql://... employee-service:dev
```

### Testing

```bash
# Run all tests
pytest tests/microservices/employee/ -v

# Run with coverage
pytest tests/microservices/employee/ --cov=hrms.microservices.employee --cov-report=html

# Run specific test file
pytest tests/microservices/employee/test_service.py -v
```

## Configuration

### Environment Variables

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET_KEY` - JWT signing key (mocked in dev)
- `REDIS_URL` - Redis connection for caching
- `LOG_LEVEL` - Logging level (DEBUG, INFO, WARNING, ERROR)

### Feature Flags

```python
FEATURES = {
    "employee_management": True,
    "department_management": True,
    "position_management": True,
    "manager_hierarchy": True,
    "employee_search": True,
    "bulk_operations": True,
    "audit_logging": True,
}
```

## Integration

### Kong API Gateway
- Routes configured in `/kong/kong.yml`
- CORS enabled for frontend integration
- JWT authentication (disabled in development)

### Other Services
- **Payroll Service**: Employee salary and compensation data
- **Attendance Service**: Employee time tracking integration
- **ESS Service**: Employee self-service portal access
- **Recruitment Service**: New hire onboarding integration

## Data Models

### Employee Model
- Personal information (name, email, phone)
- Employment details (hire date, status, position)
- Manager relationships and hierarchy
- Department and team assignments
- Tenant isolation and audit fields

### Department Model
- Department information and hierarchy
- Manager assignments
- Employee counts and statistics
- Tenant-specific organization structure

### Position Model
- Job titles and descriptions
- Salary ranges and requirements
- Department associations
- Skill and qualification requirements

## Security

### Data Protection
- Tenant data isolation at database level
- Personal information encryption
- Access control by role and hierarchy
- Audit trail for all changes

### Compliance
- GDPR compliance for employee data
- Data retention policies
- Right to be forgotten support
- Consent management for data processing

## Performance

### Optimizations
- Async database operations with connection pooling
- Caching for frequently accessed data
- Pagination for large employee lists
- Efficient search and filtering

### Caching Strategy
- Employee profile caching
- Department hierarchy caching
- Manager relationship caching
- Search result caching

## Monitoring & Logging

- Structured logging with correlation IDs
- Health check endpoint at `/health`
- Performance metrics collection
- Error tracking and alerting
- Employee data access auditing

## Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Redis cache configured
- [ ] Kong routes deployed
- [ ] Health checks enabled
- [ ] Monitoring configured
- [ ] Backup procedures in place

### Scaling
- Horizontal scaling supported
- Stateless service design
- Database read replicas for queries
- Cache layer for performance

## Support

For technical support or questions:
- Check the API documentation at `/docs`
- Review test cases for usage examples
- Consult the OpenAPI specification
- Contact the development team

## Version History

- **v1.0.0** - Initial release with core employee management
- Complete CRUD operations for employees
- Department and position management
- Manager hierarchy support
- Multi-tenant architecture
- Comprehensive test coverage
