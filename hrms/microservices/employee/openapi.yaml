openapi: 3.0.3
info:
  title: Employee Management Service
  description: |
    Microservice for employee data management, profiles, and organizational structure.

    This service provides comprehensive employee management capabilities including:
    - Employee profile management
    - Organizational hierarchy (departments and positions)
    - Employee onboarding/offboarding
    - Document management
    - Multi-tenant isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.

    ## Rate Limiting
    API calls are rate-limited to prevent abuse. See response headers for current limits.
  version: 1.0.0
  contact:
    name: oneHRMS Development Team
    email: <EMAIL>
    url: https://onehrms.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8100
    description: Development server
  - url: https://api.onehrms.com
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check service health and dependencies
      operationId: healthCheck
      security: []
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"

  /api/v1/employees:
    get:
      tags:
        - Employees
      summary: List employees
      description: Retrieve a paginated list of employees with optional filtering
      operationId: listEmployees
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
        - name: department_id
          in: query
          description: Filter by department ID
          required: false
          schema:
            type: string
            format: uuid
        - name: position_id
          in: query
          description: Filter by position ID
          required: false
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          description: Filter by employment status
          required: false
          schema:
            $ref: "#/components/schemas/EmploymentStatus"
        - name: search
          in: query
          description: Search in name, email, or employee ID
          required: false
          schema:
            type: string
            maxLength: 100
      responses:
        "200":
          description: List of employees
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedEmployeeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Employees
      summary: Create employee
      description: Create a new employee record
      operationId: createEmployee
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeCreate"
      responses:
        "200":
          description: Employee created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/employees/{employee_id}:
    get:
      tags:
        - Employees
      summary: Get employee
      description: Retrieve employee details by ID
      operationId: getEmployee
      parameters:
        - name: employee_id
          in: path
          description: Employee ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Employee details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

    put:
      tags:
        - Employees
      summary: Update employee
      description: Update employee information
      operationId: updateEmployee
      parameters:
        - name: employee_id
          in: path
          description: Employee ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeUpdate"
      responses:
        "200":
          description: Employee updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

    delete:
      tags:
        - Employees
      summary: Delete employee
      description: Soft delete an employee record
      operationId: deleteEmployee
      parameters:
        - name: employee_id
          in: path
          description: Employee ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Employee deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/employees/statistics:
    get:
      tags:
        - Employees
      summary: Get employee statistics
      description: Retrieve employee statistics and metrics
      operationId: getEmployeeStatistics
      responses:
        "200":
          description: Employee statistics
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeStatistics"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/departments:
    get:
      tags:
        - Departments
      summary: List departments
      description: Retrieve all departments in hierarchical order
      operationId: listDepartments
      responses:
        "200":
          description: List of departments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/DepartmentResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Departments
      summary: Create department
      description: Create a new department
      operationId: createDepartment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DepartmentCreate"
      responses:
        "200":
          description: Department created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepartmentResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/positions:
    get:
      tags:
        - Positions
      summary: List positions
      description: Retrieve positions, optionally filtered by department
      operationId: listPositions
      parameters:
        - name: department_id
          in: query
          description: Filter by department ID
          required: false
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: List of positions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PositionResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Positions
      summary: Create position
      description: Create a new position
      operationId: createPosition
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PositionCreate"
      responses:
        "200":
          description: Position created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PositionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from Keycloak authentication

  schemas:
    EmploymentStatus:
      type: string
      enum:
        - active
        - inactive
        - terminated
        - on_leave
        - probation
      description: Employee employment status

    Gender:
      type: string
      enum:
        - male
        - female
        - other
        - prefer_not_to_say
      description: Employee gender

    MaritalStatus:
      type: string
      enum:
        - single
        - married
        - divorced
        - widowed
        - separated
      description: Employee marital status

    EmployeeCreate:
      type: object
      required:
        - employee_id
        - first_name
        - last_name
        - email
        - hire_date
        - department_id
        - position_id
      properties:
        employee_id:
          type: string
          maxLength: 20
          description: Unique employee identifier
          example: "EMP001"
        first_name:
          type: string
          maxLength: 50
          description: Employee first name
          example: "John"
        last_name:
          type: string
          maxLength: 50
          description: Employee last name
          example: "Doe"
        middle_name:
          type: string
          maxLength: 50
          nullable: true
          description: Employee middle name
        email:
          type: string
          format: email
          maxLength: 100
          description: Employee email address
          example: "<EMAIL>"
        phone:
          type: string
          maxLength: 20
          nullable: true
          description: Employee phone number
          example: "+1234567890"
        date_of_birth:
          type: string
          format: date
          nullable: true
          description: Employee date of birth
        gender:
          $ref: "#/components/schemas/Gender"
        marital_status:
          $ref: "#/components/schemas/MaritalStatus"
        nationality:
          type: string
          maxLength: 50
          nullable: true
          description: Employee nationality
        hire_date:
          type: string
          format: date
          description: Employee hire date
        termination_date:
          type: string
          format: date
          nullable: true
          description: Employee termination date
        status:
          $ref: "#/components/schemas/EmploymentStatus"
        department_id:
          type: string
          format: uuid
          description: Department ID
        position_id:
          type: string
          format: uuid
          description: Position ID
        manager_id:
          type: string
          format: uuid
          nullable: true
          description: Manager employee ID
        address_line1:
          type: string
          maxLength: 200
          nullable: true
          description: Address line 1
        address_line2:
          type: string
          maxLength: 200
          nullable: true
          description: Address line 2
        city:
          type: string
          maxLength: 50
          nullable: true
          description: City
        state:
          type: string
          maxLength: 50
          nullable: true
          description: State/Province
        postal_code:
          type: string
          maxLength: 20
          nullable: true
          description: Postal/ZIP code
        country:
          type: string
          maxLength: 50
          nullable: true
          description: Country
        emergency_contact_name:
          type: string
          maxLength: 100
          nullable: true
          description: Emergency contact name
        emergency_contact_phone:
          type: string
          maxLength: 20
          nullable: true
          description: Emergency contact phone
        emergency_contact_relationship:
          type: string
          maxLength: 50
          nullable: true
          description: Emergency contact relationship
        is_active:
          type: boolean
          default: true
          description: Whether employee is active

    EmployeeUpdate:
      type: object
      properties:
        first_name:
          type: string
          maxLength: 50
          description: Employee first name
        last_name:
          type: string
          maxLength: 50
          description: Employee last name
        middle_name:
          type: string
          maxLength: 50
          nullable: true
          description: Employee middle name
        email:
          type: string
          format: email
          maxLength: 100
          description: Employee email address
        phone:
          type: string
          maxLength: 20
          nullable: true
          description: Employee phone number
        date_of_birth:
          type: string
          format: date
          nullable: true
          description: Employee date of birth
        gender:
          $ref: "#/components/schemas/Gender"
        marital_status:
          $ref: "#/components/schemas/MaritalStatus"
        nationality:
          type: string
          maxLength: 50
          nullable: true
          description: Employee nationality
        termination_date:
          type: string
          format: date
          nullable: true
          description: Employee termination date
        status:
          $ref: "#/components/schemas/EmploymentStatus"
        department_id:
          type: string
          format: uuid
          description: Department ID
        position_id:
          type: string
          format: uuid
          description: Position ID
        manager_id:
          type: string
          format: uuid
          nullable: true
          description: Manager employee ID
        address_line1:
          type: string
          maxLength: 200
          nullable: true
          description: Address line 1
        address_line2:
          type: string
          maxLength: 200
          nullable: true
          description: Address line 2
        city:
          type: string
          maxLength: 50
          nullable: true
          description: City
        state:
          type: string
          maxLength: 50
          nullable: true
          description: State/Province
        postal_code:
          type: string
          maxLength: 20
          nullable: true
          description: Postal/ZIP code
        country:
          type: string
          maxLength: 50
          nullable: true
          description: Country
        emergency_contact_name:
          type: string
          maxLength: 100
          nullable: true
          description: Emergency contact name
        emergency_contact_phone:
          type: string
          maxLength: 20
          nullable: true
          description: Emergency contact phone
        emergency_contact_relationship:
          type: string
          maxLength: 50
          nullable: true
          description: Emergency contact relationship
        is_active:
          type: boolean
          description: Whether employee is active

    EmployeeResponse:
      allOf:
        - $ref: "#/components/schemas/EmployeeCreate"
        - type: object
          required:
            - id
            - tenant_id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Employee unique ID
            tenant_id:
              type: string
              description: Tenant ID
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp
            department:
              $ref: "#/components/schemas/DepartmentResponse"
            position:
              $ref: "#/components/schemas/PositionResponse"

    DepartmentCreate:
      type: object
      required:
        - name
        - code
      properties:
        name:
          type: string
          maxLength: 100
          description: Department name
          example: "Engineering"
        code:
          type: string
          maxLength: 20
          description: Department code
          example: "ENG"
        description:
          type: string
          nullable: true
          description: Department description
        parent_department_id:
          type: string
          format: uuid
          nullable: true
          description: Parent department ID
        manager_id:
          type: string
          format: uuid
          nullable: true
          description: Department manager employee ID
        is_active:
          type: boolean
          default: true
          description: Whether department is active

    DepartmentResponse:
      allOf:
        - $ref: "#/components/schemas/DepartmentCreate"
        - type: object
          required:
            - id
            - tenant_id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Department unique ID
            tenant_id:
              type: string
              description: Tenant ID
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp

    PositionCreate:
      type: object
      required:
        - title
        - code
        - department_id
      properties:
        title:
          type: string
          maxLength: 100
          description: Position title
          example: "Software Engineer"
        code:
          type: string
          maxLength: 20
          description: Position code
          example: "SE"
        description:
          type: string
          nullable: true
          description: Position description
        department_id:
          type: string
          format: uuid
          description: Department ID
        level:
          type: integer
          minimum: 1
          maximum: 10
          default: 1
          description: Position level
        is_active:
          type: boolean
          default: true
          description: Whether position is active

    PositionResponse:
      allOf:
        - $ref: "#/components/schemas/PositionCreate"
        - type: object
          required:
            - id
            - tenant_id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Position unique ID
            tenant_id:
              type: string
              description: Tenant ID
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp

    PaginatedEmployeeResponse:
      type: object
      required:
        - items
        - total
        - page
        - size
        - pages
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/EmployeeResponse"
          description: List of employees
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        size:
          type: integer
          description: Page size
        pages:
          type: integer
          description: Total number of pages

    EmployeeStatistics:
      type: object
      required:
        - total_employees
        - active_employees
        - inactive_employees
        - by_department
      properties:
        total_employees:
          type: integer
          description: Total number of employees
        active_employees:
          type: integer
          description: Number of active employees
        inactive_employees:
          type: integer
          description: Number of inactive employees
        by_department:
          type: object
          additionalProperties:
            type: integer
          description: Employee count by department

    HealthStatus:
      type: object
      required:
        - status
        - timestamp
        - version
        - service
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: Service health status
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
        version:
          type: string
          description: Service version
        service:
          type: string
          description: Service name
        dependencies:
          type: object
          description: Dependency health status

    SuccessResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          example: true
          description: Operation success status
        message:
          type: string
          example: "Operation completed successfully"
          description: Success message
        data:
          type: object
          nullable: true
          description: Additional response data

    ErrorResponse:
      type: object
      required:
        - success
        - error
        - timestamp
      properties:
        success:
          type: boolean
          example: false
          description: Operation success status
        error:
          type: string
          description: Error message
        details:
          type: object
          nullable: true
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

tags:
  - name: Health
    description: Health check endpoints
  - name: Employees
    description: Employee management operations
  - name: Departments
    description: Department management operations
  - name: Positions
    description: Position management operations
