"""
oneHRMS Microservices Package

This package contains all microservices for the oneHRMS platform.
Each microservice is designed to be independently deployable and scalable.

Microservices Architecture:
- Employee Management Service (Core)
- Payroll Service
- Attendance Service (Phase 4 - Active Development)
- Leave Management Service
- Recruitment Service

Each service follows the same structure:
- FastAPI application with OpenAPI documentation
- Multi-tenant data isolation with tenant_id
- OIDC/JWT authentication integration
- Comprehensive test coverage (90%+ target)
- Docker containerization with hot reload
- TDD development approach
"""

__version__ = "2.0.0"
__author__ = "oneHRMS Development Team"

# Service registry for discovery
SERVICES = {
    "employee": {
        "name": "Employee Management Service",
        "port": 8100,
        "path": "/api/v1/employees",
        "description": "Core employee management and profile services",
    },
    "payroll": {
        "name": "Payroll Service",
        "port": 8101,
        "path": "/api/v1/payroll",
        "description": "Salary calculation, tax processing, and payroll management",
    },
    "attendance": {
        "name": "Attendance Service",
        "port": 8102,
        "path": "/api/v1/attendance",
        "description": "Time tracking, shift management, and attendance reporting",
    },
    "leave": {
        "name": "Leave Management Service",
        "port": 8103,
        "path": "/api/v1/leave",
        "description": "Leave applications, approvals, and balance management",
    },
    "recruitment": {
        "name": "Recruitment Service",
        "port": 8104,
        "path": "/api/v1/recruitment",
        "description": "Job postings, applications, and candidate management",
    },
}

# Common configuration for all services
COMMON_CONFIG = {
    "api_version": "v1",
    "docs_url": "/docs",
    "redoc_url": "/redoc",
    "openapi_url": "/openapi.json",
    "cors_origins": ["http://localhost:3000", "http://localhost:8080"],
    "cors_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "cors_headers": ["*"],
    "auth_required": True,
    "tenant_isolation": True,
    "rate_limiting": True,
    "monitoring": True,
}
