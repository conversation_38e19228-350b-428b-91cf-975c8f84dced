"""
Microservice Integration Test Suite

Comprehensive tests for microservice integration including service discovery,
health checks, circuit breakers, and inter-service communication.
"""

import asyncio
import time
from typing import Dict, List

from .health_check import get_health_checker
from .logging import get_logger
from .service_client import get_service_client, service_registry
from .service_discovery import get_service_discovery, initialize_service_discovery

logger = get_logger(__name__)


class IntegrationTestResult:
    """Result of an integration test."""
    
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.success = False
        self.message = ""
        self.details = {}
        self.duration_ms = 0.0


class MicroserviceIntegrationTester:
    """Comprehensive microservice integration tester."""

    def __init__(self):
        self.results: List[IntegrationTestResult] = []

    async def run_all_tests(self) -> Dict:
        """Run all integration tests."""
        logger.info("Starting microservice integration tests")
        start_time = time.time()
        
        tests = [
            self.test_service_registry_initialization,
            self.test_service_discovery,
            self.test_health_checks,
            self.test_service_communication,
            self.test_circuit_breaker,
            self.test_load_balancing,
            self.test_error_handling,
        ]
        
        for test in tests:
            try:
                await test()
            except Exception as e:
                result = IntegrationTestResult(test.__name__)
                result.success = False
                result.message = f"Test failed with exception: {str(e)}"
                result.details = {"error": str(e)}
                self.results.append(result)
                logger.error(f"Integration test failed: {test.__name__}: {str(e)}")
        
        total_duration = (time.time() - start_time) * 1000
        
        # Generate summary
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        
        summary = {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "total_duration_ms": total_duration,
            "results": [
                {
                    "test": r.test_name,
                    "success": r.success,
                    "message": r.message,
                    "duration_ms": r.duration_ms,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        logger.info(
            f"Integration tests completed: {passed_tests}/{total_tests} passed "
            f"({summary['success_rate']:.1f}%) in {total_duration:.1f}ms"
        )
        
        return summary

    async def test_service_registry_initialization(self):
        """Test service registry initialization."""
        result = IntegrationTestResult("service_registry_initialization")
        start_time = time.time()
        
        try:
            # Check if services are registered
            expected_services = ["employee", "payroll", "attendance", "leave", "ess", "recruitment"]
            registered_services = list(service_registry.clients.keys())
            
            missing_services = [s for s in expected_services if s not in registered_services]
            
            if missing_services:
                result.success = False
                result.message = f"Missing services: {', '.join(missing_services)}"
                result.details = {
                    "expected": expected_services,
                    "registered": registered_services,
                    "missing": missing_services
                }
            else:
                result.success = True
                result.message = f"All {len(expected_services)} services registered successfully"
                result.details = {"registered_services": registered_services}
            
        except Exception as e:
            result.success = False
            result.message = f"Service registry test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_service_discovery(self):
        """Test service discovery functionality."""
        result = IntegrationTestResult("service_discovery")
        start_time = time.time()
        
        try:
            discovery = get_service_discovery()
            
            # Test service registration
            services_status = discovery.get_all_services_status()
            
            if not services_status:
                result.success = False
                result.message = "No services found in service discovery"
                result.details = {}
            else:
                healthy_services = sum(
                    1 for status in services_status.values() 
                    if status.get("healthy_endpoints", 0) > 0
                )
                
                result.success = True
                result.message = f"Service discovery working: {healthy_services}/{len(services_status)} services healthy"
                result.details = {
                    "total_services": len(services_status),
                    "healthy_services": healthy_services,
                    "services": list(services_status.keys())
                }
            
        except Exception as e:
            result.success = False
            result.message = f"Service discovery test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_health_checks(self):
        """Test health check functionality."""
        result = IntegrationTestResult("health_checks")
        start_time = time.time()
        
        try:
            # Test health checks for all services
            health_results = await service_registry.health_check_all()
            
            healthy_count = sum(1 for h in health_results.values() if h.status == "healthy")
            total_count = len(health_results)
            
            if total_count == 0:
                result.success = False
                result.message = "No services available for health checks"
                result.details = {}
            else:
                success_rate = (healthy_count / total_count) * 100
                result.success = success_rate >= 50  # At least 50% should be healthy
                result.message = f"Health checks: {healthy_count}/{total_count} services healthy ({success_rate:.1f}%)"
                result.details = {
                    "total_services": total_count,
                    "healthy_services": healthy_count,
                    "success_rate": success_rate,
                    "health_results": {k: v.dict() for k, v in health_results.items()}
                }
            
        except Exception as e:
            result.success = False
            result.message = f"Health check test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_service_communication(self):
        """Test inter-service communication."""
        result = IntegrationTestResult("service_communication")
        start_time = time.time()
        
        try:
            # Test communication with employee service
            employee_client = get_service_client("employee")
            
            if not employee_client:
                result.success = False
                result.message = "Employee service client not found"
                result.details = {}
                result.duration_ms = (time.time() - start_time) * 1000
                self.results.append(result)
                return
            
            # Try to make a simple request
            try:
                health = await employee_client.health_check()
                
                result.success = health.status == "healthy"
                result.message = f"Service communication test: Employee service {health.status}"
                result.details = {
                    "service": "employee",
                    "status": health.status,
                    "response_time_ms": health.response_time_ms
                }
                
            except Exception as comm_error:
                result.success = False
                result.message = f"Communication failed: {str(comm_error)}"
                result.details = {"communication_error": str(comm_error)}
            
        except Exception as e:
            result.success = False
            result.message = f"Service communication test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_circuit_breaker(self):
        """Test circuit breaker functionality."""
        result = IntegrationTestResult("circuit_breaker")
        start_time = time.time()
        
        try:
            # Test circuit breaker by making requests to a non-existent service
            from .service_client import ServiceClient
            
            test_client = ServiceClient(
                service_name="test-service",
                base_url="http://non-existent-service:9999",
                timeout=1,
                max_retries=1,
                circuit_breaker_enabled=True
            )
            
            # Make multiple failing requests to trigger circuit breaker
            failures = 0
            for i in range(6):  # Should trigger circuit breaker after 5 failures
                try:
                    await test_client.health_check()
                except Exception:
                    failures += 1
            
            await test_client.close()
            
            # Circuit breaker should have opened after failures
            circuit_breaker = test_client.circuit_breaker
            is_open = circuit_breaker.state.value == "open"
            
            result.success = is_open and failures >= 5
            result.message = f"Circuit breaker test: {failures} failures, state: {circuit_breaker.state.value}"
            result.details = {
                "failures": failures,
                "circuit_breaker_state": circuit_breaker.state.value,
                "failure_count": circuit_breaker.failure_count
            }
            
        except Exception as e:
            result.success = False
            result.message = f"Circuit breaker test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_load_balancing(self):
        """Test load balancing functionality."""
        result = IntegrationTestResult("load_balancing")
        start_time = time.time()
        
        try:
            discovery = get_service_discovery()
            
            # Test round-robin load balancing
            service_name = "employee"
            endpoints = []
            
            for i in range(3):
                endpoint = discovery.get_service_endpoint(service_name)
                if endpoint:
                    endpoints.append(f"{endpoint.host}:{endpoint.port}")
            
            # Check if load balancing is working (should cycle through endpoints)
            unique_endpoints = set(endpoints)
            
            result.success = len(endpoints) > 0
            result.message = f"Load balancing test: {len(unique_endpoints)} unique endpoints from {len(endpoints)} requests"
            result.details = {
                "service": service_name,
                "endpoints_requested": endpoints,
                "unique_endpoints": list(unique_endpoints),
                "load_balancing_working": len(unique_endpoints) > 0
            }
            
        except Exception as e:
            result.success = False
            result.message = f"Load balancing test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)

    async def test_error_handling(self):
        """Test error handling and resilience."""
        result = IntegrationTestResult("error_handling")
        start_time = time.time()
        
        try:
            # Test error handling with invalid requests
            employee_client = get_service_client("employee")
            
            if not employee_client:
                result.success = False
                result.message = "Employee service client not found for error handling test"
                result.details = {}
                result.duration_ms = (time.time() - start_time) * 1000
                self.results.append(result)
                return
            
            # Test 404 error handling
            error_handled = False
            try:
                await employee_client.get("/non-existent-endpoint")
            except Exception as e:
                error_handled = True
                error_type = type(e).__name__
            
            result.success = error_handled
            result.message = f"Error handling test: {'Passed' if error_handled else 'Failed'}"
            result.details = {
                "error_handled": error_handled,
                "error_type": error_type if error_handled else None
            }
            
        except Exception as e:
            result.success = False
            result.message = f"Error handling test failed: {str(e)}"
            result.details = {"error": str(e)}
        
        result.duration_ms = (time.time() - start_time) * 1000
        self.results.append(result)


async def run_integration_tests() -> Dict:
    """Run comprehensive microservice integration tests."""
    tester = MicroserviceIntegrationTester()
    return await tester.run_all_tests()


if __name__ == "__main__":
    async def main():
        # Initialize services
        await initialize_service_discovery()
        
        # Run tests
        results = await run_integration_tests()
        
        # Print results
        print(f"\n{'='*60}")
        print("MICROSERVICE INTEGRATION TEST RESULTS")
        print(f"{'='*60}")
        print(f"Total Tests: {results['total_tests']}")
        print(f"Passed: {results['passed']}")
        print(f"Failed: {results['failed']}")
        print(f"Success Rate: {results['success_rate']:.1f}%")
        print(f"Duration: {results['total_duration_ms']:.1f}ms")
        print(f"{'='*60}")
        
        for test_result in results['results']:
            status = "✅ PASS" if test_result['success'] else "❌ FAIL"
            print(f"{status} {test_result['test']}: {test_result['message']}")
        
        print(f"{'='*60}")
    
    asyncio.run(main())
