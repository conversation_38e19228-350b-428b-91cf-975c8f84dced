"""
Notification service for HRMS microservices.

This module provides notification functionality for workflow events,
approval processes, and system alerts.
"""

import asyncio
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from .logging import get_logger

logger = get_logger(__name__)


class NotificationType(str, Enum):
    """Types of notifications."""

    REQUISITION_SUBMITTED = "requisition_submitted"
    REQUISITION_APPROVED = "requisition_approved"
    REQUISITION_REJECTED = "requisition_rejected"
    INTERVIEW_SCHEDULED = "interview_scheduled"
    OFFER_SENT = "offer_sent"
    OFFER_ACCEPTED = "offer_accepted"
    OFFER_REJECTED = "offer_rejected"
    SYSTEM_ALERT = "system_alert"


class NotificationPriority(str, Enum):
    """Notification priority levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationChannel(str, Enum):
    """Notification delivery channels."""

    EMAIL = "email"
    SLACK = "slack"
    SMS = "sms"
    IN_APP = "in_app"


class NotificationRecipient:
    """Notification recipient information."""

    def __init__(self, user_id: str, email: str, name: str, channels: List[NotificationChannel] = None):
        self.user_id = user_id
        self.email = email
        self.name = name
        self.channels = channels or [NotificationChannel.IN_APP]


class Notification:
    """Notification message."""

    def __init__(
        self,
        notification_id: str,
        notification_type: NotificationType,
        title: str,
        message: str,
        recipients: List[NotificationRecipient],
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        metadata: Dict[str, Any] = None,
        tenant_id: str = None,
    ):
        self.notification_id = notification_id
        self.notification_type = notification_type
        self.title = title
        self.message = message
        self.recipients = recipients
        self.priority = priority
        self.metadata = metadata or {}
        self.tenant_id = tenant_id
        self.created_at = datetime.utcnow()
        self.sent_at = None
        self.delivered_channels = []


class NotificationService:
    """Service for sending notifications."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self._notification_queue = asyncio.Queue()
        self._is_running = False

    async def start(self):
        """Start the notification service."""
        if not self._is_running:
            self._is_running = True
            asyncio.create_task(self._process_notifications())
            self.logger.info("Notification service started")

    async def stop(self):
        """Stop the notification service."""
        self._is_running = False
        self.logger.info("Notification service stopped")

    async def send_notification(self, notification: Notification) -> bool:
        """
        Send a notification.

        Args:
            notification: The notification to send

        Returns:
            bool: True if notification was queued successfully
        """
        try:
            await self._notification_queue.put(notification)
            self.logger.info(f"Notification queued: {notification.notification_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to queue notification: {e}")
            return False

    async def _process_notifications(self):
        """Process notifications from the queue."""
        while self._is_running:
            try:
                notification = await asyncio.wait_for(self._notification_queue.get(), timeout=1.0)
                await self._send_notification(notification)
                self._notification_queue.task_done()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing notification: {e}")

    async def _send_notification(self, notification: Notification):
        """
        Send a notification through all configured channels.

        Args:
            notification: The notification to send
        """
        try:
            for recipient in notification.recipients:
                for channel in recipient.channels:
                    success = await self._send_to_channel(notification, recipient, channel)
                    if success:
                        notification.delivered_channels.append(channel)

            notification.sent_at = datetime.utcnow()
            self.logger.info(
                f"Notification {notification.notification_id} sent to "
                f"{len(notification.recipients)} recipients via "
                f"{notification.delivered_channels}"
            )

        except Exception as e:
            self.logger.error(f"Failed to send notification {notification.notification_id}: {e}")

    async def _send_to_channel(
        self, notification: Notification, recipient: NotificationRecipient, channel: NotificationChannel
    ) -> bool:
        """
        Send notification to a specific channel.

        Args:
            notification: The notification to send
            recipient: The recipient
            channel: The channel to use

        Returns:
            bool: True if sent successfully
        """
        try:
            if channel == NotificationChannel.EMAIL:
                return await self._send_email(notification, recipient)
            elif channel == NotificationChannel.SLACK:
                return await self._send_slack(notification, recipient)
            elif channel == NotificationChannel.SMS:
                return await self._send_sms(notification, recipient)
            elif channel == NotificationChannel.IN_APP:
                return await self._send_in_app(notification, recipient)
            else:
                self.logger.warning(f"Unknown notification channel: {channel}")
                return False
        except Exception as e:
            self.logger.error(f"Failed to send via {channel}: {e}")
            return False

    async def _send_email(self, notification: Notification, recipient: NotificationRecipient) -> bool:
        """Send notification via email."""
        # TODO: Implement email sending logic
        self.logger.info(f"Email notification to {recipient.email}: {notification.title}")
        return True

    async def _send_slack(self, notification: Notification, recipient: NotificationRecipient) -> bool:
        """Send notification via Slack."""
        # TODO: Implement Slack sending logic
        self.logger.info(f"Slack notification to {recipient.name}: {notification.title}")
        return True

    async def _send_sms(self, notification: Notification, recipient: NotificationRecipient) -> bool:
        """Send notification via SMS."""
        # TODO: Implement SMS sending logic
        self.logger.info(f"SMS notification to {recipient.name}: {notification.title}")
        return True

    async def _send_in_app(self, notification: Notification, recipient: NotificationRecipient) -> bool:
        """Send in-app notification."""
        # TODO: Implement in-app notification logic
        self.logger.info(f"In-app notification to {recipient.user_id}: {notification.title}")
        return True


# Global notification service instance
notification_service = NotificationService()


# Convenience functions for common notifications
async def notify_requisition_submitted(
    requisition_id: str, submitted_by: str, approvers: List[NotificationRecipient], tenant_id: str
):
    """Notify approvers when a requisition is submitted."""
    notification = Notification(
        notification_id=f"req_submitted_{requisition_id}",
        notification_type=NotificationType.REQUISITION_SUBMITTED,
        title="New Job Requisition Submitted",
        message=f"A new job requisition has been submitted and requires your approval.",
        recipients=approvers,
        priority=NotificationPriority.HIGH,
        metadata={"requisition_id": requisition_id, "submitted_by": submitted_by},
        tenant_id=tenant_id,
    )

    await notification_service.send_notification(notification)


async def notify_requisition_approved(
    requisition_id: str, approved_by: str, requester: NotificationRecipient, tenant_id: str
):
    """Notify requester when their requisition is approved."""
    notification = Notification(
        notification_id=f"req_approved_{requisition_id}",
        notification_type=NotificationType.REQUISITION_APPROVED,
        title="Job Requisition Approved",
        message="Your job requisition has been approved and is ready for job posting creation.",
        recipients=[requester],
        priority=NotificationPriority.MEDIUM,
        metadata={"requisition_id": requisition_id, "approved_by": approved_by},
        tenant_id=tenant_id,
    )

    await notification_service.send_notification(notification)


async def notify_requisition_rejected(
    requisition_id: str,
    rejected_by: str,
    rejection_reason: str,
    requester: NotificationRecipient,
    tenant_id: str,
):
    """Notify requester when their requisition is rejected."""
    notification = Notification(
        notification_id=f"req_rejected_{requisition_id}",
        notification_type=NotificationType.REQUISITION_REJECTED,
        title="Job Requisition Rejected",
        message=f"Your job requisition has been rejected. Reason: {rejection_reason}",
        recipients=[requester],
        priority=NotificationPriority.HIGH,
        metadata={
            "requisition_id": requisition_id,
            "rejected_by": rejected_by,
            "rejection_reason": rejection_reason,
        },
        tenant_id=tenant_id,
    )

    await notification_service.send_notification(notification)
