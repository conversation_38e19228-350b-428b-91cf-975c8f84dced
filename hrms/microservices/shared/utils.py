"""
Utility functions for oneHRMS microservices.

Provides common utilities for:
- ID generation
- Validation
- Data transformation
- Caching
"""

import hashlib
import re
import secrets
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union

from .exceptions import TenantError, ValidationError


def generate_id() -> str:
    """Generate unique identifier."""
    return str(uuid.uuid4())


def generate_short_id(length: int = 8) -> str:
    """Generate short unique identifier."""
    return secrets.token_urlsafe(length)[:length]


def generate_hash(data: str, algorithm: str = "sha256") -> str:
    """Generate hash for data."""
    hash_obj = hashlib.new(algorithm)
    hash_obj.update(data.encode("utf-8"))
    return hash_obj.hexdigest()


def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """Validate phone number format."""
    # Remove all non-digit characters
    digits = re.sub(r"\D", "", phone)
    # Check if it's between 10-15 digits
    return 10 <= len(digits) <= 15


def validate_tenant_id(tenant_id: str) -> bool:
    """Validate tenant ID format."""
    if not tenant_id or len(tenant_id) < 3 or len(tenant_id) > 50:
        return False

    # Allow alphanumeric, hyphens, and underscores
    pattern = r"^[a-zA-Z0-9_-]+$"
    return re.match(pattern, tenant_id) is not None


def validate_tenant_access(user_tenant_id: str, resource_tenant_id: str) -> None:
    """Validate that user has access to resource tenant."""
    if user_tenant_id != resource_tenant_id:
        raise TenantError(
            "Access denied: User does not have access to this tenant's resources",
            details={"user_tenant": user_tenant_id, "resource_tenant": resource_tenant_id},
        )


def sanitize_string(value: str, max_length: int = 255) -> str:
    """Sanitize string input."""
    if not isinstance(value, str):
        value = str(value)

    # Remove leading/trailing whitespace
    value = value.strip()

    # Truncate if too long
    if len(value) > max_length:
        value = value[:max_length]

    return value


def normalize_currency(amount: Union[str, int, float, Decimal]) -> Decimal:
    """Normalize currency amount to Decimal with 2 decimal places."""
    try:
        if isinstance(amount, str):
            # Remove currency symbols and spaces
            amount = re.sub(r"[^\d.-]", "", amount)

        decimal_amount = Decimal(str(amount))
        return decimal_amount.quantize(Decimal("0.01"))
    except (ValueError, TypeError):
        raise ValidationError(f"Invalid currency amount: {amount}")


def format_currency(amount: Decimal, currency_code: str = "USD") -> str:
    """Format currency amount for display."""
    currency_symbols = {"USD": "$", "EUR": "€", "GBP": "£", "INR": "₹", "JPY": "¥"}

    symbol = currency_symbols.get(currency_code, currency_code)
    return f"{symbol}{amount:,.2f}"


def calculate_percentage(part: Union[int, float, Decimal], total: Union[int, float, Decimal]) -> Decimal:
    """Calculate percentage with proper handling of edge cases."""
    if total == 0:
        return Decimal("0")

    part_decimal = Decimal(str(part))
    total_decimal = Decimal(str(total))

    percentage = (part_decimal / total_decimal) * 100
    return percentage.quantize(Decimal("0.01"))


def parse_date_range(date_range: str) -> tuple[datetime, datetime]:
    """Parse date range string into start and end dates."""
    try:
        if " to " in date_range:
            start_str, end_str = date_range.split(" to ")
        elif " - " in date_range:
            start_str, end_str = date_range.split(" - ")
        else:
            raise ValueError("Invalid date range format")

        start_date = datetime.fromisoformat(start_str.strip())
        end_date = datetime.fromisoformat(end_str.strip())

        if start_date > end_date:
            raise ValueError("Start date cannot be after end date")

        return start_date, end_date
    except ValueError as e:
        raise ValidationError(f"Invalid date range: {str(e)}")


def get_utc_now() -> datetime:
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)


def convert_to_utc(dt: datetime) -> datetime:
    """Convert datetime to UTC."""
    if dt.tzinfo is None:
        # Assume local timezone if no timezone info
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)


def paginate_query_params(page: int = 1, size: int = 50) -> Dict[str, int]:
    """Validate and normalize pagination parameters."""
    # Validate page
    if page < 1:
        page = 1

    # Validate size
    if size < 1:
        size = 50
    elif size > 1000:  # Prevent excessive page sizes
        size = 1000

    skip = (page - 1) * size

    return {"skip": skip, "limit": size, "page": page, "size": size}


def mask_sensitive_data(data: Dict[str, Any], sensitive_fields: List[str] = None) -> Dict[str, Any]:
    """Mask sensitive data in dictionary."""
    if sensitive_fields is None:
        sensitive_fields = [
            "password",
            "token",
            "secret",
            "key",
            "ssn",
            "social_security",
            "credit_card",
            "bank_account",
        ]

    masked_data = data.copy()

    for key, value in masked_data.items():
        if any(field in key.lower() for field in sensitive_fields):
            if isinstance(value, str) and len(value) > 4:
                masked_data[key] = "*" * (len(value) - 4) + value[-4:]
            else:
                masked_data[key] = "***"
        elif isinstance(value, dict):
            masked_data[key] = mask_sensitive_data(value, sensitive_fields)
        elif isinstance(value, list):
            masked_data[key] = [
                mask_sensitive_data(item, sensitive_fields) if isinstance(item, dict) else item
                for item in value
            ]

    return masked_data


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Deep merge two dictionaries."""
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result


def flatten_dict(data: Dict[str, Any], parent_key: str = "", sep: str = ".") -> Dict[str, Any]:
    """Flatten nested dictionary."""
    items = []

    for key, value in data.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key

        if isinstance(value, dict):
            items.extend(flatten_dict(value, new_key, sep=sep).items())
        else:
            items.append((new_key, value))

    return dict(items)


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split list into chunks of specified size."""
    return [lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size)]


def remove_none_values(data: Dict[str, Any]) -> Dict[str, Any]:
    """Remove None values from dictionary."""
    return {k: v for k, v in data.items() if v is not None}


def convert_snake_to_camel(snake_str: str) -> str:
    """Convert snake_case to camelCase."""
    components = snake_str.split("_")
    return components[0] + "".join(word.capitalize() for word in components[1:])


def convert_camel_to_snake(camel_str: str) -> str:
    """Convert camelCase to snake_case."""
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", camel_str)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
