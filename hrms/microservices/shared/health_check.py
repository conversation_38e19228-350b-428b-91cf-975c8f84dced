"""
Comprehensive Health Check System

Provides detailed health checks for microservices including database connectivity,
service dependencies, and system resources.
"""

import asyncio
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import psutil
from pydantic import BaseModel
from sqlalchemy import text

from .database import get_db_session
from .logging import get_logger
from .service_client import get_service_client

logger = get_logger(__name__)


class HealthCheckStatus(BaseModel):
    """Health check status for a component."""
    name: str
    status: str  # healthy, unhealthy, degraded
    message: str
    response_time_ms: Optional[float] = None
    details: Optional[Dict] = None


class SystemHealth(BaseModel):
    """Overall system health status."""
    service: str
    status: str  # healthy, unhealthy, degraded
    version: str
    timestamp: datetime
    uptime_seconds: float
    checks: List[HealthCheckStatus]
    dependencies: Dict[str, HealthCheckStatus]


class HealthChecker:
    """Comprehensive health checker for microservices."""

    def __init__(self, service_name: str, version: str = "1.0.0"):
        self.service_name = service_name
        self.version = version
        self.start_time = time.time()
        self.required_dependencies: List[str] = []
        self.optional_dependencies: List[str] = []

    def add_required_dependency(self, service_name: str):
        """Add a required service dependency."""
        if service_name not in self.required_dependencies:
            self.required_dependencies.append(service_name)

    def add_optional_dependency(self, service_name: str):
        """Add an optional service dependency."""
        if service_name not in self.optional_dependencies:
            self.optional_dependencies.append(service_name)

    async def check_database_health(self) -> HealthCheckStatus:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            async with get_db_session() as session:
                # Simple query to test connectivity
                result = await session.execute(text("SELECT 1"))
                await result.fetchone()
                
                # Test transaction capability
                await session.execute(text("SELECT NOW()"))
                await session.commit()
                
                response_time = (time.time() - start_time) * 1000
                
                return HealthCheckStatus(
                    name="database",
                    status="healthy",
                    message="Database connection successful",
                    response_time_ms=response_time,
                    details={
                        "connection_pool": "active",
                        "transaction_test": "passed"
                    }
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"Database health check failed: {str(e)}")
            
            return HealthCheckStatus(
                name="database",
                status="unhealthy",
                message=f"Database connection failed: {str(e)}",
                response_time_ms=response_time,
                details={"error": str(e)}
            )

    async def check_service_dependency(self, service_name: str) -> HealthCheckStatus:
        """Check health of a service dependency."""
        start_time = time.time()
        
        try:
            client = get_service_client(service_name)
            if not client:
                return HealthCheckStatus(
                    name=service_name,
                    status="unhealthy",
                    message=f"Service client not found: {service_name}",
                    response_time_ms=(time.time() - start_time) * 1000
                )
            
            health = await client.health_check()
            response_time = (time.time() - start_time) * 1000
            
            status = "healthy" if health.status == "healthy" else "unhealthy"
            
            return HealthCheckStatus(
                name=service_name,
                status=status,
                message=f"Service {service_name}: {health.status}",
                response_time_ms=response_time,
                details={
                    "service_version": health.version,
                    "service_response_time": health.response_time_ms
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"Service dependency check failed for {service_name}: {str(e)}")
            
            return HealthCheckStatus(
                name=service_name,
                status="unhealthy",
                message=f"Service {service_name} unreachable: {str(e)}",
                response_time_ms=response_time,
                details={"error": str(e)}
            )

    def check_system_resources(self) -> HealthCheckStatus:
        """Check system resource usage."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # Determine status based on thresholds
            status = "healthy"
            warnings = []
            
            if cpu_percent > 80:
                status = "degraded"
                warnings.append(f"High CPU usage: {cpu_percent}%")
            
            if memory_percent > 85:
                status = "degraded"
                warnings.append(f"High memory usage: {memory_percent}%")
            
            if disk_percent > 90:
                status = "degraded"
                warnings.append(f"High disk usage: {disk_percent}%")
            
            if cpu_percent > 95 or memory_percent > 95:
                status = "unhealthy"
            
            message = "System resources normal"
            if warnings:
                message = "; ".join(warnings)
            
            return HealthCheckStatus(
                name="system_resources",
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "disk_percent": disk_percent,
                    "memory_available_gb": round(memory.available / (1024**3), 2),
                    "disk_free_gb": round(disk.free / (1024**3), 2)
                }
            )
            
        except Exception as e:
            logger.error(f"System resource check failed: {str(e)}")
            return HealthCheckStatus(
                name="system_resources",
                status="unhealthy",
                message=f"Failed to check system resources: {str(e)}",
                details={"error": str(e)}
            )

    def check_environment_config(self) -> HealthCheckStatus:
        """Check environment configuration."""
        try:
            required_vars = [
                "DATABASE_URL",
                "LOG_LEVEL",
            ]
            
            missing_vars = []
            for var in required_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                return HealthCheckStatus(
                    name="environment",
                    status="unhealthy",
                    message=f"Missing required environment variables: {', '.join(missing_vars)}",
                    details={"missing_variables": missing_vars}
                )
            
            return HealthCheckStatus(
                name="environment",
                status="healthy",
                message="Environment configuration valid",
                details={
                    "log_level": os.getenv("LOG_LEVEL"),
                    "environment": os.getenv("ENVIRONMENT", "development")
                }
            )
            
        except Exception as e:
            return HealthCheckStatus(
                name="environment",
                status="unhealthy",
                message=f"Environment check failed: {str(e)}",
                details={"error": str(e)}
            )

    async def perform_comprehensive_health_check(self) -> SystemHealth:
        """Perform comprehensive health check."""
        start_time = time.time()
        checks = []
        dependencies = {}
        
        # Core system checks
        try:
            # Database check
            db_check = await self.check_database_health()
            checks.append(db_check)
            
            # System resources check
            resource_check = self.check_system_resources()
            checks.append(resource_check)
            
            # Environment check
            env_check = self.check_environment_config()
            checks.append(env_check)
            
            # Check required dependencies
            dependency_tasks = []
            for dep in self.required_dependencies:
                task = asyncio.create_task(self.check_service_dependency(dep))
                dependency_tasks.append((dep, task, True))  # True = required
            
            # Check optional dependencies
            for dep in self.optional_dependencies:
                task = asyncio.create_task(self.check_service_dependency(dep))
                dependency_tasks.append((dep, task, False))  # False = optional
            
            # Wait for all dependency checks
            for dep_name, task, is_required in dependency_tasks:
                try:
                    dep_check = await task
                    dependencies[dep_name] = dep_check
                except Exception as e:
                    dependencies[dep_name] = HealthCheckStatus(
                        name=dep_name,
                        status="unhealthy",
                        message=f"Dependency check failed: {str(e)}",
                        details={"error": str(e), "required": is_required}
                    )
            
        except Exception as e:
            logger.error(f"Health check error: {str(e)}")
            checks.append(HealthCheckStatus(
                name="health_check_system",
                status="unhealthy",
                message=f"Health check system error: {str(e)}",
                details={"error": str(e)}
            ))
        
        # Determine overall status
        overall_status = self._determine_overall_status(checks, dependencies)
        
        uptime = time.time() - self.start_time
        
        return SystemHealth(
            service=self.service_name,
            status=overall_status,
            version=self.version,
            timestamp=datetime.utcnow(),
            uptime_seconds=uptime,
            checks=checks,
            dependencies=dependencies
        )

    def _determine_overall_status(
        self, 
        checks: List[HealthCheckStatus], 
        dependencies: Dict[str, HealthCheckStatus]
    ) -> str:
        """Determine overall system status."""
        # Check core system health
        core_unhealthy = any(check.status == "unhealthy" for check in checks)
        if core_unhealthy:
            return "unhealthy"
        
        # Check required dependencies
        required_deps_unhealthy = any(
            dep.status == "unhealthy" 
            for dep_name, dep in dependencies.items() 
            if dep_name in self.required_dependencies
        )
        if required_deps_unhealthy:
            return "unhealthy"
        
        # Check for degraded status
        core_degraded = any(check.status == "degraded" for check in checks)
        deps_degraded = any(dep.status == "degraded" for dep in dependencies.values())
        
        if core_degraded or deps_degraded:
            return "degraded"
        
        return "healthy"


# Global health checker instances
health_checkers: Dict[str, HealthChecker] = {}


def get_health_checker(service_name: str, version: str = "1.0.0") -> HealthChecker:
    """Get or create health checker for a service."""
    if service_name not in health_checkers:
        health_checkers[service_name] = HealthChecker(service_name, version)
    return health_checkers[service_name]


async def create_health_check_endpoint(service_name: str, version: str = "1.0.0"):
    """Create a health check endpoint response."""
    checker = get_health_checker(service_name, version)
    return await checker.perform_comprehensive_health_check()
