"""
Logging configuration for oneHRMS microservices.

Provides structured logging with:
- JSON formatting for production
- Console formatting for development
- Request tracing
- Performance monitoring
"""

import json
import logging
import os
import sys
from contextvars import ContextVar
from datetime import datetime
from typing import Any, Dict, Optional

# Context variables for request tracing
request_id_var: ContextVar[Optional[str]] = ContextVar("request_id", default=None)
tenant_id_var: ContextVar[Optional[str]] = ContextVar("tenant_id", default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar("user_id", default=None)


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add context information
        request_id = request_id_var.get()
        if request_id:
            log_data["request_id"] = request_id

        tenant_id = tenant_id_var.get()
        if tenant_id:
            log_data["tenant_id"] = tenant_id

        user_id = user_id_var.get()
        if user_id:
            log_data["user_id"] = user_id

        # Add exception information
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # Add extra fields
        if hasattr(record, "extra"):
            log_data.update(record.extra)

        return json.dumps(log_data)


class ConsoleFormatter(logging.Formatter):
    """Console formatter for development."""

    def __init__(self):
        super().__init__(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
        )


def setup_logging(service_name: str, log_level: str = None, json_format: bool = None) -> None:
    """Setup logging configuration for a microservice."""

    # Get configuration from environment
    log_level = log_level or os.getenv("LOG_LEVEL", "INFO")
    json_format = json_format if json_format is not None else os.getenv("LOG_FORMAT", "console") == "json"

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))

    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)

    # Set formatter
    if json_format:
        formatter = JSONFormatter()
    else:
        formatter = ConsoleFormatter()

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Configure specific loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)

    # Add service name to all logs
    logging.getLogger().info(f"Logging configured for service: {service_name}")


def get_logger(name: str) -> logging.Logger:
    """Get logger instance with context support."""
    return logging.getLogger(name)


class RequestLogger:
    """Request logging middleware."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def log_request(
        self,
        method: str,
        path: str,
        request_id: str,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
    ):
        """Log incoming request."""

        # Set context variables
        request_id_var.set(request_id)
        if tenant_id:
            tenant_id_var.set(tenant_id)
        if user_id:
            user_id_var.set(user_id)

        log_data = {"event": "request_started", "method": method, "path": path, "request_id": request_id}

        if extra:
            log_data.update(extra)

        self.logger.info("Request started", extra=log_data)

    def log_response(self, status_code: int, response_time_ms: float, extra: Optional[Dict[str, Any]] = None):
        """Log response."""

        log_data = {
            "event": "request_completed",
            "status_code": status_code,
            "response_time_ms": response_time_ms,
        }

        if extra:
            log_data.update(extra)

        level = logging.INFO if status_code < 400 else logging.WARNING
        self.logger.log(level, "Request completed", extra=log_data)

    def log_error(self, error: Exception, extra: Optional[Dict[str, Any]] = None):
        """Log error."""

        log_data = {"event": "request_error", "error_type": type(error).__name__, "error_message": str(error)}

        if extra:
            log_data.update(extra)

        self.logger.error("Request error", extra=log_data, exc_info=True)


class PerformanceLogger:
    """Performance monitoring logger."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def log_database_query(self, query: str, duration_ms: float, rows_affected: int = 0):
        """Log database query performance."""

        log_data = {
            "event": "database_query",
            "query": query[:200] + "..." if len(query) > 200 else query,
            "duration_ms": duration_ms,
            "rows_affected": rows_affected,
        }

        level = logging.WARNING if duration_ms > 1000 else logging.DEBUG
        self.logger.log(level, "Database query executed", extra=log_data)

    def log_external_api_call(self, service: str, endpoint: str, duration_ms: float, status_code: int):
        """Log external API call performance."""

        log_data = {
            "event": "external_api_call",
            "service": service,
            "endpoint": endpoint,
            "duration_ms": duration_ms,
            "status_code": status_code,
        }

        level = logging.WARNING if duration_ms > 5000 or status_code >= 400 else logging.INFO
        self.logger.log(level, "External API call", extra=log_data)


# Global logger instances
def get_request_logger(service_name: str) -> RequestLogger:
    """Get request logger for service."""
    logger = get_logger(f"{service_name}.requests")
    return RequestLogger(logger)


def get_performance_logger(service_name: str) -> PerformanceLogger:
    """Get performance logger for service."""
    logger = get_logger(f"{service_name}.performance")
    return PerformanceLogger(logger)
