"""
Service Discovery and Health Monitoring

Provides dynamic service discovery, health monitoring, and load balancing
for microservice communication.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set

from pydantic import BaseModel

from .logging import get_logger
from .service_client import ServiceClient, ServiceHealth, service_registry

logger = get_logger(__name__)


class ServiceEndpoint(BaseModel):
    """Service endpoint information."""
    host: str
    port: int
    protocol: str = "http"
    weight: int = 100
    health_check_path: str = "/health"
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True
    consecutive_failures: int = 0


class ServiceDefinition(BaseModel):
    """Service definition with multiple endpoints."""
    name: str
    version: str
    endpoints: List[ServiceEndpoint]
    load_balancing_strategy: str = "round_robin"  # round_robin, weighted, least_connections
    health_check_interval: int = 30  # seconds
    failure_threshold: int = 3
    recovery_threshold: int = 2


class ServiceDiscovery:
    """Service discovery and health monitoring system."""

    def __init__(self):
        self.services: Dict[str, ServiceDefinition] = {}
        self.endpoint_counters: Dict[str, int] = {}  # For round-robin
        self.health_monitor_task: Optional[asyncio.Task] = None
        self.is_monitoring = False

    def register_service(self, service_def: ServiceDefinition):
        """Register a service with its endpoints."""
        self.services[service_def.name] = service_def
        self.endpoint_counters[service_def.name] = 0
        
        logger.info(
            f"Registered service: {service_def.name} with {len(service_def.endpoints)} endpoints",
            extra={
                'service': service_def.name,
                'endpoints': len(service_def.endpoints),
                'strategy': service_def.load_balancing_strategy
            }
        )

    def unregister_service(self, service_name: str):
        """Unregister a service."""
        if service_name in self.services:
            del self.services[service_name]
            del self.endpoint_counters[service_name]
            logger.info(f"Unregistered service: {service_name}")

    def get_healthy_endpoints(self, service_name: str) -> List[ServiceEndpoint]:
        """Get all healthy endpoints for a service."""
        service = self.services.get(service_name)
        if not service:
            return []
        
        return [ep for ep in service.endpoints if ep.is_healthy]

    def get_service_endpoint(self, service_name: str) -> Optional[ServiceEndpoint]:
        """Get next available endpoint using load balancing."""
        healthy_endpoints = self.get_healthy_endpoints(service_name)
        if not healthy_endpoints:
            logger.warning(f"No healthy endpoints available for service: {service_name}")
            return None

        service = self.services[service_name]
        strategy = service.load_balancing_strategy

        if strategy == "round_robin":
            return self._round_robin_selection(service_name, healthy_endpoints)
        elif strategy == "weighted":
            return self._weighted_selection(healthy_endpoints)
        elif strategy == "least_connections":
            return self._least_connections_selection(healthy_endpoints)
        else:
            # Default to round-robin
            return self._round_robin_selection(service_name, healthy_endpoints)

    def _round_robin_selection(self, service_name: str, endpoints: List[ServiceEndpoint]) -> ServiceEndpoint:
        """Round-robin endpoint selection."""
        counter = self.endpoint_counters[service_name]
        selected = endpoints[counter % len(endpoints)]
        self.endpoint_counters[service_name] = (counter + 1) % len(endpoints)
        return selected

    def _weighted_selection(self, endpoints: List[ServiceEndpoint]) -> ServiceEndpoint:
        """Weighted endpoint selection."""
        import random
        
        total_weight = sum(ep.weight for ep in endpoints)
        if total_weight == 0:
            return random.choice(endpoints)
        
        random_weight = random.randint(1, total_weight)
        current_weight = 0
        
        for endpoint in endpoints:
            current_weight += endpoint.weight
            if current_weight >= random_weight:
                return endpoint
        
        return endpoints[-1]  # Fallback

    def _least_connections_selection(self, endpoints: List[ServiceEndpoint]) -> ServiceEndpoint:
        """Least connections endpoint selection (simplified)."""
        # For now, just return the first endpoint
        # In a real implementation, this would track active connections
        return endpoints[0]

    def get_service_url(self, service_name: str) -> Optional[str]:
        """Get service URL for the next available endpoint."""
        endpoint = self.get_service_endpoint(service_name)
        if not endpoint:
            return None
        
        return f"{endpoint.protocol}://{endpoint.host}:{endpoint.port}"

    async def start_health_monitoring(self):
        """Start health monitoring for all services."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.health_monitor_task = asyncio.create_task(self._health_monitor_loop())
        logger.info("Started health monitoring")

    async def stop_health_monitoring(self):
        """Stop health monitoring."""
        self.is_monitoring = False
        if self.health_monitor_task:
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped health monitoring")

    async def _health_monitor_loop(self):
        """Main health monitoring loop."""
        while self.is_monitoring:
            try:
                await self._check_all_services_health()
                await asyncio.sleep(10)  # Check every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring error: {str(e)}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _check_all_services_health(self):
        """Check health of all service endpoints."""
        tasks = []
        
        for service_name, service in self.services.items():
            for endpoint in service.endpoints:
                if self._should_check_health(endpoint, service.health_check_interval):
                    task = asyncio.create_task(
                        self._check_endpoint_health(service_name, endpoint)
                    )
                    tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    def _should_check_health(self, endpoint: ServiceEndpoint, interval: int) -> bool:
        """Check if endpoint health should be checked."""
        if not endpoint.last_health_check:
            return True
        
        time_since_check = datetime.utcnow() - endpoint.last_health_check
        return time_since_check.total_seconds() >= interval

    async def _check_endpoint_health(self, service_name: str, endpoint: ServiceEndpoint):
        """Check health of a specific endpoint."""
        url = f"{endpoint.protocol}://{endpoint.host}:{endpoint.port}"
        
        try:
            # Create temporary client for health check
            client = ServiceClient(
                service_name=f"{service_name}-health",
                base_url=url,
                timeout=5,
                max_retries=1,
                circuit_breaker_enabled=False
            )
            
            health = await client.health_check()
            await client.close()
            
            # Update endpoint health status
            endpoint.last_health_check = datetime.utcnow()
            
            if health.status == "healthy":
                if not endpoint.is_healthy:
                    logger.info(f"Endpoint recovered: {service_name} -> {url}")
                endpoint.is_healthy = True
                endpoint.consecutive_failures = 0
            else:
                self._handle_endpoint_failure(service_name, endpoint, url)
                
        except Exception as e:
            self._handle_endpoint_failure(service_name, endpoint, url, str(e))

    def _handle_endpoint_failure(self, service_name: str, endpoint: ServiceEndpoint, url: str, error: str = ""):
        """Handle endpoint failure."""
        endpoint.consecutive_failures += 1
        endpoint.last_health_check = datetime.utcnow()
        
        service = self.services[service_name]
        if endpoint.consecutive_failures >= service.failure_threshold:
            if endpoint.is_healthy:
                logger.warning(
                    f"Endpoint marked unhealthy: {service_name} -> {url} (failures: {endpoint.consecutive_failures})"
                )
            endpoint.is_healthy = False

    def get_service_status(self, service_name: str) -> Dict:
        """Get comprehensive status of a service."""
        service = self.services.get(service_name)
        if not service:
            return {"error": "Service not found"}
        
        healthy_endpoints = self.get_healthy_endpoints(service_name)
        
        return {
            "name": service.name,
            "version": service.version,
            "total_endpoints": len(service.endpoints),
            "healthy_endpoints": len(healthy_endpoints),
            "load_balancing_strategy": service.load_balancing_strategy,
            "endpoints": [
                {
                    "host": ep.host,
                    "port": ep.port,
                    "is_healthy": ep.is_healthy,
                    "consecutive_failures": ep.consecutive_failures,
                    "last_health_check": ep.last_health_check.isoformat() if ep.last_health_check else None,
                }
                for ep in service.endpoints
            ]
        }

    def get_all_services_status(self) -> Dict:
        """Get status of all services."""
        return {
            service_name: self.get_service_status(service_name)
            for service_name in self.services.keys()
        }


# Global service discovery instance
service_discovery = ServiceDiscovery()


def register_service_from_env(service_name: str):
    """Register service from environment variables."""
    # Look for environment variables like:
    # EMPLOYEE_SERVICE_HOSTS=host1:8100,host2:8100
    # EMPLOYEE_SERVICE_VERSION=1.0.0
    
    hosts_env = f"{service_name.upper()}_SERVICE_HOSTS"
    version_env = f"{service_name.upper()}_SERVICE_VERSION"
    
    hosts_str = os.getenv(hosts_env)
    version = os.getenv(version_env, "1.0.0")
    
    if not hosts_str:
        # Fallback to single host
        host = os.getenv(f"{service_name.upper()}_SERVICE_HOST", f"{service_name}-service")
        port = int(os.getenv(f"{service_name.upper()}_SERVICE_PORT", "8100"))
        hosts_str = f"{host}:{port}"
    
    endpoints = []
    for host_port in hosts_str.split(","):
        host_port = host_port.strip()
        if ":" in host_port:
            host, port_str = host_port.rsplit(":", 1)
            port = int(port_str)
        else:
            host = host_port
            port = 8100  # Default port
        
        endpoints.append(ServiceEndpoint(
            host=host,
            port=port,
            protocol="http"
        ))
    
    service_def = ServiceDefinition(
        name=service_name,
        version=version,
        endpoints=endpoints
    )
    
    service_discovery.register_service(service_def)


async def initialize_service_discovery():
    """Initialize service discovery with default services."""
    services = ["employee", "payroll", "attendance", "leave", "ess", "recruitment"]
    
    for service_name in services:
        register_service_from_env(service_name)
    
    # Start health monitoring
    await service_discovery.start_health_monitoring()
    
    logger.info(f"Service discovery initialized with {len(services)} services")


def get_service_discovery() -> ServiceDiscovery:
    """Get global service discovery instance."""
    return service_discovery
