"""
Shared service client for inter-microservice communication.

This module provides a robust HTTP client for communication between microservices
with circuit breaker patterns, retry logic, health checks, and comprehensive error handling.
"""

import asyncio
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import aiohttp
from pydantic import BaseModel

from .exceptions import ServiceUnavailableError, ValidationError
from .logging import get_logger

logger = get_logger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class ServiceHealth(BaseModel):
    """Service health status."""
    service: str
    status: str
    version: str
    timestamp: datetime
    response_time_ms: Optional[float] = None


class CircuitBreaker:
    """Circuit breaker implementation for service resilience."""

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception,
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED

    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
            else:
                raise ServiceUnavailableError("Circuit breaker is OPEN")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )

    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED

    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN


class ServiceClient:
    """HTTP client for inter-microservice communication."""

    def __init__(
        self,
        service_name: str,
        base_url: str,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        circuit_breaker_enabled: bool = True,
    ):
        self.service_name = service_name
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Circuit breaker
        self.circuit_breaker = CircuitBreaker() if circuit_breaker_enabled else None
        
        # Health tracking
        self.last_health_check = None
        self.health_status = None
        
        # Session management
        self._session = None

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': f'oneHRMS-ServiceClient/{self.service_name}',
                }
            )
        return self._session

    async def close(self):
        """Close HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make HTTP request with retry and circuit breaker."""
        url = f"{self.base_url}{endpoint}"
        
        async def _request():
            session = await self._get_session()
            
            request_headers = {}
            if headers:
                request_headers.update(headers)
            
            async with session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=request_headers,
            ) as response:
                response_data = await response.json()
                
                if response.status >= 400:
                    logger.error(
                        f"Service request failed: {method} {url} -> {response.status}",
                        extra={
                            'service': self.service_name,
                            'method': method,
                            'url': url,
                            'status': response.status,
                            'response': response_data,
                        }
                    )
                    raise ServiceUnavailableError(
                        f"Service {self.service_name} returned {response.status}: {response_data}"
                    )
                
                return response_data

        # Apply circuit breaker if enabled
        if self.circuit_breaker:
            return await self._with_retry(
                lambda: self.circuit_breaker.call(_request)
            )
        else:
            return await self._with_retry(_request)

    async def _with_retry(self, func):
        """Execute function with retry logic."""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await func()
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    logger.warning(
                        f"Request failed (attempt {attempt + 1}/{self.max_retries + 1}), "
                        f"retrying in {delay}s: {str(e)}",
                        extra={'service': self.service_name, 'attempt': attempt + 1}
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"Request failed after {self.max_retries + 1} attempts: {str(e)}",
                        extra={'service': self.service_name}
                    )
        
        raise last_exception

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make GET request."""
        return await self._make_request('GET', endpoint, params=params, headers=headers)

    async def post(
        self,
        endpoint: str,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make POST request."""
        return await self._make_request('POST', endpoint, data=data, headers=headers)

    async def put(
        self,
        endpoint: str,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make PUT request."""
        return await self._make_request('PUT', endpoint, data=data, headers=headers)

    async def patch(
        self,
        endpoint: str,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make PATCH request."""
        return await self._make_request('PATCH', endpoint, data=data, headers=headers)

    async def delete(
        self,
        endpoint: str,
        headers: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make DELETE request."""
        return await self._make_request('DELETE', endpoint, headers=headers)

    async def health_check(self) -> ServiceHealth:
        """Check service health."""
        start_time = time.time()
        
        try:
            response = await self.get('/health')
            response_time = (time.time() - start_time) * 1000
            
            health = ServiceHealth(
                service=response.get('service', self.service_name),
                status=response.get('status', 'unknown'),
                version=response.get('version', 'unknown'),
                timestamp=datetime.utcnow(),
                response_time_ms=response_time,
            )
            
            self.last_health_check = datetime.utcnow()
            self.health_status = health
            
            logger.info(
                f"Health check successful for {self.service_name}: {health.status}",
                extra={
                    'service': self.service_name,
                    'status': health.status,
                    'response_time_ms': response_time,
                }
            )
            
            return health
            
        except Exception as e:
            logger.error(
                f"Health check failed for {self.service_name}: {str(e)}",
                extra={'service': self.service_name, 'error': str(e)}
            )
            
            health = ServiceHealth(
                service=self.service_name,
                status='unhealthy',
                version='unknown',
                timestamp=datetime.utcnow(),
                response_time_ms=(time.time() - start_time) * 1000,
            )
            
            self.health_status = health
            return health

    def is_healthy(self, max_age_minutes: int = 5) -> bool:
        """Check if service is considered healthy."""
        if not self.health_status:
            return False
        
        if not self.last_health_check:
            return False
        
        age = datetime.utcnow() - self.last_health_check
        if age > timedelta(minutes=max_age_minutes):
            return False
        
        return self.health_status.status == 'healthy'


class ServiceRegistry:
    """Registry for managing service clients."""

    def __init__(self):
        self.clients: Dict[str, ServiceClient] = {}

    def register_service(
        self,
        service_name: str,
        base_url: str,
        **client_kwargs
    ) -> ServiceClient:
        """Register a service client."""
        client = ServiceClient(service_name, base_url, **client_kwargs)
        self.clients[service_name] = client
        
        logger.info(
            f"Registered service client: {service_name} -> {base_url}",
            extra={'service': service_name, 'base_url': base_url}
        )
        
        return client

    def get_client(self, service_name: str) -> Optional[ServiceClient]:
        """Get service client by name."""
        return self.clients.get(service_name)

    async def health_check_all(self) -> Dict[str, ServiceHealth]:
        """Check health of all registered services."""
        results = {}
        
        for service_name, client in self.clients.items():
            try:
                health = await client.health_check()
                results[service_name] = health
            except Exception as e:
                logger.error(
                    f"Failed to check health for {service_name}: {str(e)}",
                    extra={'service': service_name, 'error': str(e)}
                )
                results[service_name] = ServiceHealth(
                    service=service_name,
                    status='error',
                    version='unknown',
                    timestamp=datetime.utcnow(),
                )
        
        return results

    async def close_all(self):
        """Close all service clients."""
        for client in self.clients.values():
            await client.close()


# Global service registry instance
service_registry = ServiceRegistry()


def get_service_client(service_name: str) -> Optional[ServiceClient]:
    """Get service client from global registry."""
    return service_registry.get_client(service_name)


async def register_default_services():
    """Register default microservices."""
    import os
    
    services = [
        {
            'name': 'employee',
            'url': os.getenv('EMPLOYEE_SERVICE_URL', 'http://employee-service:8100'),
        },
        {
            'name': 'payroll',
            'url': os.getenv('PAYROLL_SERVICE_URL', 'http://payroll-service:8101'),
        },
        {
            'name': 'attendance',
            'url': os.getenv('ATTENDANCE_SERVICE_URL', 'http://attendance-service:8102'),
        },
        {
            'name': 'leave',
            'url': os.getenv('LEAVE_SERVICE_URL', 'http://leave-service:8103'),
        },
        {
            'name': 'ess',
            'url': os.getenv('ESS_SERVICE_URL', 'http://ess-service:8104'),
        },
        {
            'name': 'recruitment',
            'url': os.getenv('RECRUITMENT_SERVICE_URL', 'http://recruitment-service:8105'),
        },
    ]
    
    for service in services:
        service_registry.register_service(
            service_name=service['name'],
            base_url=service['url'],
            timeout=30,
            max_retries=3,
            circuit_breaker_enabled=True,
        )
    
    logger.info(f"Registered {len(services)} default services")
