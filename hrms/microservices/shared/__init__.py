"""
Shared components for oneHRMS microservices.

This module contains common functionality used across all microservices:
- Authentication and authorization
- Database models and utilities
- Multi-tenancy support
- Logging and monitoring
- Error handling
- API utilities
"""

from .auth import AuthMiddleware, get_current_tenant, get_current_user
from .database import DatabaseManager, TenantAwareModel
from .exceptions import AuthenticationError, HRMSException, ValidationError
from .logging import get_logger, setup_logging
from .models import BaseModel, TenantMixin, TimestampMixin
from .utils import generate_id, validate_tenant_access

__all__ = [
    "AuthMiddleware",
    "get_current_user",
    "get_current_tenant",
    "DatabaseManager",
    "TenantAwareModel",
    "HRMSException",
    "ValidationError",
    "AuthenticationError",
    "setup_logging",
    "get_logger",
    "BaseModel",
    "TimestampMixin",
    "TenantMixin",
    "generate_id",
    "validate_tenant_access",
]
