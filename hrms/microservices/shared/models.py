"""
Shared database models and mixins for oneHRMS microservices.

Provides base models with common functionality:
- Tenant isolation
- Timestamps
- Soft deletion
- Audit trails
"""

from datetime import datetime
from typing import Generic, Optional, TypeVar
from uuid import uuid4

from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field
from sqlalchemy import Boolean, Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base

T = TypeVar("T")

Base = declarative_base()


class BaseModel(PydanticBaseModel):
    """Base Pydantic model with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,  # Updated from orm_mode for Pydantic v2
        validate_assignment=True,
        use_enum_values=True,
        json_encoders={datetime: lambda v: v.isoformat()},
    )


class TimestampMixin:
    """Mixin for adding timestamp fields to SQLAlchemy models."""

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class TenantMixin:
    """Mixin for adding tenant isolation to SQLAlchemy models."""

    tenant_id = Column(String(50), nullable=False, index=True)


class SoftDeleteMixin:
    """Mixin for soft deletion functionality."""

    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)


class AuditMixin:
    """Mixin for audit trail functionality."""

    created_by = Column(String(100), nullable=True)
    updated_by = Column(String(100), nullable=True)


class TenantAwareModel(Base, TimestampMixin, TenantMixin, SoftDeleteMixin, AuditMixin):
    """
    Base SQLAlchemy model with tenant isolation and common fields.

    All microservice models should inherit from this base class.
    """

    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id}, tenant_id={self.tenant_id})>"


# Common Pydantic schemas
class TenantAwareSchema(BaseModel):
    """Base schema for tenant-aware API models."""

    id: Optional[str] = Field(None, description="Unique identifier")
    tenant_id: str = Field(..., description="Tenant identifier")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class CreateSchema(BaseModel):
    """Base schema for create operations."""

    pass


class UpdateSchema(BaseModel):
    """Base schema for update operations."""

    pass


class ResponseSchema(TenantAwareSchema):
    """Base schema for API responses."""

    pass


# Common response models
class SuccessResponse(BaseModel, Generic[T]):
    """Standard success response."""

    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[T] = None


class ErrorResponse(BaseModel):
    """Standard error response."""

    success: bool = False
    error: str
    details: Optional[dict] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response wrapper."""

    items: list[T]
    total: int
    page: int = 1
    size: int = 50
    pages: int

    @classmethod
    def create(cls, items: list, total: int, page: int = 1, size: int = 50):
        """Create paginated response."""
        pages = (total + size - 1) // size
        return cls(items=items, total=total, page=page, size=size, pages=pages)


# User and Tenant models
class User(BaseModel):
    """User model for authentication."""

    id: str
    email: str
    full_name: str
    roles: list[str] = []
    tenant_id: str
    is_active: bool = True


class Tenant(BaseModel):
    """Tenant model for multi-tenancy."""

    id: str
    name: str
    domain: str
    is_active: bool = True
    settings: dict = {}


# Health check models
class HealthStatus(BaseModel):
    """Health check status."""

    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "2.0.0"
    service: str
    dependencies: dict = {}


class DatabaseHealth(BaseModel):
    """Database health status."""

    connected: bool
    response_time_ms: float
    pool_size: int = 0
    active_connections: int = 0
