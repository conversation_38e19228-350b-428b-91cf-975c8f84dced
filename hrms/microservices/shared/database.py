"""
Database utilities and connection management for oneHRMS microservices.

Provides:
- Database connection management
- Tenant-aware query utilities
- Migration support
- Connection pooling
"""

import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import QueuePool

from .exceptions import DatabaseError, TenantError
from .logging import get_logger
from .models import Base, TenantAwareModel

logger = get_logger(__name__)


class DatabaseManager:
    """Database connection and session management."""

    def __init__(
        self,
        database_url: Optional[str] = None,
        async_database_url: Optional[str] = None,
        echo: bool = False,
        pool_size: int = 10,
        max_overflow: int = 20,
    ):

        self.database_url = database_url or os.getenv("DATABASE_URL", "sqlite:///./hrms.db")
        self.async_database_url = async_database_url or self.database_url.replace("://", "+asyncpg://")
        self.echo = echo or os.getenv("DB_ECHO", "false").lower() == "true"

        # Create engines
        self.engine = create_engine(
            self.database_url,
            echo=self.echo,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_pre_ping=True,
        )

        self.async_engine = create_async_engine(
            self.async_database_url,
            echo=self.echo,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_pre_ping=True,
        )

        # Create session factories
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        self.AsyncSessionLocal = async_sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )

    def create_tables(self):
        """Create all tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
            raise DatabaseError(f"Failed to create database tables: {str(e)}")

    def drop_tables(self):
        """Drop all tables (use with caution)."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {str(e)}")
            raise DatabaseError(f"Failed to drop database tables: {str(e)}")

    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async database session."""
        async with self.AsyncSessionLocal() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {str(e)}")
                raise DatabaseError(f"Database operation failed: {str(e)}")
            finally:
                await session.close()

    def get_session(self) -> Session:
        """Get synchronous database session."""
        return self.SessionLocal()

    async def health_check(self) -> Dict[str, Any]:
        """Check database health."""
        try:
            start_time = datetime.utcnow()

            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                result.fetchone()

            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000

            return {
                "connected": True,
                "response_time_ms": response_time,
                "pool_size": self.async_engine.pool.size(),
                "active_connections": self.async_engine.pool.checkedout(),
            }
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {"connected": False, "response_time_ms": 0, "error": str(e)}


class TenantAwareRepository:
    """Base repository with tenant isolation."""

    def __init__(self, db_manager: DatabaseManager, model_class: type):
        self.db_manager = db_manager
        self.model_class = model_class

    def _validate_tenant_access(self, tenant_id: str, obj: TenantAwareModel):
        """Validate that object belongs to the specified tenant."""
        if obj.tenant_id != tenant_id:
            raise TenantError(f"Access denied: Object belongs to different tenant")

    async def create(self, tenant_id: str, data: Dict[str, Any]) -> TenantAwareModel:
        """Create new record with tenant isolation."""
        async with self.db_manager.get_async_session() as session:
            data["tenant_id"] = tenant_id
            obj = self.model_class(**data)
            session.add(obj)
            await session.flush()
            await session.refresh(obj)
            return obj

    async def get_by_id(self, tenant_id: str, obj_id: str) -> Optional[TenantAwareModel]:
        """Get record by ID with tenant isolation."""
        async with self.db_manager.get_async_session() as session:
            obj = await session.get(self.model_class, obj_id)
            if obj:
                self._validate_tenant_access(tenant_id, obj)
            return obj

    async def list_by_tenant(
        self, tenant_id: str, skip: int = 0, limit: int = 100, filters: Optional[Dict[str, Any]] = None
    ) -> List[TenantAwareModel]:
        """List records for tenant with pagination."""
        async with self.db_manager.get_async_session() as session:
            query = session.query(self.model_class).filter(
                self.model_class.tenant_id == tenant_id, self.model_class.is_deleted == False
            )

            if filters:
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)

            query = query.offset(skip).limit(limit)
            result = await session.execute(query)
            return result.scalars().all()

    async def update(self, tenant_id: str, obj_id: str, data: Dict[str, Any]) -> Optional[TenantAwareModel]:
        """Update record with tenant isolation."""
        async with self.db_manager.get_async_session() as session:
            obj = await session.get(self.model_class, obj_id)
            if not obj:
                return None

            self._validate_tenant_access(tenant_id, obj)

            for key, value in data.items():
                if hasattr(obj, key) and key not in ["id", "tenant_id", "created_at"]:
                    setattr(obj, key, value)

            obj.updated_at = datetime.utcnow()
            await session.flush()
            await session.refresh(obj)
            return obj

    async def delete(self, tenant_id: str, obj_id: str) -> bool:
        """Soft delete record with tenant isolation."""
        async with self.db_manager.get_async_session() as session:
            obj = await session.get(self.model_class, obj_id)
            if not obj:
                return False

            self._validate_tenant_access(tenant_id, obj)

            obj.is_deleted = True
            obj.deleted_at = datetime.utcnow()
            await session.flush()
            return True

    async def count_by_tenant(self, tenant_id: str, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records for tenant."""
        async with self.db_manager.get_async_session() as session:
            query = session.query(self.model_class).filter(
                self.model_class.tenant_id == tenant_id, self.model_class.is_deleted == False
            )

            if filters:
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)

            result = await session.execute(query.count())
            return result.scalar()


# Global database manager instance - lazy initialization
db_manager = None


def get_db_manager() -> DatabaseManager:
    """Get or create the global database manager instance."""
    global db_manager
    if db_manager is None:
        try:
            db_manager = DatabaseManager()
        except Exception as e:
            logger.warning(f"Failed to initialize database manager: {e}")
            # Return a mock database manager for development
            db_manager = MockDatabaseManager()
    return db_manager


class MockDatabaseManager:
    """Mock database manager for development/testing when DB is not available."""

    def __init__(self):
        self.async_engine = None
        self.sync_engine = None
        self.async_session_factory = None
        self.sync_session_factory = None

    async def get_async_session(self):
        """Mock async session."""
        return None

    def get_sync_session(self):
        """Mock sync session."""
        return None

    def get_session(self):
        """Mock session (alias for get_sync_session)."""
        return None

    async def create_tables(self):
        """Mock table creation."""
        pass

    async def drop_tables(self):
        """Mock table dropping."""
        pass

    async def health_check(self):
        """Mock health check."""
        return "healthy"

    async def initialize(self):
        """Mock initialization."""
        pass


# Dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database session."""
    async with db_manager.get_async_session() as session:
        yield session
