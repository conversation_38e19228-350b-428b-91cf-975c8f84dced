"""
Authentication and authorization utilities for HRMS microservices.

This module provides JWT token validation, user extraction, and role-based access control.
"""

from typing import List, Optional

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from .exceptions import ForbiddenError, UnauthorizedError
from .models import Tenant, User

# Security scheme
security = HTTPBearer(auto_error=False)  # Don't auto-error for development mode

# JWT configuration
JWT_SECRET_KEY = "your-secret-key"  # Should be from environment
JWT_ALGORITHM = "HS256"


def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> User:
    """
    Extract and validate the current user from JWT token.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        User: The authenticated user

    Raises:
        UnauthorizedError: If token is invalid or missing
    """
    import os

    # Development mode bypass (always enabled for now)
    if True:  # os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
        return User(
            id="dev-user-001",
            email="<EMAIL>",
            full_name="Development User",
            roles=["admin", "manager", "hr"],
            tenant_id="dev-tenant",
            is_active=True,
        )

    # Check if credentials are provided
    if credentials is None:
        raise UnauthorizedError("No authentication credentials provided")

    try:
        token = credentials.credentials
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

        user_id = payload.get("sub")
        if user_id is None:
            raise UnauthorizedError("Invalid token payload")

        # Extract user information from token
        user = User(
            id=user_id,
            email=payload.get("email", ""),
            full_name=payload.get("full_name", ""),
            roles=payload.get("roles", []),
            tenant_id=payload.get("tenant_id", ""),
            is_active=True,
        )

        return user

    except jwt.ExpiredSignatureError:
        raise UnauthorizedError("Token has expired")
    except jwt.JWTError:
        raise UnauthorizedError("Invalid token")
    except Exception as e:
        raise UnauthorizedError(f"Authentication failed: {str(e)}")


def get_current_tenant(user: User = Depends(get_current_user)) -> Tenant:
    """
    Extract tenant information from the authenticated user.

    Args:
        user: The authenticated user

    Returns:
        Tenant: The tenant information
    """
    return Tenant(
        id=user.tenant_id, name="Development Tenant", domain="dev.company.com", is_active=True, settings={}
    )


def require_role(required_roles: List[str]):
    """
    Dependency factory for role-based access control.

    Args:
        required_roles: List of roles that are allowed to access the endpoint

    Returns:
        A dependency function that validates user roles
    """

    def role_checker(user: User = Depends(get_current_user)) -> User:
        """
        Validate that the user has at least one of the required roles.

        Args:
            user: The authenticated user

        Returns:
            User: The authenticated user if validation passes

        Raises:
            ForbiddenError: If user doesn't have required roles
        """
        if not user.roles:
            raise ForbiddenError("User has no roles assigned")

        user_roles = set(user.roles)
        required_roles_set = set(required_roles)

        # Check if user has any of the required roles
        if not user_roles.intersection(required_roles_set):
            raise ForbiddenError(
                f"Access denied. Required roles: {required_roles}. " f"User roles: {list(user_roles)}"
            )

        return user

    return role_checker


def require_any_role(*roles: str):
    """
    Convenience function for requiring any of the specified roles.

    Args:
        *roles: Variable number of role strings

    Returns:
        A dependency function that validates user roles
    """
    return require_role(list(roles))


def require_all_roles(*roles: str):
    """
    Dependency factory for requiring all specified roles.

    Args:
        *roles: Variable number of role strings

    Returns:
        A dependency function that validates user has all roles
    """

    def role_checker(user: User = Depends(get_current_user)) -> User:
        """
        Validate that the user has all of the required roles.

        Args:
            user: The authenticated user

        Returns:
            User: The authenticated user if validation passes

        Raises:
            ForbiddenError: If user doesn't have all required roles
        """
        if not user.roles:
            raise ForbiddenError("User has no roles assigned")

        user_roles = set(user.roles)
        required_roles_set = set(roles)

        # Check if user has all of the required roles
        if not required_roles_set.issubset(user_roles):
            missing_roles = required_roles_set - user_roles
            raise ForbiddenError(
                f"Access denied. Missing required roles: {list(missing_roles)}. "
                f"User roles: {list(user_roles)}"
            )

        return user

    return role_checker


# Common role combinations
require_manager = require_any_role("Manager", "manager")
require_admin = require_any_role("Admin", "admin")
require_hr = require_any_role("HR", "hr", "Human Resources")
require_finance = require_any_role("Finance", "finance")
require_recruiter = require_any_role("Recruiter", "recruiter")

# Multi-role requirements
require_manager_or_admin = require_any_role("Manager", "manager", "Admin", "admin")
require_hr_or_admin = require_any_role("HR", "hr", "Human Resources", "Admin", "admin")
require_finance_or_admin = require_any_role("Finance", "finance", "Admin", "admin")


class AuthMiddleware:
    """Authentication middleware for FastAPI applications."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """Process requests through authentication middleware."""
        # For now, this is a placeholder middleware
        # In production, this would handle JWT validation, tenant extraction, etc.
        await self.app(scope, receive, send)
