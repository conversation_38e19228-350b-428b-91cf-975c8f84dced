"""
Employee Service Client

Provides typed interface for communicating with the Employee Management Service.
"""

from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel

from ..service_client import ServiceClient


class EmployeeBasic(BaseModel):
    """Basic employee information."""
    id: str
    employee_id: str
    first_name: str
    last_name: str
    email: str
    department_id: Optional[str] = None
    position: Optional[str] = None
    status: str = "active"


class EmployeeDetail(EmployeeBasic):
    """Detailed employee information."""
    phone: Optional[str] = None
    hire_date: Optional[str] = None
    manager_id: Optional[str] = None
    salary: Optional[float] = None
    employment_type: Optional[str] = None


class DepartmentInfo(BaseModel):
    """Department information."""
    id: str
    name: str
    code: str
    manager_id: Optional[str] = None


class EmployeeServiceClient:
    """Client for Employee Management Service."""

    def __init__(self, service_client: ServiceClient):
        self.client = service_client

    async def get_employee(self, employee_id: str, tenant_id: str) -> Optional[EmployeeDetail]:
        """Get employee by ID."""
        try:
            response = await self.client.get(
                f"/api/v1/employees/{employee_id}",
                headers={"X-Tenant-ID": tenant_id}
            )
            return EmployeeDetail(**response)
        except Exception:
            return None

    async def get_employee_by_email(self, email: str, tenant_id: str) -> Optional[EmployeeDetail]:
        """Get employee by email."""
        try:
            response = await self.client.get(
                "/api/v1/employees/search",
                params={"email": email},
                headers={"X-Tenant-ID": tenant_id}
            )
            if response.get("items"):
                return EmployeeDetail(**response["items"][0])
            return None
        except Exception:
            return None

    async def get_employees(
        self,
        tenant_id: str,
        department_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> List[EmployeeBasic]:
        """Get list of employees."""
        try:
            params = {"limit": limit, "offset": offset}
            if department_id:
                params["department_id"] = department_id
            if status:
                params["status"] = status

            response = await self.client.get(
                "/api/v1/employees",
                params=params,
                headers={"X-Tenant-ID": tenant_id}
            )
            
            return [EmployeeBasic(**emp) for emp in response.get("items", [])]
        except Exception:
            return []

    async def validate_employee(self, employee_id: str, tenant_id: str) -> bool:
        """Validate if employee exists and is active."""
        employee = await self.get_employee(employee_id, tenant_id)
        return employee is not None and employee.status == "active"

    async def validate_manager(self, manager_id: str, tenant_id: str) -> bool:
        """Validate if employee can be a manager."""
        employee = await self.get_employee(manager_id, tenant_id)
        if not employee:
            return False
        
        # Additional manager validation logic can be added here
        return employee.status == "active"

    async def get_employee_manager(self, employee_id: str, tenant_id: str) -> Optional[EmployeeBasic]:
        """Get employee's manager."""
        employee = await self.get_employee(employee_id, tenant_id)
        if not employee or not employee.manager_id:
            return None
        
        return await self.get_employee(employee.manager_id, tenant_id)

    async def get_department(self, department_id: str, tenant_id: str) -> Optional[DepartmentInfo]:
        """Get department information."""
        try:
            response = await self.client.get(
                f"/api/v1/departments/{department_id}",
                headers={"X-Tenant-ID": tenant_id}
            )
            return DepartmentInfo(**response)
        except Exception:
            return None

    async def get_department_employees(
        self, 
        department_id: str, 
        tenant_id: str,
        include_inactive: bool = False
    ) -> List[EmployeeBasic]:
        """Get all employees in a department."""
        status = None if include_inactive else "active"
        return await self.get_employees(
            tenant_id=tenant_id,
            department_id=department_id,
            status=status
        )

    async def check_employee_permissions(
        self,
        employee_id: str,
        tenant_id: str,
        required_permissions: List[str]
    ) -> Dict[str, bool]:
        """Check employee permissions."""
        try:
            response = await self.client.post(
                f"/api/v1/employees/{employee_id}/permissions/check",
                data={"permissions": required_permissions},
                headers={"X-Tenant-ID": tenant_id}
            )
            return response.get("permissions", {})
        except Exception:
            return {perm: False for perm in required_permissions}

    async def get_employee_hierarchy(
        self,
        employee_id: str,
        tenant_id: str,
        levels_up: int = 3,
        levels_down: int = 2
    ) -> Dict:
        """Get employee organizational hierarchy."""
        try:
            response = await self.client.get(
                f"/api/v1/employees/{employee_id}/hierarchy",
                params={"levels_up": levels_up, "levels_down": levels_down},
                headers={"X-Tenant-ID": tenant_id}
            )
            return response
        except Exception:
            return {"managers": [], "reports": []}

    async def update_employee_status(
        self,
        employee_id: str,
        tenant_id: str,
        status: str,
        reason: Optional[str] = None
    ) -> bool:
        """Update employee status."""
        try:
            data = {"status": status}
            if reason:
                data["reason"] = reason

            await self.client.patch(
                f"/api/v1/employees/{employee_id}/status",
                data=data,
                headers={"X-Tenant-ID": tenant_id}
            )
            return True
        except Exception:
            return False

    async def search_employees(
        self,
        tenant_id: str,
        query: str,
        filters: Optional[Dict] = None,
        limit: int = 20
    ) -> List[EmployeeBasic]:
        """Search employees by various criteria."""
        try:
            params = {"q": query, "limit": limit}
            if filters:
                params.update(filters)

            response = await self.client.get(
                "/api/v1/employees/search",
                params=params,
                headers={"X-Tenant-ID": tenant_id}
            )
            
            return [EmployeeBasic(**emp) for emp in response.get("items", [])]
        except Exception:
            return []

    async def get_employee_stats(self, tenant_id: str) -> Dict:
        """Get employee statistics."""
        try:
            response = await self.client.get(
                "/api/v1/employees/stats",
                headers={"X-Tenant-ID": tenant_id}
            )
            return response
        except Exception:
            return {
                "total_employees": 0,
                "active_employees": 0,
                "departments": 0,
                "new_hires_this_month": 0
            }

    async def bulk_validate_employees(
        self,
        employee_ids: List[str],
        tenant_id: str
    ) -> Dict[str, bool]:
        """Validate multiple employees at once."""
        try:
            response = await self.client.post(
                "/api/v1/employees/bulk/validate",
                data={"employee_ids": employee_ids},
                headers={"X-Tenant-ID": tenant_id}
            )
            return response.get("results", {})
        except Exception:
            return {emp_id: False for emp_id in employee_ids}


def create_employee_client(service_client: ServiceClient) -> EmployeeServiceClient:
    """Create employee service client."""
    return EmployeeServiceClient(service_client)
