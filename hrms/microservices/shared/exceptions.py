"""
Custom exceptions for oneHRMS microservices.

Provides standardized error handling across all services.
"""

from typing import Any, Dict, Optional

from fastapi import HTTPException


class HRMSException(Exception):
    """Base exception for oneHRMS applications."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(HRMSException):
    """Raised when data validation fails."""

    def __init__(self, message: str = "Validation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class AuthenticationError(HRMSException):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class AuthorizationError(HRMSException):
    """Raised when authorization fails."""

    def __init__(self, message: str = "Authorization failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class UnauthorizedError(HRMSException):
    """Raised when user is not authorized."""

    def __init__(self, message: str = "Unauthorized access", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class ForbiddenError(HRMSException):
    """Raised when access is forbidden."""

    def __init__(self, message: str = "Access forbidden", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class TenantError(HRMSException):
    """Raised when tenant-related operations fail."""

    def __init__(self, message: str = "Tenant operation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class DatabaseError(HRMSException):
    """Raised when database operations fail."""

    def __init__(self, message: str = "Database operation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class BusinessLogicError(HRMSException):
    """Raised when business logic validation fails."""

    def __init__(
        self, message: str = "Business logic validation failed", details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, details)


class NotFoundError(HRMSException):
    """Raised when requested resource is not found."""

    def __init__(self, message: str = "Resource not found", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class ConflictError(HRMSException):
    """Raised when operation conflicts with existing data."""

    def __init__(
        self,
        message: str = "Operation conflicts with existing data",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, details)


class RateLimitError(HRMSException):
    """Raised when rate limit is exceeded."""

    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


# HTTP Exception mappings
def create_http_exception(exc: HRMSException) -> HTTPException:
    """Convert HRMSException to HTTPException."""

    status_code_mapping = {
        ValidationError: 400,
        AuthenticationError: 401,
        UnauthorizedError: 401,
        AuthorizationError: 403,
        ForbiddenError: 403,
        NotFoundError: 404,
        ConflictError: 409,
        RateLimitError: 429,
        TenantError: 400,
        DatabaseError: 500,
        BusinessLogicError: 422,
        HRMSException: 500,
    }

    status_code = status_code_mapping.get(type(exc), 500)

    return HTTPException(
        status_code=status_code,
        detail={"error": exc.message, "details": exc.details, "type": type(exc).__name__},
    )
