openapi: 3.0.3
info:
  title: Payroll Management Service
  description: |
    Microservice for payroll processing, salary calculations, and payroll runs.

    This service provides comprehensive payroll management capabilities including:
    - Salary component management (earnings and deductions)
    - Salary structure configuration
    - Salary slip generation and processing
    - Payroll entry and batch processing
    - Tax calculations and deductions
    - Multi-tenant payroll isolation

    ## Authentication
    All endpoints require JWT authentication via the Authorization header.

    ## Multi-tenancy
    All operations are automatically scoped to the authenticated user's tenant.

    ## Rate Limiting
    API calls are rate-limited to prevent abuse. See response headers for current limits.
  version: 1.0.0
  contact:
    name: oneHRMS Development Team
    email: <EMAIL>
    url: https://onehrms.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8101
    description: Development server
  - url: https://api.onehrms.com
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check service health and dependencies
      operationId: healthCheck
      security: []
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"

  /api/v1/payroll/components:
    get:
      tags:
        - Salary Components
      summary: List salary components
      description: Retrieve salary components with optional filtering by type
      operationId: listSalaryComponents
      parameters:
        - name: component_type
          in: query
          description: Filter by component type
          required: false
          schema:
            $ref: "#/components/schemas/ComponentType"
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: List of salary components
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SalaryComponentResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Salary Components
      summary: Create salary component
      description: Create a new salary component
      operationId: createSalaryComponent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SalaryComponentCreate"
      responses:
        "200":
          description: Salary component created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SalaryComponentResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/payroll/components/{component_id}:
    get:
      tags:
        - Salary Components
      summary: Get salary component
      description: Retrieve salary component details by ID
      operationId: getSalaryComponent
      parameters:
        - name: component_id
          in: path
          description: Salary component ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Salary component details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SalaryComponentResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

    put:
      tags:
        - Salary Components
      summary: Update salary component
      description: Update salary component information
      operationId: updateSalaryComponent
      parameters:
        - name: component_id
          in: path
          description: Salary component ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SalaryComponentUpdate"
      responses:
        "200":
          description: Salary component updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SalaryComponentResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/payroll/salary-structures:
    get:
      tags:
        - Salary Structures
      summary: List salary structures
      description: Retrieve salary structures with optional filtering
      operationId: listSalaryStructures
      parameters:
        - name: company
          in: query
          description: Filter by company
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: List of salary structures
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SalaryStructureResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Salary Structures
      summary: Create salary structure
      description: Create a new salary structure
      operationId: createSalaryStructure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SalaryStructureCreate"
      responses:
        "200":
          description: Salary structure created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SalaryStructureResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/payroll/salary-slips:
    get:
      tags:
        - Salary Slips
      summary: List salary slips
      description: Retrieve salary slips with optional filtering
      operationId: listSalarySlips
      parameters:
        - name: employee_id
          in: query
          description: Filter by employee ID
          required: false
          schema:
            type: string
            format: uuid
        - name: start_date
          in: query
          description: Filter by start date
          required: false
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter by end date
          required: false
          schema:
            type: string
            format: date
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
      responses:
        "200":
          description: List of salary slips
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SalarySlipResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

    post:
      tags:
        - Salary Slips
      summary: Generate salary slip
      description: Generate a new salary slip for an employee
      operationId: generateSalarySlip
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SalarySlipCreate"
      responses:
        "200":
          description: Salary slip generated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SalarySlipResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "409":
          $ref: "#/components/responses/Conflict"
        "422":
          $ref: "#/components/responses/ValidationError"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/v1/payroll/statistics:
    get:
      tags:
        - Statistics
      summary: Get payroll statistics
      description: Retrieve payroll statistics and metrics
      operationId: getPayrollStatistics
      parameters:
        - name: start_date
          in: query
          description: Filter by start date
          required: false
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          description: Filter by end date
          required: false
          schema:
            type: string
            format: date
        - name: company
          in: query
          description: Filter by company
          required: false
          schema:
            type: string
        - name: department
          in: query
          description: Filter by department
          required: false
          schema:
            type: string
      responses:
        "200":
          description: Payroll statistics
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PayrollStatistics"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from Keycloak authentication

  schemas:
    ComponentType:
      type: string
      enum:
        - earning
        - deduction
      description: Salary component type

    PayrollFrequency:
      type: string
      enum:
        - monthly
        - fortnightly
        - bimonthly
        - weekly
        - daily
      description: Payroll frequency

    SalarySlipStatus:
      type: string
      enum:
        - draft
        - submitted
        - cancelled
      description: Salary slip status

    SalaryComponentCreate:
      type: object
      required:
        - name
        - abbr
        - type
      properties:
        name:
          type: string
          maxLength: 100
          description: Component name
          example: "Basic Salary"
        abbr:
          type: string
          maxLength: 20
          description: Component abbreviation
          example: "BS"
        type:
          $ref: "#/components/schemas/ComponentType"
        description:
          type: string
          nullable: true
          description: Component description
        depends_on_payment_days:
          type: boolean
          default: true
          description: Whether component depends on payment days
        is_tax_applicable:
          type: boolean
          default: true
          description: Whether component is tax applicable
        is_flexible_benefit:
          type: boolean
          default: false
          description: Whether component is a flexible benefit
        amount:
          type: number
          format: decimal
          minimum: 0
          default: 0
          description: Component amount
        is_active:
          type: boolean
          default: true
          description: Whether component is active

    SalaryComponentUpdate:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
          description: Component name
        abbr:
          type: string
          maxLength: 20
          description: Component abbreviation
        description:
          type: string
          nullable: true
          description: Component description
        depends_on_payment_days:
          type: boolean
          description: Whether component depends on payment days
        is_tax_applicable:
          type: boolean
          description: Whether component is tax applicable
        is_flexible_benefit:
          type: boolean
          description: Whether component is a flexible benefit
        amount:
          type: number
          format: decimal
          minimum: 0
          description: Component amount
        is_active:
          type: boolean
          description: Whether component is active

    SalaryComponentResponse:
      allOf:
        - $ref: "#/components/schemas/SalaryComponentCreate"
        - type: object
          required:
            - id
            - tenant_id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Component unique ID
            tenant_id:
              type: string
              description: Tenant ID
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp

    SalaryStructureCreate:
      type: object
      required:
        - name
        - company
      properties:
        name:
          type: string
          maxLength: 100
          description: Structure name
          example: "Software Engineer Structure"
        company:
          type: string
          maxLength: 100
          description: Company name
          example: "Tech Corp"
        currency:
          type: string
          maxLength: 10
          default: "USD"
          description: Currency code
        payroll_frequency:
          $ref: "#/components/schemas/PayrollFrequency"
        is_active:
          type: boolean
          default: true
          description: Whether structure is active
        earnings:
          type: array
          items:
            $ref: "#/components/schemas/SalaryStructureDetail"
          description: Earning components
        deductions:
          type: array
          items:
            $ref: "#/components/schemas/SalaryStructureDetail"
          description: Deduction components

    SalaryStructureDetail:
      type: object
      required:
        - salary_component_id
        - component_type
      properties:
        salary_component_id:
          type: string
          format: uuid
          description: Salary component ID
        component_type:
          $ref: "#/components/schemas/ComponentType"
        amount:
          type: number
          format: decimal
          minimum: 0
          default: 0
          description: Component amount

    SalaryStructureResponse:
      allOf:
        - $ref: "#/components/schemas/SalaryStructureCreate"
        - type: object
          required:
            - id
            - tenant_id
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Structure unique ID
            tenant_id:
              type: string
              description: Tenant ID
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp

    SalarySlipCreate:
      type: object
      required:
        - employee_id
        - employee_name
        - company
        - payroll_frequency
        - start_date
        - end_date
        - salary_structure_id
      properties:
        employee_id:
          type: string
          format: uuid
          description: Employee ID
        employee_name:
          type: string
          maxLength: 100
          description: Employee name
          example: "John Doe"
        company:
          type: string
          maxLength: 100
          description: Company name
        department:
          type: string
          maxLength: 100
          nullable: true
          description: Department name
        designation:
          type: string
          maxLength: 100
          nullable: true
          description: Employee designation
        payroll_frequency:
          $ref: "#/components/schemas/PayrollFrequency"
        start_date:
          type: string
          format: date
          description: Payroll period start date
        end_date:
          type: string
          format: date
          description: Payroll period end date
        salary_structure_id:
          type: string
          format: uuid
          description: Salary structure ID
        total_working_days:
          type: integer
          minimum: 0
          default: 0
          description: Total working days in period
        payment_days:
          type: number
          format: decimal
          minimum: 0
          default: 0
          description: Payment days
        currency:
          type: string
          maxLength: 10
          default: "USD"
          description: Currency code

    SalarySlipResponse:
      allOf:
        - $ref: "#/components/schemas/SalarySlipCreate"
        - type: object
          required:
            - id
            - tenant_id
            - gross_pay
            - total_deduction
            - net_pay
            - status
            - created_at
            - updated_at
          properties:
            id:
              type: string
              format: uuid
              description: Salary slip unique ID
            tenant_id:
              type: string
              description: Tenant ID
            gross_pay:
              type: number
              format: decimal
              description: Gross pay amount
            total_deduction:
              type: number
              format: decimal
              description: Total deduction amount
            net_pay:
              type: number
              format: decimal
              description: Net pay amount
            status:
              $ref: "#/components/schemas/SalarySlipStatus"
            created_at:
              type: string
              format: date-time
              description: Creation timestamp
            updated_at:
              type: string
              format: date-time
              description: Last update timestamp

    PayrollStatistics:
      type: object
      required:
        - total_slips
        - total_gross_pay
        - total_net_pay
        - by_status
      properties:
        total_slips:
          type: integer
          description: Total number of salary slips
        total_gross_pay:
          type: number
          format: decimal
          description: Total gross pay amount
        total_net_pay:
          type: number
          format: decimal
          description: Total net pay amount
        by_status:
          type: object
          additionalProperties:
            type: integer
          description: Salary slip count by status

    HealthStatus:
      type: object
      required:
        - status
        - timestamp
        - version
        - service
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: Service health status
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
        version:
          type: string
          description: Service version
        service:
          type: string
          description: Service name
        dependencies:
          type: object
          description: Dependency health status

    SuccessResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          example: true
          description: Operation success status
        message:
          type: string
          example: "Operation completed successfully"
          description: Success message

    ErrorResponse:
      type: object
      required:
        - success
        - error
        - timestamp
      properties:
        success:
          type: boolean
          example: false
          description: Operation success status
        error:
          type: string
          description: Error message
        details:
          type: object
          nullable: true
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

tags:
  - name: Health
    description: Health check endpoints
  - name: Salary Components
    description: Salary component management operations
  - name: Salary Structures
    description: Salary structure management operations
  - name: Salary Slips
    description: Salary slip generation and processing
  - name: Statistics
    description: Payroll statistics and reporting
