"""
Payroll Management Microservice

Comprehensive payroll processing service for salary calculations, tax processing,
deductions, and payroll runs.

Features:
- Salary structure management
- Salary slip generation and processing
- Tax calculations and deductions
- Payroll entry and batch processing
- Employee benefit management
- Multi-tenant payroll isolation
- Integration with Employee service

API Endpoints:
- GET /api/v1/payroll/salary-structures - List salary structures
- POST /api/v1/payroll/salary-structures - Create salary structure
- GET /api/v1/payroll/salary-slips - List salary slips
- POST /api/v1/payroll/salary-slips - Generate salary slip
- GET /api/v1/payroll/payroll-entries - List payroll entries
- POST /api/v1/payroll/payroll-entries - Create payroll run
- GET /api/v1/payroll/components - List salary components
- POST /api/v1/payroll/components - Create salary component
"""

__version__ = "1.0.0"
__service_name__ = "payroll-service"
__service_port__ = 8101
