# Payroll Management Microservice

A comprehensive payroll processing service providing salary calculation, tax management, and compensation administration for the oneHRMS platform.

## Overview

The Payroll Management microservice handles all aspects of employee compensation and payroll processing:

- Salary and wage calculations
- Tax deductions and compliance
- Benefits and allowances management
- Payroll run processing and reporting
- Multi-tenant payroll isolation
- Integration with employee and attendance data

## Features

### 💰 Salary Management
- Base salary and hourly wage configuration
- Salary history tracking and adjustments
- Performance-based compensation
- Commission and bonus calculations
- Overtime and holiday pay processing

### 🧾 Payroll Processing
- Automated payroll run execution
- Pay period management (weekly, bi-weekly, monthly)
- Gross and net pay calculations
- Payslip generation and distribution
- Payroll approval workflows

### 📊 Tax & Deductions
- Federal and state tax calculations
- Social Security and Medicare deductions
- Health insurance and benefits deductions
- Retirement plan contributions
- Custom deduction management

### 📈 Reporting & Analytics
- Payroll summary reports
- Tax liability reports
- Employee compensation analysis
- Department payroll costs
- Year-end tax reporting (W-2, 1099)

## API Endpoints

### Salary Management
- `GET /api/v1/payroll/salaries` - List employee salaries
- `POST /api/v1/payroll/salaries` - Create salary record
- `GET /api/v1/payroll/salaries/{id}` - Get salary details
- `PUT /api/v1/payroll/salaries/{id}` - Update salary information
- `GET /api/v1/payroll/employees/{id}/salary-history` - Get salary history

### Payroll Runs
- `GET /api/v1/payroll/runs` - List payroll runs
- `POST /api/v1/payroll/runs` - Create new payroll run
- `GET /api/v1/payroll/runs/{id}` - Get payroll run details
- `POST /api/v1/payroll/runs/{id}/process` - Process payroll run
- `POST /api/v1/payroll/runs/{id}/approve` - Approve payroll run

### Payslips
- `GET /api/v1/payroll/payslips` - List payslips
- `GET /api/v1/payroll/payslips/{id}` - Get payslip details
- `GET /api/v1/payroll/employees/{id}/payslips` - Get employee payslips
- `POST /api/v1/payroll/payslips/{id}/send` - Send payslip to employee

### Tax & Deductions
- `GET /api/v1/payroll/tax-settings` - Get tax configuration
- `PUT /api/v1/payroll/tax-settings` - Update tax settings
- `GET /api/v1/payroll/deductions` - List deduction types
- `POST /api/v1/payroll/deductions` - Create deduction type

## Architecture

### Service Structure
```
hrms/microservices/payroll/
├── __init__.py          # Service package initialization
├── api.py               # FastAPI application and endpoints
├── service.py           # Business logic layer
├── repository.py        # Data access layer
├── models.py            # Pydantic models and database schemas
├── openapi.yaml         # OpenAPI specification
├── Dockerfile           # Multi-stage Docker configuration
└── README.md            # This documentation
```

### Database Schema

#### Salaries
- Employee salary records with effective dates
- Base salary, hourly rates, and commission structures
- Salary adjustment history and approvals
- Multi-tenant isolation

#### Payroll Runs
- Payroll processing batches by pay period
- Status tracking and approval workflows
- Calculation results and summaries
- Error handling and reprocessing

#### Payslips
- Individual employee pay statements
- Detailed earnings and deductions breakdown
- Tax withholdings and net pay calculations
- Digital delivery and access tracking

#### Tax Settings
- Federal and state tax tables
- Deduction configurations and limits
- Benefits and allowance settings
- Compliance rules and calculations

## Business Rules

### Salary Management
- Salary changes require approval workflow
- Historical salary data preservation
- Minimum wage compliance validation
- Overtime calculation rules

### Payroll Processing
- Pay period validation and cutoff dates
- Attendance integration for hourly employees
- Tax calculation accuracy requirements
- Approval before payment processing

### Tax Compliance
- Current tax table maintenance
- Deduction limit enforcement
- Year-end reporting requirements
- Multi-state tax handling

## Multi-Tenancy

All payroll data is strictly isolated by tenant:
- Separate payroll configurations per tenant
- Tenant-specific tax settings and rules
- Isolated employee salary data
- Independent payroll run processing

## Authentication & Authorization

- JWT-based authentication required for all endpoints
- Role-based access control (Payroll Admin, HR Manager, Employee)
- Sensitive payroll data protection
- Mocked authentication for development environment

## Development

### Running the Service

```bash
# Development with hot reload
uvicorn hrms.microservices.payroll.api:app --host 0.0.0.0 --port 8101 --reload

# Production
gunicorn hrms.microservices.payroll.api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8101
```

### Docker

```bash
# Build development image
docker build --target development -t payroll-service:dev .

# Build production image
docker build --target production -t payroll-service:prod .

# Run container
docker run -p 8101:8101 -e DATABASE_URL=postgresql://... payroll-service:dev
```

### Testing

```bash
# Run all tests
pytest tests/microservices/payroll/ -v

# Run with coverage
pytest tests/microservices/payroll/ --cov=hrms.microservices.payroll --cov-report=html

# Run specific test file
pytest tests/microservices/payroll/test_service.py -v
```

## Configuration

### Environment Variables

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET_KEY` - JWT signing key (mocked in dev)
- `TAX_API_KEY` - External tax service API key
- `ENCRYPTION_KEY` - Salary data encryption key
- `PAYROLL_SCHEDULE` - Default payroll frequency

### Feature Flags

```python
FEATURES = {
    "salary_management": True,
    "payroll_processing": True,
    "tax_calculations": True,
    "benefits_deductions": True,
    "overtime_processing": True,
    "commission_tracking": True,
    "year_end_reporting": True,
}
```

## Integration

### Kong API Gateway
- Routes configured in `/kong/kong.yml`
- CORS enabled for frontend integration
- JWT authentication (disabled in development)

### Other Services
- **Employee Service**: Employee data and organizational structure
- **Attendance Service**: Time tracking for hourly pay calculations
- **ESS Service**: Employee payslip access and tax form downloads

### External Integrations
- Tax calculation services (future)
- Banking and payment processors (future)
- Accounting system integration (future)

## Calculations

### Gross Pay Calculation
- Base salary for salaried employees
- Hourly rate × hours worked for hourly employees
- Overtime premium calculations
- Commission and bonus additions

### Tax Withholdings
- Federal income tax based on W-4 information
- State income tax (if applicable)
- Social Security (6.2% up to wage base)
- Medicare (1.45% + additional 0.9% for high earners)

### Deductions
- Pre-tax deductions (health insurance, retirement)
- Post-tax deductions (garnishments, union dues)
- Voluntary deductions (parking, meals)

## Security

### Data Protection
- Salary data encryption at rest and in transit
- PCI compliance for payment processing
- Access logging and audit trails
- Role-based data access controls

### Compliance
- SOX compliance for financial reporting
- Tax regulation compliance
- Data retention policies
- Privacy protection for sensitive data

## Performance

### Optimizations
- Batch processing for large payroll runs
- Async calculations for complex scenarios
- Caching for tax tables and configurations
- Efficient database queries and indexing

### Scalability
- Horizontal scaling for payroll processing
- Queue-based processing for large datasets
- Database partitioning by tenant and date
- Load balancing for concurrent operations

## Monitoring & Logging

- Structured logging with correlation IDs
- Health check endpoint at `/health`
- Payroll processing metrics and alerts
- Error tracking and notification
- Performance monitoring and optimization

## Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Encryption keys configured
- [ ] Kong routes deployed
- [ ] Health checks enabled
- [ ] Monitoring configured
- [ ] Backup procedures in place
- [ ] Tax tables updated

### Scaling
- Horizontal scaling supported
- Stateless service design
- Database connection pooling
- Queue processing for batch operations

## Support

For technical support or questions:
- Check the API documentation at `/docs`
- Review test cases for usage examples
- Consult the OpenAPI specification
- Contact the development team

## Version History

- **v1.0.0** - Initial release with core payroll functionality
- Complete salary management system
- Payroll run processing and approval
- Tax calculation and deduction management
- Payslip generation and distribution
- Multi-tenant architecture
- Comprehensive test coverage
