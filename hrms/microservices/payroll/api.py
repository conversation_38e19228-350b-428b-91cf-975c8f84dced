"""
Payroll service FastAPI application.

Provides REST API endpoints for payroll management with OpenAPI documentation.
"""

from contextlib import asynccontextmanager
from datetime import date
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse

from ..shared.auth import Tenant, User, get_current_tenant, get_current_user
from ..shared.database import db_manager
from ..shared.exceptions import HRMSException, create_http_exception
from ..shared.logging import get_logger, get_request_logger, setup_logging
from ..shared.models import ErrorResponse, HealthStatus, PaginatedResponse, SuccessResponse
from ..shared.utils import paginate_query_params
from .models import (
    ComponentType,
    PayrollEntryCreate,
    PayrollEntryResponse,
    SalaryComponentCreate,
    SalaryComponentResponse,
    SalaryComponentUpdate,
    SalarySlipCreate,
    SalarySlipResponse,
    SalaryStructure<PERSON><PERSON>,
    SalaryStructureResponse,
    SalaryStructureUpdate,
)
from .service import PayrollService

# Setup logging
setup_logging("payroll-service")
logger = get_logger(__name__)
request_logger = get_request_logger("payroll-service")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Payroll Management Service")

    # Create database tables
    db_manager.create_tables()

    logger.info("Payroll Management Service started successfully")

    yield

    # Shutdown
    logger.info("Shutting down Payroll Management Service")


# Create FastAPI app
app = FastAPI(
    title="Payroll Management Service",
    description="Microservice for payroll processing, salary calculations, and payroll runs",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Initialize service
payroll_service = PayrollService(db_manager)


# Exception handler
@app.exception_handler(HRMSException)
async def hrms_exception_handler(request, exc: HRMSException):
    """Handle HRMS exceptions."""
    http_exc = create_http_exception(exc)
    return JSONResponse(status_code=http_exc.status_code, content=http_exc.detail)


# Health check endpoint
@app.get("/health", response_model=HealthStatus, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    db_health = await db_manager.health_check()

    return HealthStatus(service="payroll-service", dependencies={"database": db_health})


# Salary Component endpoints
@app.post("/api/v1/payroll/components", response_model=SalaryComponentResponse, tags=["Salary Components"])
async def create_salary_component(
    component_data: SalaryComponentCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new salary component."""
    try:
        result = await payroll_service.create_salary_component(tenant.id, component_data, user.id)
        return result
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/components", response_model=List[SalaryComponentResponse], tags=["Salary Components"]
)
async def list_salary_components(
    component_type: Optional[ComponentType] = Query(None, description="Filter by component type"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List salary components with filtering."""
    try:
        pagination = paginate_query_params(page, size)

        components = await payroll_service.list_salary_components(
            tenant.id, component_type, pagination["skip"], pagination["limit"]
        )

        return components
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/components/{component_id}",
    response_model=SalaryComponentResponse,
    tags=["Salary Components"],
)
async def get_salary_component(
    component_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get salary component by ID."""
    try:
        return await payroll_service.get_salary_component(tenant.id, component_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.put(
    "/api/v1/payroll/components/{component_id}",
    response_model=SalaryComponentResponse,
    tags=["Salary Components"],
)
async def update_salary_component(
    component_id: str,
    component_data: SalaryComponentUpdate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Update salary component."""
    try:
        return await payroll_service.update_salary_component(tenant.id, component_id, component_data, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Salary Structure endpoints
@app.post(
    "/api/v1/payroll/salary-structures", response_model=SalaryStructureResponse, tags=["Salary Structures"]
)
async def create_salary_structure(
    structure_data: SalaryStructureCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new salary structure."""
    try:
        result = await payroll_service.create_salary_structure(tenant.id, structure_data, user.id)
        return result
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/salary-structures",
    response_model=List[SalaryStructureResponse],
    tags=["Salary Structures"],
)
async def list_salary_structures(
    company: Optional[str] = Query(None, description="Filter by company"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List salary structures with filtering."""
    try:
        pagination = paginate_query_params(page, size)

        structures = await payroll_service.list_salary_structures(
            tenant.id, company, pagination["skip"], pagination["limit"]
        )

        return structures
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/salary-structures/{structure_id}",
    response_model=SalaryStructureResponse,
    tags=["Salary Structures"],
)
async def get_salary_structure(
    structure_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get salary structure by ID."""
    try:
        return await payroll_service.get_salary_structure(tenant.id, structure_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Salary Slip endpoints
@app.post("/api/v1/payroll/salary-slips", response_model=SalarySlipResponse, tags=["Salary Slips"])
async def generate_salary_slip(
    slip_data: SalarySlipCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Generate a new salary slip."""
    try:
        result = await payroll_service.generate_salary_slip(tenant.id, slip_data, user.id)
        return result
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/payroll/salary-slips", response_model=List[SalarySlipResponse], tags=["Salary Slips"])
async def list_salary_slips(
    employee_id: Optional[str] = Query(None, description="Filter by employee ID"),
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List salary slips with filtering."""
    try:
        pagination = paginate_query_params(page, size)

        slips = await payroll_service.list_salary_slips(
            tenant.id, employee_id, start_date, end_date, pagination["skip"], pagination["limit"]
        )

        return slips
    except HRMSException as e:
        raise create_http_exception(e)


@app.get("/api/v1/payroll/salary-slips/{slip_id}", response_model=SalarySlipResponse, tags=["Salary Slips"])
async def get_salary_slip(
    slip_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get salary slip by ID."""
    try:
        return await payroll_service.get_salary_slip(tenant.id, slip_id)
    except HRMSException as e:
        raise create_http_exception(e)


@app.post(
    "/api/v1/payroll/salary-slips/{slip_id}/submit", response_model=SalarySlipResponse, tags=["Salary Slips"]
)
async def submit_salary_slip(
    slip_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Submit salary slip."""
    try:
        return await payroll_service.submit_salary_slip(tenant.id, slip_id, user.id)
    except HRMSException as e:
        raise create_http_exception(e)


# Payroll Entry endpoints
@app.post("/api/v1/payroll/payroll-entries", response_model=PayrollEntryResponse, tags=["Payroll Entries"])
async def create_payroll_entry(
    entry_data: PayrollEntryCreate,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new payroll entry."""
    try:
        result = await payroll_service.create_payroll_entry(tenant.id, entry_data, user.id)
        return result
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/payroll-entries", response_model=List[PayrollEntryResponse], tags=["Payroll Entries"]
)
async def list_payroll_entries(
    company: Optional[str] = Query(None, description="Filter by company"),
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """List payroll entries with filtering."""
    try:
        pagination = paginate_query_params(page, size)

        entries = await payroll_service.list_payroll_entries(
            tenant.id, company, start_date, end_date, pagination["skip"], pagination["limit"]
        )

        return entries
    except HRMSException as e:
        raise create_http_exception(e)


@app.get(
    "/api/v1/payroll/payroll-entries/{entry_id}",
    response_model=PayrollEntryResponse,
    tags=["Payroll Entries"],
)
async def get_payroll_entry(
    entry_id: str, user: User = Depends(get_current_user), tenant: Tenant = Depends(get_current_tenant)
):
    """Get payroll entry by ID."""
    try:
        return await payroll_service.get_payroll_entry(tenant.id, entry_id)
    except HRMSException as e:
        raise create_http_exception(e)


# Statistics endpoint
@app.get("/api/v1/payroll/statistics", response_model=Dict[str, Any], tags=["Statistics"])
async def get_payroll_statistics(
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    company: Optional[str] = Query(None, description="Filter by company"),
    department: Optional[str] = Query(None, description="Filter by department"),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Get payroll statistics."""
    try:
        filters = {}
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if company:
            filters["company"] = company
        if department:
            filters["department"] = department

        return await payroll_service.get_payroll_statistics(tenant.id, filters)
    except HRMSException as e:
        raise create_http_exception(e)


# Application lifecycle is now handled by the lifespan context manager above


if __name__ == "__main__":
    uvicorn.run("api:app", host="0.0.0.0", port=8101, reload=True, log_level="info")
