"""
Payroll service business logic layer.

Handles all business operations for payroll management including salary calculations,
tax processing, and payroll runs.
"""

import calendar
import os
from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from ..shared.database import DatabaseManager
from ..shared.exceptions import BusinessLogicError, ConflictError, NotFoundError, ValidationError
from ..shared.logging import get_logger
from ..shared.utils import generate_id, normalize_currency
from .models import (
    ComponentType,
    PayrollEntry,
    PayrollEntryCreate,
    PayrollEntryResponse,
    PayrollEntryStatus,
    PayrollFrequency,
    SalaryComponent,
    SalaryComponentCreate,
    SalaryComponentResponse,
    SalaryComponentUpdate,
    SalarySlip,
    SalarySlipCreate,
    SalarySlipResponse,
    SalarySlipStatus,
    SalaryStructure,
    SalaryStructureCreate,
    SalaryStructureResponse,
    SalaryStructureUpdate,
)
from .repository import (
    PayrollEntryRepository,
    SalaryComponentRepository,
    SalaryDetailRepository,
    SalarySlipRepository,
    SalaryStructureRepository,
)

logger = get_logger(__name__)


class PayrollService:
    """Payroll management service."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.component_repo = SalaryComponentRepository(db_manager)
        self.structure_repo = SalaryStructureRepository(db_manager)
        self.slip_repo = SalarySlipRepository(db_manager)
        self.entry_repo = PayrollEntryRepository(db_manager)
        self.detail_repo = SalaryDetailRepository(db_manager)

    # Salary Component operations
    async def create_salary_component(
        self, tenant_id: str, component_data: SalaryComponentCreate, created_by: str
    ) -> SalaryComponentResponse:
        """Create new salary component."""
        logger.info(f"Creating salary component for tenant {tenant_id}")

        # Check for duplicates
        if await self.component_repo.check_name_exists(tenant_id, component_data.name):
            raise ConflictError(f"Salary component name '{component_data.name}' already exists")

        if await self.component_repo.check_abbr_exists(tenant_id, component_data.abbr):
            raise ConflictError(f"Salary component abbreviation '{component_data.abbr}' already exists")

        # Validate business rules
        await self._validate_component_creation(tenant_id, component_data)

        # Create component
        component_dict = component_data.dict()
        component_dict["created_by"] = created_by

        component = await self.component_repo.create(tenant_id, component_dict)

        logger.info(f"Salary component created successfully: {component.id}")
        return SalaryComponentResponse.from_orm(component)

    async def get_salary_component(self, tenant_id: str, component_id: str) -> SalaryComponentResponse:
        """Get salary component by ID."""
        component = await self.component_repo.get_by_id(tenant_id, component_id)
        if not component:
            raise NotFoundError(f"Salary component not found: {component_id}")

        return SalaryComponentResponse.from_orm(component)

    async def update_salary_component(
        self, tenant_id: str, component_id: str, component_data: SalaryComponentUpdate, updated_by: str
    ) -> SalaryComponentResponse:
        """Update salary component."""
        logger.info(f"Updating salary component {component_id} for tenant {tenant_id}")

        # Get existing component
        component = await self.component_repo.get_by_id(tenant_id, component_id)
        if not component:
            raise NotFoundError(f"Salary component not found: {component_id}")

        # Check for duplicates (excluding current component)
        if component_data.name and await self.component_repo.check_name_exists(
            tenant_id, component_data.name, component_id
        ):
            raise ConflictError(f"Salary component name '{component_data.name}' already exists")

        if component_data.abbr and await self.component_repo.check_abbr_exists(
            tenant_id, component_data.abbr, component_id
        ):
            raise ConflictError(f"Salary component abbreviation '{component_data.abbr}' already exists")

        # Update component
        update_dict = component_data.dict(exclude_unset=True)
        update_dict["updated_by"] = updated_by

        updated_component = await self.component_repo.update(tenant_id, component_id, update_dict)

        logger.info(f"Salary component updated successfully: {component_id}")
        return SalaryComponentResponse.from_orm(updated_component)

    async def list_salary_components(
        self, tenant_id: str, component_type: Optional[ComponentType] = None, skip: int = 0, limit: int = 100
    ) -> List[SalaryComponentResponse]:
        """List salary components with filtering."""
        if component_type:
            components = await self.component_repo.get_by_type(tenant_id, component_type)
        else:
            components = await self.component_repo.list_by_tenant(tenant_id, skip, limit)

        return [SalaryComponentResponse.from_orm(comp) for comp in components]

    # Salary Structure operations
    async def create_salary_structure(
        self, tenant_id: str, structure_data: SalaryStructureCreate, created_by: str
    ) -> SalaryStructureResponse:
        """Create new salary structure."""
        logger.info(f"Creating salary structure for tenant {tenant_id}")

        # Check for duplicates
        if await self.structure_repo.check_name_exists(tenant_id, structure_data.name):
            raise ConflictError(f"Salary structure name '{structure_data.name}' already exists")

        # Validate business rules
        await self._validate_structure_creation(tenant_id, structure_data)

        # Create structure
        structure_dict = structure_data.dict(exclude={"earnings", "deductions"})
        structure_dict["created_by"] = created_by

        structure = await self.structure_repo.create(tenant_id, structure_dict)

        # Create earnings and deductions
        await self._create_structure_details(tenant_id, structure.id, structure_data, created_by)

        logger.info(f"Salary structure created successfully: {structure.id}")
        return SalaryStructureResponse.from_orm(structure)

    async def get_salary_structure(self, tenant_id: str, structure_id: str) -> SalaryStructureResponse:
        """Get salary structure by ID."""
        structure = await self.structure_repo.get_with_details(tenant_id, structure_id)
        if not structure:
            raise NotFoundError(f"Salary structure not found: {structure_id}")

        return SalaryStructureResponse.from_orm(structure)

    async def list_salary_structures(
        self, tenant_id: str, company: Optional[str] = None, skip: int = 0, limit: int = 100
    ) -> List[SalaryStructureResponse]:
        """List salary structures with filtering."""
        filters = {}
        if company:
            filters["company"] = company

        structures = await self.structure_repo.list_by_tenant(tenant_id, skip, limit, filters)
        return [SalaryStructureResponse.from_orm(struct) for struct in structures]

    # Salary Slip operations
    async def generate_salary_slip(
        self, tenant_id: str, slip_data: SalarySlipCreate, created_by: str
    ) -> SalarySlipResponse:
        """Generate salary slip for employee."""
        logger.info(f"Generating salary slip for employee {slip_data.employee_id}")

        # Check for existing slip in the same period
        existing_slip = await self.slip_repo.get_by_employee_and_period(
            tenant_id, slip_data.employee_id, slip_data.start_date, slip_data.end_date
        )
        if existing_slip:
            raise ConflictError(
                f"Salary slip already exists for employee {slip_data.employee_id} "
                f"for period {slip_data.start_date} to {slip_data.end_date}"
            )

        # Get salary structure
        structure = await self.structure_repo.get_with_details(tenant_id, slip_data.salary_structure_id)
        if not structure:
            raise ValidationError("Salary structure not found")

        # Validate business rules
        await self._validate_slip_creation(tenant_id, slip_data)

        # Create salary slip
        slip_dict = slip_data.dict()
        slip_dict["created_by"] = created_by
        slip_dict["status"] = SalarySlipStatus.DRAFT

        # Calculate working days if not provided
        if slip_dict["total_working_days"] == 0:
            slip_dict["total_working_days"] = self._calculate_working_days(
                slip_data.start_date, slip_data.end_date
            )

        # Set payment days if not provided
        if slip_dict["payment_days"] == 0:
            slip_dict["payment_days"] = slip_dict["total_working_days"] - slip_data.leave_without_pay

        slip = await self.slip_repo.create(tenant_id, slip_dict)

        # Calculate salary components
        await self._calculate_salary_components(tenant_id, slip, structure, created_by)

        # Calculate totals
        await self._calculate_salary_totals(tenant_id, slip.id)

        # Get updated slip with calculations
        updated_slip = await self.slip_repo.get_with_details(tenant_id, slip.id)

        logger.info(f"Salary slip generated successfully: {slip.id}")
        return SalarySlipResponse.from_orm(updated_slip)

    async def get_salary_slip(self, tenant_id: str, slip_id: str) -> SalarySlipResponse:
        """Get salary slip by ID."""
        slip = await self.slip_repo.get_with_details(tenant_id, slip_id)
        if not slip:
            raise NotFoundError(f"Salary slip not found: {slip_id}")

        return SalarySlipResponse.from_orm(slip)

    async def list_salary_slips(
        self,
        tenant_id: str,
        employee_id: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[SalarySlipResponse]:
        """List salary slips with filtering."""
        if employee_id:
            slips = await self.slip_repo.get_by_employee(tenant_id, employee_id, skip, limit)
        else:
            filters = {}
            if start_date:
                filters["start_date"] = start_date
            if end_date:
                filters["end_date"] = end_date

            slips = await self.slip_repo.list_by_tenant(tenant_id, skip, limit, filters)

        return [SalarySlipResponse.from_orm(slip) for slip in slips]

    async def submit_salary_slip(self, tenant_id: str, slip_id: str, submitted_by: str) -> SalarySlipResponse:
        """Submit salary slip."""
        slip = await self.slip_repo.get_by_id(tenant_id, slip_id)
        if not slip:
            raise NotFoundError(f"Salary slip not found: {slip_id}")

        if slip.status != SalarySlipStatus.DRAFT:
            raise BusinessLogicError(f"Cannot submit salary slip in {slip.status} status")

        # Update status
        update_dict = {"status": SalarySlipStatus.SUBMITTED, "updated_by": submitted_by}

        updated_slip = await self.slip_repo.update(tenant_id, slip_id, update_dict)

        logger.info(f"Salary slip submitted successfully: {slip_id}")
        return SalarySlipResponse.from_orm(updated_slip)

    # Payroll Entry operations
    async def create_payroll_entry(
        self, tenant_id: str, entry_data: PayrollEntryCreate, created_by: str
    ) -> PayrollEntryResponse:
        """Create new payroll entry."""
        logger.info(f"Creating payroll entry for tenant {tenant_id}")

        # Check for duplicates
        if await self.entry_repo.check_name_exists(tenant_id, entry_data.name):
            raise ConflictError(f"Payroll entry name '{entry_data.name}' already exists")

        # Validate business rules
        await self._validate_entry_creation(tenant_id, entry_data)

        # Create entry
        entry_dict = entry_data.dict()
        entry_dict["created_by"] = created_by
        entry_dict["status"] = PayrollEntryStatus.DRAFT
        entry_dict["number_of_employees"] = 0

        entry = await self.entry_repo.create(tenant_id, entry_dict)

        logger.info(f"Payroll entry created successfully: {entry.id}")
        return PayrollEntryResponse.from_orm(entry)

    async def get_payroll_entry(self, tenant_id: str, entry_id: str) -> PayrollEntryResponse:
        """Get payroll entry by ID."""
        entry = await self.entry_repo.get_with_salary_slips(tenant_id, entry_id)
        if not entry:
            raise NotFoundError(f"Payroll entry not found: {entry_id}")

        return PayrollEntryResponse.from_orm(entry)

    async def list_payroll_entries(
        self,
        tenant_id: str,
        company: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[PayrollEntryResponse]:
        """List payroll entries with filtering."""
        if start_date and end_date:
            entries = await self.entry_repo.get_by_period(tenant_id, start_date, end_date, company)
        else:
            filters = {}
            if company:
                filters["company"] = company

            entries = await self.entry_repo.list_by_tenant(tenant_id, skip, limit, filters)

        return [PayrollEntryResponse.from_orm(entry) for entry in entries]

    async def get_payroll_statistics(
        self, tenant_id: str, filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get payroll statistics."""
        return await self.slip_repo.get_statistics(tenant_id, filters)

    # Private helper methods
    async def _validate_component_creation(self, tenant_id: str, component_data: SalaryComponentCreate):
        """Validate salary component creation business rules."""
        # Validate formula if provided
        if component_data.formula:
            self._validate_formula(component_data.formula)

        # Validate condition if provided
        if component_data.condition:
            self._validate_condition(component_data.condition)

        # Validate amount
        if component_data.amount < 0:
            raise ValidationError("Component amount cannot be negative")

    async def _validate_structure_creation(self, tenant_id: str, structure_data: SalaryStructureCreate):
        """Validate salary structure creation business rules."""
        # Validate earnings and deductions components exist
        for earning in structure_data.earnings:
            component = await self.component_repo.get_by_id(tenant_id, earning.salary_component_id)
            if not component:
                raise ValidationError(f"Salary component not found: {earning.salary_component_id}")
            if component.type != ComponentType.EARNING:
                raise ValidationError(f"Component {component.name} is not an earning component")

        for deduction in structure_data.deductions:
            component = await self.component_repo.get_by_id(tenant_id, deduction.salary_component_id)
            if not component:
                raise ValidationError(f"Salary component not found: {deduction.salary_component_id}")
            if component.type != ComponentType.DEDUCTION:
                raise ValidationError(f"Component {component.name} is not a deduction component")

    async def _validate_slip_creation(self, tenant_id: str, slip_data: SalarySlipCreate):
        """Validate salary slip creation business rules."""
        # Validate dates
        if slip_data.start_date >= slip_data.end_date:
            raise ValidationError("Start date must be before end date")

        if slip_data.posting_date < slip_data.start_date:
            raise ValidationError("Posting date cannot be before start date")

        # Validate working days
        if slip_data.leave_without_pay > slip_data.payment_days:
            raise ValidationError("Leave without pay cannot exceed payment days")

        # Validate timesheet details
        if slip_data.salary_slip_based_on_timesheet:
            if slip_data.total_working_hours <= 0:
                raise ValidationError("Total working hours must be greater than 0 for timesheet-based slips")
            if slip_data.hour_rate <= 0:
                raise ValidationError("Hour rate must be greater than 0 for timesheet-based slips")

    async def _validate_entry_creation(self, tenant_id: str, entry_data: PayrollEntryCreate):
        """Validate payroll entry creation business rules."""
        # Validate dates
        if entry_data.start_date >= entry_data.end_date:
            raise ValidationError("Start date must be before end date")

        if entry_data.posting_date < entry_data.start_date:
            raise ValidationError("Posting date cannot be before start date")

    def _validate_formula(self, formula: str):
        """Validate salary component formula."""
        # Basic validation - in production, implement proper formula parsing
        if not formula.strip():
            raise ValidationError("Formula cannot be empty")

        # Check for dangerous operations
        dangerous_keywords = ["import", "exec", "eval", "__", "open", "file"]
        for keyword in dangerous_keywords:
            if keyword in formula.lower():
                raise ValidationError(f"Formula contains dangerous keyword: {keyword}")

    def _validate_condition(self, condition: str):
        """Validate salary component condition."""
        # Basic validation - in production, implement proper condition parsing
        if not condition.strip():
            raise ValidationError("Condition cannot be empty")

        # Check for dangerous operations
        dangerous_keywords = ["import", "exec", "eval", "__", "open", "file"]
        for keyword in dangerous_keywords:
            if keyword in condition.lower():
                raise ValidationError(f"Condition contains dangerous keyword: {keyword}")

    def _calculate_working_days(self, start_date: date, end_date: date) -> int:
        """Calculate working days between two dates."""
        # Simple calculation - exclude weekends
        working_days = 0
        current_date = start_date

        while current_date <= end_date:
            # Monday = 0, Sunday = 6
            if current_date.weekday() < 5:  # Monday to Friday
                working_days += 1
            current_date = date(current_date.year, current_date.month, current_date.day + 1)

        return working_days

    async def _create_structure_details(
        self, tenant_id: str, structure_id: str, structure_data: SalaryStructureCreate, created_by: str
    ):
        """Create salary structure details (earnings and deductions)."""
        # Simplified implementation - in production, create proper repository for structure details
        logger.info(f"Creating structure details for structure {structure_id}")

        # Create earnings
        for earning in structure_data.earnings:
            logger.debug(f"Adding earning component: {earning.salary_component_id}")

        # Create deductions
        for deduction in structure_data.deductions:
            logger.debug(f"Adding deduction component: {deduction.salary_component_id}")

    async def _calculate_salary_components(
        self, tenant_id: str, slip: SalarySlip, structure: SalaryStructure, created_by: str
    ):
        """Calculate salary components for a salary slip."""
        # This is a simplified implementation
        # In production, implement proper formula evaluation and component calculation

        # Calculate earnings
        total_earnings = Decimal("0")
        for earning in structure.earnings:
            amount = self._evaluate_component_amount(earning, slip)
            total_earnings += amount

            # Create salary detail record
            await self._create_salary_detail(
                tenant_id, slip.id, earning, ComponentType.EARNING, amount, created_by
            )

        # Calculate deductions
        total_deductions = Decimal("0")
        for deduction in structure.deductions:
            amount = self._evaluate_component_amount(deduction, slip)
            total_deductions += amount

            # Create salary detail record
            await self._create_salary_detail(
                tenant_id, slip.id, deduction, ComponentType.DEDUCTION, amount, created_by
            )

    def _evaluate_component_amount(self, component_detail, slip: SalarySlip) -> Decimal:
        """Evaluate component amount based on formula or fixed amount."""
        # Simplified implementation - in production, implement proper formula evaluation
        if component_detail.formula:
            # Basic formula evaluation (implement proper parser in production)
            return component_detail.amount
        else:
            return component_detail.amount

    async def _create_salary_detail(
        self,
        tenant_id: str,
        slip_id: str,
        component_detail,
        component_type: ComponentType,
        amount: Decimal,
        created_by: str,
    ):
        """Create salary detail record."""
        # Simplified implementation - in production, create proper salary detail
        logger.debug(
            f"Creating salary detail for slip {slip_id}, component type {component_type}, amount {amount}"
        )

    async def _calculate_salary_totals(self, tenant_id: str, slip_id: str):
        """Calculate salary slip totals."""
        # Get all salary details
        earnings = await self.detail_repo.get_by_component_type(tenant_id, slip_id, ComponentType.EARNING)
        deductions = await self.detail_repo.get_by_component_type(tenant_id, slip_id, ComponentType.DEDUCTION)

        # Calculate totals
        gross_pay = sum(detail.amount for detail in earnings)
        total_deduction = sum(detail.amount for detail in deductions)
        net_pay = gross_pay - total_deduction

        # Update salary slip
        update_dict = {
            "gross_pay": gross_pay,
            "total_deduction": total_deduction,
            "net_pay": net_pay,
            "rounded_total": net_pay.quantize(Decimal("1")),  # Round to nearest integer
        }

        await self.slip_repo.update(tenant_id, slip_id, update_dict)
