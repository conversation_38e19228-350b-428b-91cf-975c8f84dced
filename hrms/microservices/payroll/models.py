"""
Payroll service database models.

Defines all data models for payroll management with tenant isolation.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator
from sqlalchemy import <PERSON>olean, Column, Date, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Foreign<PERSON>ey, Integer, Numeric, String, Text
from sqlalchemy.orm import relationship

from ..shared.models import BaseModel as PydanticBaseModel
from ..shared.models import TenantAwareModel


class PayrollFrequency(str, Enum):
    """Payroll frequency enumeration."""

    MONTHLY = "monthly"
    FORTNIGHTLY = "fortnightly"
    BIMONTHLY = "bimonthly"
    WEEKLY = "weekly"
    DAILY = "daily"


class ComponentType(str, Enum):
    """Salary component type enumeration."""

    EARNING = "earning"
    DEDUCTION = "deduction"


class SalarySlipStatus(str, Enum):
    """Salary slip status enumeration."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    CANCELLED = "cancelled"


class PayrollEntryStatus(str, Enum):
    """Payroll entry status enumeration."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    CANCELLED = "cancelled"


# SQLAlchemy Models
class SalaryComponent(TenantAwareModel):
    """Salary component model."""

    __tablename__ = "salary_components"

    name = Column(String(100), nullable=False)
    abbr = Column(String(20), nullable=False)
    type = Column(SQLEnum(ComponentType), nullable=False)
    description = Column(Text, nullable=True)

    # Component behavior
    depends_on_payment_days = Column(Boolean, default=True, nullable=False)
    is_tax_applicable = Column(Boolean, default=True, nullable=False)
    is_flexible_benefit = Column(Boolean, default=False, nullable=False)
    variable_based_on_taxable_salary = Column(Boolean, default=False, nullable=False)
    exempted_from_income_tax = Column(Boolean, default=False, nullable=False)
    statistical_component = Column(Boolean, default=False, nullable=False)
    do_not_include_in_total = Column(Boolean, default=False, nullable=False)

    # Formula and conditions
    condition = Column(Text, nullable=True)
    formula = Column(Text, nullable=True)
    amount = Column(Numeric(15, 2), default=0, nullable=False)

    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    salary_details = relationship("SalaryDetail", back_populates="component")


class SalaryStructure(TenantAwareModel):
    """Salary structure model."""

    __tablename__ = "salary_structures"

    name = Column(String(100), nullable=False)
    company = Column(String(100), nullable=False)
    currency = Column(String(10), default="USD", nullable=False)
    payroll_frequency = Column(SQLEnum(PayrollFrequency), default=PayrollFrequency.MONTHLY, nullable=False)

    # Timesheet settings
    salary_slip_based_on_timesheet = Column(Boolean, default=False, nullable=False)
    salary_component = Column(String(36), ForeignKey("salary_components.id"), nullable=True)
    hour_rate = Column(Numeric(10, 2), default=0, nullable=False)

    # Benefits
    leave_encashment_amount_per_day = Column(Numeric(10, 2), default=0, nullable=False)
    max_benefits = Column(Numeric(15, 2), default=0, nullable=False)

    # Payment details
    mode_of_payment = Column(String(50), nullable=True)
    payment_account = Column(String(100), nullable=True)

    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)

    # Relationships
    earnings = relationship(
        "SalaryStructureDetail",
        foreign_keys="SalaryStructureDetail.salary_structure_id",
        primaryjoin="and_(SalaryStructure.id==SalaryStructureDetail.salary_structure_id, "
        "SalaryStructureDetail.component_type=='earning')",
        back_populates="salary_structure",
    )
    deductions = relationship(
        "SalaryStructureDetail",
        foreign_keys="SalaryStructureDetail.salary_structure_id",
        primaryjoin="and_(SalaryStructure.id==SalaryStructureDetail.salary_structure_id, "
        "SalaryStructureDetail.component_type=='deduction')",
        back_populates="salary_structure",
    )
    salary_slips = relationship("SalarySlip", back_populates="salary_structure")


class SalaryStructureDetail(TenantAwareModel):
    """Salary structure detail model."""

    __tablename__ = "salary_structure_details"

    salary_structure_id = Column(String(36), ForeignKey("salary_structures.id"), nullable=False)
    salary_component_id = Column(String(36), ForeignKey("salary_components.id"), nullable=False)
    component_type = Column(SQLEnum(ComponentType), nullable=False)

    # Component configuration
    condition = Column(Text, nullable=True)
    formula = Column(Text, nullable=True)
    amount = Column(Numeric(15, 2), default=0, nullable=False)

    # Relationships
    salary_structure = relationship("SalaryStructure", back_populates="earnings")
    salary_component = relationship("SalaryComponent")


class SalarySlip(TenantAwareModel):
    """Salary slip model."""

    __tablename__ = "salary_slips"

    # Employee information
    employee_id = Column(String(36), nullable=False, index=True)  # Reference to Employee service
    employee_name = Column(String(100), nullable=False)
    company = Column(String(100), nullable=False)
    department = Column(String(100), nullable=True)
    designation = Column(String(100), nullable=True)

    # Payroll period
    payroll_frequency = Column(SQLEnum(PayrollFrequency), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    posting_date = Column(Date, default=date.today, nullable=False)

    # Salary structure
    salary_structure_id = Column(String(36), ForeignKey("salary_structures.id"), nullable=False)
    payroll_entry_id = Column(String(36), ForeignKey("payroll_entries.id"), nullable=True)

    # Working days
    total_working_days = Column(Integer, default=0, nullable=False)
    payment_days = Column(Numeric(5, 2), default=0, nullable=False)
    leave_without_pay = Column(Numeric(5, 2), default=0, nullable=False)
    absent_days = Column(Numeric(5, 2), default=0, nullable=False)

    # Timesheet details
    salary_slip_based_on_timesheet = Column(Boolean, default=False, nullable=False)
    total_working_hours = Column(Numeric(8, 2), default=0, nullable=False)
    hour_rate = Column(Numeric(10, 2), default=0, nullable=False)

    # Amounts
    currency = Column(String(10), default="USD", nullable=False)
    exchange_rate = Column(Numeric(10, 4), default=1, nullable=False)
    gross_pay = Column(Numeric(15, 2), default=0, nullable=False)
    total_deduction = Column(Numeric(15, 2), default=0, nullable=False)
    net_pay = Column(Numeric(15, 2), default=0, nullable=False)
    rounded_total = Column(Numeric(15, 2), default=0, nullable=False)

    # Year to date
    year_to_date = Column(Numeric(15, 2), default=0, nullable=False)
    month_to_date = Column(Numeric(15, 2), default=0, nullable=False)

    # Payment details
    mode_of_payment = Column(String(50), nullable=True)
    bank_name = Column(String(100), nullable=True)
    bank_account_no = Column(String(50), nullable=True)

    # Status and flags
    status = Column(SQLEnum(SalarySlipStatus), default=SalarySlipStatus.DRAFT, nullable=False)

    # Tax settings
    deduct_tax_for_unclaimed_employee_benefits = Column(Boolean, default=False, nullable=False)
    deduct_tax_for_unsubmitted_tax_exemption_proof = Column(Boolean, default=False, nullable=False)

    # Relationships
    salary_structure = relationship("SalaryStructure", back_populates="salary_slips")
    payroll_entry = relationship("PayrollEntry", back_populates="salary_slips")
    earnings = relationship(
        "SalaryDetail",
        foreign_keys="SalaryDetail.salary_slip_id",
        primaryjoin="and_(SalarySlip.id==SalaryDetail.salary_slip_id, "
        "SalaryDetail.component_type=='earning')",
        back_populates="salary_slip",
    )
    deductions = relationship(
        "SalaryDetail",
        foreign_keys="SalaryDetail.salary_slip_id",
        primaryjoin="and_(SalarySlip.id==SalaryDetail.salary_slip_id, "
        "SalaryDetail.component_type=='deduction')",
        back_populates="salary_slip",
    )


class SalaryDetail(TenantAwareModel):
    """Salary detail model for earnings and deductions."""

    __tablename__ = "salary_details"

    salary_slip_id = Column(String(36), ForeignKey("salary_slips.id"), nullable=False)
    salary_component_id = Column(String(36), ForeignKey("salary_components.id"), nullable=False)
    component_type = Column(SQLEnum(ComponentType), nullable=False)

    # Component details
    salary_component = Column(String(100), nullable=False)  # Component name
    abbr = Column(String(20), nullable=False)

    # Amounts
    amount = Column(Numeric(15, 2), default=0, nullable=False)
    default_amount = Column(Numeric(15, 2), default=0, nullable=False)
    additional_amount = Column(Numeric(15, 2), default=0, nullable=False)
    year_to_date = Column(Numeric(15, 2), default=0, nullable=False)

    # Component behavior
    depends_on_payment_days = Column(Boolean, default=True, nullable=False)
    is_tax_applicable = Column(Boolean, default=True, nullable=False)
    is_flexible_benefit = Column(Boolean, default=False, nullable=False)
    do_not_include_in_total = Column(Boolean, default=False, nullable=False)

    # Relationships
    salary_slip = relationship("SalarySlip", back_populates="earnings")
    component = relationship("SalaryComponent", back_populates="salary_details")


class PayrollEntry(TenantAwareModel):
    """Payroll entry model for batch processing."""

    __tablename__ = "payroll_entries"

    name = Column(String(100), nullable=False)
    company = Column(String(100), nullable=False)
    payroll_frequency = Column(SQLEnum(PayrollFrequency), nullable=False)

    # Period
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    posting_date = Column(Date, default=date.today, nullable=False)

    # Filters
    branch = Column(String(100), nullable=True)
    department = Column(String(100), nullable=True)
    designation = Column(String(100), nullable=True)

    # Settings
    salary_slip_based_on_timesheet = Column(Boolean, default=False, nullable=False)
    validate_attendance = Column(Boolean, default=True, nullable=False)

    # Tax settings
    deduct_tax_for_unclaimed_employee_benefits = Column(Boolean, default=False, nullable=False)
    deduct_tax_for_unsubmitted_tax_exemption_proof = Column(Boolean, default=False, nullable=False)

    # Accounting
    currency = Column(String(10), default="USD", nullable=False)
    exchange_rate = Column(Numeric(10, 4), default=1, nullable=False)
    payroll_payable_account = Column(String(100), nullable=True)

    # Status
    status = Column(SQLEnum(PayrollEntryStatus), default=PayrollEntryStatus.DRAFT, nullable=False)
    number_of_employees = Column(Integer, default=0, nullable=False)

    # Relationships
    salary_slips = relationship("SalarySlip", back_populates="payroll_entry")


# Pydantic Schemas
class SalaryComponentBase(PydanticBaseModel):
    """Base salary component schema."""

    name: str = Field(..., min_length=1, max_length=100)
    abbr: str = Field(..., min_length=1, max_length=20)
    type: ComponentType
    description: Optional[str] = None
    depends_on_payment_days: bool = True
    is_tax_applicable: bool = True
    is_flexible_benefit: bool = False
    variable_based_on_taxable_salary: bool = False
    exempted_from_income_tax: bool = False
    statistical_component: bool = False
    do_not_include_in_total: bool = False
    condition: Optional[str] = None
    formula: Optional[str] = None
    amount: Decimal = Field(default=Decimal("0"), ge=0)
    is_active: bool = True


class SalaryComponentCreate(SalaryComponentBase):
    """Salary component creation schema."""

    pass


class SalaryComponentUpdate(PydanticBaseModel):
    """Salary component update schema."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    abbr: Optional[str] = Field(None, min_length=1, max_length=20)
    type: Optional[ComponentType] = None
    description: Optional[str] = None
    depends_on_payment_days: Optional[bool] = None
    is_tax_applicable: Optional[bool] = None
    is_flexible_benefit: Optional[bool] = None
    variable_based_on_taxable_salary: Optional[bool] = None
    exempted_from_income_tax: Optional[bool] = None
    statistical_component: Optional[bool] = None
    do_not_include_in_total: Optional[bool] = None
    condition: Optional[str] = None
    formula: Optional[str] = None
    amount: Optional[Decimal] = Field(None, ge=0)
    is_active: Optional[bool] = None


class SalaryComponentResponse(SalaryComponentBase):
    """Salary component response schema."""

    id: str
    tenant_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class SalaryStructureDetailBase(PydanticBaseModel):
    """Base salary structure detail schema."""

    salary_component_id: str
    component_type: ComponentType
    condition: Optional[str] = None
    formula: Optional[str] = None
    amount: Decimal = Field(default=Decimal("0"), ge=0)


class SalaryStructureBase(PydanticBaseModel):
    """Base salary structure schema."""

    name: str = Field(..., min_length=1, max_length=100)
    company: str = Field(..., min_length=1, max_length=100)
    currency: str = Field(default="USD", max_length=10)
    payroll_frequency: PayrollFrequency = PayrollFrequency.MONTHLY
    salary_slip_based_on_timesheet: bool = False
    salary_component: Optional[str] = None
    hour_rate: Decimal = Field(default=Decimal("0"), ge=0)
    leave_encashment_amount_per_day: Decimal = Field(default=Decimal("0"), ge=0)
    max_benefits: Decimal = Field(default=Decimal("0"), ge=0)
    mode_of_payment: Optional[str] = None
    payment_account: Optional[str] = None
    is_active: bool = True
    is_default: bool = False


class SalaryStructureCreate(SalaryStructureBase):
    """Salary structure creation schema."""

    earnings: List[SalaryStructureDetailBase] = []
    deductions: List[SalaryStructureDetailBase] = []


class SalaryStructureUpdate(PydanticBaseModel):
    """Salary structure update schema."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    company: Optional[str] = Field(None, min_length=1, max_length=100)
    currency: Optional[str] = Field(None, max_length=10)
    payroll_frequency: Optional[PayrollFrequency] = None
    salary_slip_based_on_timesheet: Optional[bool] = None
    salary_component: Optional[str] = None
    hour_rate: Optional[Decimal] = Field(None, ge=0)
    leave_encashment_amount_per_day: Optional[Decimal] = Field(None, ge=0)
    max_benefits: Optional[Decimal] = Field(None, ge=0)
    mode_of_payment: Optional[str] = None
    payment_account: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class SalaryStructureResponse(SalaryStructureBase):
    """Salary structure response schema."""

    id: str
    tenant_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class SalarySlipBase(PydanticBaseModel):
    """Base salary slip schema."""

    employee_id: str
    employee_name: str = Field(..., min_length=1, max_length=100)
    company: str = Field(..., min_length=1, max_length=100)
    department: Optional[str] = Field(None, max_length=100)
    designation: Optional[str] = Field(None, max_length=100)
    payroll_frequency: PayrollFrequency
    start_date: date
    end_date: date
    posting_date: date = Field(default_factory=date.today)
    salary_structure_id: str
    total_working_days: int = Field(default=0, ge=0)
    payment_days: Decimal = Field(default=Decimal("0"), ge=0)
    leave_without_pay: Decimal = Field(default=Decimal("0"), ge=0)
    absent_days: Decimal = Field(default=Decimal("0"), ge=0)
    salary_slip_based_on_timesheet: bool = False
    total_working_hours: Decimal = Field(default=Decimal("0"), ge=0)
    hour_rate: Decimal = Field(default=Decimal("0"), ge=0)
    currency: str = Field(default="USD", max_length=10)
    exchange_rate: Decimal = Field(default=Decimal("1"), gt=0)
    mode_of_payment: Optional[str] = None
    bank_name: Optional[str] = None
    bank_account_no: Optional[str] = None
    deduct_tax_for_unclaimed_employee_benefits: bool = False
    deduct_tax_for_unsubmitted_tax_exemption_proof: bool = False


class SalarySlipCreate(SalarySlipBase):
    """Salary slip creation schema."""

    pass


class SalarySlipResponse(SalarySlipBase):
    """Salary slip response schema."""

    id: str
    tenant_id: str
    gross_pay: Decimal
    total_deduction: Decimal
    net_pay: Decimal
    rounded_total: Decimal
    year_to_date: Decimal
    month_to_date: Decimal
    status: SalarySlipStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class PayrollEntryBase(PydanticBaseModel):
    """Base payroll entry schema."""

    name: str = Field(..., min_length=1, max_length=100)
    company: str = Field(..., min_length=1, max_length=100)
    payroll_frequency: PayrollFrequency
    start_date: date
    end_date: date
    posting_date: date = Field(default_factory=date.today)
    branch: Optional[str] = None
    department: Optional[str] = None
    designation: Optional[str] = None
    salary_slip_based_on_timesheet: bool = False
    validate_attendance: bool = True
    deduct_tax_for_unclaimed_employee_benefits: bool = False
    deduct_tax_for_unsubmitted_tax_exemption_proof: bool = False
    currency: str = Field(default="USD", max_length=10)
    exchange_rate: Decimal = Field(default=Decimal("1"), gt=0)
    payroll_payable_account: Optional[str] = None


class PayrollEntryCreate(PayrollEntryBase):
    """Payroll entry creation schema."""

    pass


class PayrollEntryResponse(PayrollEntryBase):
    """Payroll entry response schema."""

    id: str
    tenant_id: str
    status: PayrollEntryStatus
    number_of_employees: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
