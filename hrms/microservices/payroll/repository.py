"""
Payroll service repository layer.

Handles all database operations for payroll management with tenant isolation.
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..shared.database import DatabaseManager, TenantAwareRepository
from ..shared.exceptions import ConflictError, NotFoundError, ValidationError
from ..shared.logging import get_logger
from .models import (
    ComponentType,
    PayrollEntry,
    PayrollEntryStatus,
    SalaryComponent,
    SalaryDetail,
    SalarySlip,
    SalarySlipStatus,
    SalaryStructure,
    SalaryStructureDetail,
)

logger = get_logger(__name__)


class SalaryComponentRepository(TenantAwareRepository):
    """Repository for salary component operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, SalaryComponent)

    async def get_by_name(self, tenant_id: str, name: str) -> Optional[SalaryComponent]:
        """Get salary component by name."""
        async with self.db_manager.get_async_session() as session:
            query = select(SalaryComponent).where(
                and_(
                    SalaryComponent.tenant_id == tenant_id,
                    SalaryComponent.name == name,
                    SalaryComponent.is_deleted == False,
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_abbr(self, tenant_id: str, abbr: str) -> Optional[SalaryComponent]:
        """Get salary component by abbreviation."""
        async with self.db_manager.get_async_session() as session:
            query = select(SalaryComponent).where(
                and_(
                    SalaryComponent.tenant_id == tenant_id,
                    SalaryComponent.abbr == abbr,
                    SalaryComponent.is_deleted == False,
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_type(self, tenant_id: str, component_type: ComponentType) -> List[SalaryComponent]:
        """Get salary components by type."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalaryComponent)
                .where(
                    and_(
                        SalaryComponent.tenant_id == tenant_id,
                        SalaryComponent.type == component_type,
                        SalaryComponent.is_deleted == False,
                        SalaryComponent.is_active == True,
                    )
                )
                .order_by(SalaryComponent.name)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def check_name_exists(self, tenant_id: str, name: str, exclude_id: Optional[str] = None) -> bool:
        """Check if component name already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(SalaryComponent.id)).where(
                and_(
                    SalaryComponent.tenant_id == tenant_id,
                    SalaryComponent.name == name,
                    SalaryComponent.is_deleted == False,
                )
            )

            if exclude_id:
                query = query.where(SalaryComponent.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0

    async def check_abbr_exists(self, tenant_id: str, abbr: str, exclude_id: Optional[str] = None) -> bool:
        """Check if component abbreviation already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(SalaryComponent.id)).where(
                and_(
                    SalaryComponent.tenant_id == tenant_id,
                    SalaryComponent.abbr == abbr,
                    SalaryComponent.is_deleted == False,
                )
            )

            if exclude_id:
                query = query.where(SalaryComponent.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0


class SalaryStructureRepository(TenantAwareRepository):
    """Repository for salary structure operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, SalaryStructure)

    async def get_by_name(self, tenant_id: str, name: str) -> Optional[SalaryStructure]:
        """Get salary structure by name."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalaryStructure)
                .options(selectinload(SalaryStructure.earnings), selectinload(SalaryStructure.deductions))
                .where(
                    and_(
                        SalaryStructure.tenant_id == tenant_id,
                        SalaryStructure.name == name,
                        SalaryStructure.is_deleted == False,
                    )
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_with_details(self, tenant_id: str, structure_id: str) -> Optional[SalaryStructure]:
        """Get salary structure with earnings and deductions."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalaryStructure)
                .options(selectinload(SalaryStructure.earnings), selectinload(SalaryStructure.deductions))
                .where(
                    and_(
                        SalaryStructure.tenant_id == tenant_id,
                        SalaryStructure.id == structure_id,
                        SalaryStructure.is_deleted == False,
                    )
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_default_structure(self, tenant_id: str, company: str) -> Optional[SalaryStructure]:
        """Get default salary structure for company."""
        async with self.db_manager.get_async_session() as session:
            query = select(SalaryStructure).where(
                and_(
                    SalaryStructure.tenant_id == tenant_id,
                    SalaryStructure.company == company,
                    SalaryStructure.is_default == True,
                    SalaryStructure.is_active == True,
                    SalaryStructure.is_deleted == False,
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def check_name_exists(self, tenant_id: str, name: str, exclude_id: Optional[str] = None) -> bool:
        """Check if structure name already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(SalaryStructure.id)).where(
                and_(
                    SalaryStructure.tenant_id == tenant_id,
                    SalaryStructure.name == name,
                    SalaryStructure.is_deleted == False,
                )
            )

            if exclude_id:
                query = query.where(SalaryStructure.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0


class SalarySlipRepository(TenantAwareRepository):
    """Repository for salary slip operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, SalarySlip)

    async def get_by_employee_and_period(
        self, tenant_id: str, employee_id: str, start_date: date, end_date: date
    ) -> Optional[SalarySlip]:
        """Get salary slip by employee and period."""
        async with self.db_manager.get_async_session() as session:
            query = select(SalarySlip).where(
                and_(
                    SalarySlip.tenant_id == tenant_id,
                    SalarySlip.employee_id == employee_id,
                    SalarySlip.start_date == start_date,
                    SalarySlip.end_date == end_date,
                    SalarySlip.is_deleted == False,
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_with_details(self, tenant_id: str, slip_id: str) -> Optional[SalarySlip]:
        """Get salary slip with earnings and deductions."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalarySlip)
                .options(
                    selectinload(SalarySlip.earnings),
                    selectinload(SalarySlip.deductions),
                    selectinload(SalarySlip.salary_structure),
                )
                .where(
                    and_(
                        SalarySlip.tenant_id == tenant_id,
                        SalarySlip.id == slip_id,
                        SalarySlip.is_deleted == False,
                    )
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_employee(
        self, tenant_id: str, employee_id: str, skip: int = 0, limit: int = 100
    ) -> List[SalarySlip]:
        """Get salary slips by employee."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalarySlip)
                .where(
                    and_(
                        SalarySlip.tenant_id == tenant_id,
                        SalarySlip.employee_id == employee_id,
                        SalarySlip.is_deleted == False,
                    )
                )
                .order_by(desc(SalarySlip.start_date))
                .offset(skip)
                .limit(limit)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_payroll_entry(self, tenant_id: str, payroll_entry_id: str) -> List[SalarySlip]:
        """Get salary slips by payroll entry."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalarySlip)
                .where(
                    and_(
                        SalarySlip.tenant_id == tenant_id,
                        SalarySlip.payroll_entry_id == payroll_entry_id,
                        SalarySlip.is_deleted == False,
                    )
                )
                .order_by(SalarySlip.employee_name)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_statistics(
        self, tenant_id: str, filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get salary slip statistics."""
        async with self.db_manager.get_async_session() as session:
            # Base query
            base_query = select(SalarySlip).where(
                and_(SalarySlip.tenant_id == tenant_id, SalarySlip.is_deleted == False)
            )

            # Apply filters
            if filters:
                if "start_date" in filters:
                    base_query = base_query.where(SalarySlip.start_date >= filters["start_date"])
                if "end_date" in filters:
                    base_query = base_query.where(SalarySlip.end_date <= filters["end_date"])
                if "company" in filters:
                    base_query = base_query.where(SalarySlip.company == filters["company"])
                if "department" in filters:
                    base_query = base_query.where(SalarySlip.department == filters["department"])

            # Total slips
            total_query = select(func.count(SalarySlip.id)).select_from(base_query.subquery())
            total_result = await session.execute(total_query)
            total_slips = total_result.scalar()

            # Total gross pay
            gross_query = select(func.sum(SalarySlip.gross_pay)).select_from(base_query.subquery())
            gross_result = await session.execute(gross_query)
            total_gross_pay = gross_result.scalar() or Decimal("0")

            # Total net pay
            net_query = select(func.sum(SalarySlip.net_pay)).select_from(base_query.subquery())
            net_result = await session.execute(net_query)
            total_net_pay = net_result.scalar() or Decimal("0")

            # By status
            status_query = (
                select(SalarySlip.status, func.count(SalarySlip.id).label("count"))
                .select_from(base_query.subquery())
                .group_by(SalarySlip.status)
            )
            status_result = await session.execute(status_query)
            by_status = {row.status: row.count for row in status_result}

            return {
                "total_slips": total_slips,
                "total_gross_pay": float(total_gross_pay),
                "total_net_pay": float(total_net_pay),
                "by_status": by_status,
            }


class PayrollEntryRepository(TenantAwareRepository):
    """Repository for payroll entry operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, PayrollEntry)

    async def get_by_name(self, tenant_id: str, name: str) -> Optional[PayrollEntry]:
        """Get payroll entry by name."""
        async with self.db_manager.get_async_session() as session:
            query = select(PayrollEntry).where(
                and_(
                    PayrollEntry.tenant_id == tenant_id,
                    PayrollEntry.name == name,
                    PayrollEntry.is_deleted == False,
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_with_salary_slips(self, tenant_id: str, entry_id: str) -> Optional[PayrollEntry]:
        """Get payroll entry with salary slips."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(PayrollEntry)
                .options(selectinload(PayrollEntry.salary_slips))
                .where(
                    and_(
                        PayrollEntry.tenant_id == tenant_id,
                        PayrollEntry.id == entry_id,
                        PayrollEntry.is_deleted == False,
                    )
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none()

    async def get_by_period(
        self, tenant_id: str, start_date: date, end_date: date, company: Optional[str] = None
    ) -> List[PayrollEntry]:
        """Get payroll entries by period."""
        async with self.db_manager.get_async_session() as session:
            query = select(PayrollEntry).where(
                and_(
                    PayrollEntry.tenant_id == tenant_id,
                    PayrollEntry.start_date >= start_date,
                    PayrollEntry.end_date <= end_date,
                    PayrollEntry.is_deleted == False,
                )
            )

            if company:
                query = query.where(PayrollEntry.company == company)

            query = query.order_by(desc(PayrollEntry.start_date))

            result = await session.execute(query)
            return result.scalars().all()

    async def check_name_exists(self, tenant_id: str, name: str, exclude_id: Optional[str] = None) -> bool:
        """Check if payroll entry name already exists."""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(PayrollEntry.id)).where(
                and_(
                    PayrollEntry.tenant_id == tenant_id,
                    PayrollEntry.name == name,
                    PayrollEntry.is_deleted == False,
                )
            )

            if exclude_id:
                query = query.where(PayrollEntry.id != exclude_id)

            result = await session.execute(query)
            count = result.scalar()
            return count > 0


class SalaryDetailRepository(TenantAwareRepository):
    """Repository for salary detail operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, SalaryDetail)

    async def get_by_salary_slip(self, tenant_id: str, salary_slip_id: str) -> List[SalaryDetail]:
        """Get salary details by salary slip."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalaryDetail)
                .where(
                    and_(
                        SalaryDetail.tenant_id == tenant_id,
                        SalaryDetail.salary_slip_id == salary_slip_id,
                        SalaryDetail.is_deleted == False,
                    )
                )
                .order_by(SalaryDetail.component_type, SalaryDetail.salary_component)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def get_by_component_type(
        self, tenant_id: str, salary_slip_id: str, component_type: ComponentType
    ) -> List[SalaryDetail]:
        """Get salary details by component type."""
        async with self.db_manager.get_async_session() as session:
            query = (
                select(SalaryDetail)
                .where(
                    and_(
                        SalaryDetail.tenant_id == tenant_id,
                        SalaryDetail.salary_slip_id == salary_slip_id,
                        SalaryDetail.component_type == component_type,
                        SalaryDetail.is_deleted == False,
                    )
                )
                .order_by(SalaryDetail.salary_component)
            )

            result = await session.execute(query)
            return result.scalars().all()

    async def delete_by_salary_slip(self, tenant_id: str, salary_slip_id: str) -> bool:
        """Delete all salary details for a salary slip."""
        async with self.db_manager.get_async_session() as session:
            # Get all details for the salary slip
            query = select(SalaryDetail).where(
                and_(
                    SalaryDetail.tenant_id == tenant_id,
                    SalaryDetail.salary_slip_id == salary_slip_id,
                    SalaryDetail.is_deleted == False,
                )
            )
            result = await session.execute(query)
            details = result.scalars().all()

            # Soft delete each detail
            for detail in details:
                detail.is_deleted = True
                detail.deleted_at = datetime.utcnow()

            await session.flush()
            return True
