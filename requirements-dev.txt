# Development dependencies for oneHRMS microservices

# Include production dependencies
-r requirements.txt

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-xdist==3.5.0
pytest-mock==3.12.0
pytest-benchmark==4.0.0
pytest-html==4.1.1
httpx==0.25.2

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
flake8-docstrings==1.7.0
flake8-bugbear==23.11.28
flake8-comprehensions==3.14.0
mypy==1.7.1

# Pre-commit hooks
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-swagger-ui-tag==0.6.8

# Development tools
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# Database tools
pgcli==4.0.1

# API testing
tavern==2.4.1

# Load testing
locust==2.17.0

# Debugging
pdb++==0.10.3
ipdb==0.13.13

# Linting and refactoring
rope==1.11.0
pyupgrade==3.15.0
autoflake==2.2.1

# Type checking
types-requests==*********
types-PyYAML==*********
types-redis==********
