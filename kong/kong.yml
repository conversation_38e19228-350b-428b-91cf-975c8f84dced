# Kong Declarative Configuration for oneHRMS Microservices
# This file defines all routes, services, and plugins for the API Gateway

_format_version: "3.0"
_transform: true

services:
  # Employee Management Service
  - name: employee-service
    url: http://employee-service:8100
    protocol: http
    host: employee-service
    port: 8100
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - employee
      - microservice

  # Payroll Service
  - name: payroll-service
    url: http://payroll-service:8101
    protocol: http
    host: payroll-service
    port: 8101
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - payroll
      - microservice

  # Attendance Service (placeholder for future implementation)
  - name: attendance-service
    url: http://attendance-service:8102
    protocol: http
    host: attendance-service
    port: 8102
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - attendance
      - microservice

  # Leave Management Service (placeholder for future implementation)
  - name: leave-service
    url: http://leave-service:8103
    protocol: http
    host: leave-service
    port: 8103
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - leave
      - microservice

  # Employee Self-Service (ESS) Service
  - name: ess-service
    url: http://ess-service:8103
    protocol: http
    host: ess-service
    port: 8103
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - ess
      - microservice

  # Recruitment Service
  - name: recruitment-service
    url: http://recruitment-service:8104
    protocol: http
    host: recruitment-service
    port: 8104
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - hrms
      - recruitment
      - microservice

routes:
  # Employee Service Routes
  - name: employee-api
    service: employee-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - PATCH
    paths:
      - /api/v1/employees
    strip_path: false
    preserve_host: false
    tags:
      - employee
      - api

  - name: employee-departments-api
    service: employee-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/departments
    strip_path: false
    preserve_host: false
    tags:
      - employee
      - departments
      - api

  - name: employee-positions-api
    service: employee-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/positions
    strip_path: false
    preserve_host: false
    tags:
      - employee
      - positions
      - api

  - name: employee-health
    service: employee-service
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /health/employee
    strip_path: true
    preserve_host: false
    tags:
      - employee
      - health

  # Payroll Service Routes
  - name: payroll-components-api
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/payroll/components
    strip_path: false
    preserve_host: false
    tags:
      - payroll
      - components
      - api

  - name: payroll-structures-api
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/payroll/salary-structures
    strip_path: false
    preserve_host: false
    tags:
      - payroll
      - structures
      - api

  - name: payroll-slips-api
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/payroll/salary-slips
    strip_path: false
    preserve_host: false
    tags:
      - payroll
      - slips
      - api

  - name: payroll-entries-api
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    paths:
      - /api/v1/payroll/payroll-entries
    strip_path: false
    preserve_host: false
    tags:
      - payroll
      - entries
      - api

  - name: payroll-statistics-api
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /api/v1/payroll/statistics
    strip_path: false
    preserve_host: false
    tags:
      - payroll
      - statistics
      - api

  - name: payroll-health
    service: payroll-service
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /health/payroll
    strip_path: true
    preserve_host: false
    tags:
      - payroll
      - health

  # Attendance Service Routes (placeholder)
  - name: attendance-api
    service: attendance-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - PATCH
    paths:
      - /api/v1/attendance
    strip_path: false
    preserve_host: false
    tags:
      - attendance
      - api

  # Leave Service Routes (placeholder)
  - name: leave-api
    service: leave-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - PATCH
    paths:
      - /api/v1/leave
    strip_path: false
    preserve_host: false
    tags:
      - leave
      - api

  # Recruitment Service Routes (placeholder)
  - name: recruitment-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - PATCH
    paths:
      - /api/v1/recruitment
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - api

  # Employee Self-Service (ESS) Routes
  - name: ess-dashboard-api
    service: ess-service
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /api/v1/ess/dashboard
    strip_path: false
    preserve_host: false
    tags:
      - ess
      - dashboard

  - name: ess-profile-api
    service: ess-service
    protocols:
      - http
      - https
    methods:
      - GET
      - PUT
    paths:
      - /api/v1/ess/profile
    strip_path: false
    preserve_host: false
    tags:
      - ess
      - profile

  - name: ess-leave-api
    service: ess-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PATCH
    paths:
      - /api/v1/ess/leave-applications
    strip_path: false
    preserve_host: false
    tags:
      - ess
      - leave

  - name: ess-documents-api
    service: ess-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - DELETE
    paths:
      - /api/v1/ess/documents
    strip_path: false
    preserve_host: false
    tags:
      - ess
      - documents

  - name: ess-timesheets-api
    service: ess-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PATCH
    paths:
      - /api/v1/ess/timesheets
    strip_path: false
    preserve_host: false
    tags:
      - ess
      - timesheets

  # Recruitment Service Routes
  - name: recruitment-jobs-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - PATCH
    paths:
      - /api/v1/recruitment/jobs
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - jobs

  - name: recruitment-candidates-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
    paths:
      - /api/v1/recruitment/candidates
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - candidates

  - name: recruitment-applications-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PATCH
    paths:
      - /api/v1/recruitment/applications
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - applications

  - name: recruitment-interviews-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PATCH
    paths:
      - /api/v1/recruitment/interviews
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - interviews

  - name: recruitment-offers-api
    service: recruitment-service
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PATCH
    paths:
      - /api/v1/recruitment/offers
    strip_path: false
    preserve_host: false
    tags:
      - recruitment
      - offers

plugins:
  # Global CORS Plugin
  - name: cors
    config:
      origins:
        - "http://localhost:3000"
        - "http://localhost:8080"
        - "https://*.onehrms.com"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - PATCH
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - Authorization
        - X-Tenant-ID
        - X-Request-ID
      exposed_headers:
        - X-Auth-Token
        - X-Request-ID
        - X-Response-Time
      credentials: true
      max_age: 3600
      preflight_continue: false

  # Global Rate Limiting Plugin
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000
      day: 100000
      policy: local
      fault_tolerant: true
      hide_client_headers: false

  # Request/Response Logging Plugin
  - name: http-log
    config:
      http_endpoint: "http://logging-service:8080/logs"
      method: POST
      timeout: 10000
      keepalive: 60000
      content_type: application/json
      flush_timeout: 2

  # JWT Authentication Plugin (for production)
  - name: jwt
    config:
      uri_param_names:
        - jwt
      cookie_names:
        - jwt
      header_names:
        - authorization
      claims_to_verify:
        - exp
        - iat
      key_claim_name: iss
      secret_is_base64: false
      anonymous: ""
      run_on_preflight: true
    enabled: false # Disabled for development

  # Request ID Plugin
  - name: correlation-id
    config:
      header_name: X-Request-ID
      generator: uuid#counter
      echo_downstream: true

  # Response Transformer Plugin
  - name: response-transformer
    config:
      add:
        headers:
          - "X-Powered-By: oneHRMS"
          - "X-API-Version: v1"
      append:
        headers:
          - "X-Response-Time: $upstream_response_time"

# Consumer definitions (for API key management)
consumers:
  - username: hrms-frontend
    custom_id: frontend-app
    tags:
      - frontend
      - web

  - username: hrms-mobile
    custom_id: mobile-app
    tags:
      - mobile
      - app

  - username: hrms-admin
    custom_id: admin-portal
    tags:
      - admin
      - portal

# API Keys for consumers (development only)
keyauth_credentials:
  - consumer: hrms-frontend
    key: frontend-dev-key-12345

  - consumer: hrms-mobile
    key: mobile-dev-key-67890

  - consumer: hrms-admin
    key: admin-dev-key-abcde

# Upstream definitions for load balancing
upstreams:
  - name: employee-service-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    healthchecks:
      active:
        timeout: 1
        concurrency: 10
        http_path: "/health"
        healthy:
          interval: 10
          http_statuses:
            - 200
            - 302
          successes: 2
        unhealthy:
          interval: 10
          http_statuses:
            - 429
            - 404
            - 500
            - 501
            - 502
            - 503
            - 504
            - 505
          tcp_failures: 3
          timeouts: 3
          http_failures: 5
      passive:
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 203
            - 204
            - 205
            - 206
            - 300
            - 301
            - 302
            - 303
            - 304
            - 307
            - 308
          successes: 3
        unhealthy:
          http_statuses:
            - 429
            - 500
            - 503
          tcp_failures: 3
          timeouts: 3
          http_failures: 3
    tags:
      - employee
      - upstream

# Targets for upstreams
targets:
  - target: employee-service:8100
    upstream: employee-service-upstream
    weight: 100
    tags:
      - employee
      - primary
