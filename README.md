# oneHRMS - Modern Human Resource Management System

<div align="center">
  <img src="docs/assets/onehrms-logo.png" alt="oneHRMS Logo" width="200"/>
  <h3>Comprehensive HR Management Solution</h3>
  <p>Modern, scalable, and user-friendly HR management system built with React, TypeScript, and FastAPI</p>

  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![React](https://img.shields.io/badge/React-18.2.0-blue.svg)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
  [![FastAPI](https://img.shields.io/badge/FastAPI-0.104.0-green.svg)](https://fastapi.tiangolo.com/)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14-blue.svg)](https://www.postgresql.org/)
</div>

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+
- **Python** 3.11+
- **PostgreSQL** 14+
- **Docker** & Docker Compose
- **Git**

### Development Setup
```bash
# Clone the repository
git clone https://github.com/your-org/onehrms.git
cd onehrms

# Start all services with Docker
docker-compose up -d

# Access the application
# Frontend: http://localhost:3002
# Backend API: http://localhost:8000
# Keycloak: http://localhost:8080
```

### Manual Setup
```bash
# Frontend setup
cd frontend/dashboard-ui
npm install
npm run dev

# Backend setup
cd hrms
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

## 📋 Table of Contents

- [Features](#-features)
- [Architecture](#-architecture)
- [Technology Stack](#-technology-stack)
- [Project Structure](#-project-structure)
- [Development](#-development)
- [Deployment](#-deployment)
- [API Documentation](#-api-documentation)
- [Recent Fixes & Improvements](#-recent-fixes--improvements)
- [Contributing](#-contributing)
- [Support](#-support)

## ✨ Features

### 🏠 Dashboard
- **Overview Cards**: Quick statistics and metrics
- **Recent Activity**: Latest system activities
- **Quick Actions**: Fast access to common tasks
- **Notifications**: Real-time alerts and updates
- **Analytics**: HR metrics and trends

### 👥 Employee Management
- **Employee Profiles**: Comprehensive employee information
- **Organization Chart**: Visual hierarchy and reporting structure
- **Document Management**: Secure storage of employee documents
- **Performance Tracking**: Goal setting and review cycles
- **Career Development**: Training and development tracking

### ⏰ Attendance & Leave Management
- **Time Tracking**: Check-in/check-out with geolocation
- **Leave Management**: Comprehensive leave request system
- **Calendar Integration**: Visual attendance and leave calendar
- **Shift Management**: Flexible work schedule support
- **Overtime Tracking**: Automatic overtime calculation

### 💰 My Salary & Expenses
- **Salary Information**: Current salary and benefits display
- **Payslip Management**: Download and view payslips
- **Benefits Tracking**: Health insurance, retirement plans
- **Expense Claims**: Submit and track expense requests
- **Tax Information**: Income tax details and calculations

### 📋 Tasks
- **Task Creation**: Create and assign tasks
- **Progress Tracking**: Monitor task completion
- **Priority Management**: Set task priorities
- **Deadline Tracking**: Due date management
- **Collaboration**: Team task coordination

### ⚙️ Admin Panel
- **User Management**: Create and manage user accounts
- **Role Management**: Define and assign user roles
- **System Configuration**: Application settings and preferences
- **Security Management**: Access control and permissions
- **Audit Logs**: System activity tracking

### 📊 Payroll Management
- **Salary Structure**: Define salary components
- **Payroll Processing**: Run monthly payroll
- **Tax Management**: Income tax calculations
- **Benefits Administration**: Manage employee benefits
- **Payslip Generation**: Automated payslip creation

### 📋 Recruitment Management
- **Job Postings**: Create and manage job openings
- **Candidate Tracking**: Manage applicant pipeline
- **Interview Scheduling**: Coordinate interview processes
- **Hiring Workflow**: End-to-end recruitment process
- **Onboarding**: New hire setup and orientation

## 🏗️ Architecture

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
│   Port: 3002    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   Keycloak     │◄─────────────┘
                        │   (Auth)       │
                        │   Port: 8080   │
                        └─────────────────┘
```

### Microservices Architecture
- **Employee Service**: Employee management and profiles
- **Attendance Service**: Time tracking and attendance
- **Leave Service**: Leave management and approvals
- **Payroll Service**: Salary and compensation
- **Recruitment Service**: Hiring and candidate management
- **ESS Service**: Employee self-service portal

## 🛠️ Technology Stack

### Frontend
- **React 18**: Modern UI framework
- **TypeScript**: Type-safe development
- **Material-UI**: Component library
- **React Router**: Client-side routing
- **Vite**: Build tool and dev server
- **Recharts**: Data visualization
- **Date-fns**: Date manipulation

### Backend
- **FastAPI**: Modern Python web framework
- **SQLAlchemy**: Database ORM
- **PostgreSQL**: Primary database
- **Keycloak**: Authentication and authorization
- **Alembic**: Database migrations
- **Pydantic**: Data validation

### Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **Kong**: API Gateway
- **Redis**: Caching and sessions
- **Nginx**: Reverse proxy

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Jest**: Unit testing
- **Playwright**: E2E testing
- **Husky**: Git hooks

## 📁 Project Structure

```
oneHRMS/
├── frontend/
│   └── dashboard-ui/          # React frontend application
│       ├── src/
│       │   ├── components/    # Reusable UI components
│       │   ├── features/      # Feature-based modules
│       │   ├── pages/         # Page components
│       │   ├── services/      # API services
│       │   ├── hooks/         # Custom React hooks
│       │   ├── utils/         # Utility functions
│       │   ├── types/         # TypeScript definitions
│       │   └── theme/         # Material-UI theme
│       ├── public/            # Static assets
│       └── tests/             # Test files
├── hrms/                      # Backend services
│   ├── microservices/         # Microservice modules
│   │   ├── employee/          # Employee service
│   │   ├── attendance/        # Attendance service
│   │   ├── leave/            # Leave service
│   │   ├── payroll/          # Payroll service
│   │   └── recruitment/      # Recruitment service
│   ├── shared/               # Shared utilities
│   └── tests/                # Backend tests
├── kong/                      # API Gateway configuration
├── docker/                    # Docker configurations
├── docs/                      # Documentation
└── scripts/                   # Utility scripts
```

## 🚀 Development

### Prerequisites
- Node.js 18+
- Python 3.11+
- PostgreSQL 14+
- Docker & Docker Compose

### Development Setup

#### Frontend Development
```bash
cd frontend/dashboard-ui

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

#### Backend Development
```bash
cd hrms

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="*************************************"
export SECRET_KEY="your-secret-key"
export KEYCLOAK_URL="http://localhost:8080"

# Run migrations
alembic upgrade head

# Start development server
uvicorn main:app --reload
```

#### Database Setup
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb hrms

# Create user
sudo -u postgres psql
CREATE USER hrms_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE hrms TO hrms_user;
\q
```

### Docker Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild services
docker-compose up -d --build
```

## 🚀 Deployment

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy services
docker-compose -f docker-compose.prod.yml up -d

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
```

### Environment Configuration
```bash
# Frontend environment variables
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_AUTH_URL=https://auth.yourdomain.com
VITE_APP_NAME=oneHRMS

# Backend environment variables
DATABASE_URL=******************************
KEYCLOAK_URL=https://auth.yourdomain.com
SECRET_KEY=your-production-secret-key
```

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n onehrms

# View logs
kubectl logs -f deployment/backend -n onehrms
```

## 📚 API Documentation

### Interactive Documentation
- **Swagger UI**: `https://api.onehrms.com/docs`
- **ReDoc**: `https://api.onehrms.com/redoc`
- **OpenAPI Spec**: `https://api.onehrms.com/openapi.json`

### API Endpoints
- **Employee API**: `/api/v1/employees`
- **Attendance API**: `/api/v1/attendance`
- **Leave API**: `/api/v1/leaves`
- **Payroll API**: `/api/v1/payroll`
- **Recruitment API**: `/api/v1/recruitment`

### Authentication
- **OAuth2/OIDC**: Keycloak integration
- **JWT Tokens**: Secure token-based authentication
- **Role-based Access**: Granular permission system

## 🔧 Recent Fixes & Improvements

### 🐛 Critical Bug Fixes (December 2024)

#### **Authentication System Fixes**
- **✅ Fixed AuthContext Type Errors**: Resolved all TypeScript type mismatches in authentication context
- **✅ Fixed AuthProvider Configuration**: Corrected config type handling for mock authentication
- **✅ Fixed UserPreferences Import**: Added missing import for UserPreferences type
- **✅ Fixed Mock User Creation**: Added required properties (username, isActive) to mock user objects
- **✅ Fixed Token Handling**: Added proper null checks for refresh tokens

#### **Job Requisition Form Fixes**
- **✅ Material-UI Grid v2 Migration**: Replaced deprecated Grid components with Box using CSS Grid
- **✅ Fixed Form Validation**: Updated validation schema to handle optional fields properly
- **✅ Fixed Type Conversions**: Resolved Date/string type mismatches for expected_start_date
- **✅ Fixed Form Submission**: Added proper type assertions for form data handling
- **✅ Fixed Component Structure**: Improved responsive layout and form organization

#### **API Client Generation Fixes**
- **✅ Fixed OpenAPI Generator**: Resolved Java Runtime issues and configuration problems
- **✅ Fixed Type Exports**: Removed duplicate export declarations in auth types
- **✅ Fixed Environment Variables**: Corrected VITE_API_BASE_URL usage in Vite environment
- **✅ Fixed Build Process**: Resolved npm dependency conflicts and build errors

#### **UI/UX Improvements**
- **✅ Fixed Console Errors**: Resolved browser console errors and warnings
- **✅ Fixed Image Loading**: Replaced external placeholder URLs with local data URIs
- **✅ Fixed Performance Issues**: Reduced excessive console logging and improved click handlers
- **✅ Fixed QueryClient Setup**: Added proper TanStack Query provider configuration

### 🚀 Performance Improvements

#### **Frontend Performance**
- **✅ Reduced Bundle Size**: Optimized imports and removed unused dependencies
- **✅ Improved Loading Times**: Fixed lazy loading and code splitting issues
- **✅ Enhanced Error Handling**: Better error boundaries and user feedback
- **✅ Optimized Re-renders**: Fixed unnecessary component re-renders

#### **Development Experience**
- **✅ Faster Build Times**: Optimized build configuration and dependencies
- **✅ Better Type Safety**: Enhanced TypeScript configuration and type definitions
- **✅ Improved Debugging**: Better error messages and development tools
- **✅ Cleaner Code**: Removed deprecated patterns and improved code organization

### 🔒 Security Enhancements

#### **Authentication Security**
- **✅ Role-Based Access Control**: Implemented proper RBAC for job requisitions
- **✅ Secure Token Handling**: Improved token refresh and validation
- **✅ Input Validation**: Enhanced form validation and sanitization
- **✅ XSS Protection**: Added proper content security policies

### 📱 UI/UX Enhancements

#### **Recruitment Module**
- **✅ Modern Form Design**: Implemented clean, responsive job requisition forms
- **✅ Status Indicators**: Added visual status chips for requisition states
- **✅ Search & Filter**: Enhanced list views with search and filtering capabilities
- **✅ Responsive Layout**: Improved mobile and tablet compatibility

#### **Admin Interface**
- **✅ Tab Navigation**: Clean tab-based navigation for different modules
- **✅ Data Tables**: Enhanced table components with sorting and pagination
- **✅ Modal Dialogs**: Improved modal components for better user interaction
- **✅ Loading States**: Better loading indicators and skeleton screens

### 🛠️ Technical Debt Resolution

#### **Code Quality**
- **✅ TypeScript Strict Mode**: Enabled strict type checking throughout
- **✅ ESLint Configuration**: Updated linting rules for better code quality
- **✅ Prettier Integration**: Consistent code formatting across the project
- **✅ Component Refactoring**: Improved component structure and reusability

#### **Dependency Management**
- **✅ Updated Dependencies**: Latest stable versions of all packages
- **✅ Security Audits**: Fixed known vulnerabilities in dependencies
- **✅ Build Optimization**: Improved build process and artifact generation
- **✅ Development Tools**: Enhanced development tooling and debugging

### 📊 Testing Improvements

#### **Test Coverage**
- **✅ Unit Tests**: Added comprehensive unit tests for critical components
- **✅ Integration Tests**: Enhanced API integration testing
- **✅ E2E Tests**: Improved end-to-end testing for user workflows
- **✅ Mock Services**: Better mock implementations for development

### 🔄 Migration Notes

#### **Breaking Changes**
- **Material-UI Grid**: Migrated from Grid v1 to Box with CSS Grid
- **Form Validation**: Updated validation schemas to be more strict
- **Type Definitions**: Enhanced type safety with stricter interfaces
- **API Client**: Updated generated API client structure

#### **Upgrade Guide**
```bash
# Update dependencies
npm install

# Regenerate API clients
npm run generate:api

# Clear cache and rebuild
npm run clean && npm run build
```

### 🎯 Next Steps

#### **Planned Improvements**
- **🔜 Advanced Analytics**: Enhanced reporting and analytics features
- **🔜 Mobile App**: Native mobile application development
- **🔜 Real-time Notifications**: WebSocket-based real-time updates
- **🔜 Advanced Workflows**: Complex approval workflows and automation
- **🔜 AI Integration**: Machine learning for HR insights and automation

#### **Performance Targets**
- **📈 Page Load Time**: < 2 seconds for all pages
- **📈 Bundle Size**: < 500KB for main bundle
- **📈 Lighthouse Score**: > 90 for all metrics
- **📈 Test Coverage**: > 80% for all modules

---

## 🤝 Contributing

### Development Workflow
1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**: Follow coding guidelines
4. **Write tests**: Ensure good test coverage
5. **Submit a pull request**: Include detailed description

### Code Style
- **TypeScript**: Strict mode enabled
- **ESLint**: Enforced code quality rules
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Standardized commit messages

### Testing
- **Unit Tests**: Component and utility testing
- **Integration Tests**: Feature workflow testing
- **E2E Tests**: Full user journey testing
- **API Tests**: Backend endpoint testing

## 📖 Documentation

### Comprehensive Guides
- [Developer Guide](docs/DEVELOPER_GUIDE.md) - Complete development guide
- [Features Overview](docs/FEATURES_OVERVIEW.md) - Detailed feature documentation
- [API Reference](docs/API_REFERENCE.md) - Complete API documentation
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment instructions

### Additional Resources
- [Component Library](docs/COMPONENT_LIBRARY.md) - UI component documentation
- [Testing Guide](docs/TESTING.md) - Testing strategies and examples
- [Security Guide](docs/SECURITY.md) - Security best practices

## 🆘 Support

### Getting Help
- **Documentation**: [docs.onehrms.com](https://docs.onehrms.com)
- **GitHub Issues**: [Report bugs](https://github.com/your-org/onehrms/issues)
- **Community Forum**: [Ask questions](https://community.onehrms.com)
- **Discord**: [Join our community](https://discord.gg/onehrms)

### Reporting Issues
When reporting issues, please include:
- **Environment**: OS, Node.js version, Python version
- **Steps to reproduce**: Detailed reproduction steps
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Screenshots**: If applicable
- **Logs**: Error logs and stack traces

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team**: For the amazing React framework
- **Material-UI Team**: For the excellent component library
- **FastAPI Team**: For the modern Python web framework
- **Keycloak Team**: For the enterprise authentication solution
- **Open Source Community**: For all the amazing tools and libraries

## 📊 Project Status

- **Version**: 1.0.0
- **Status**: Active Development
- **Last Updated**: December 2024
- **Maintainers**: oneHRMS Development Team

---

<div align="center">
  <p>Made with ❤️ by the oneHRMS Development Team</p>
  <p>
    <a href="https://github.com/your-org/onehrms">GitHub</a> •
    <a href="https://docs.onehrms.com">Documentation</a> •
    <a href="https://community.onehrms.com">Community</a>
  </p>
</div>
