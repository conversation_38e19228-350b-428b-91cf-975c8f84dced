[project]
name = "onehrms"
version = "1.0.0"
description = "Modern HR Management System with Microservices Architecture"
authors = [
    { name = "oneHRMS Development Team", email = "<EMAIL>" }
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.9"
keywords = ["hr", "hrms", "microservices", "fastapi", "attendance", "payroll"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: FastAPI",
    "Topic :: Office/Business :: Financial :: Accounting",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # Core FastAPI and async support
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "gunicorn>=21.2.0",
    # Database and ORM
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.0",
    "alembic>=1.12.0",
    # Redis and caching
    "redis>=5.0.0",
    "hiredis>=2.2.0",
    # Data validation and serialization
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
    # HTTP client and requests
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    # Authentication and security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    # Utilities
    "python-dotenv>=1.0.0",
    "click>=8.1.0",
    "rich>=13.6.0",
    "typer>=0.9.0",
    # Date and time handling
    "python-dateutil>=2.8.0",
    "pytz>=2023.3",
    # Logging and monitoring
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    # File handling
    "openpyxl>=3.1.0",
    "pandas>=2.1.0",
    # Email
    "emails>=0.6.0",
    # Configuration
    "pyyaml>=6.0.0",
    "toml>=0.10.0",
    "email-validator>=2.2.0",
    "aiofiles>=24.1.0",
    "psutil>=7.0.0",
    "python-json-logger>=3.3.0",
    "bcrypt>=4.3.0",
    "bleach>=6.2.0",
    "slowapi>=0.1.9",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.3.0",
    "pytest-html>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "factory-boy>=3.3.0",
    "faker>=20.0.0",

    # Code quality
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "flake8-docstrings>=1.7.0",
    "flake8-bugbear>=23.9.0",
    "flake8-comprehensions>=3.14.0",
    "mypy>=1.6.0",
    "bandit>=1.7.0",
    "pydocstyle>=6.3.0",

    # Pre-commit
    "pre-commit>=3.5.0",

    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",

    # Development tools
    "ipython>=8.16.0",
    "ipdb>=0.13.0",
    "watchdog>=3.0.0",

    # Type stubs
    "types-redis>=4.6.0",
    "types-requests>=2.31.0",
    "types-python-dateutil>=2.8.0",
    "types-PyYAML>=6.0.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.3.0",
    "factory-boy>=3.3.0",
    "faker>=20.0.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]

[project.urls]
Homepage = "https://onehrms.com"
Documentation = "https://docs.onehrms.com"
Repository = "https://github.com/onehrms/onehrms"
"Bug Reports" = "https://github.com/onehrms/onehrms/issues"
"Feature Requests" = "https://github.com/onehrms/onehrms/discussions"

[project.scripts]
onehrms = "hrms.cli:main"
attendance-service = "hrms.microservices.attendance.api:main"
employee-service = "hrms.microservices.employee.api:main"
payroll-service = "hrms.microservices.payroll.api:main"

[build-system]
requires = ["hatchling>=1.18.0"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "hrms/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["hrms"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | node_modules
  | __pycache__
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["hrms", "tests"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "redis.*",
    "sqlalchemy.*",
    "alembic.*",
    "asyncpg.*",
    "psycopg2.*",
    "uvicorn.*",
    "gunicorn.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=hrms",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=90",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow tests",
    "auth: Authentication tests",
    "database: Database tests",
    "api: API tests",
    "service: Service layer tests",
    "repository: Repository layer tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["hrms"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linting
[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]

# Ruff configuration (alternative to flake8)
[tool.ruff]
line-length = 88
target-version = "py39"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.ruff.isort]
known-first-party = ["hrms"]

# Pydocstyle configuration
[tool.pydocstyle]
convention = "google"
add-ignore = ["D100", "D101", "D102", "D103", "D104", "D105", "D106", "D107"]
